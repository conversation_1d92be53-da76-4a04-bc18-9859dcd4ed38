# Generated by Django 3.1.11 on 2021-05-19 14:32

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('waiting_list', '0005_change_in_meta_classes_and_status'),
    ]

    operations = [
        migrations.RenameField(
            model_name='waitinglistentry',
            old_name='email_sent_at',
            new_name='token_sent_at',
        ),
        migrations.AlterField(
            model_name='waitinglistentry',
            name='status',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'New'),
                    (1, 'Initial Email Sent'),
                    (2, 'Token Email Sent'),
                    (3, 'Token Used For Entry'),
                    (4, 'Token Used For Purchase'),
                    (5, 'No Limit Mail Sent'),
                ],
                default=0,
            ),
        ),
        migrations.AlterField(
            model_name='waitinglistentrystatus',
            name='status',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'New'),
                    (1, 'Initial Email Sent'),
                    (2, 'Token Email Sent'),
                    (3, 'Token Used For Entry'),
                    (4, 'Token Used For Purchase'),
                    (5, 'No Limit Mail Sent'),
                ]
            ),
        ),
    ]
