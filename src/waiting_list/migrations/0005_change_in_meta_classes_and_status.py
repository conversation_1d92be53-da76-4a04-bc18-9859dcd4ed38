# Generated by Django 3.1.8 on 2021-05-12 11:32

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('waiting_list', '0004_added_desired_time_to_waiting_list'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='waitinglistentry',
            options={
                'verbose_name': 'Customer entry waiting for T03',
                'verbose_name_plural': 'Customers entries waiting for T03',
            },
        ),
        migrations.AlterModelOptions(
            name='waitinglistsetup',
            options={
                'get_latest_by': 'created_at',
                'verbose_name': 'Limiting T03 Sale log',
                'verbose_name_plural': 'Limiting T03 Sale logs',
            },
        ),
        migrations.AlterField(
            model_name='waitinglistentry',
            name='status',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'New'),
                    (1, 'Token Email Sent'),
                    (2, 'Token Used For Entry'),
                    (3, 'Token Used For Purchase'),
                ],
                default=0,
            ),
        ),
    ]
