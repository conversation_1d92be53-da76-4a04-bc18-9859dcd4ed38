# Generated by Django 3.2.16 on 2023-02-23 08:56

from django.db import migrations


def rebind_countries_default_region(apps, schema_editor):
    Country = apps.get_model('regions', 'Country')
    non_default_region_countries = Country.objects.exclude(region__name='_default')

    FeedVariant = apps.get_model('product_feeds', 'FeedVariant')
    for fv in FeedVariant.objects.filter(country__region__name='_default').distinct():
        proper_country = non_default_region_countries.filter(
            region__name=fv.country.name
        ).first()
        if fv.id == proper_country.id:
            # country already ok
            continue
        fv.country = proper_country
        fv.save()


class Migration(migrations.Migration):

    dependencies = [
        ('product_feeds', '0032_feedvariant_validation_started_at'),
    ]

    operations = [
        migrations.RunPython(
            rebind_countries_default_region,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
