from rest_framework.permissions import BasePermission

from producers.models import Manufactor


class ManufacturerPermission(BasePermission):
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        try:
            manufactor = Manufactor.objects.get(owner=request.user)
        except Manufactor.DoesNotExist:
            return False

        return manufactor.name in Manufactor.MANUFACTURERS_API_INTEGRATED


class ProducerPanelPermission(BasePermission):
    message = 'You are not allowed to use this panel'

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.profile.is_producer


class ProducerPanelWarehousePermission(BasePermission):
    message = 'You are not allowed to use this panel'

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and request.user.profile.is_warehouse_operator
        )
