from django.db import models

from custom.enums import ShelfType
from custom.models import Timestampable
from producers.enums import (
    BatchFeatureEnum,
    BatchMailingStatusEnum,
)
from producers.models import ProductBatch


class AutoBatchingMailingConfiguration(models.Model):
    manufactor = models.ForeignKey(
        'Manufactor',
        on_delete=models.SET_NULL,
        null=True,
        related_name='mailing_configurations',
    )
    shelf_type = models.PositiveSmallIntegerField(
        choices=ShelfType.choices(), null=True, blank=True, default=None
    )
    include_all_shelf_types = models.BooleanField(default=False)
    feature = models.PositiveSmallIntegerField(
        choices=BatchFeatureEnum.choices(),
        default=BatchFeatureEnum.NONE,
    )

    mail_body = models.TextField()
    mail_subject = models.CharField(
        blank=True,
        default='',
        help_text=(
            'Include all key information here - ',
            'this will override the auto-generated subject.',
        ),
        max_length=255,
    )
    email = models.TextField(help_text='TO')
    cc_email = models.TextField(help_text='CC')

    attach_order_xlsx = models.BooleanField(default=False)
    attach_match_usage = models.BooleanField(default=False)
    attach_summary_usage = models.BooleanField(default=False)
    attach_summary_usage_for_telmex = models.BooleanField(default=False)
    attach_t13_handles = models.BooleanField(default=False)
    attach_production_timeline_with_labels = models.BooleanField(default=False)
    attach_t1_drawer_handle = models.BooleanField(default=False)
    attach_t1_door_handle = models.BooleanField(default=False)
    attach_nameplate_zip = models.BooleanField(default=False)
    attach_t25_legs_report = models.BooleanField(default=False)
    attach_nesting_desk_beam = models.BooleanField(default=False)
    attach_s93_karton = models.BooleanField(default=False)

    def __str__(self):
        shelf_type = (
            ShelfType(self.shelf_type).production_code
            if not self.include_all_shelf_types
            else 'ALL SHELF TYPES'
        )
        return (
            f'{self.id} '
            f'- {self.manufactor} '
            f'- {shelf_type} '
            f'- {BatchFeatureEnum(self.feature).name}'
        )

    @property
    def email_list(self):
        if self.email:
            return self.email.split(',')
        return []

    @property
    def cc_email_list(self):
        if self.cc_email:
            return self.cc_email.split(',')
        return []

    def does_batch_match(self, batch):
        if not BatchFeatureEnum(self.feature).does_batch_have_feature(batch):
            return False
        cached_shelf_type = batch.get_batch_type()
        return cached_shelf_type == ShelfType(self.shelf_type).production_code


class BatchMailingStatus(Timestampable):
    batches = models.ManyToManyField(
        ProductBatch,
        blank=True,
        related_name='mailing_statuses',
    )
    status = models.IntegerField(
        choices=BatchMailingStatusEnum.choices(),
        default=BatchMailingStatusEnum.NOT_SENT,
    )
    mail_conf = models.ForeignKey(
        'AutoBatchingMailingConfiguration',
        on_delete=models.SET_NULL,
        null=True,
        related_name='statuses',
    )
    error_message = models.TextField(blank=True)

    class Meta:
        verbose_name = 'Batch mailing status'
        verbose_name_plural = 'Batch mailing statuses'
