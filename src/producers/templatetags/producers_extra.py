from django import template
from django.utils.translation import gettext_lazy as _

register = template.Library()


@register.inclusion_tag('tag_producer_menu.html', takes_context=True)
def show_producer_menu(context: dict, is_recovery: bool = False) -> dict:
    user = context['request'].user
    if user.is_authenticated:
        manufactor = user.manufactor_set.first()
        if manufactor is None:
            return {'user': user}
        return {
            'new_shelves': 1,
            'ivy_batches': 1,
            'user': user,
            'is_recovery': is_recovery,
            'logistic_manifest': manufactor.pk == 1,
        }
    return {
        'is_recovery': is_recovery,
    }


@register.filter
def not_yet_date(value):
    return _('Not yet') if value is None else value
