"""Modol pomocniczy eksporterow danych.

Zawiera funkcje pomocnicze eksporterow.

__ Klasy i funkcje:
-- Export: Klasa zawierajaca funkcje pomocnicze eksporterow.
    -- merge_dict           @class
        Laczy slowniki w celu ulozenia ich na okreslonych layoutach

    -- save                 @class
        Zapisuje rysunki na dysku.

    -- param                @static
        Filtruje okreslone dane elementu do danych uzwanych w danym formacie.

    -- delete_drawings      @static
        Usuwa istniejace pliki o podanych nazwie i rozszerzeniu.

    -- print_dict           @static
        Drukuje slownik w czytelnym formacie

    -- sort_dict            @static
        Sortuje liste slownikow wg zadanego klucza

    -- filter_dict          @static
        Filtruje slownik do podanej listy kluczy

    -- get_size             @static
        Zwraca canvas dla danych w slowniku - bada wszystkie elementy
        lub zwraca wczesniej zdefniowane wartosci.

    -- set_transformation   @static
        Modyfikuje dane w dict skalujac elementy oraz ewentualnie tworzac
        lustrzane odbicie rysunku.
"""


import glob
import os
import pprint

from copy import deepcopy

from past.utils import old_div

#  "kolor": [HSL dla GH, nazwa dla SVG, DXF]
color = {
    'green': [(0.33, 0.76, 0.5), 'green', None],
    'red': [(0, 0.76, 0.5), 'red', None],
    'blue': [(0.55, 1, 0.35), 'blue', None],
    'orange': [(0.109, 0.757, 0.542), 'orange', None],
    'black': [(0, 0, 0), 'black', None],
    'grey': [(0, 0, 0.75), 'grey', None],
    'darkgrey': [(0, 0, 0.35), 'darkgrey', None],
    'lightgrey': [(0, 0, 0.15), 'lightgrey', None],
    'white': [(1, 1, 1), 'white', None],
    'yellow': [(0.163, 1, 0.5), 'yellow', None],
    'pink': [(0.919, 1, 0.5), 'pink', None],
    'green': [(0.316, 1, 0.5), 'green', None],
}


class Export(object):
    """Klasa z pomocniczymi funkcjami eksporterow."""

    @classmethod
    def prepare_data(
        cls,
        data_in,
        layers=None,
        sort=0,
        layout=0,
        dystans=[0],
        reset_orgin=False,
        fit_scale=False,
        flip=False,
        move=(None, None),
    ):
        """Modyfikuje dane DICT zgodnie z parametrami:.
        LISTA INPUTOW:
        ____ Modyfikacja "rysunkow":
        > data_in   - surowe dane (dict)
        > layers    - lista warstw (grup graficznych) ktore ma zawierac rysunek
                      Podanie listy list warstw utworzy kilka rysunkow z jednego
                      rysunku zawezonych do podanych danych:
                        layers=[["warstwa1", "warstwa2"], ["warstwa1", "warstwa3"], ...]
                      Podane listy tuples umozliwia dodanie dodatku do nazwy:
                        layers=[("dodatek nazwy", ["warstwa1", "warstwa2" ...]), ...]
        > sort      - sortuj liste dict na podstawie okreslonego parametru
            0 - Nie sortuj
            1 - ['drawing']['horizontal_number'])
        > reset_orgin - Przegeneruj rysunek przyjmujac jego naroznik w pkt 0, 0
        > fit_scale - Dopasuj parametry bounding box oraz size rysunku
                      do zawartosci
        > flip - Odbij rysunek w pionie (odbicie w poziomie przy zmianie
                                         parametrow)

        ____ Modyfikacja "ukladu"
        > layoutu   - Uklad rysunkow:
            0 - Zapisz osobne rysunki
            1 - Zapisz ZBIORCZY
        > dystans   - Odleglosc miedzy rysunkami
                      Podawane w formie listy (dowolna ilosc parametrow)
                      odleglosci miedzy kolejnymi rysunkami
        """
        # ____ 1. Przefiltruj i posortuj dane:
        data_cut = cls.filter_dict(data_in, layers)
        data_sort = cls.sort_dict(data_cut, sort=sort)
        # ____ 2. Polacz slowniki:
        # __ 2a. Pzesun dane w slowniku jesli ujemny canvas:
        if reset_orgin:
            for d in data_sort:
                size = cls.get_size(d, tryb=1)
                if size[0] < 0 and size[1] < 0:
                    d = cls.set_transformation(
                        d,
                        input_x=(size[2], size[2], -size[0]),
                        input_y=(size[3], size[3], -size[1]),
                    )
                elif size[0] < 0:
                    d = cls.set_transformation(d, input_x=(size[2], size[2], -size[0]))
                elif size[1] < 0:
                    d = cls.set_transformation(d, input_y=(size[3], size[3], -size[1]))
            # __ 2b. Odbij obracek
        if flip:
            if layout in [1]:
                data_sort = data_sort[::-1]
            for d in data_sort:
                size = cls.get_size(d, tryb=2)
                d = cls.set_transformation(
                    d, input_x=(size[2], size[2], 0), input_y=(size[3], -size[3], 0)
                )
        # __ 2c. Polacz dane slownikow
        data_merge = cls.merge_dict(data_sort, layout=layout, dystans=dystans)
        # __ 2d. Dopasuj rozmiary obrazka do rozmiarow faktycznych
        if fit_scale:
            for d in data_merge:
                size = cls.get_size(d, tryb=1)
                d['drawing']['canvas'] = size
                scale = old_div(max(size[2], size[3]), 1000)
                d['drawing']['size'] = (
                    old_div(size[2], scale),
                    old_div(size[3], scale),
                )
        # __ 2e. Przesun
        if move[0] != None or move[1] != None:
            for d in data_merge:
                size = cls.get_size(d, tryb=2)
                d = cls.set_transformation(
                    d,
                    input_x=(size[2], size[2], 0 if move[0] == None else move[0]),
                    input_y=(size[3], size[3], 0 if move[1] == None else move[1]),
                )
        # __ Zwroc zmodyfikowane dane
        return data_merge

    @classmethod
    def merge_dict(cls, data, layout=0, dystans=[0]):
        """Przeksztalcanie zbioru dict do innego zbioru
        (np. jedngo dict jako jeden rysunek).
        """
        if layout == 0:
            return data
        elif layout in [1]:
            # ___ 0. Deklaruje zmienne
            drawings = []
            data_scale = []
            drawing_x = []
            drawing_y = []
            # ___ 1. Sprawdzam rozmiar
            for d in data:
                x, y = cls.get_size(d)
                drawing_x.append(x)
                drawing_y.append(y)
            # ___ 2. Wykonuje transformacje
            # Tworze liste dystansow
            dystans = dystans * len(data)  # Co za duzo to zdrowo :)
            # Wykonuje transformacje
            position = 0
            for nr, d in enumerate(data):
                # Dane wejsciowe (domyslne - bez zmian):
                # > *input_x - (szerokosc wejsciowa, szerokosc docelowa, przesuniecie)
                # > *input_y - (wysokosc wejsciowa, wysokosc docelowa, przesuniecie)
                data_scale.append(
                    cls.set_transformation(
                        d,
                        input_x=(drawing_x[nr], drawing_x[nr], 0),
                        input_y=(drawing_y[nr], drawing_y[nr], position),
                    )
                )
                position = position + drawing_y[nr] + dystans[nr]
            # ___ 3. Lacze rysunki
            if data_scale == []:  # Warunek dla pustych danych
                return []
            data_merge = deepcopy(data_scale[0])
            for nr, d in enumerate(data_scale[1:]):
                data_merge['graphic'] += d['graphic']
            # ___ 4. Zwracam efekt
            return [data_merge]

    @staticmethod
    def gen_names(drawings, extension='', name='bez_nazwy', naming_system=3):
        """Generuje nazwy rysunkow.

        INPUTS:
        > drawings       - rysunki do wygenerowania nazw (rysunek, nazwa)
        > extension      - rozszerzenie plikow
        > *name          - nazwy plikow do zapisania
                                    DOMYSLNIE 'bez_nazwy'
        > *naming_system - Wybor skladowych nazwy plikow:
                                    DOMYSLNIE 3
                nazwa zbiorcza   nazwa rysunku      numeracja automatyczna
                   [name]          drawing[1]
            0 -                     x
            1 -     x               x
            2 -     x
            3 -     x                                       x
            4 -                     x                       x
            5 -     x               x                       x
        """

        # Utworz liste rysunkow jestli tylko jeden rysunek
        if not isinstance(drawings, list):
            drawings = [drawings]
        # Dodaj nazwy rysunkow
        for nr, drawing in enumerate(drawings):
            new_name = ''
            if naming_system in [1, 2, 3, 5]:
                # Dodaj nazwe ogolna
                new_name += name
            if naming_system in [0, 1, 4, 5]:
                # Dodaj nazwe rysunku
                new_name = new_name + '_' + drawing[1]
            if naming_system in [3, 4, 5]:
                # Dodaj numeracje
                new_name = new_name + '_' + str(nr)
            # Dodaj rozszerzenie
            new_name += extension
            drawings[nr] = (drawing[0], new_name)
        return drawings

    @staticmethod
    def save(drawings, path=''):
        """Zapisuje rysunki na dysku.

        INPUTS:
        > drawings       - rysunki do zapisania (rysunek, nazwa)
        > path           - sciezka do folderu
        """
        # Utworz folder dla rysunkow
        if not os.path.exists(path):
            os.makedirs(path)
        # print drawings
        # Zapisz kazdy rysunek
        for nr, drawing in enumerate(drawings):
            # Utworz sciezke
            current_path = path + drawing[1]
            # Zapisz plik

            drawing[0].saveas(current_path)

    @staticmethod
    def param(data, keys):
        """Generuje parametry wywolywanych funkcji.

        Zwraca wszystkie klucze (+ dane) z dostarczonego slownika (data)
        ktore wystepuja w inpucie keys (lista kluczy do wyodrebnienia).
        Dodatkowo klucze moga byc podane jako str lub tuple na potrzeby
        konwersji (zgodnie z ponizszym opisem):

        INPUTS:
        > data - {Dane do przefiltrowania w poszukiwaniu parametrow dla funkcji
        > keys - [lista kluczy przyjmowanych przez dana funkcje
            FORATY KLUCZY (jesli k in data.keys()), to:
            "k"         - dopisuje do param k: data[k]
            (k, k1)     - dopisuje do param k1: data[k]
            (k, k1, x)
                jesli type(x) == float - dopisuje do param k1: data[k] * x
                        Zapis na potrzeby uzycia mnoznika dla roznych formatow
                        Np. wielkosc tekstu w SVG = 0.7 wielkosci tekstu w DWG
                jesli type(x) == dict  - dopisuje do param k1: x[data[k]]
                        Zapis na potrzeby uzycia tablicy dla roznych formatow
                        Np. kolor 'red' w SVG = #111111 w DWG
        """
        param = {}
        for key in keys:
            if isinstance(key, str):
                # Jesli string to po prostu przerzuc
                if key in data:
                    param[key] = data[key]

            elif isinstance(key, tuple) and len(key) == 2:
                # Jesli tuple o dlugosci 2 - (k, k1)
                # Dopisuje do param k1: data[k]
                if key[0] in data:
                    param[key[1]] = data[key[0]]
            elif isinstance(key, tuple) and (
                len(key) == 3 and isinstance(key[2], float)
            ):
                # Jesli tuple o dlugosci 3 i x to float - (k, k1, x)
                # Dopisuje k1: data[k] * x
                if key[0] in data:
                    param[key[1]] = data[key[0]] * key[2]
            elif isinstance(key, tuple) and (
                len(key) == 3 and isinstance(key[2], dict)
            ):
                # Jesli tuple o dlugosci 3 i x to dic (k, k1, x)
                # Dopisuje k1: x[data[k]]
                if key[0] in data:
                    param[key[1]] = key[2][data[key[0]]]

        return param

    @staticmethod
    def delete_drawings(path, name='', extension=''):
        """Usuwa pliki o podanym poczatku nazwy i rozszerzeniu.

        INPUTS:
        > name      - poczatek nazwy szukanych plikow
        > *extension - rozszerzenie plikow do usuniecia (domyslnie - wszystkie)
        """
        # Usun pliki o podanej nazwie
        sciezka = path + name + '*' + extension
        for filename in glob.glob(sciezka):
            os.remove(filename)

    @staticmethod
    def print_dict(data, item=None, dict_list=True):
        """Drukuje podane dict w czytelny sposob. Item pozwala zawezic dane."""
        if dict_list and item != None:
            print(
                [
                    str(nr) + '__' + d['drawing']['file_name']
                    for nr, d in enumerate(data)
                ]
            )

        if item != None and item <= len(data):
            pprint.pprint(data[item])
        elif item != None:
            pprint.pprint(data[0])
        else:
            pprint.pprint(data)

    @staticmethod
    def sort_dict(data, sort=None):
        """Sortuje slwoniki wg podanych parametrow."""
        if sort == 0:
            return data
        elif sort == 1:
            data.sort(key=lambda x: x['drawing']['horizontal_number'])
            return data

    @staticmethod
    def get_color(color_in, mode):
        """Zwraca kolor w odpowiednim formacie"""
        if mode == 'GH':
            if isinstance(color_in, str):
                return color[color_in][0]

    @staticmethod
    def filter_dict(dicts_in, layers_in):
        """Filtruje slownik - ogranicza do podanej listy warstw"""

        if layers_in == None:
            return dicts_in

        dicts_out = []
        if not any(isinstance(el, list) for el in layers_in):
            if not any(isinstance(el, tuple) for el in layers_in):
                layers_in = [layers_in]

        for layers in layers_in:
            if isinstance(layers, tuple):
                name = layers[0]
                layers = layers[1]
            else:
                name = ''
            for dict_org in dicts_in:
                layers_list = []  # Lista wykorzystywanych warstw
                _dict = {}  # Utworz kopie slownika do edycji
                _dict['graphic'] = []  # Lista grup (warstw)
                _dict['drawing'] = deepcopy(dict_org['drawing'])
                _dict['drawing']['file_name'] += name
                _dict['drawing']['layers_list'] = []
                # Lista warstw rysunku
                dicts_out.append(_dict)  # Dodaj slownik do listy slownikow
                # Dla kazdego rysunku:
                for group in dict_org['graphic']:
                    if group['group_data']['layer'] in layers:
                        if group['group_data']['layer'] not in layers_list:
                            layers_list.append(group['group_data']['layer'])
                        _dict['graphic'].append(deepcopy(group))

                _dict['drawing']['layers_list'] = layers_list
        return dicts_out

    @staticmethod
    def get_size(data, tryb=0):
        """Zwraca szerokosc i wysokosc rysunku.

        Bada czy rysunek ma okreslony rozmiar, jesli nie bada najdalej
        wysuniete punkty posrod wszystkich elementow rysunku.

        Tryb okresla format zwracanych danych:
        > 0 - Podaj rozmiar (wym X i wym Y bez koordynatow)
        > 1 - Podaj koordynaty i rozmiar (start_x, start_y, wym_x, wym_y)
        > 2 - Podaj koordynaty uwzgledniajac obszar canvasu
        """
        coor_x = (0, 0)  # Skrajne wspolrzedne X (min, max)
        coor_y = (0, 0)  # Skrajne wspolrzedne Y (min, max)
        for nr, group in enumerate(data['graphic']):
            # ________ LINIE
            if 'line' in list(group.keys()):
                for ele in group['line']:
                    if isinstance(ele, tuple):
                        coor_x = (
                            min(coor_x[0], ele[0], ele[2]),
                            max(coor_x[1], ele[0], ele[2]),
                        )
                        coor_y = (
                            min(coor_y[0], ele[1], ele[3]),
                            max(coor_y[1], ele[1], ele[3]),
                        )
                    elif isinstance(ele, dict):
                        coor_x = (
                            min(coor_x[0], ele['start'][0], ele['end'][0]),
                            max(coor_x[1], ele['start'][0], ele['end'][0]),
                        )
                        coor_y = (
                            min(coor_y[0], ele['start'][1], ele['end'][1]),
                            max(coor_y[1], ele['start'][1], ele['end'][1]),
                        )
            # ________ POLILINE
            if 'polyline' in list(group.keys()):
                for ele in group['polyline']:
                    if isinstance(ele, list):
                        for point in ele:
                            coor_x = (
                                min(coor_x[0], point[0]),
                                max(coor_x[1], point[0]),
                            )
                            coor_y = (
                                min(coor_y[0], point[1]),
                                max(coor_y[1], point[1]),
                            )
                    elif isinstance(ele, dict):
                        for point in ele['points']:
                            coor_x = (
                                min(coor_x[0], point[0]),
                                max(coor_x[1], point[0]),
                            )
                            coor_y = (
                                min(coor_y[0], point[1]),
                                max(coor_y[1], point[1]),
                            )
            # ________ PROSTOKATY
            if 'rect' in list(group.keys()):
                for ele in group['rect']:
                    if isinstance(ele, tuple):
                        coor_x = (
                            min(coor_x[0], ele[0] + ele[2]),
                            max(coor_x[1], ele[0] + ele[2]),
                        )
                        coor_y = (
                            min(coor_y[0], ele[1] + ele[3]),
                            max(coor_y[1], ele[1] + ele[3]),
                        )
                    elif isinstance(ele, dict):
                        coor_x = (
                            min(coor_x[0], ele['insert'][0] + ele['size'][0]),
                            max(coor_x[1], ele['insert'][0] + ele['size'][0]),
                        )
                        coor_y = (
                            min(coor_y[0], ele['insert'][1] + ele['size'][1]),
                            max(coor_y[1], ele['insert'][1] + ele['size'][1]),
                        )
            # ________ POLIGONY
            if 'polygon' in list(group.keys()):
                for ele in group['polygon']:
                    if isinstance(ele, tuple):
                        for point in ele[0]:
                            coor_x = (
                                min(coor_x[0], point[0]),
                                max(coor_x[1], point[0]),
                            )
                            coor_y = (
                                min(coor_y[0], point[1]),
                                max(coor_y[1], point[1]),
                            )
                    elif isinstance(ele, dict):
                        for point in ele['points']:
                            coor_x = (
                                min(coor_x[0], point[0]),
                                max(coor_x[1], point[0]),
                            )
                            coor_y = (
                                min(coor_y[0], point[1]),
                                max(coor_y[1], point[1]),
                            )
            # _______ OKREGI
            if 'circle' in list(group.keys()):
                for ele in group['circle']:
                    if isinstance(ele, tuple):
                        coor_x = (
                            min(coor_x[0], ele[0] + ele[2]),
                            max(coor_x[1], ele[0] + ele[2]),
                        )
                        coor_y = (
                            min(coor_y[0], ele[1] + ele[2]),
                            max(coor_y[1], ele[1] + ele[2]),
                        )
                    elif isinstance(ele, dict):
                        coor_x = (
                            min(coor_x[0], ele['center'][0] + ele['radius']),
                            max(coor_x[1], ele['center'][0] + ele['radius']),
                        )
                        coor_y = (
                            min(coor_y[0], ele['center'][1] + ele['radius']),
                            max(coor_y[1], ele['center'][1] + ele['radius']),
                        )

        size_x = int(coor_x[1] - coor_x[0] if coor_x[0] < 0 else coor_x[1])
        size_y = int(coor_y[1] - coor_y[0] if coor_y[0] < 0 else coor_y[1])
        if tryb == 0:
            return (size_x, size_y)
        elif tryb == 1:
            return (coor_x[0], coor_y[0], size_x, size_y)
        elif tryb == 2:
            drawing_size = (0, 0, 0, 0)
            if 'size' in list(data['drawing'].keys()):
                drawing_size = (
                    0,
                    0,
                    data['drawing']['size'][0],
                    data['drawing']['size'][1],
                )
            if 'canvas' in list(data['drawing'].keys()):
                drawing_size = (
                    min(drawing_size[0], data['drawing']['canvas'][0]),
                    min(drawing_size[1], data['drawing']['canvas'][1]),
                    max(drawing_size[2], data['drawing']['canvas'][2]),
                    max(drawing_size[3], data['drawing']['canvas'][3]),
                )
            drawing_size = (
                (min(drawing_size[0], coor_x[0])),
                (min(drawing_size[1], coor_y[0])),
                (max(drawing_size[2], size_x)),
                (max(drawing_size[3], size_y)),
            )
            return drawing_size

    @staticmethod
    def set_transformation(data, input_x=None, input_y=None):
        """Transformacja rysunku na danych slownikowych.

        Funkcja skaluje i/lub tworzy odbicie lustrzane danych w okreslonej osi.
        Wartosci docelowe ujemne tworza lustrzane odbicie.
        Przesuniecie przesuwa rysunek o podana wartosc.
        gdy w slowniku circle lub text jest klucz "lock" i w nim ktoras
        nazwa parametru np 'font-size' to nie bedzie sie ten parametr skalowac

        Dane wejsciowe (domyslne - bez zmian):
        > *input_x - (szerokosc wejsciowa, szerokosc docelowa, przesuniecie)
        > *input_y - (wysokosc wejsciowa, wysokosc docelowa, przesuniecie)
        """
        # Ustal parametry transformacji rysunkow osobno dla x i y
        sx = sy = 1  # Skala domyslna (s)
        mx = my = ax = ay = 0  # Odbicie (a) i przesuniecie domyslne (m)
        if input_x:
            # __ Skala X: Oblicz skale oraz ewentualnie mnoznik ujemny dla lustra
            # Oblicz skale:
            sx = abs(float(input_x[1]) / input_x[0])
            # Oblicz mirror:
            sx = sx if abs(input_x[1]) == input_x[1] else -sx
            # Okresl przesuniecie:
            mx = input_x[2]  # Move X
            # Okresl odbicie:
            ax = 0 if abs(input_x[1]) == input_x[1] else -input_x[0]  # Flip X
        if input_y:
            # Skala Y: Oblicz skale oraz ewentualnie mnoznik ujemny dla lustra
            sy = abs(float(input_y[1]) / input_y[0])
            sy = sy if abs(input_y[1]) == input_y[1] else -sy
            my = input_y[2]  # Move Y
            ay = 0 if abs(input_y[1]) == input_y[1] else -input_y[0]  # Flip Y
        # Modyfikuj dane rysunku
        if 'drawing' in list(data.keys()):  # Jesli zdefiniowano parametry rys
            if 'canvas' in list(data['drawing'].keys()):
                canvas = data['drawing']['canvas']
                data['drawing']['canvas'] = (
                    (canvas[0]) * abs(sx) + mx,
                    (canvas[1]) * abs(sy) + my,
                    (canvas[2]) * abs(sx) + mx,
                    (canvas[3]) * abs(sy) + my,
                )
            if 'size' in list(data['drawing'].keys()):
                size = data['drawing']['size']
                data['drawing']['size'] = (
                    (size[0]) * abs(sx) + mx,
                    (size[1]) * abs(sy) + my,
                )
        # Dla kazdej grupy elementow
        for nr, group in enumerate(data['graphic']):
            # ________ GRUPA
            if 'group_data' in list(group.keys()):  # Jesli zdefiniowano styl gr
                pass

            # ________ TEKSTY
            if 'text' in list(group.keys()):  # Jesli wystepuja teksty
                for nr, ele in enumerate(group['text']):
                    if isinstance(ele, tuple):
                        group['text'][nr] = (
                            ele[0],
                            ((ele[1]) + ax) * sx + mx,
                            ((ele[2]) + ay) * sy + my,
                        )
                        if len(ele) == 4:
                            group['text'][nr] = (
                                ele[0],
                                ((ele[1]) + ax) * sx + mx,
                                ((ele[2]) + ay) * sy
                                + my,  # - float(ele[3]) * min(abs(sx), abs(sy)),
                                float(ele[3]) * min(abs(sx), abs(sy)),
                            )
                    elif isinstance(ele, dict):
                        if 'font-size' in list(ele.keys()):
                            size = float(ele['font-size']) * min(abs(sx), abs(sy))
                            if (
                                'lock' not in list(ele.keys())
                                or 'font-size' not in ele['lock']
                            ):
                                ele['font-size'] = size
                            if (
                                'lock' not in list(ele.keys())
                                or 'insert' not in ele['lock']
                            ):
                                ele['insert'] = (
                                    ((ele['insert'][0]) + ax) * sx + mx,
                                    ((ele['insert'][1]) + ay) * sy + my,  # + size
                                )
                        # elif "text_size" in ele.keys():
                        #     size = float(ele["text_size"]) * min(abs(sx), abs(sy))
                        #     ele["text_size"] = size
                        #     ele["insert"] = (
                        #         ((ele["insert"][0]) + ax) * sx + mx,
                        #         ((ele["insert"][1]) + ay) * sy + my# + size
                        #         )
                        elif 'height' in list(ele.keys()):
                            size = float(ele['height']) * min(abs(sx), abs(sy))
                            ele['height'] = size
                            ele['insert'] = (
                                ((ele['insert'][0]) + ax) * sx + mx,
                                ((ele['insert'][1]) + ay) * sy + my,  # + size
                            )
                        else:
                            ele['insert'] = (
                                ((ele['insert'][0]) + ax) * sx + mx,
                                ((ele['insert'][1]) + ay) * sy + my,
                            )

            # ________ LINIE
            if 'line' in list(group.keys()):  # Jesli wystepuja linie
                for nr, ele in enumerate(group['line']):
                    if isinstance(ele, tuple):
                        group['line'][nr] = (
                            ((ele[0]) + ax) * sx + mx,
                            ((ele[1]) + ay) * sy + my,
                            ((ele[2]) + ax) * sx + mx,
                            ((ele[3]) + ay) * sy + my,
                        )
                    elif isinstance(ele, dict):
                        ele['start'] = (
                            ((ele['start'][0]) + ax) * sx + mx,
                            ((ele['start'][1]) + ay) * sy + my,
                        )
                        ele['end'] = (
                            ((ele['end'][0]) + ax) * sx + mx,
                            ((ele['end'][1]) + ay) * sy + my,
                        )

            # ________ POLILINE
            if 'polyline' in list(group.keys()):  # Jesli wystepuja polilinie
                for ele in group['polyline']:
                    if isinstance(ele, list):
                        for nr, point in enumerate(ele):
                            ele[nr] = (
                                ((point[0]) + ax) * sx + mx,
                                ((point[1]) + ay) * sy + my,
                            )
                    elif isinstance(ele, dict):
                        for nr, point in enumerate(ele['points']):
                            ele['points'][nr] = (
                                ((point[0]) + ax) * sx + mx,
                                ((point[1]) + ay) * sy + my,
                            )

            # ________ PROSTOKATY
            if 'rect' in list(group.keys()):  # Jesli wystepuja prostokaty
                for nr, ele in enumerate(group['rect']):
                    if isinstance(ele, tuple):
                        _x = (
                            ((ele[0]) + ax) * sx + mx
                            if ax == 0
                            else -ax - ele[0] - ele[2] * abs(sx)
                        )
                        _y = (
                            ((ele[1]) + ay) * sy + my
                            if ay == 0
                            else -ay - ele[1] - ele[3] * abs(sy)
                        )
                        group['rect'][nr] = (
                            _x,
                            _y,
                            (ele[2]) * abs(sx),
                            (ele[3]) * abs(sy),
                        )
                    elif isinstance(ele, dict):
                        # FUCKUP?
                        _x = (
                            ((ele['insert'][0]) + ax) * sx + mx
                            if ax == 0
                            else -ax - (ele['insert'][0]) - (ele['size'][0]) * abs(sx)
                        )
                        _y = (
                            ((ele['insert'][1]) + ay) * sy + my
                            if ay == 0
                            else -ay - (ele['insert'][1]) - (ele['size'][1]) * abs(sy)
                        )
                        ele['insert'] = (_x, _y)
                        ele['size'] = (
                            (ele['size'][0]) * abs(sx),
                            (ele['size'][1]) * abs(sy),
                        )

            # ________ OKREGI
            if 'circle' in list(group.keys()):  # Jesli wystepuja okregi
                for nr, ele in enumerate(group['circle']):
                    if isinstance(ele, tuple):
                        group['circle'][nr] = (
                            ((ele[0]) + ax) * sx + mx,
                            ((ele[1]) + ay) * sy + my,
                            (ele[2]) * min(abs(sx), abs(sy)),
                        )
                    elif isinstance(ele, dict):
                        ele['center'] = (
                            ((ele['center'][0]) + ax) * sx + mx,
                            ((ele['center'][1]) + ay) * sy + my,
                        )
                        if (
                            'lock' not in list(ele.keys())
                            or 'radius' not in ele['lock']
                        ):
                            ele['radius'] = (ele['radius']) * min(abs(sx), abs(sy))
                        # else:
                        #     ele["radius"] = ele["radius"]

            # ________ POLIGONY
            if 'polygon' in list(group.keys()):  # Jesli wystepuja poligony
                for ele in group['polygon']:
                    if isinstance(ele, tuple):
                        for nr, point in enumerate(ele[0]):
                            ele[0][nr] = (
                                ((point[0]) + ax) * sx + mx,
                                ((point[1]) + ay) * sy + my,
                            )
                    elif isinstance(ele, dict):
                        for nr, point in enumerate(ele['points']):
                            ele['points'][nr] = (
                                ((point[0]) + ax) * sx + mx,
                                ((point[1]) + ay) * sy + my,
                            )
        return data

    @staticmethod
    def transform_data(data, group_id=None, object_keys=None, transform=(0, 0)):
        """
        funkcja do korekty polozenia fragmentow rysunku np po skonczeniu wszystkich skalowan itp,
        np na potrzeby korekty pozycji tekstu do rsvg
        :param data:
        :param group_id:
        :param object_keys:
        :param transform:
        :return:
        """
        if group_id is None:
            group_id = []
        if object_keys is None:
            object_keys = []

        # if data.get('graphic'):
        #     if group[  if group.get('group_data')
        if data[0].get('graphic'):
            for group in data[0]['graphic']:
                if group.get('group_data'):
                    if group['group_data'].get('id'):
                        if group['group_data'].get('id') in group_id:
                            for o_type in object_keys:
                                if o_type in group:
                                    for j, d in enumerate(group[o_type]):
                                        if o_type == 'text':
                                            # print d, 'tutaj'
                                            if isinstance(d, dict):
                                                d['insert'] = (
                                                    d['insert'][0] + transform[0],
                                                    d['insert'][1] + transform[1],
                                                )
                                            if isinstance(d, tuple):
                                                if len(d) == 3:
                                                    group[o_type][j] = (
                                                        d[0],
                                                        d[1] + transform[0],
                                                        d[2] + transform[1],
                                                    )
                                                elif len(d) == 4:
                                                    group[o_type][j] = (
                                                        d[0],
                                                        d[1] + transform[0],
                                                        d[2] + transform[1],
                                                        d[3],
                                                    )
                                            # d['insert'][0] += transform[0]
                                            # d['insert'][1] += transform[0]
                                            # TODO dodac inne typy danych np polygon i typ zapis
        return data

    @staticmethod
    def reset_origin(data, reset_x=0, reset_y=0, reset_z=0):
        """pozycjonuje rysunek na wspolrzednych 0,0,0 (zgodnie ze zmiennymi funkcji.
        DATA to zawsze lista!"""

        def flatten(input):
            """Zwraca wszystkie tuple z podanej stuktury.
            Input i output zawsze jako lista!"""
            output = []
            if not input:
                return []

            for lvl1 in input:
                if type(lvl1) in [dict, list]:
                    output.extend(
                        flatten(lvl1 if isinstance(lvl1, list) else list(lvl1.values()))
                    )
                elif isinstance(lvl1, tuple):
                    output.append(lvl1)

            return output

        def tuples_move(object, x=0, y=0, z=0):
            """Modyfikuje wszystkie tuple w obiekcie o podane wartosci"""
            modyfikator = [x, y, z] + [0] * 100
            for lvl1_index, lvl1 in enumerate(object):
                if isinstance(lvl1, dict):
                    for key, value in list(lvl1.items()):
                        if type(value) in [list, dict]:
                            lvl1[key] = tuples_move(value, x, y, z)
                        elif isinstance(value, tuple):
                            lvl1[key] = tuple(
                                [
                                    value - modyfikator[index]
                                    for index, value in enumerate(value)
                                ]
                            )

                elif isinstance(lvl1, tuple):
                    object[lvl1_index] = tuple(
                        [value - modyfikator[index] for index, value in enumerate(lvl1)]
                    )

            return object

        raw = flatten(data)
        minima = [
            next(x for x in sorted(raw, key=lambda x: x[index]))[index]
            for index in range(3)
        ]
        minima = [
            minima[0] + reset_x if reset_x is not None else 0,
            minima[1] + reset_y if reset_y is not None else 0,
            minima[2] + reset_z if reset_z is not None else 0,
        ]
        if minima != [0, 0, 0]:
            # print "  -> Moving", minima
            return tuples_move(data, *minima)
        else:
            return data
