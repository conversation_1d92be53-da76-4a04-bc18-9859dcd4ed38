[HEADER]
TYPE=BPP
VER=150

[DESCRIPTION]
|

[VARIABLES] {# lpx,lpy,lpz to wymiary horizontala do frezowania #}
PAN=LPX|{{bg_dict['graphic'][1]['rect'][0]['size'][0]}}||4|
PAN=LPY|{{bg_dict['graphic'][1]['rect'][0]['size'][1]}}||4|
PAN=LPZ|18||4|
PAN=ORLST|"5"||3|
PAN=SIMMETRY|0||1|
PAN=TLCHK|0||1|
PAN=TOOLING|""||3|
PAN=CUSTSTR|$B$KBsExportToNcRoverNET.XncExtraPanelData$V""||3|
PAN=FCN|1.000000||2|
PAN=XCUT|0||4|
PAN=YCUT|0||4|
PAN=JIGTH|0||4|
PAN=CKOP|0||1|
PAN=UNIQUE|0||1|
PAN=MATERIAL|"wood"||3|
PAN=PUTLST|""||3|
PAN=OPPWKRS|0||1|
PAN=UNICLAMP|0||1|
PAN=CHKCOLL|0||1|
PAN=WTPIANI|0||1|
PAN=COLLTOOL|0||1|
PAN=CALCEDTH|0||1|
PAN=ENABLELABEL|0||1|
PAN=LOCKWASTE|0||1|
PAN=LOADEDGEOPT|0||1|
PAN=ITLTYPE|0||1|
PAN=RUNPAV|0||1|
PAN=FLIPEND|0||1|
PAN=ENABLEMACHLINKS|0||1|
PAN=ENABLEPURSUITS|0||1|
PAN=ENABLEFASTVERTBORINGS|0||1|
PAN=FASTVERTBORINGSVALUE|0||4|

[PROGRAM]{#komendy do frezowania nawiertow,staly blok kodu bez zmian#}
{%- if is_extended is sameas true %}
    {%- if shelf_type == 0 %}
@ OFFSET, "", "Strona D", 82165260, "", 0 : 5, 5, 0, 0
    {%- else %}
@ OFFSET, "", "Strona D", 82165260, "", 0 : -0.35, -1.3, 0, 0
    {%- endif %}
{%- else %}
    {%- if shelf_type == 0 %}
@ OFFSET, "", "Strona D", 82165260, "", 0 : -6.5, -5, 0, 0
    {%- else %}
@ OFFSET, "", "Strona D", 82165260, "", 0 : -10.8, 0, 0, 0
    {%- endif %}
{%- endif %}
{%- if shelf_type == 0 %} {# obrobka krawedzi tylko dla type 01 #}
    {%- if material == 5 %}{#inna obrobka krawedzi horizontala przy fornirze#}
@ ROUT, "", "", 150692092, "", 0 : "P1007_2", 0, "2,4", 0, 1, "", 1, 20.1, -1, 0, 0, 32, 32, 50, 0, 45, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "DIAMAN", 102, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 25, 0, 0, 0, "", 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  @ START_POINT, "", "", 150692156, "", 0 : 1.5, 0, 0
  @ LINC_EP, "", "", 150692220, "", 0 : -1.5, 1.5, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 217332924, "", 0 : 0, lpy-3, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 217332092, "", 0 : 1.5, 1.5, 0, 0, 0, 0, 0, 0
  @ ENDPATH, "", "", 150692284, "", 0 :
@ ROUT, "", "", 94235820, "", 0 : "P1007", 0, "2,4", 0, 1, "", 1, 20.1, -1, 0, 0, 32, 32, 50, 0, 45, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "DIAMAN", 101, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 0, 0, 0, "", 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  @ START_POINT, "", "", 150752316, "", 0 : 0, 0, 0
  @ LINC_EP, "", "", 150752380, "", 0 : LPX, 0, 0, 0, 0, 0, 0, 0
  @ ENDPATH, "", "", 150752444, "", 0 :
    {%- else %}{#zwykla obrobka krawedzi horizontala przy pozostalych materialach#}
    {%- if joint_left > 0 or joint_right > 0 %}
@ ROUT, "", "", 82068148, "", 0 : "P1011", 0, "2", 0, 3, "", 1, 19.9, -1, 0, 0, 32, 32, 50, 0, 45, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "DIAMAN", 102, 1, 2, 1, 45, 0, 0, 0, 0, 0, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, "", 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
    {%- else %}
@ ROUT, "", "", 82068148, "", 0 : "P1011", 0, "2", 0, 3, "", 1, 19.9, -1, 0, 0, 32, 32, 50, 0, 45, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "DIAMAN", 100, 1, 2, 1, 45, 0, 0, 0, 0, 0, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, "", 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0
    {%- endif %}
  @ START_POINT, "", "", 83012932, "", 0 : 1.5, 0, 0
  @ LINC_EP, "", "", 83012996, "", 0 : LPX-3, 0, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 94417628, "", 0 : 1.5, 1.5, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 191777396, "", 0 : 0, LPY-3, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 94438572, "", 0 : -1.5, 1.5, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 83020484, "", 0 : -LPX+3, 0, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 94438444, "", 0 : -1.5, -1.5, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 191777076, "", 0 : 0, -LPY+3, 0, 0, 0, 0, 0, 0
  @ LINC_EP, "", "", 93610500, "", 0 : 1.5, -1.5, 0, 0, 0, 0, 0, 0
  @ ENDPATH, "", "", 82069364, "", 0 :
    {%- endif %}
{%- endif %}
{%- if joint_left > 0 %} {# lamello z lewej strony #}
    {%- if bg_dict['graphic'][1]['rect'][0]['size'][1] >= 240 and bg_dict['graphic'][1]['rect'][0]['size'][1] <= 310 %} {#jesli glebokosc horizontala 240-310 mm to jedno lamello na srodku #}
    @ ROUT, "", "", 94708780, "", 0 : "LAMELLO1", 0, "1", 0, 9, "", 1, 125, -1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "LAMELLO", 101, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, 1, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      @ START_POINT, "", "", 94708908, "", 0 : -60, LPY/2, 0
      @ LINC_EP, "", "", 94708972, "", 0 : 23.9, 0, 0, 0, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709036, "", 0 : 0, 0.001, -1.4, 1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709100, "", 0 : 0, -0.001, 1.4, -1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709164, "", 0 : -24.3, 0, 0, 0, 0, 0, 0, 0
      @ ENDPATH, "", "", 94709228, "", 0 :
        {%- if joint_left == 2 %}
            {%- if shelf_type == 2 %} {# srednica na kluczyk w fornirach 5 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "1", 7.5, LPY/2, 0, 14, 5, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- else %} {# w pozostalych 6 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "1", 7.5, LPY/2, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- endif %}
        {%- endif %}
    @ BG, "", "", 94707692, "", 0 : 1, "2,3", 44.3, 0, 0, 35, 8.2, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 4, -1, "KOLEK1", 0, "", "WIED8.2", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- else %}
    @ ROUT, "", "", 94708780, "", 0 : "LAMELLO1", 0, "1,2", 0, 9, "", 1, 125, -1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "LAMELLO", 101, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, 1, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      @ START_POINT, "", "", 94708908, "", 0 : -60, 94, 0
      @ LINC_EP, "", "", 94708972, "", 0 : 23.9, 0, 0, 0, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709036, "", 0 : 0, 0.001, -1.4, 1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709100, "", 0 : 0, -0.001, 1.4, -1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 94709164, "", 0 : -24.3, 0, 0, 0, 0, 0, 0, 0
      @ ENDPATH, "", "", 94709228, "", 0 :
        {%- if joint_left == 2 %}
            {%- if shelf_type == 2 %} {# srednica na kluczyk w fornirach 5 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "1,2", 7.5, 94, 0, 14, 5, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- else %} {# w pozostalych 6 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "1,2", 7.5, 94, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- endif %}
        {%- endif %}
    @ BG, "", "", 94707692, "", 0 : 1, "2,3", 43, 0, 0, 35, 8.2, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 4, -1, "KOLEK1", 0, "", "WIED8.2", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- endif %}
{%- endif %}
{%- if joint_right > 0 %} {# lamello z prawej strony #}
    {%- if bg_dict['graphic'][1]['rect'][0]['size'][1] >= 240 and bg_dict['graphic'][1]['rect'][0]['size'][1] <= 310 %} {#jesli glebokosc horizontala 240-310 mm to jedno lamello na srodku #}
    @ ROUT, "", "", 94783364, "", 0 : "LAMELLO2", 0, "4", 0, 9, "", 1, 125, -1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "LAMELLO", 101, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, 1, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      @ START_POINT, "", "", 95391148, "", 0 : -60, LPY/2, 0
      @ LINC_EP, "", "", 95391212, "", 0 : 23.9, 0, 0, 0, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391276, "", 0 : 0, 0.001, -1.4, 1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391340, "", 0 : 0, -0.001, 1.4, -1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391404, "", 0 : -24.3, 0, 0, 0, 0, 0, 0, 0
      @ ENDPATH, "", "", 95391468, "", 0 :
        {%- if joint_right == 2 %}
            {%- if shelf_type == 2 %} {# srednica na kluczyk w fornirach 5 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "4", 7.5, LPY/2, 0, 14, 5, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- else %} {# w pozostalych 6 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "4", 7.5, LPY/2, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- endif %}
        {%- endif %}
    @ BG, "", "", 95391660, "", 0 : 3, "2,3", 44.3, 0, 0, 35, 8.2, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 4, -1, "KOLEK2", 0, "", "WIED8.2", 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- else %}
    @ ROUT, "", "", 94783364, "", 0 : "LAMELLO2", 0, "4,3", 0, 9, "", 1, 125, -1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, -1, 0, 0, 0, 0, 0, "LAMELLO", 101, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, 1, 0, 0, 0, "", 5, 0, 20, 80, 60, 0, "", "", "ROUT", 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      @ START_POINT, "", "", 95391148, "", 0 : -60, 94, 0
      @ LINC_EP, "", "", 95391212, "", 0 : 23.9, 0, 0, 0, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391276, "", 0 : 0, 0.001, -1.4, 1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391340, "", 0 : 0, -0.001, 1.4, -1.4, 0, 0, 0, 0
      @ LINC_EP, "", "", 95391404, "", 0 : -24.3, 0, 0, 0, 0, 0, 0, 0
      @ ENDPATH, "", "", 95391468, "", 0 :
        {%- if joint_right == 2 %}
            {%- if shelf_type == 2 %} {# srednica na kluczyk w fornirach 5 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "4,3", 7.5, 94, 0, 14, 5, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- else %} {# w pozostalych 6 mm #}
        @ BG, "", "", 95392876, "", 0 : 0, "4,3", 7.5, 94, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
            {%- endif %}
        {%- endif %}
    @ BG, "", "", 95391660, "", 0 : 3, "2,3", 43, 0, 0, 35, 8.2, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 4, -1, "KOLEK2", 0, "", "WIED8.2", 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- endif %}
{%- endif %}
{%- if (joint_left > 0 or joint_right > 0) and shelf_type == 1 %} {# dodatkowy offset po wykonaniu lamello #}
@ OFFSET, "", "Strona D", 87861036, "", 0 : -0.35, -0.2, 0, 0
{%- endif %}
{%- if bg_dict["drawing"]["horizontal_level"] == 0 %}
    {%- for i in bg_dict["graphic"][3]['circle'] %}
@ BG, "", "", {{i['id']}}, "", 0 : 0, "2", {{i["center"][0]}}, {{i["center"][1]}}, 0, {{dia}}, {{wied_num_legs}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{i['pid']}}", 0, "", "WIED{{wied_num_legs}}", 100, 1, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- endfor %}
{%- else %}
    {%- for i in bg_dict["graphic"][3]['circle'] %}
        {%- if i['ELEM_TYPE'] == 'B' or i['ELEM_TYPE'] == 'S' %}{#nawierty pod plecy i supporty - inna srednica #}
@ BG, "", "", {{i['id']}}, "", 0 : 0, "2", {{i["center"][0]}}, {{i["center"][1]}}, 0, {{dia}}, {{wied_num_back}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{i['pid']}}", 0, "", "WIED{{wied_num_back}}", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- else %}{#nawierty pod pozostale elementy #}
@ BG, "", "", {{i['id']}}, "", 0 : 0, "2", {{i["center"][0]}}, {{i["center"][1]}}, 0, {{dia}}, {{wied_num_modeez}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{i['pid']}}", 0, "", "WIED{{wied_num_modeez}}", 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- endif %}
    {%- endfor %}
{%- endif %}
{%- if bg_dict["graphic"][5]['circle']|length > 0 %}
    {%- for i in bg_dict["graphic"][5]['circle'] %}
@ BG, "", "", {{i['id']}}, "", 0 : 0, "2", {{i["center"][0]}}, {{i["center"][1]}}, 0,  10, {{wied_num_bumper}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{i['pid']}}", 0, "", "WIED{{wied_num_bumper}}", 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
    {%- endfor %}
{%- endif %}
{%- if bg_dict["graphic"][2]['circle']|length > 0 %}
    {%- if is_extended is sameas true%} {# inne offsety gdy nie ma lamello i rozne dla type 01/02#}
    @ WAIT, "", "Obrot po osi Y", 83019972, "", 0 : 1, 5, 0, 0, 1, 0
        {%- if shelf_type == 0 %}
    @ OFFSET, "", "Strona G", 83013636, "", 0 : -0.3, -0.2, 0, 0
        {%- else %}
    @ OFFSET, "", "Strona G", 83013636, "", 0 : -0.35, -0.2, 0, 0
        {%- endif %}
    {%- else %}
    @ WAIT, "", "Obrot po osi Y", 83019972, "", 0 : 1, 5, 0, 0, 1, 0
        {%- if shelf_type == 0 %}
    @ OFFSET, "", "Strona G", 83013636, "", 0 : -10.8, 0, 0, 0
        {%- else %}
    @ OFFSET, "", "Strona G", 83013636, "", 0 : -10.8, 0, 0, 0
        {%- endif %}
    {%- endif %}
    {%- if joint_left == 3 %} {# nawiert na kluczyk #}
        {%- if bg_dict['graphic'][1]['rect'][0]['size'][1] >= 240 and bg_dict['graphic'][1]['rect'][0]['size'][1] <= 310 %} {#jesli glebokosc horizontala 240-310 mm to jeden otwor na srodku pod kluczyk tak jak lamello #}
    @ BG, "", "", 95392876, "", 0 : 0, "1", 7.5, LPY/2, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- else %}
    @ BG, "", "", 95392876, "", 0 : 0, "1,2", 7.5, 94, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- endif %}
    {%- endif %}
    {%- if joint_right == 3 %} {# nawiert na kluczyk #}
        {%- if bg_dict['graphic'][1]['rect'][0]['size'][1] >= 240 and bg_dict['graphic'][1]['rect'][0]['size'][1] <= 310 %} {#jesli glebokosc horizontala 240-310 mm to jeden otwor na srodku pod kluczyk tak jak lamello #}
    @ BG, "", "", 95392899, "", 0 : 0, "4", 7.5, LPY/2, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- else %}
    @ BG, "", "", 95392899, "", 0 : 0, "4,3", 7.5, 94, 0, 14, 6, 0, -1, 0, 0, 0, 0, 0, 2, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "IMBUS2", 0, "", "WIED6", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- endif %}
    {%- endif %}
    {%- for j in bg_dict["graphic"][2]['circle'] %}
        {%- if j['ELEM_TYPE'] == 'B' or j['ELEM_TYPE'] == 'S' %}{#nawierty pod plecy i supporty - inna srednica #}
@ BG, "", "", {{j['id']}}, "", 0 : 0, "2", {{j["center"][0]}}, {{j["center"][1]}}, 0, {{dia}}, {{wied_num_back}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{j['pid']}}", 0, "", "WIED{{wied_num_back}}", 3, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- else %}{#nawierty pod pozostale elementy #}
@ BG, "", "", {{j['id']}}, "", 0 : 0, "2", {{j["center"][0]}}, {{j["center"][1]}}, 0, {{dia}}, {{wied_num_modeez}}, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, -1, "P{{j['pid']}}", 0, "", "WIED{{wied_num_modeez}}", 0, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0, 0, 0
        {%- endif %}
    {%- endfor %}
{%- endif %}

[VBSCRIPT]

[MACRODATA]

[TDCODES]

[PCF]

[TOOLING]

[SUBPROGS]
