import os

from django.conf import settings

from jinja2 import (
    Environment,
    FileSystemLoader,
)


def render_svg(
    svg_data: list,
    template_name: str,
) -> str:
    template_path = os.path.join(
        settings.APPS_DIR,
        'producers',
        'gh',
        'moduly_instrukcja',
        'templates',
    )
    svg_template = Environment(
        loader=FileSystemLoader(template_path),
        autoescape=True,
    ).get_template(template_name)

    return svg_template.render(svg_data=svg_data)
