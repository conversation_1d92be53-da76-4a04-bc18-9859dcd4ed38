{% extends "producer_base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-6"><p></p></div>
</div>
<div class="row">
    <div class="inner cover col-md-12 hero-feature">
        <form method="post" action="" accept-charset="UTF-8">
            {% csrf_token %}
            <div class="form-group">
                <label for="objects">List of batches</label>
                <textarea
                    class="form-control"
                    rows=14
                    placeholder="List of batches in loose format ex.&#x0a;T1_12345&#x0a;T2_23456, T3_123"
                    id="objects"
                    name="objects"
                >
                    {{ related_items }}
                </textarea>
            </div>
            <div class="form-group">
                <label for="carrier">Carrier</label>
                <select name="carrier" id="carrier">
                    <option value="000326">TNT</option>
                </select>
            </div>
            <div class="form-group">
                <label for="registration">Car registration number</label>
                <input class="form-control" name="registration" id="registration" placeholder="">
            </div>
            <button class="btn-info btn" type="submit" name="type" value="manifest">Download XML</button>
        </form>
    </div>
</div>

{% endblock %}
