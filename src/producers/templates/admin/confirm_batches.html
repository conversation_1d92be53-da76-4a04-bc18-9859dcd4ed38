{% extends "admin/base_site.html" %}
{% block content %}
<h2>Confirm batches</h2>
<form method="post">{% csrf_token %}
  {{ form.as_p }}
  <button type="submit" class="button">Create batches</button>
</form>

<style>
    .batches-table, .products-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 2em;
        table-layout: fixed;
        margin-left: 0;
    }
    .batches-table th, .batches-table td,
    .products-table th, .products-table td {
        border-bottom: 1px solid #ccc;
        padding: 2px 4px;
        width: 12%;
        text-align: left;
        vertical-align: top;
        word-break: break-word;
        font-size: 90%;
    }
    .products-table {
        width: 50%;
        margin: 0.5em 0 1em 0;
        background: #fafbfc;
        border: 1px solid #eee;
    }
    .products-table th, .products-table td {
        border-bottom: 1px solid #eee;
        font-weight: normal;
        font-size: 90%;
    }
    .batch-header {
        margin-top: 2em;
        margin-bottom: 0.5em;
        font-size: 1.05em;
        font-weight: bold;
        background: #f0f0f0;
        padding: 8px 6px;
        border-radius: 3px;
        border-left: 4px solid #bbb;
    }
</style>

{% for batch in batches %}
    <table class="products-table" style="margin-top:2em;">
        <thead>
            <tr>
                <th colspan="8" style="background: #f0f0f0; font-weight: bold; font-size: 1.05em; border-left: 4px solid #bbb; padding: 8px 6px;">
                    {{ batch.product_group_key }},
                    Area: {{ batch.area }} m³,
                    Size: {{ batch.size }},
                </th>
            </tr>
            <tr>
                <th colspan="8" style="background: #f0f0f0; font-size: 1em; border-left: 4px solid #bbb; padding: 8px 6px;">
                    {{ batch.product_group_key.settings }}
                </th>
            </tr>
            <tr>
                <th>Id</th>
                <th>Shelf</th>
                <th>Color</th>
                <th>Area</th>
                <th>Desk</th>
                <th>S plus</th>
                <th>Priority</th>
                <th>Source priority</th>
            </tr>
        </thead>
        {% if batch.products %}
        <tbody>
            {% for product in batch.products %}
            <tr>
                <td>{{ product.id }}</td>
                <td>{{ product.cached_shelf_type }}</td>
                <td>{{ product.color_option.name }}</td>
                <td>{{ product.cached_area }}</td>
                <td>{{ product.is_desk }}</td>
                <td>{{ product.has_plus_feature }}</td>
                <td>{{ product.get_priority_display }}</td>
                <td>{{ product.get_source_priority_display }}</td>
            </tr>
            {% endfor %}
        </tbody>
        {% endif %}
    </table>
{% endfor %}
{% endblock %}
