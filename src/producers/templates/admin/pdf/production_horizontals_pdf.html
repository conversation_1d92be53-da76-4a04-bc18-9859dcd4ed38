{% load static %}
<!doctype html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Packing pdf</title>

    <link rel="stylesheet" type="text/css" href="{% static "css/Ivy_styles.css" %}">
    <style>
        @page {
            size: A4 landscape;
            margin: 0mm;
            /*margin: 52pt 41pt;*/
        }

        @media print {
            html, body {
                /*
                width: 794px;
                height: 1123px;
                */
                width: 100%;
                height: 100%;
                margin: 0 !important;
                position: relative;
            }
        }

        /*
                body {
                        margin: 0;
                        padding: 0;

                    width: 210mm;
                }
        */

        div.page {
            position: relative;
            height: 790px;
            page-break-inside: avoid;
        }

        svg {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
        }

        .content {
            position: relative;
            top: 0px;
            left: 0px;

        }

        /*----FIX na niedzialajacy import styli ----------------------------------------------------------------------*/

        <!--.default {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.default_txt {-->
          <!--fill: black;-->
          <!--stroke: None;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.example {-->
          <!--fill: red;-->
          <!--stroke: red;-->
          <!--stroke-width: 1;-->
          <!--font-size: 30px;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--/* Dla rysunku frotu */-->
        <!--.front_horizontalsVerticals {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_horizontals {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_Verticals {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_doors {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
          <!--stroke-dasharray: 10;-->
        <!--}-->
        <!--.front_drawers {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
          <!--stroke-dasharray: 10;-->
        <!--}-->
        <!--.front_backs {-->
          <!--fill: grey;-->
          <!--fill-opacity: 0.1;-->
          <!--stroke: black;-->
          <!--stroke-width: 1.2;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_supports {-->
          <!--fill: grey;-->
          <!--fill-opacity: 0.15;-->
          <!--stroke: black;-->
          <!--stroke-width: 2;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_labels {-->
          <!--stroke: black;-->
          <!--fill: black;-->
          <!--font-size: 65px;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.front_dimension {-->
          <!--stroke: black;-->
          <!--stroke-width: 2;-->
          <!--fill: black;-->
          <!--font-size: 45px;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--/* Dla rysunku zawartosci paczki */-->
        <!--.paczki_tabLines {-->
          <!--fill: None;-->
          <!--stroke: black;-->
          <!--stroke-width: 2;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.paczki_tabLinesthin {-->
          <!--fill: None;-->
          <!--stroke: grey;-->
          <!--stroke-width: 1;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.paczki_tabText {-->
          <!--fill: black;-->
          <!--text-anchor: middle;-->
          <!--stroke: None;-->
          <!--font: bold 100% arial;-->
          <!--text-shadow: 0 1px 2px rgba(0,0,0,.2);-->
        <!--}-->
        <!--.paczki_headText {-->
          <!--fill: grey;-->
          <!--stroke: None;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.paczki_levels {-->
          <!--fill: grey;-->
          <!--stroke: None;-->
          <!--font-family: arial;-->
        <!--}-->
        <!--.paczki_fill {-->
          <!--fill: grey;-->
          <!--fill-opacity: 0.5;-->
          <!--stroke: black;-->
          <!--font-family: arial;-->
        <!--}-->

        /*----FIX koniec ---------------------------------------------------------------------------------------------*/

        /*
      @media print {
          html {
              width: 210mm;
              margin: 20px !important;
          }

          .divpage{
          position: relative;
          page-break-inside: avoid;
          page-break-after: always;
          height: 100%;
          width: 1730px;
          height: 2460px;
          break-after: always;
      }

      }
  </style>
</head>
<body>
<div class="content">
    {% for svg in svgs %}
        <div class="page">
            {{ svg|safe }}
        </div>
    {% endfor %}
</div>
</body>
</html>