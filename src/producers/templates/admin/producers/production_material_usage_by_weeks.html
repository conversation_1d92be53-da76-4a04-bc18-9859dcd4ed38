{% extends "admin/base_site.html" %}
{% load humanize %}
{% load util_tags %}
{% load static %}

{% block extrahead %}
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="{% static 'datatables.min.css' %}"/>

    <script type="text/javascript" src="{% static 'datatables.js' %}"></script>

    <script type="text/javascript" src="{% static 'dataTables.buttons.min.js' %}"></script>
    <style>
        #panel-refresh .panel-heading { border: 1px solid; cursor: pointer;}
        #panel-refresh .panel-heading:hover { background-color: #F5F5F5; }
        #panel-refresh-icon { position: relative; top: 10px; }
    </style>

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script type="text/javascript">

        $(document).ready(function() {
            $('#example').DataTable({
                scrollY: true,
                scrollX: true,
                scrollCollapse: true,
                "lengthMenu": [ [25, 50, 100, 200, -1], [25, 50, 100, 200, "All"] ],
                info: false,
                dom: 'Bflrtip',
                buttons: [
                    'csv', 'excel', 'print'
                ],
                "columnDefs": [
                    { "type": "date", "targets": 3 },
                ],
                initComplete:function () {
                    this.api().columns('.selected-filter').every( function () {
                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                                .appendTo( $(column.footer()).empty() )
                                .on( 'change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                            $(this).val()
                                    );

                                    column
                                            .search( val ? '^'+val+'$' : '', true, false )
                                            .draw();
                                } );

                        column.data().unique().sort().each( function ( d, j ) {
                            select.append( '<option value="'+d+'">'+d+'</option>' )
                        } );
                    } );
                }
            });

            $('#example2').DataTable({
                scrollY: true,
                scrollX: true,
                scrollCollapse: true,
                "lengthMenu": [ [25, 50, 100, 200, -1], [25, 50, 100, 200, "All"] ],
                info: false,
                dom: 'Bflrtip',
                buttons: [
                    'csv', 'excel', 'print'
                ],
                initComplete:function () {
                    this.api().columns('.selected-filter').every( function () {
                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                                .appendTo( $(column.footer()).empty() )
                                .on( 'change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                            $(this).val()
                                    );

                                    column
                                            .search( val ? '^'+val+'$' : '', true, false )
                                            .draw();
                                } );

                        column.data().unique().sort().each( function ( d, j ) {
                            select.append( '<option value="'+d+'">'+d+'</option>' )
                        } );
                    } );
                }
            });
        } );

    </script>

{% endblock %}

{% block title %}Material usage by weeks, based on sent to customer date from logistic orders {% endblock %}

{% block content %}
    <h2>Summary</h2>
    <h3>Material 0= white plywood, 1=black , 2 = sycamore</h3>
    <table id="example2" class="display table table-striped table-bordered" cellspacing="0" width="100%">
        <thead>
        <tr>
            <th>Week</th>
            <th>Material</th>
            <th>Total m2</th>
            <th>Shelfs</th>
            <th>Average m2</th>
        </tr>
        </thead>
        <tbody>
            {% for material, summary in summary_materials.items %}
            <tr>
                <td>All time</td>
                <td>{{ material }}</td>
                <td>{{ summary.m2 }}</td>
                <td>{{ summary.items }}</td>
                <td>{{ summary.avg_m2 }}</td>
            </tr>
            {% endfor %}
            {% for entry in week_materials %}
            <tr>
                <td>{{ entry.0 }}</td>
                <td>{{ entry.1 }}</td>
                <td>{{ entry.2 }}</td>
                <td>{{ entry.3 }}</td>
                <td>{{ entry.4 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <table id="example" class="display table table-striped table-bordered" cellspacing="0" width="100%">
        <thead>
        <tr>
            <th>Item id </th>
            <th>Material</th>
            <th>m2</th>
            <th>Sent to customer date</th>
            <th>Week</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Item id </th>
            <th>Material</th>
            <th>m2</th>
            <th>Sent to customer date</th>
            <th>Week</th>
        </tr>
        </tfoot>
        <tbody>
        {% for item in items %}
            <tr>
                <td>{{ item.1.id }}</td>
                <td>{{ item.1.material }}</td>
                <td>{{ item.1.m2 }}</td>
                <td>{{ item.0.isoformat }}</td>
                <td>{{ item.1.week }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

{% endblock %}
