import pytest

from producers.templatetags.frontview_tags import selected


@pytest.mark.parametrize(
    ('row', 'elements_to_select'),
    [
        (['v2'], ['v2']),
        (['v2c'], ['v2']),
        (['v2c$r'], ['v2']),
        (['v21c$r'], ['v21']),
    ],
)
def test_selected_assigns_cell_selected_class_if_element_selected(
    row,
    elements_to_select,
):
    assert selected(row, elements_to_select) == 'element-complaint cell-selected'


@pytest.mark.parametrize(
    ('row', 'elements_to_select'),
    [
        (['v2'], ['v1']),
        (['v20c'], ['v2']),
    ],
)
def test_selected_doesnt_assign_cell_selected_class_if_element_not_selected(
    row,
    elements_to_select,
):
    assert selected(row, elements_to_select) == 'element-complaint'
