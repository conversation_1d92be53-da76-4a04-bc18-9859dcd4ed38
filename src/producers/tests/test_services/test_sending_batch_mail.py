from unittest.mock import Mock

import pytest

from custom.enums import ShelfType
from producers.enums import BatchFeatureEnum
from producers.services.send_auto_batching_mail import (
    group_batches_by_mail_conf,
    validate_if_many_order_xlsx_will_be_generated,
)


@pytest.mark.django_db
def test_group_batches_by_main_conf(
    auto_batching_mailing_configuration_factory, product_batch_factory, manufactor
):
    mail_conf_1 = auto_batching_mailing_configuration_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        feature=BatchFeatureEnum.BOTH,
    )
    mail_conf_2 = auto_batching_mailing_configuration_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        feature=BatchFeatureEnum.NONE,
    )
    mail_conf_3 = auto_batching_mailing_configuration_factory(
        manufactor=manufactor,
        shelf_type=None,
        include_all_shelf_types=True,
        feature=BatchFeatureEnum.NONE,
    )
    batch_type01 = product_batch_factory(manufactor=manufactor)
    batch_type01.get_batch_type = ShelfType.TYPE01.production_code
    batch_type01.get_batch_type = Mock()
    batch_type01.get_batch_type.return_value = ShelfType.TYPE01.production_code

    batch_type02 = product_batch_factory(manufactor=manufactor)
    batch_type02.get_batch_type = ShelfType.TYPE02.production_code
    batch_type02.get_batch_type = Mock()
    batch_type02.get_batch_type.return_value = ShelfType.TYPE02.production_code

    result = group_batches_by_mail_conf([batch_type01, batch_type02])
    expected = {
        mail_conf_1.id: [batch_type01.id],
        mail_conf_2.id: [batch_type01.id],
        mail_conf_3.id: [batch_type01.id, batch_type02.id],
    }
    assert result == expected


@pytest.mark.django_db
def test_validate_not_many_order_xlsx_will_be_created(
    auto_batching_mailing_configuration_factory, product_batch_factory, manufactor
):
    mail_conf_1 = auto_batching_mailing_configuration_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        feature=BatchFeatureEnum.BOTH,
        attach_order_xlsx=True,
    )
    mail_conf_2 = auto_batching_mailing_configuration_factory(
        manufactor=manufactor,
        shelf_type=ShelfType.TYPE01,
        feature=BatchFeatureEnum.NONE,
        attach_order_xlsx=True,
    )
    batch_type01 = product_batch_factory(manufactor=manufactor)
    grouped_batches = {
        mail_conf_1.id: [batch_type01.id],
        mail_conf_2.id: [batch_type01.id],
    }
    result = validate_if_many_order_xlsx_will_be_generated(grouped_batches)
    expected = {batch_type01.id}
    assert result == expected
