from django.shortcuts import reverse

import pytest

from rest_framework import status

from producers.choices import ProductStatus
from production_margins.enums import ElementsOrderType


@pytest.fixture
def batch_with_products(
    elements_order, product_batch_factory, product_factory, manufactor_owner
):
    batch = product_batch_factory(
        manufactor=manufactor_owner.manufactor_set.first(),
    )
    elements_order.batches.set([batch])
    elements_order.manufactor = batch.manufactor
    elements_order.order_type = ElementsOrderType.NEW_ORDER
    elements_order.save()
    products = list()
    products.append(product_factory(batch=batch, status=ProductStatus.SENT_TO_CUSTOMER))
    products.append(
        product_factory(batch=batch, status=ProductStatus.ASSIGNED_TO_PRODUCTION)
    )
    return {
        'elements_order': elements_order,
        'batch': batch,
        'products': products,
        'manufactor_owner': manufactor_owner,
    }


def test_elements_order_api_return_200(admin_client, batch_with_products):
    admin_client.force_login(batch_with_products['manufactor_owner'])
    url = reverse('elements_order_rest-list')
    response = admin_client.get(url)
    assert response.status_code == status.HTTP_200_OK
    assert response.data['count'] == 1
