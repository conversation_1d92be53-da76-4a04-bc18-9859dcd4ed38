# Generated by Django 4.1.9 on 2024-02-15 13:34

import django.core.files.storage

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0096_delete_supplier'),
    ]

    operations = [
        migrations.AddField(
            model_name='productbatchdetailsjetty',
            name='nesting_handle_blende',
            field=models.FileField(
                blank=True,
                max_length=400,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/batch/nesting_front_drawers/%Y/%m'
                ),
                verbose_name='Rozkroj blendy uchwytu',
            ),
        ),
    ]
