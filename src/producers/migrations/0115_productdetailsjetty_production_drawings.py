# Generated by Django 4.1.13 on 2025-02-05 15:53

import django.core.files.storage

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0114_autobatchingmailingconfiguration_attach_nesting_desk_beam'),
    ]

    operations = [
        migrations.AddField(
            model_name='productdetailsjetty',
            name='production_drawings',
            field=models.FileField(
                blank=True,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/product/production_drawings/%Y/%m'
                ),
            ),
        ),
    ]
