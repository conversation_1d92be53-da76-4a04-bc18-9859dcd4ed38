(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[8],{e51e:function(t,e,n){"use strict";n.r(e);var l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"},[n("div",[n("div",{staticStyle:{"font-size":"30vh"}},[t._v("\n      404\n    ")]),n("div",{staticClass:"text-h2",staticStyle:{opacity:".4"}},[t._v("\n      Oops. Nothing here...\n    ")]),n("q-btn",{staticClass:"q-mt-xl",attrs:{color:"white","text-color":"blue",unelevated:"",to:"/",label:"Go Home","no-caps":""}})],1)])},s=[],o={name:"ErrorNotFound"},a=o,c=n("2877"),i=n("9c40"),r=n("eebe"),u=n.n(r),p=Object(c["a"])(a,l,s,!1,null,null,null);e["default"]=p.exports;u()(p,"components",{QBtn:i["a"]})}}]);