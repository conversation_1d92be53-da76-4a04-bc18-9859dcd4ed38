(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[4],{"0e6e":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-page",{staticClass:"q-pa-lg q-mx-auto"},[a("BaseRecoveryMaterialsTable",e._b({ref:"table",attrs:{title:"Shelf market"},scopedSlots:e._u([{key:"body-cell-report",fn:function(t){return[a("q-td",{staticClass:"table-details q-gutter-sm",attrs:{props:t}},[t.row.recovery_report?a("a",{attrs:{href:""+t.row.recovery_report.manufactor_report}},[e._v("\n          "+e._s(t.row.recovery_report.id)+"\n        ")]):a("span",[e._v("-")])])]}}])},"BaseRecoveryMaterialsTable",{tableButtons:e.tableButtons,fetchData:e.fetchData,tableData:e.tableData,loading:e.loading,columns:e.columns},!1)),a("q-dialog",{model:{value:e.addItemModal,callback:function(t){e.addItemModal=t},expression:"addItemModal"}},[a("div",{staticClass:"modal-wrapper q-pa-lg bg-white"},[a("AddProductForShelfMarket",{attrs:{"close-modal":e.closeModals}})],1)])],1)},o=[],l=a("63f9"),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("div",{staticClass:"text-h6"},[e._v("\n    Dodaj produkt na Shelf Market\n  ")]),a("ProductInfo",{attrs:{"set-searched-product-id":e.setSearchedProductId}}),e.searchedProductId?a("div",{staticClass:"column"},[a("q-select",{attrs:{options:e.qualityFactorOptions,label:"Współczynnik jakościowy"},model:{value:e.qualityFactor,callback:function(t){e.qualityFactor=t},expression:"qualityFactor"}}),a("q-btn",{staticClass:"q-mt-lg q-mx-auto bg-indigo-9 text-white",attrs:{disable:!e.qualityFactor,label:"Dodaj"},on:{click:e.addProductToShelfMarket}})],1):e._e()],1)},s=[],i=a("8461"),d={name:"AddProductForShelfMarket",components:{ProductInfo:i["a"]},props:{closeModal:{type:Function,required:!0}},data(){return{searchedProductId:null,qualityFactor:null,qualityFactorOptions:[.5,.8,.9,1,1.1,1.2]}},methods:{addProductToShelfMarket(){this.$axios({method:"POST",url:"/api/v1/product_material_recovery/shelf_market/",data:{product:this.searchedProductId,quality_factor:this.qualityFactor}}).then((()=>{this.$q.notify({message:"Pomyślnie dodano produkt",type:"positive"}),this.closeModal()})).catch((e=>{if(400===e.response.status){const t=e.response.data.non_field_errors&&e.response.data.non_field_errors.join(", ");this.$q.notify({message:t||"Wystąpił błąd",type:"negative"})}else this.$q.notify({message:`Wystąpił błąd - ${e.message}`,type:"negative"})}))},setSearchedProductId(e){this.searchedProductId=e}}},c=d,u=a("2877"),p=a("ddd8"),m=a("9c40"),h=a("eebe"),f=a.n(h),y=Object(u["a"])(c,n,s,!1,null,null,null),b=y.exports;f()(y,"components",{QSelect:p["a"],QBtn:m["a"]});var g=a("bd4c"),q={name:"ShelfMarket",components:{BaseRecoveryMaterialsTable:l["a"],AddProductForShelfMarket:b},data(){return{tableButtons:[{label:"Dodaj pozycję",icon:"add",handleClick:()=>{this.addItemModal=!0},enableOnOnlySelectedItems:!1},{label:"Usuń pozycję",icon:"delete",handleClick:e=>this.deleteItems(e),enableOnOnlySelectedItems:!0,disableFunction:e=>e.some((e=>e.recovery_report))},{label:"Wygeneruj raport",icon:"description",handleClick:e=>this.generateReport(e),enableOnOnlySelectedItems:!0,disableFunction:e=>e.some((e=>e.recovery_report))}],tableData:[],columns:[{name:"id",align:"center",label:"ID pozycji",field:({id:e})=>e},{name:"productId",align:"center",label:"ID regału",field:({product:e})=>e.id},{name:"manufactor",align:"center",label:"Producent",field:({product:e})=>e.manufactor},{name:"color",align:"center",label:"Kolor",field:({product:e})=>e.color},{name:"cachedDepth",align:"center",label:"Głębokość",field:({product:e})=>e.depth},{name:"createdAt",align:"center",label:"Data utworzenia",field:({product:e})=>g["a"].formatDate(e.created_at,"DD-MM-YYYY HH:mm")},{name:"carrier",align:"center",label:"Typ wysyłki",field:({product:e})=>e.carrier||"-"},{name:"qualityFactor",align:"center",label:"Współczynnik jakościowy",field:({quality_factor:e})=>e||"-"},{name:"report",align:"center",label:"Nr raportu",field:({recovery_report:e})=>e?e.id:"-"},{name:"reportCreatedAt",align:"center",label:"Data utworzenia raportu",field:({recovery_report:e})=>e?g["a"].formatDate(e.created_at,"YYYY-MM-DD HH:mm"):"-"}],loading:!0,addItemModal:!1}},methods:{fetchData(){this.loading=!0,this.$axios.get("/api/v1/product_material_recovery/shelf_market/").then((e=>{this.tableData=e.data,this.loading=!1})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})}))},deleteItems(e){this.$q.dialog({title:"Confirm",message:"Czy na pewno chcesz usunąć pozycję?",cancel:!0,persistent:!0}).onOk((()=>{this.$axios.delete(`/api/v1/product_material_recovery/shelf_market?ids=${e.join(",")}`).then((()=>{this.fetchData()})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})}))}))},generateReport(e){this.$q.dialog({title:"Confirm",message:"Czy na pewno chcesz wygenerować raport?",cancel:!0,persistent:!0}).onOk((()=>{this.$axios({method:"POST",url:"/api/v1/product_material_recovery/shelf_market/create_report/",data:e.reduce(((e,t)=>(e.push({shelf_market_id:t}),e)),[])}).then((()=>{this.$q.notify({message:"Pomyślnie wygenerowano raport",type:"positive"}),this.$refs.table.resetSelection(),this.fetchData()})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})}))}))},closeModals(){this.addItemModal=!1,this.$refs.table.resetSelection(),this.fetchData()}}},_=q,v=(a("71c0"),a("9989")),I=a("db86"),k=a("24e8"),D=Object(u["a"])(_,r,o,!1,null,"06b30ea4",null);t["default"]=D.exports;f()(D,"components",{QPage:v["a"],QTd:I["a"],QDialog:k["a"]})},5958:function(e,t,a){},"63f9":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-table",{staticClass:"sticky-header-table",attrs:{title:e.title,"row-key":"id",data:e.tableData,columns:e.columns,loading:e.loading,"loading-label":"Pobieranie danych...","rows-per-page-label":"Ilość na stronie:","rows-per-page-options":[25,10,50,100,0],selection:"multiple",selected:e.selected,flat:"",bordered:"",separator:"cell"},on:{"update:selected":function(t){e.selected=t}},scopedSlots:e._u([{key:"top",fn:function(){return e._l(e.tableButtons,(function(t,r){return a("q-btn",{key:"table-btn-"+r,attrs:{label:t.label,icon:t.icon,disable:t.disableFunction&&t.disableFunction(e.selected)||t.enableOnOnlySelectedItems&&(e.loading||0===e.selected.length),flat:"",dense:"",color:"primary"},on:{click:function(a){t.handleClick(e.getSelectedIds())}}})}))},proxy:!0},e._l(e.$scopedSlots,(function(t,a){return{key:a,fn:function(t){return[e._t(a,null,null,t)]}}}))],null,!0)})},o=[],l={name:"BaseRecoveryMaterialsTable",props:{title:{type:String,required:!0},columns:{type:Array,required:!0},fetchData:{type:Function,required:!0},tableButtons:{type:Array,required:!1,default:()=>[]},loading:{type:Boolean,required:!1,default:!1},tableData:{type:Array,required:!1,default:()=>[]},selection:{type:String,required:!1,default:"multiple"}},data(){return{pagination:{rowsPerPage:250},selected:[],filter:""}},created(){this.fetchData()},methods:{getSelectedIds(){return this.selected.map((e=>e.id))},resetSelection(){this.selected=[]}}},n=l,s=a("2877"),i=a("eaac"),d=a("9c40"),c=a("eebe"),u=a.n(c),p=Object(s["a"])(n,r,o,!1,null,null,null);t["a"]=p.exports;u()(p,"components",{QTable:i["a"],QBtn:d["a"]})},"71c0":function(e,t,a){"use strict";a("5958")},8461:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("div",{staticClass:"column"},[a("q-input",{attrs:{label:"ID Produktu",dense:"",autofocus:""},model:{value:e.searchedProductId,callback:function(t){e.searchedProductId=t},expression:"searchedProductId"}}),a("q-btn",{staticClass:"q-my-sm bg-indigo-9 text-white",attrs:{loading:e.loading,label:"Szukaj"},on:{click:e.getProductInfo}})],1),e.productInfo.id?a("div",{staticClass:"column items-center"},[a("q-input",{staticClass:"q-my-sm",attrs:{value:e.productInfo.id,label:"ID Produktu",dense:"",disable:"",outlined:"",readonly:""}}),a("q-input",{staticClass:"q-my-sm",attrs:{value:e.productInfo.manufactor,label:"Producent",dense:"",disable:"",outlined:"",readonly:""}}),a("q-input",{staticClass:"q-my-sm",attrs:{value:e.productInfo.color,label:"Kolor",dense:"",disable:"",outlined:"",readonly:""}}),a("q-input",{staticClass:"q-my-sm",attrs:{value:e.productInfo.depth,label:"Głębokość",dense:"",disable:"",outlined:"",readonly:""}}),a("q-input",{staticClass:"q-my-sm",attrs:{value:e.getFormattedDate(e.productInfo.created_at),label:"Utworzony",dense:"",disable:"",outlined:"",readonly:""}}),a("q-input",{staticClass:"q-my-sm",attrs:{value:e.productInfo.carrier,label:"Typ wysyłki",dense:"",disable:"",outlined:"",readonly:""}})],1):e._e()])},o=[],l=(a("a79d"),a("bd4c")),n={name:"ProductInfo",props:{setSearchedProductId:{type:Function,required:!0}},data(){return{loading:!1,productInfo:{id:null,manufactor:null,color:null,depth:null,created_at:null,carrier:null},searchedProductId:null}},methods:{getFormattedDate(e){return l["a"].formatDate(e,"DD-MM-YYYY HH:mm")},getProductInfo(){this.loading=!0,this.$axios.get(`/api/v1/product_material_recovery/${this.searchedProductId}/product_info/`).then((e=>{const{id:t,manufactor:a,color:r,depth:o,created_at:l,carrier:n}=e.data;this.productInfo={id:t,manufactor:a,color:r,depth:o,created_at:l,carrier:n},this.setSearchedProductId(this.searchedProductId)})).catch((e=>{404===e.response.status?this.$q.notify({message:"Nie znaleziono produktu o podanym ID",type:"negative"}):this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})})).finally((()=>{this.loading=!1}))}}},s=n,i=a("2877"),d=a("27f9"),c=a("9c40"),u=a("eebe"),p=a.n(u),m=Object(i["a"])(s,r,o,!1,null,null,null);t["a"]=m.exports;p()(m,"components",{QInput:d["a"],QBtn:c["a"]})}}]);