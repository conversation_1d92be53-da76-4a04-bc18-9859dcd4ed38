import csv

from datetime import datetime
from optparse import make_option

from django.core.management.base import BaseCommand
from django.utils import timezone

from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from payments.models import Transaction


class Command(BaseCommand):
    help = (
        'Merge two materials into one, disabling '
        'the first one, moving all usages and so on'
    )

    option_list = list(BaseCommand.option_list) + [
        make_option(
            '-f',
            '--input-file',
            action='store',
            type='string',
            dest='inputfile',
            help='import report',
        ),
    ]

    def handle(self, *args, **options):
        url = options['inputfile']
        csv_file = open(url, 'r')
        if not url.endswith('.csv'):
            return

        if 'payments_accounting' in url or 'settlement_detail_report' in url:

            print(f'Processing accounting report: {url}')

            if 'payments_accounting' in url:

                for r in csv.reader(csv_file):
                    psp_reference = r[2]
                    merchant_reference = r[3]
                    record_type = r[9].lower()

                    try:
                        transaction = Transaction.objects.get(
                            reference=psp_reference, status='AUTHORISATION'
                        )
                    except Transaction.DoesNotExist:
                        print(
                            f'Transaction pspReference={psp_reference} not found. '
                            f'Searching by merchantReference={merchant_reference}'
                        )
                        transaction = Transaction.objects.filter(
                            merchant_reference=merchant_reference,
                            status='AUTHORISATION',
                        ).last()

                    if transaction:
                        if 'settledexternally' in record_type:
                            print(
                                f'Setting settled date for order {transaction.order_id}'
                            )
                            booking_date = timezone.make_aware(
                                datetime.strptime(r[5], '%Y-%m-%d %H:%M:%S')
                            )
                            # transaction.update(status=record_type.upper())
                            transaction.order.settled_at = booking_date
                            transaction.order.payable_booking_date = booking_date
                            transaction.order.save(
                                update_fields=['settled_at', 'payable_booking_date']
                            )

                            order = transaction.order
                            if not Invoice.objects.filter(order=order).exists():
                                Invoice.objects.create(
                                    order=order,
                                    sell_at=booking_date,
                                    corrected_sell_at=booking_date,
                                    issued_at=timezone.now(),
                                    currency_symbol=order.get_region().currency.symbol,
                                )
                    else:
                        print(
                            f'Notification without transaction for '
                            f'pspReference={psp_reference} '
                            f'merchantReference={merchant_reference}'
                        )

            elif 'settlement_detail_report' in url:

                records = []
                batch_payable_date = None
                split_batch_status = [
                    'merchantpayout',
                    'depositcorrection',
                    'balancetransfer',
                ]

                for r in csv.reader(csv_file):
                    psp_reference = r[2]
                    merchant_reference = r[3]
                    creation_date = r[5]
                    record_type = r[7].lower()

                    if record_type == 'type':
                        continue

                    if merchant_reference and len(merchant_reference) > 0:
                        records.append(
                            {
                                'psp_reference': psp_reference,
                                'merchant_reference': merchant_reference,
                                'creation_date': creation_date,
                                'record_type': record_type,
                            }
                        )
                    elif record_type in split_batch_status:
                        batch_payable_date = creation_date

                if batch_payable_date:
                    for r in records:
                        transaction = None
                        try:
                            transaction = Transaction.objects.get(
                                reference=r['psp_reference'], status='AUTHORISATION'
                            )
                        except Transaction.DoesNotExist:
                            print(
                                'Transaction pspReference={} not found. '
                                'Searching by merchantReference={}'.format(
                                    r['psp_reference'],
                                    r['merchant_reference'],
                                )
                            )
                            transaction = Transaction.objects.filter(
                                merchant_reference=r['merchant_reference'],
                                status='AUTHORISATION',
                            ).last()
                        if transaction:
                            order = transaction.order
                            print(
                                f'Setting payable booking date '
                                f'for order {order.id}: {batch_payable_date}'
                            )
                            payable_booking_date = timezone.make_aware(
                                datetime.strptime(
                                    batch_payable_date, '%Y-%m-%d %H:%M:%S'
                                )
                            )
                            order.payable_booking_date = payable_booking_date
                            order.settled_at = payable_booking_date
                            order.save(
                                update_fields=['payable_booking_date', 'settled_at']
                            )
                            if not Invoice.objects.filter(
                                order=order, status=InvoiceStatus.ENABLED
                            ).exists():
                                Invoice.objects.create(
                                    order=order,
                                    sell_at=payable_booking_date,
                                    corrected_sell_at=payable_booking_date,
                                    issued_at=timezone.now(),
                                    currency_symbol=order.get_region().currency.symbol,
                                )
                            else:
                                print(
                                    f'Invoice for order_id={order.id} already exists!'
                                )
                        else:
                            print(
                                'Transaction pspReference={} merchantReference={} '
                                'does not exist!'.format(
                                    r['psp_reference'],
                                    r['merchant_reference'],
                                )
                            )
