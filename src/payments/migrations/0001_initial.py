# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('live', models.BooleanField(default=False)),
                (
                    'merchant_reference',
                    models.CharField(
                        blank=True,
                        max_length=128,
                        verbose_name='Merchant reference - merchantReference',
                    ),
                ),
                (
                    'psp_reference',
                    models.CharField(
                        blank=True,
                        max_length=128,
                        verbose_name='Psp reference - pspReference',
                    ),
                ),
                ('code', models.CharField(max_length=128)),
                ('success', models.BooleanField(default=False)),
                ('event_date', models.DateTimeField()),
                (
                    'payment_method',
                    models.CharField(
                        blank=True, max_length=128, verbose_name='Payment method'
                    ),
                ),
                ('operations', models.Char<PERSON>ield(max_length=1024)),
                ('reason', models.TextField(blank=True)),
                ('amount_value', models.IntegerField(blank=True, null=True)),
                ('amount_currency', models.CharField(max_length=10)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notification',
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'amount',
                    models.IntegerField(blank=True, null=True, verbose_name='Amount'),
                ),
                (
                    'reference',
                    models.CharField(
                        blank=True,
                        max_length=128,
                        verbose_name='Payment reference - pspReference',
                    ),
                ),
                (
                    'merchant_reference',
                    models.CharField(
                        blank=True,
                        max_length=128,
                        verbose_name='Merchant reference - merchantReference',
                    ),
                ),
                (
                    'skin_code',
                    models.CharField(
                        blank=True, max_length=128, verbose_name='Skin code (adyen)'
                    ),
                ),
                (
                    'payment_method',
                    models.CharField(
                        blank=True, max_length=128, verbose_name='Payment method'
                    ),
                ),
                (
                    'status',
                    models.CharField(blank=True, max_length=128, verbose_name='Status'),
                ),
                (
                    'return_data',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='Return data'
                    ),
                ),
                ('live', models.BooleanField(default=False, verbose_name='Live')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='Date Created'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='Date Updated'),
                ),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='transactions',
                        to='orders.Order',
                        verbose_name='Source order',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Transaction',
                'verbose_name_plural': 'Transactions',
            },
        ),
        migrations.AddField(
            model_name='notification',
            name='transaction',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='payments.Transaction',
            ),
        ),
    ]
