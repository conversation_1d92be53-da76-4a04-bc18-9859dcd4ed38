# import needs to stay here so that django can process it serializer module
from django.core.serializers.json import Deserializer  # noqa: F401
from django.core.serializers.json import Serializer as DjangoSerializer

from model_transfers.data_managers import CstmExportManager


class Serializer(DjangoSerializer):
    def handle_field(self, obj, field):
        super().handle_field(obj, field)
        model_name = obj._meta.label
        handler = (
            CstmExportManager.files_config.get(model_name, {})
            .get(
                field.name,
                {},
            )
            .get('handler', None)
        )
        if self._current[field.name] and handler and 'CompressImageHandler' in handler:
            value = self._current[field.name]
            self._current[field.name] = value[: value.rfind('.')] + '.jpeg'
