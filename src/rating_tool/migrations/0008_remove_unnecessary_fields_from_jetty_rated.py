# Generated by Django 3.1.7 on 2021-04-08 14:37

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('rating_tool', '0007_clean_up_base64_from_boards_and_categories'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_1',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_2',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_3',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_4',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_5',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_6',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_7',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='category_8',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='color',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='custom',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='depth',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='doors',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='drawers',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='extended',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='height',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='in_unique_list',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='info',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='open',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='pattern',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='price',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_1',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_10',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_11',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_12',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_13',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_14',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_15',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_16',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_17',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_18',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_19',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_2',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_20',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_3',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_4',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_5',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_6',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_7',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_8',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='rating_9',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='scoring',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='section',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='shelf_type',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='size_l',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='size_m',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='size_s',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='sort_by',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='source',
        ),
        migrations.RemoveField(
            model_name='jettyrated',
            name='width',
        ),
    ]
