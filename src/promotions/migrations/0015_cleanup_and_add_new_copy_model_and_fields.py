# Generated by Django 3.2.16 on 2023-04-06 07:18

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):
    dependencies = [
        ('regions', '0006_change_chf_currency_rate'),
        ('vouchers', '0024_alter_itemdiscount_furniture_category_name'),
        ('promotions', '0014_remove_promotionconfig_languages'),
    ]

    operations = [
        # Some overall refactor (change names, fix description etc.)
        migrations.RenameField(
            model_name='promotion',
            old_name='promocode',
            new_name='promo_code',
        ),
        migrations.RenameField(
            model_name='promotion',
            old_name='promogroup',
            new_name='promo_group',
        ),
        migrations.AlterField(
            model_name='promotion',
            name='promo_group',
            field=models.ManyToManyField(
                related_name='promotions', to='vouchers.VoucherGroup'
            ),
        ),
        migrations.AlterField(
            model_name='promotionpicture',
            name='object_id',
            field=models.PositiveIntegerField(
                blank=True, null=True, verbose_name='Furniture ID'
            ),
        ),
        migrations.AlterModelOptions(
            name='promotionconfig',
            options={
                'ordering': ('created_at',),
                'verbose_name': 'Promotion config',
                'verbose_name_plural': 'Promotion configs',
            },
        ),
        migrations.AlterField(
            model_name='promotionconfig',
            name='enabled_regions',
            field=models.ManyToManyField(
                to='regions.Region',
            ),
        ),
        migrations.AlterField(
            model_name='promotionconfig',
            name='short_name',
            field=models.CharField(
                help_text='for cookies, analytics and so on, only letters',
                max_length=150,
            ),
        ),
        # Create PromotionConfigCopy
        migrations.CreateModel(
            name='PromotionConfigCopy',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'language',
                    models.CharField(
                        choices=[
                            ('en', 'English'),
                            ('de', 'Deutsch'),
                            ('fr', 'Français'),
                            ('es', 'Español'),
                            ('nl', 'Nederlands'),
                        ],
                        default='en',
                        max_length=2,
                    ),
                ),
                ('hp_promo_copy_header', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_text', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_cta', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_link', models.TextField(blank=True, null=True)),
                ('ribbon_copy_header_1', models.TextField(blank=True, null=True)),
                (
                    'ribbon_copy_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                ('ribbon_copy_link', models.TextField(blank=True, null=True)),
                ('cart_ribbon_copy_header_1', models.TextField(blank=True, null=True)),
                (
                    'cart_ribbon_copy_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_promo_alert_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_promo_alert_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_promo_alert_mobile_1_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                ('grid_copy_slot_1_header_1', models.TextField(blank=True, null=True)),
                ('grid_url', models.URLField(blank=True, max_length=100)),
                (
                    'config',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='copies',
                        to='promotions.promotionconfig',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Promotion Config Copy',
                'verbose_name_plural': 'Promotion configs copy',
            },
        ),
        # VoucherCopyConfig new fields
        migrations.AlterModelOptions(
            name='vouchercopyconfig',
            options={
                'verbose_name': 'Voucher Copy Config',
                'verbose_name_plural': 'Voucher Copy Configs',
            },
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_alert_popup_paragraph_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_alert_popup_paragraph_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_alert_popup_paragraph_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_ribbon_left_red',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_ribbon_left_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_ribbon_right_red',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='copy_promo_ribbon_right_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vouchercopyconfig',
            name='language',
            field=models.CharField(
                choices=[
                    ('en', 'English'),
                    ('de', 'Deutsch'),
                    ('fr', 'Français'),
                    ('es', 'Español'),
                    ('nl', 'Nederlands'),
                ],
                default='en',
                max_length=2,
            ),
        ),
    ]
