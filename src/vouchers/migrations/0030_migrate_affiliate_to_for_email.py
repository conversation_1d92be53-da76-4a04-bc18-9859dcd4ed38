# Generated by Django 4.1.8 on 2023-07-19 08:22
from django.core.paginator import Paginator
from django.db import migrations


def migrate_affiliate_field(apps, schema_editor):
    Voucher = apps.get_model('vouchers', 'Voucher')
    vouchers_with_affiliate = (
        Voucher.objects.exclude(affiliate__exact='')
        .exclude(affiliate__isnull=True)
        .filter(for_email='')
    )

    for page in Paginator(vouchers_with_affiliate, 1000):
        vouchers_to_update = []
        for voucher in page:
            voucher.for_email = voucher.affiliate
            vouchers_to_update.append(voucher)

        Voucher.objects.bulk_update(vouchers_to_update, ['for_email', 'affiliate'])


class Migration(migrations.Migration):
    dependencies = [
        ('vouchers', '0029_rename_furniture_category_name_in_item_discount'),
    ]

    operations = [
        migrations.RunPython(
            migrate_affiliate_field,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
