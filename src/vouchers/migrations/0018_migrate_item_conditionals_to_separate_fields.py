# Generated by Django 3.2.16 on 2022-10-12 09:51

from django.db import migrations


def distribute_json_field_to_fields(apps, schema_editor):
    """
    Values saved in item_conditionals were not validated at all, so we need to check
    three things:
    1. if key from JSONField is in new, valid fields
    2. if field we're considering has choices - is given value a valid choice
    3. if is not a valid choice - maybe it can be cast to integer and then is a
    valid choice?
    """
    filter_fields = {
        'shelf_type',
        'furniture_type',
        'material',
        'price_order',
        'is_cheapest_item',
        'is_most_expensive_item',
        'box_variant',
        'furniture_category_name',
    }
    ItemDiscount = apps.get_model('vouchers', 'ItemDiscount')
    for item_discount in ItemDiscount.objects.all():
        if not item_discount.item_conditionals:
            continue
        for key, value in item_discount.item_conditionals.items():
            # check if field name is valid
            if key not in filter_fields:
                continue
            field = getattr(ItemDiscount, key).field

            if choices := getattr(field, 'choices'):
                flat_list = [choice[0] for choice in choices]
                if value not in flat_list and type(value) is str and value.isdigit():
                    value = int(value)
                    if value not in flat_list:
                        continue
            setattr(item_discount, key, value)
        try:
            item_discount.save()
        except ValueError:
            continue


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0017_add_item_conditionals'),
    ]

    operations = [
        migrations.RunPython(
            code=distribute_json_field_to_fields,
            reverse_code=migrations.RunPython.noop,
            elidable=True,
        ),
    ]
