from django.db import migrations


def unbind_region_default(apps, schema_editor):
    VoucherGroup = apps.get_model('vouchers', 'VoucherGroup')
    Region = apps.get_model('regions', 'Region')

    try:
        default_region = Region.objects.get(name='_default')
        voucher_groups_with_default_region = VoucherGroup.objects.filter(
            region=default_region,
        )
        for voucher_group in voucher_groups_with_default_region:
            voucher_group.region.remove(default_region)
            voucher_group.save()
    except Region.DoesNotExist:
        # test migration, do nothing
        pass


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0043_alter_voucher_origin'),
    ]

    operations = [
        migrations.RunPython(
            unbind_region_default,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
