from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from dixa.authentication import DixaAuthentication
from dixa.models import ChannelMapping
from dixa.permissions import DixaPermission
from dixa.serializers import DixaWebhookMessageSerializer
from dixa.tasks import (
    handle_dixa_webhook_for_complaint_photos,
    handle_dixa_webhook_for_slack,
)


class DixaWebhookView(APIView):
    authentication_classes = (DixaAuthentication,)
    permission_classes = (DixaPermission,)

    def post(self, request, *args, **kwargs):
        data = request.data['data']
        self.validate(data)
        queue = data['conversation']['queue']
        if queue and ChannelMapping.objects.filter(dixa_queue_id=queue['id']).exists():
            handle_dixa_webhook_for_slack.delay(data)

        if data.get('attachments', []):
            user_email = data.get('author', {}).get('email', '')
            conversation_id = data.get('conversation', {}).get('csid', '')
            handle_dixa_webhook_for_complaint_photos.delay(
                data['attachments'], user_email, conversation_id
            )

        return Response(status=status.HTTP_200_OK)

    @classmethod
    def validate(cls, data):
        serializer = DixaWebhookMessageSerializer(data=data)
        serializer.is_valid(raise_exception=True)
