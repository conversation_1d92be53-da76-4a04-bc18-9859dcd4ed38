from decimal import Decimal
from typing import Optional

from django.core.cache import cache
from django.utils.translation import get_language

from rest_framework import serializers

from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.services.prices_for_serializers import (
    RegionCurrencySerializerMixin,
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from gallery.slugs import get_slug_for_furniture
from pricing_v3.omnibus import OmnibusCalculator
from promotions.models import Promotion


class BaseWishlistSerializer(
    RegionCurrencySerializerMixin,
    serializers.Serializer,
):
    id = serializers.IntegerField()
    category = serializers.CharField(source='furniture_category')
    region_price = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    promotion = serializers.SerializerMethodField()
    omnibus_price = serializers.SerializerMethodField()

    @property
    def strikethrough_promotion(self) -> Optional[Promotion]:
        return self.context.get('strikethrough_promotion')

    @property
    def omnibus_calculator(self) -> OmnibusCalculator:
        omnibus_calculator = self.context.get('omnibus_calculator')
        return omnibus_calculator or OmnibusCalculator.get_instance(self.region)

    def get_region_price(self, obj) -> Decimal:
        return obj.get_regionalized_price(
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount(self, obj) -> Decimal:
        return get_region_price_with_discount(
            furniture=obj,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.strikethrough_promotion,
        )

    def get_promotion(self, obj) -> float:
        return (
            self.strikethrough_promotion.promo_code.get_discount_value_for_item(obj)
            if self.strikethrough_promotion
            and self.strikethrough_promotion.promo_code.is_geometry_affected(obj)
            else 0.0
        )

    def get_omnibus_price(self, instance) -> Decimal | None:
        if not self.strikethrough_promotion:
            return
        return self.omnibus_calculator.calculate_lowest_price(
            geometry=instance,
            region_calculations_object=self.region_calculations_object
        )


class FurnitureWishlistSerializer(
    BaseWishlistSerializer,
):
    furniture_status = serializers.ReadOnlyField(
        source='get_furniture_status_display',
        help_text='furniture status, text version',
    )
    furniture_type = serializers.ReadOnlyField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField(read_only=True)
    title = serializers.ReadOnlyField(source='default_title')
    width = serializers.FloatField(source='get_width')
    height = serializers.FloatField(source='get_height')
    depth = serializers.FloatField(source='get_depth')
    seo_slug = serializers.SerializerMethodField()
    brand = serializers.CharField(source='get_pattern_name')
    variant = serializers.SerializerMethodField()
    color_name = serializers.CharField(source='translated_material_name')
    size_txt = serializers.CharField(source='get_size')
    dimension15 = serializers.IntegerField(source='base_preset')
    preview = serializers.ImageField()
    configurator_type = serializers.IntegerField()
    shelf_type = serializers.IntegerField()
    density = serializers.SerializerMethodField()
    drawers = serializers.SerializerMethodField()
    doors = serializers.SerializerMethodField()
    fabric = serializers.CharField(read_only=True)


    fields = [
        'preview',
        'title',
        'url',
        'created_at',
        'width',
        'height',
        'depth',
        'configurator_type',
        'id',
        'seo_slug',
        'shelf_type',
        'brand',
        'category',
        'variant',
        'color_name',
        'material',
        'size_txt',
        'dimension15',
        'physical_product_version',
        'promotion',
        'omnibus_price',
        'region_price',
        'region_price_in_euro',
        'region_price_with_discount',
        'region_price_with_discount_in_euro',

    ]

    @staticmethod
    def get_drawers(obj) -> int:
        return len(getattr(obj, 'drawers', []))

    @staticmethod
    def get_density(obj) -> int:
        return getattr(obj, 'property1', 0)

    @staticmethod
    def get_doors(obj) -> int:
        return len(getattr(obj, 'doors', []))


    @staticmethod
    def get_variant(obj):
        if isinstance(obj, Jetty):
            return obj.get_variant(color_prefix=False)
        return obj.get_variant()

    def get_seo_slug(self, obj):
        return get_slug_for_furniture(obj, get_language())

    def get_region_price_in_euro(self, obj) -> Decimal:
        return get_region_price_in_euro(
            furniture=obj,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount_in_euro(self, obj) -> Decimal:
        return get_region_price_with_discount_in_euro(
            furniture=obj,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.strikethrough_promotion,
        )

    def get_url(self, obj):
        return obj.get_url_with_region(region=self.region)

#
# class BaseShelvingWishlistSerializer(FurnitureWishlistSerializer):
#     density = serializers.SerializerMethodField()
#     drawers = serializers.SerializerMethodField()
#     doors = serializers.SerializerMethodField()
#
#     class Meta:
#         abstract = True
#         fields = FurnitureWishlistSerializer.Meta.fields + [
#             'density',
#             'drawers',
#             'doors',
#         ]
#
#     @staticmethod
#     def get_drawers(obj) -> int:
#         return len(obj.drawers)
#
#     @staticmethod
#     def get_density(obj) -> int:
#         return getattr(obj, 'property1', 0)
#
#     @staticmethod
#     def get_doors(obj) -> int:
#         return len(obj.doors)
#

# class JettyWishlistSerializer(BaseShelvingWishlistSerializer):
#     class Meta(BaseShelvingWishlistSerializer.Meta):
#         model = Jetty
#
#
# class SottyWishlistSerializer(FurnitureWishlistSerializer):
#     fabric = serializers.CharField(read_only=True)
#
#     class Meta(FurnitureWishlistSerializer.Meta):
#         model = Sotty
#         fields = FurnitureWishlistSerializer.Meta.fields + ['fabric']
#
#
# class WattyWishlistSerializer(BaseShelvingWishlistSerializer):
#     class Meta(BaseShelvingWishlistSerializer.Meta):
#         model = Watty


class BaseWishlistDetailSerializer(BaseWishlistSerializer, serializers.ModelSerializer):
    class Meta:
        abstract = True
        fields = (
            'id',
            'category',
            'shelf_type',
            'preview',
            'omnibus_price',
            'promotion',
            'region_price',
            'region_price_with_discount',
        )


class JettyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Jetty


class SottyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Sotty


class WattyWishlistDetailSerializer(BaseWishlistDetailSerializer):
    class Meta(BaseWishlistDetailSerializer.Meta):
        model = Watty
