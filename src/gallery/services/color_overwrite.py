from custom.enums import ShelfType
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)


def get_color_override(cv: str, instance: Jetty | Watty) -> int:
    override_value = int(cv) if cv.isdigit() else None
    original_color = instance.material
    shelf_type_colors = ShelfType(instance.shelf_type).colors
    if (
        override_value in shelf_type_colors.values()
        and shelf_type_colors(override_value).is_active
    ):
        return override_value
    elif not shelf_type_colors(original_color).is_active:
        return shelf_type_colors.get_fallback().value
    return original_color
