# Generated by Django 4.1.13 on 2025-01-17 12:29

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0113_alter_customdna_configurator_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='furnitureimage',
            name='type',
            field=models.CharField(
                choices=[
                    ('feed', 'Feed Image'),
                    ('insta_grid', 'Insta Grid Image'),
                    ('s4l_render', 'S4L Render'),
                    ('s4l_unreal_scene', 'S4L Unreal Scene'),
                    ('s4l_unreal_blender', 'S4L Unreal Blender'),
                    ('unreal_studio', 'Unreal Studio'),
                    ('unreal_configurator_preview', 'Unreal Configurator Preview'),
                    ('render', 'Render'),
                    ('render_small', 'Render Small'),
                    ('hover_render', 'Hover Render'),
                    ('hover_render_small', 'Hover Render Small'),
                    ('grid_large', 'Grid Large'),
                ],
                default='feed',
                max_length=100,
            ),
        ),
    ]
