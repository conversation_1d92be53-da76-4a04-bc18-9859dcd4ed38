# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import jsonfield.fields
import sorl.thumbnail.fields

import gallery.ivy_elements
import gallery.statistics


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='CatalogueInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('active', models.BooleanField(default=False)),
                ('visible', models.BooleanField(default=True)),
                ('text_header', models.CharField(blank=True, max_length=64, null=True)),
                ('text_main', models.Char<PERSON><PERSON>(blank=True, max_length=256, null=True)),
                ('text_button', models.CharField(blank=True, max_length=64, null=True)),
                ('text_below', models.CharField(blank=True, max_length=64, null=True)),
                (
                    'text_configurator',
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                (
                    'image',
                    models.ImageField(
                        blank=True, null=True, upload_to='catalogue_infos'
                    ),
                ),
                ('button_url', models.CharField(blank=True, max_length=256, null=True)),
                (
                    'button_url_type',
                    models.PositiveSmallIntegerField(
                        choices=[(1, 'url'), (2, 'deep url')]
                    ),
                ),
                (
                    'button_colour',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'white'),
                            (2, 'black'),
                            (3, 'red'),
                            (4, 'green'),
                            (5, 'blue'),
                            (6, 'gray'),
                        ]
                    ),
                ),
                (
                    'device',
                    models.CharField(
                        choices=[
                            ('all', 'all'),
                            ('iphone', 'iphone'),
                            ('ipad', 'ipad'),
                        ],
                        max_length=16,
                    ),
                ),
                (
                    'country',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'all'),
                            (2, 'Poland'),
                            (3, 'Germany'),
                            (4, 'Austria'),
                            (5, 'United Kingdom'),
                            (6, 'United States of America'),
                        ]
                    ),
                ),
                (
                    'language',
                    models.CharField(
                        choices=[
                            ('all', 'all'),
                            ('pl', 'Polish'),
                            ('de', 'German'),
                            ('en', 'English'),
                        ],
                        max_length=4,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='CatalogueInfoFurnitureOverride',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture',
                    models.PositiveSmallIntegerField(
                        choices=[(0, 'jetty'), (1, 'table'), (2, 'grinder')]
                    ),
                ),
                ('text_header', models.CharField(blank=True, max_length=64, null=True)),
                ('text_main', models.CharField(blank=True, max_length=256, null=True)),
                ('text_button', models.CharField(blank=True, max_length=64, null=True)),
                ('url', models.CharField(blank=True, max_length=512, null=True)),
                (
                    'image',
                    models.ImageField(
                        blank=True, null=True, upload_to='catalogue_info_overrides'
                    ),
                ),
                ('order', models.IntegerField(default=0)),
                (
                    'catalogue_info',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='overrides',
                        to='gallery.CatalogueInfo',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='CustomDna',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('title', models.CharField(max_length=250)),
                ('range_info', models.CharField(default='', max_length=250)),
                ('snapping_info', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True, null=True)),
                ('dna', models.FileField(upload_to='our_dna')),
                (
                    'shelf_type',
                    models.IntegerField(
                        choices=[
                            (0, 'TYPE01_PLYWOOD'),
                            (1, 'TYPE02'),
                            (2, 'TYPE01_VENEER'),
                        ],
                        default=0,
                        help_text='TYPE01_PLYWOOD = 0, TYPE02 = 1, TYPE01_VENEER = 2',
                    ),
                ),
                (
                    'full_file_dna',
                    models.FileField(blank=True, null=True, upload_to='full_dna'),
                ),
                (
                    'visible_on_web',
                    models.BooleanField(
                        default=False, help_text='Make dna visible on web'
                    ),
                ),
                (
                    'pattern_slot',
                    models.IntegerField(
                        choices=[
                            (0, 'slant'),
                            (1, 'gradient'),
                            (2, 'pattern'),
                            (3, 'grid'),
                        ],
                        default=0,
                        help_text='Type1&2 - 0=slant, 1=gradient, 2=pattern, 3=grid',
                    ),
                ),
                (
                    'new_dna_tools',
                    models.BooleanField(
                        default=False,
                        help_text='Check if you want to use webdesigner dnatools',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='DnaLoop',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('title', models.CharField(max_length=250)),
                (
                    'shelf_type',
                    models.IntegerField(
                        choices=[(0, 'TYPE01'), (1, 'TYPE02')], default=0
                    ),
                ),
                (
                    'designer_type',
                    models.IntegerField(
                        choices=[(0, 'DESIGNER OLD'), (1, 'WEBDESIGNER')], default=0
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, null=True)),
                (
                    'data',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, null=True
                    ),
                ),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='DnaTestSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('title', models.CharField(max_length=250)),
                (
                    'shelf_type',
                    models.IntegerField(
                        choices=[(0, 'TYPE01'), (1, 'TYPE02')], default=0
                    ),
                ),
                (
                    'designer_type',
                    models.IntegerField(
                        choices=[(0, 'DESIGNER OLD'), (1, 'WEBDESIGNER')], default=0
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, null=True)),
                (
                    'data',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, null=True
                    ),
                ),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Fold',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('legs', models.IntegerField()),
                ('stretch', models.IntegerField()),
                ('height', models.IntegerField()),
                ('width', models.IntegerField()),
                ('color', models.IntegerField(blank=True, default=0)),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                ('price', models.FloatField(default=0, verbose_name='Furniture price')),
                ('title', models.CharField(blank=True, max_length=256, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('preview_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Fold configuration',
                'verbose_name_plural': 'Fold configurations',
            },
        ),
        migrations.CreateModel(
            name='FurnitureGridImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'image',
                    sorl.thumbnail.fields.ImageField(
                        upload_to='grid_images_additional'
                    ),
                ),
                ('color', models.IntegerField()),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='FurnitureImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('furniture_object_id', models.PositiveIntegerField()),
                (
                    'image',
                    sorl.thumbnail.fields.ImageField(
                        upload_to='gallery_images_additional'
                    ),
                ),
                (
                    'image_webp',
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images_additional',
                        verbose_name='Webp version, generate by action',
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('feed', 'Feed image'),
                            ('insta_grid', 'Insta grid image'),
                        ],
                        default='feed',
                        max_length=100,
                    ),
                ),
                (
                    'color',
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (0, 'white/snow_white'),
                            (1, 'black/terracotta'),
                            (2, 'not used/midnight blue'),
                            (3, 'grey/sand'),
                            (4, 'purple/mint'),
                            (5, 'ash/not used'),
                        ],
                        null=True,
                    ),
                ),
                ('enabled', models.BooleanField(default=False)),
                ('position', models.PositiveSmallIntegerField(default=0)),
                (
                    'furniture_content_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.ContentType',
                    ),
                ),
            ],
            options={
                'ordering': ['position'],
            },
        ),
        migrations.CreateModel(
            name='FurnitureTokens',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('token', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Jetty token for contest',
                'verbose_name_plural': 'Jetty tokens',
            },
        ),
        migrations.CreateModel(
            name='Grinder',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('elements', jsonfield.fields.JSONField()),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                ('price', models.FloatField(default=0, verbose_name='Furniture price')),
                ('title', models.CharField(blank=True, max_length=256, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('preview_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Grinder configuration',
                'verbose_name_plural': 'Grinder configurations',
            },
        ),
        migrations.CreateModel(
            name='Ivy',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('depth', models.IntegerField()),
                ('width', models.FloatField()),
                ('color', models.FloatField()),
                ('supports', models.BooleanField(default=True)),
                ('opensides', models.BooleanField(default=True)),
                ('configuration', jsonfield.fields.JSONField()),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                ('price', models.FloatField(default=0, verbose_name='Furniture price')),
                ('title', models.CharField(blank=True, max_length=256, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('preview_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                ('gridheights', models.CharField(max_length=255)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Ivy configuration',
                'verbose_name_plural': 'Ivy configurations',
            },
        ),
        migrations.CreateModel(
            name='Jetty',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('depth', models.IntegerField()),
                ('color', models.IntegerField(blank=True, default=0)),
                ('material', models.IntegerField(default=0)),
                ('property1', models.FloatField()),
                ('pattern', models.IntegerField()),
                (
                    'dna_name',
                    models.CharField(blank=True, default='', max_length=100, null=True),
                ),
                ('verticals', jsonfield.fields.JSONField()),
                ('horizontals', jsonfield.fields.JSONField()),
                ('supports', jsonfield.fields.JSONField(blank=True, default=[])),
                ('modules', jsonfield.fields.JSONField(blank=True, default=[])),
                ('rows', jsonfield.fields.JSONField()),
                ('joints', jsonfield.fields.JSONField(blank=True, default=[])),
                ('doors', jsonfield.fields.JSONField(blank=True, default=[])),
                ('backs', jsonfield.fields.JSONField(blank=True, default=[])),
                ('legs', jsonfield.fields.JSONField(blank=True, default=[])),
                ('drawers', jsonfield.fields.JSONField(blank=True, default=[])),
                ('row_styles', jsonfield.fields.JSONField(blank=True, default=[])),
                (
                    'backpanel_styles',
                    jsonfield.fields.JSONField(blank=True, default=[], null=True),
                ),
                (
                    'shelf_category',
                    models.CharField(blank=True, max_length=150, null=True),
                ),
                (
                    'shelf_type',
                    models.IntegerField(
                        default=0,
                        help_text='0 - Type 01, 1 - Type 02, 2 - Type 01 Veneer',
                    ),
                ),
                ('width', models.IntegerField(default=0)),
                ('height', models.IntegerField(default=0)),
                ('row_amount', models.IntegerField(default=0)),
                ('snapping', models.IntegerField(default=0)),
                ('max_capacity', models.IntegerField(default=0)),
                (
                    'grid_all_colors',
                    models.FileField(blank=True, null=True, upload_to='grid_images'),
                ),
                (
                    'grid_all_colors_webp',
                    models.FileField(blank=True, null=True, upload_to='grid_images'),
                ),
                ('grid_prices', jsonfield.fields.JSONField(blank=True, null=True)),
                (
                    'configurator_type',
                    models.PositiveSmallIntegerField(
                        choices=[(1, 'ROW'), (2, 'COLUMN')], default=1
                    ),
                ),
                (
                    'configurator_params',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                (
                    'inserts',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'plinth',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'long_legs',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'cable_management',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                ('price', models.FloatField(default=0, verbose_name='Furniture price')),
                ('title', models.CharField(blank=True, max_length=256, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('preview_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=(
                gallery.statistics.SimilarityJettyMixin,
                gallery.ivy_elements.IvyStatisticMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name='LimitedEditionInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture',
                    models.PositiveSmallIntegerField(
                        choices=[(0, 'jetty'), (1, 'table'), (2, 'grinder'), (3, 'all')]
                    ),
                ),
                ('total_amount', models.IntegerField(default=0)),
                ('actual_amount', models.IntegerField(default=0)),
                ('active_until', models.DateField(blank=True, null=True)),
                ('active', models.BooleanField(default=False)),
                ('limited_info_header', models.TextField(blank=True, null=True)),
                ('limited_info_description', models.TextField(blank=True, null=True)),
                ('limited_info_hover', models.TextField(blank=True, null=True)),
                ('image_path', models.TextField(blank=True, null=True)),
                ('image_template_path', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RecentPurchase',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('object_id', models.PositiveIntegerField()),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('visible_from', models.DateTimeField(auto_now_add=True)),
                ('buyer_name', models.CharField(default='', max_length=128)),
                ('buyer_city', models.CharField(default='', max_length=128)),
                ('purchase_date', models.DateTimeField(auto_now_add=True)),
                (
                    'content_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.ContentType',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Table',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('property1', models.FloatField()),
                ('pattern', models.IntegerField()),
                ('width', models.IntegerField()),
                ('length', models.IntegerField()),
                ('leg_angle', models.IntegerField()),
                ('material', models.IntegerField(default=0)),
                ('material_frame', models.IntegerField(default=0)),
                ('points', jsonfield.fields.JSONField()),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                ('price', models.FloatField(default=0, verbose_name='Furniture price')),
                ('title', models.CharField(blank=True, max_length=256, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('preview_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Table configuration',
                'verbose_name_plural': 'Table configurations',
            },
        ),
        migrations.AddField(
            model_name='furnituretokens',
            name='shelf',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='gallery.Jetty'
            ),
        ),
        migrations.AddField(
            model_name='furnituregridimage',
            name='furniture',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='gallery.Jetty'
            ),
        ),
        migrations.AlterUniqueTogether(
            name='catalogueinfo',
            unique_together=set([('active', 'device', 'country', 'language')]),
        ),
        migrations.AlterUniqueTogether(
            name='catalogueinfofurnitureoverride',
            unique_together=set([('catalogue_info', 'furniture')]),
        ),
    ]
