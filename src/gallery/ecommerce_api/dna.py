import json

from rest_framework.generics import <PERSON>trieveAPI<PERSON>iew
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from custom.mixins import GZipMixin
from custom.renderers import PlainTextRenderer
from gallery.models import CustomDna
from gallery.serializers import CustomDnaQuerySerializer


class CustomDnaView(GZipMixin, RetrieveAPIView):
    """
    View for retrieving actual custom DNA based on shelf_type and collection_type.
    """

    authentication_classes = []
    permission_classes = [AllowAny]
    renderer_classes = [PlainTextRenderer]

    def get_queryset(self):
        return CustomDna.objects.filter(visible_on_web=True)

    def retrieve(self, request, *args, **kwargs):
        serializer = CustomDnaQuerySerializer(data=request.GET)
        serializer.is_valid(raise_exception=True)
        queryset = (
            self.get_queryset()
            .order_by('created_at')
            .filter(
                **serializer.validated_data,
            )
        )
        dna = queryset.last()
        dna_string = json.dumps(dna.get_details())
        return Response(dna_string)
