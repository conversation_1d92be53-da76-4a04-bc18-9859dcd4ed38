from rest_framework.generics import RetrieveAP<PERSON><PERSON>iew
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>

from custom.enums import Sofa01Color
from ecommerce_api.mixins import EcommerceAPIMixin
from gallery.ecommerce_api.mixins import ColorOverrideViewMixin
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.serializers import (
    JettySerializer,
    SottyConfiguratorSerializer,
    WattySerializer,
)
from regions.services.limitations import LimitationService


class FurnitureConfiguratorBaseView(
    ColorOverrideViewMixin,
    EcommerceAPIMixin,
    RetrieveAPIView,
):
    renderer_classes = (JSONRenderer,)

    def get_serializer_context(self):
        return {
            'region': self.region,
            **super().get_serializer_context(),
        }


class JettyConfiguratorView(FurnitureConfiguratorBaseView):
    queryset = Jetty.objects.all()
    # TODO: create dedicated serializer
    serializer_class = JettySerializer


class SottyConfiguratorView(FurnitureConfiguratorBaseView):
    queryset = Sotty.objects.all()
    serializer_class = SottyConfiguratorSerializer

    def get_object(self) -> Sotty:
        """Fallback to default material if UK and corduroy is selected."""
        obj = super().get_object()
        limitation_service = LimitationService(region=self.region)
        if (
            not limitation_service.is_corduroy_available
            and obj.fabric == Sotty.Fabric.CORDUROY
        ):
            obj.set_material(Sofa01Color.get_fallback(), with_save=False)
        return obj


class WattyConfiguratorView(FurnitureConfiguratorBaseView):
    queryset = Watty.objects.all()
    # TODO: create dedicated serializer
    serializer_class = WattySerializer
