{"superior_object_type": "mesh", "superior_object_ids": [2498], "superior_object_line": 1, "serialized_at": "2022-08-02T09:30:01.042147", "serialization": {"mesh": {"2498": {"presets": {"666": {"id": 6329, "image": "/media/mesh_presets/1f986c20-c30e-4e26-bf01-4d21a5f29b66.webp", "geom_id": 2498, "depth": 400, "height": 700, "width": 1470, "density": 52, "distortion": 0, "plinth": false, "backPanels": false, "configurator_custom_params": {}, "owner": "<EMAIL>", "rating": 0, "comment": "", "tags": ""}}, "setups": {"1": {"configs": [{"parameters": {"config_id": 88578, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88577, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18033, "dim_x": [836, 1836]}}, "2": {"configs": [{"parameters": {"config_id": 88581, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88579, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88580, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18034, "dim_x": [1254, 2454]}}, "3": {"configs": [{"parameters": {"config_id": 88585, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88582, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88583, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88584, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18035, "dim_x": [1572, 3072]}}, "4": {"configs": [{"parameters": {"config_id": 88590, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88586, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88587, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88588, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88589, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18036, "dim_x": [1860, 3690]}}, "5": {"configs": [{"parameters": {"config_id": 88844, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88845, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88846, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88847, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88848, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88854, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18107, "dim_x": [2208, 4308]}}, "6": {"configs": [{"parameters": {"config_id": 88857, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88858, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88859, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88860, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88861, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88862, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88863, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18109, "dim_x": [2526, 4926]}}, "7": {"configs": [{"parameters": {"config_id": 88849, "comp_id": 1066, "component": null, "table": 1066, "series_pick": 4751, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "density_mode": "setup_range_stepper", "distortion_tag": "shift"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88850, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 2, "#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88851, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88852, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88853, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88855, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88856, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 88864, "comp_id": 1065, "component": null, "table": 1065, "series_pick": 4754, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#open_back": 3, "#object_type": 1, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": true, "setup_id": 18108, "dim_x": [2844, 5544]}}}, "parameters": {"distortion_mode": "shift", "density_mode": "setup_range_stepper", "size_y": [300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200], "size_x": [900, 2400], "object_type": 1}, "constants": {"#object_type": 1, "pattern": 3}}}, "component_table": {"1066": {"configs": {"desk\\": 4751}}, "1065": {"configs": {"some drawer": 4772, "some door": 4774, "door drawer": 4773, "drawer": 4755, "opening": 4756, "mix": 4754, "drawers": 4752, "door": 4753}}}, "component_series": {"4751": {"setups": {"700": 54804}, "parameters": {"size_y": [700], "name": "desk\\", "series_id": 4751}}, "4753": {"setups": {"700": 54807}, "parameters": {"size_y": [700], "name": "door", "series_id": 4753}}, "4773": {"setups": {"700": 54827}, "parameters": {"size_y": [700], "name": "door drawer", "series_id": 4773}}, "4755": {"setups": {"700": 54809}, "parameters": {"size_y": [700], "name": "drawer", "series_id": 4755}}, "4752": {"setups": {"700": 54805}, "parameters": {"size_y": [700], "name": "drawers", "series_id": 4752}}, "4754": {"setups": {"700": 54808}, "parameters": {"size_y": [700], "name": "mix", "series_id": 4754}}, "4756": {"setups": {"700": 54806}, "parameters": {"size_y": [700], "name": "opening", "series_id": 4756}}, "4774": {"setups": {"700": 54825}, "parameters": {"size_y": [700], "name": "some door", "series_id": 4774}}, "4772": {"setups": {"700": 54826}, "parameters": {"size_y": [700], "name": "some drawer", "series_id": 4772}}}, "component": {"54804": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166592, "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "face__s_bottom": 0, "face__s_back": "#open_back", "dim_x": "600-1200", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166645, "type": "FB", "e_id": 286, "cable__pos_y": "-59", "cable__pos_x": 89, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "face__s_bottom": 0, "dim_x": "600-1200", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "600-1200", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54807": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166602, "type": "D", "e_id": 286, "cable__pos_y": "50,350", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "-200,-400", "e_size_y": 700, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54827": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166654, "type": "D", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "-200", "e_size_y": 500, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166652, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54809": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166608, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166607, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166606, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54805": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166595, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166593, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166594, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54808": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166605, "type": "D", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166604, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166603, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54806": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166599, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166598, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166597, "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54825": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166647, "type": "D", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "-200", "e_size_y": 500, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166646, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "54826": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 166651, "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166650, "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 166649, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "face__s_back": "#open_back", "dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "300-550", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}}}, "configurator_data": {"table": {"1066": {"700": [{"series_id": 4751, "component_id": 54804, "series_name": "desk\\", "series_groups": "", "order": 0, "inconsequent": false}]}, "1065": {"700": [{"series_id": 4772, "component_id": 54826, "series_name": "some drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4774, "component_id": 54825, "series_name": "some door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4773, "component_id": 54827, "series_name": "door drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4755, "component_id": 54809, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4756, "component_id": 54806, "series_name": "opening", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4754, "component_id": 54808, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4752, "component_id": 54805, "series_name": "drawers", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 4753, "component_id": 54807, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}]}}, "component": {"54804": {"id": 54804, "name": "desk\\ - desk"}, "54826": {"id": 54826, "name": "some drawer - some drawer"}, "54825": {"id": 54825, "name": "some door - some door"}, "54827": {"id": 54827, "name": "door drawer - door drawer"}, "54809": {"id": 54809, "name": "drawer - drawer"}, "54806": {"id": 54806, "name": "opening - open"}, "54808": {"id": 54808, "name": "mix - mix"}, "54805": {"id": 54805, "name": "drawers - drawers"}, "54807": {"id": 54807, "name": "door - door"}}}, "superior_object_collection": "Desk"}