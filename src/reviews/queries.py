from django.db.models import (
    Case,
    CharField,
    OuterRef,
    QuerySet,
    SmallIntegerField,
    Subquery,
    Value,
    When,
)

from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from gallery.enums import FurnitureCategory
from reviews.choices import TemplateChoices
from reviews.enums import (
    FURNITURE_CATEGORY_TO_REVIEW_CATEGORY,
    ReviewCategoryEnum,
)
from reviews.models import (
    Review,
    ReviewTranslation,
)


def get_pdp_reviews_shelf_type_annotations(shelf_type: ShelfType) -> Case:
    shelf_type_annotations = {
        ShelfType.TYPE01.value: Case(
            When(shelf_type=ShelfType.TYPE01.value, then=Value(3)),
            When(shelf_type=ShelfType.VENEER_TYPE01.value, then=Value(2)),
            When(shelf_type=ShelfType.TYPE02.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE02.value: Case(
            When(shelf_type=ShelfType.TYPE02.value, then=Value(3)),
            When(shelf_type=ShelfType.TYPE01.value, then=Value(2)),
            When(shelf_type=ShelfType.VENEER_TYPE01.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.VENEER_TYPE01.value: Case(
            When(shelf_type=ShelfType.VENEER_TYPE01.value, then=Value(3)),
            When(shelf_type=ShelfType.TYPE01.value, then=Value(2)),
            When(shelf_type=ShelfType.TYPE02.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE03.value: Case(
            When(shelf_type=ShelfType.TYPE03.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE13.value: Case(
            When(shelf_type=ShelfType.TYPE13.value, then=Value(2)),
            When(shelf_type=ShelfType.TYPE03.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.VENEER_TYPE13.value: Case(
            When(shelf_type=ShelfType.VENEER_TYPE13.value, then=Value(2)),
            When(shelf_type=ShelfType.TYPE13.value, then=Value(1)),
            When(shelf_type=ShelfType.VENEER_TYPE01.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE23.value: Case(
            When(shelf_type=ShelfType.TYPE23.value, then=Value(3)),
            When(
                shelf_type__in=[ShelfType.TYPE24.value, ShelfType.TYPE25.value],
                then=Value(2),
            ),
            When(shelf_type=ShelfType.TYPE03.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE24.value: Case(
            When(shelf_type=ShelfType.TYPE24.value, then=Value(3)),
            When(
                shelf_type__in=[ShelfType.TYPE23.value, ShelfType.TYPE25.value],
                then=Value(2),
            ),
            When(shelf_type=ShelfType.TYPE03.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.TYPE25.value: Case(
            When(shelf_type=ShelfType.TYPE25.value, then=Value(3)),
            When(
                shelf_type__in=[ShelfType.TYPE23.value, ShelfType.TYPE24.value],
                then=Value(2),
            ),
            When(shelf_type=ShelfType.TYPE03.value, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ShelfType.SOFA_TYPE01.value: Case(
            When(shelf_type=ShelfType.SOFA_TYPE01.value, then=Value(3)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
    }
    return shelf_type_annotations[shelf_type]


def get_pdp_reviews_category_annotations(furniture_category: FurnitureCategory) -> Case:
    review_category = FURNITURE_CATEGORY_TO_REVIEW_CATEGORY.get(furniture_category)
    category_annotations = {
        ReviewCategoryEnum.CATEGORY_SHOERACK: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_SHOERACK, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_BOOKCASE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_BOOKCASE, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_WALLSTORAGE, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_TVSHELF: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_TVSHELF, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_WALLSTORAGE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_WALLSTORAGE, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_BOOKCASE, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_SIDEBOARD: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_TVSHELF, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_VINYL_STORAGE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_VINYL_STORAGE, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_TVSHELF, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_CHEST_OF_DRAWERS: Case(
            When(
                categories=ReviewCategoryEnum.CATEGORY_CHEST_OF_DRAWERS, then=Value(2)
            ),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_WARDROBE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_WARDROBE, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_BEDSIDE_TABLE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_BEDSIDE_TABLE, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_DESK: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_DESK, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_SOFA: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_SOFA, then=Value(2)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
        ReviewCategoryEnum.CATEGORY_DRESSING_TABLE: Case(
            When(categories=ReviewCategoryEnum.CATEGORY_DRESSING_TABLE, then=Value(3)),
            When(categories=ReviewCategoryEnum.CATEGORY_DESK, then=Value(2)),
            When(categories=ReviewCategoryEnum.CATEGORY_SIDEBOARD, then=Value(1)),
            default=Value(0),
            output_field=SmallIntegerField(),
        ),
    }
    return category_annotations[review_category]


def get_pdp_reviews(
    shelf_type: ShelfType,
    furniture_category: FurnitureCategory,
    language: LanguageEnum,
) -> QuerySet[Review]:

    review_translation = ReviewTranslation.objects.filter(
        review=OuterRef('pk'),
        language=language,
    ).values('title', 'description')[:1]

    return (
        Review.latest_objects.filter(
            enabled=True,
            recommended_for_pdp=True,
            template_type__in=[TemplateChoices.SQUARE, TemplateChoices.VERTICAL],
        )
        .annotate(
            translated_title=Subquery(
                review_translation.values('title'),
                output_field=CharField(),
            ),
            translated_description=Subquery(
                review_translation.values('description'),
                output_field=CharField(),
            ),
            category_order=get_pdp_reviews_category_annotations(furniture_category),
            shelf_type_order=get_pdp_reviews_shelf_type_annotations(shelf_type),
        )
        .order_by('-category_order', '-shelf_type_order', '-created_at')[:15]
    )
