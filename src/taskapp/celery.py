import logging
import os

from django.apps import (
    AppConfig,
    apps,
)
from django.conf import settings

import requests

from celery import Celery
from celery.signals import task_failure

from admin_customization.celery_scheduler import admin_customization_tasks_scheduler
from automatic_batching.celery_scheduler import (
    automatic_batching_tasks_scheduler,
    automatic_batching_tasks_scheduler_dev,
)
from carts.celery_scheduler import carts_tasks_scheduler
from complaints.celery_scheduler import complaints_tasks_scheduler
from custom.celery_scheduler import custom_tasks_scheduler
from custom_audiences.celery_sheduler import custom_audiences_tasks_scheduler
from customer_service.celery_scheduler import customer_service_tasks_scheduler
from dixa.celery_scheduler import dixa_tasks_scheduler
from django_mailer.celery_scheduler import django_mailer_tasks_scheduler
from dynamic_delivery.celery_scheduler import dynamic_delivery_tasks_scheduler
from events.celery_scheduler import (
    events_tasks_scheduler,
    events_tasks_scheduler_dev,
)
from feeds.celery_scheduler import feeds_tasks_scheduler
from free_returns.celery_scheduler import free_return_tasks_scheduler
from gallery.celery_scheduler import gallery_tasks_scheduler
from invoice.celery_scheduler import (
    invoice_tasks_scheduler,
    invoice_tasks_scheduler_dev,
)
from kpi.celery_scheduler import kpi_tasks_scheduler
from mailing.celery_scheduler import (
    mailing_flow_tasks_scheduler,
    mailing_tasks_scheduler,
)
from orders.celery_scheduler import (
    orders_tasks_scheduler,
    orders_tasks_scheduler_dev,
)
from pricing_v3.celery_scheduler import pricing_tasks_scheduler
from producers.celery_scheduler import producers_tasks_scheduler
from product_feeds.celery_scheduler import product_feeds_tasks_scheduler
from promotions.celery_scheduler import promotions_tasks_scheduler
from render_tasks.celery_scheduler import render_tasks_tasks_scheduler
from reviews.celery_scheduler import reviews_tasks_scheduler
from warehouse.celery_scheduler import warehouse_tasks_scheduler

if not settings.configured:
    # set the default Django settings module for the 'celery' program.
    os.environ.setdefault(
        'DJANGO_SETTINGS_MODULE',
        'cstm_be.settings.production',
    )

logger = logging.getLogger('cstm')

if not settings.IS_TESTING:
    from ddtrace import patch

    patch(celery=True)

app = Celery('cstm')


# NOTE: celery can't into timezones. You have to set the time in UTC.
# so if you want to run it at 3pm, set it to 1pm in winter and 2pm in summer.
# good times.
if settings.IS_PRODUCTION:
    app.conf.beat_schedule = {
        **automatic_batching_tasks_scheduler,
        **producers_tasks_scheduler,
        **dynamic_delivery_tasks_scheduler,
        **admin_customization_tasks_scheduler,
        **complaints_tasks_scheduler,
        **custom_tasks_scheduler,
        **custom_audiences_tasks_scheduler,
        **customer_service_tasks_scheduler,
        **dixa_tasks_scheduler,
        **gallery_tasks_scheduler,
        **kpi_tasks_scheduler,
        **mailing_flow_tasks_scheduler,
        **mailing_tasks_scheduler,
        **orders_tasks_scheduler,
        **invoice_tasks_scheduler,
        **product_feeds_tasks_scheduler,
        **pricing_tasks_scheduler,
        **reviews_tasks_scheduler,
        **warehouse_tasks_scheduler,
        **events_tasks_scheduler,
        **free_return_tasks_scheduler,
        **django_mailer_tasks_scheduler,
        **feeds_tasks_scheduler,
        **render_tasks_tasks_scheduler,
        **promotions_tasks_scheduler,
        **carts_tasks_scheduler,
    }
else:
    app.conf.beat_schedule = {
        **events_tasks_scheduler_dev,
        **invoice_tasks_scheduler_dev,
        **orders_tasks_scheduler_dev,
        **automatic_batching_tasks_scheduler_dev,
    }

app.conf.task_routes = {
    'producers.tasks.*': {'queue': 'producers'},
    'mailing.tasks.*': {'queue': 'mailing'},
    'invoice.tasks.*': {'queue': 'invoice'},
    'payments.tasks.*': {'queue': 'payments'},
    'customer_service.tasks.*': {'queue': 'customer_service'},
    'orders.tasks.process_paid_order_to_production': {'queue': 'process_to_production'},
}


class CeleryConfig(AppConfig):
    """Celery main config."""

    name = 'taskapp'
    verbose_name = 'Celery Config'

    def ready(self):
        """Autodiscover tasks."""
        # Using a string here means the worker will not have to
        # pickle the object when using Windows.
        app.config_from_object('django.conf:settings', namespace='CELERY')
        installed_apps = [app_config.name for app_config in apps.get_app_configs()]
        app.autodiscover_tasks(lambda: installed_apps, force=True)


@app.task(bind=True)
def debug_task(self):
    """Debug only task that prints ``celery`` request id."""
    print('Request: {0}'.format(self.request))


@task_failure.connect
def sigkill_logger(sender=None, headers=None, body=None, **kwargs):
    exception = kwargs.get('exception')
    if exception and 'SIGKILL' in str(exception):
        logger.error('SIGKILL on task - %s', sender.name)
        if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
            requests.post(
                settings.SLACK_WEBHOOK,
                json={
                    'text': f'SIGKILL on task - {sender.name}',
                    'channel': 'platforma-priv',
                    'username': 'Killer',
                    'icon_emoji': ':krzychu-rzuci-na-to-okiem:',
                },
            )


def tasks_in_queue(name_list: list[str]) -> bool:
    inspect = app.control.inspect()

    return (
        check_if_task_is_in_queue(inspect.scheduled().items(), name_list)
        or check_if_task_is_in_queue(inspect.active().items(), name_list)
        or check_if_task_is_in_queue(inspect.reserved().items(), name_list)
    )


def check_if_task_is_in_queue(task_queue: dict, name_list: list[str]):
    for queue_name, tasks in task_queue:
        return any(task for task in tasks if task.get('name') in name_list)
