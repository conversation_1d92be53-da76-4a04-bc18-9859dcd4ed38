from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.core.paginator import Paginator
from django.utils import timezone

from celery import shared_task

from custom.metrics import task_metrics
from user_profile.services.user_profile_cleaner import UserProfileCleaner

User = get_user_model()


@shared_task
@task_metrics
def task_delete_user_sessions(user_id):
    for page in Paginator(
        Session.objects.filter(expire_date__gte=timezone.now()),
        1000,
    ):
        for session in page:
            if session.get_decoded().get('_auth_user_id') == str(user_id):
                session.delete()


@shared_task
def delete_old_users_and_carts():
    cleaner = UserProfileCleaner()
    cleaner()


@shared_task
def anonymize_users(user_ids: list[int]) -> None:
    from user_profile.services.anonymize import anonymize_user

    for user in User.objects.filter(id__in=user_ids):
        anonymize_user(user)
