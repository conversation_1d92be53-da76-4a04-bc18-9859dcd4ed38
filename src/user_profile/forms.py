from django import forms

from user_profile.models import UserProfile


class AddressForm(forms.ModelForm):
    class Meta(object):
        model = UserProfile
        fields = (
            'first_name',
            'last_name',
            'company_name',
            'street_address_1',
            'street_address_2',
            'city',
            'postal_code',
            'country',
            'phone',
            'company_name',
            'vat',
        )


class InvoiceAddressForm(forms.ModelForm):
    class Meta(object):
        model = UserProfile
        fields = (
            'invoice_first_name',
            'invoice_last_name',
            'invoice_company_name',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_vat',
        )
