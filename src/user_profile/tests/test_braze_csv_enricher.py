import tempfile

import pytest

from custom.exceptions import CsvImportError
from events.utils import hash_normalized_string
from user_profile.services.braze_csv_enricher import Braze<PERSON><PERSON>nricher


def generate_temp_file(content):
    file = tempfile.NamedTemporaryFile(mode='w+t', suffix='.csv')
    file.write(content)
    file.seek(0)
    return file


class TestBrazeCSVEnricher:
    @pytest.fixture(scope='class')
    def file_content(self):
        headers = 'first_name,last_name,email'
        content = 'jan,nowak,<EMAIL>\nmarian,konopnicki,<EMAIL>'
        return f'{headers}\n{content}'

    def test_creating_external_id(self, file_content):
        file = generate_temp_file(file_content)
        extended_data = BrazeCSVEnricher(file).generate_extended_data()
        assert extended_data[0].get('external_id') == hash_normalized_string(
            extended_data[0].get('email')
        )
        assert extended_data[1].get('external_id') == hash_normalized_string(
            extended_data[1].get('email')
        )

    def test_file_lacking_email_column(self):
        wrong_headers = 'first_name,second_name,third_name,fourth_name,fifth_name'
        content = 'jego,wysokosc,krol,jerzy,trzeci'
        file = generate_temp_file(f'{wrong_headers}\n{content}')
        with pytest.raises(CsvImportError) as e:
            BrazeCSVEnricher(file).generate_extended_data()
            assert 'does not have "email" column' in e.value

    def test_generating_file_name(self, file_content):
        file = generate_temp_file(file_content)
        new_file_name = BrazeCSVEnricher(file).new_file_name
        assert new_file_name.startswith('braze_enriched_')
        assert new_file_name.endswith('.csv')

    def test_one_row_lacks_email(self, file_content):
        file_content += '\nted,kaczynski,'
        file = generate_temp_file(file_content)
        with pytest.raises(CsvImportError) as e:
            BrazeCSVEnricher(file).generate_extended_data()
            assert 'Some rows are missing email addresses - they need to be filled' in e
