from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.utils import timezone

import pytest

from carts.models import Cart
from user_profile.models import UserProfile
from user_profile.services.anonymize import anonymize_user

User = get_user_model()


@pytest.mark.django_db
class TestAnonymizeUser:
    def test_anonymizes_user_fields(self, user_factory):
        """Test that user fields are properly anonymized."""
        user = user_factory(
            username='testuser',
            email='<EMAIL>',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
        )

        anonymize_user(user)

        user.refresh_from_db()

        assert user.first_name == 'anonymize'
        assert user.last_name == 'anonymize'
        assert user.email == 'anonymize'
        assert user.username == f'anon_{user.id}'

    def test_anonymizes_user_profile_fields(self, user_factory):
        """Test that user profile fields are properly anonymized."""
        user = user_factory(
            username='testuser',
            email='<EMAIL>',
            first_name='<PERSON>',
            last_name='<PERSON>e',
        )

        profile = user.profile
        profile.first_name = '<PERSON>'
        profile.last_name = 'Doe'
        profile.email = '<EMAIL>'
        profile.phone = '+1234567890'
        profile.company_name = 'Test Company'
        profile.street_address_1 = '123 Main St'
        profile.street_address_2 = 'Apt 4B'
        profile.city = 'Test City'
        profile.postal_code = '12345'
        profile.country = 'Test Country'
        profile.country_area = 'Test Area'
        profile.vat = 'VAT123'
        profile.notes = 'Test notes'
        profile.invoice_company_name = 'Invoice Company'
        profile.invoice_first_name = 'Invoice John'
        profile.invoice_last_name = 'Invoice Doe'
        profile.invoice_email = '<EMAIL>'
        profile.invoice_street_address_1 = '456 Invoice St'
        profile.invoice_street_address_2 = 'Suite 100'
        profile.invoice_city = 'Invoice City'
        profile.invoice_postal_code = '67890'
        profile.invoice_country = 'Invoice Country'
        profile.save()

        anonymize_user(user)

        profile.refresh_from_db()

        assert profile.first_name == 'anonymize'
        assert profile.last_name == 'anonymize'
        assert profile.email == 'anonymize'
        assert profile.phone == 'anonymize'
        assert profile.company_name == 'anonymize'
        assert profile.street_address_1 == 'anonymize'
        assert profile.street_address_2 == 'anonymize'
        assert profile.city == 'anonymize'
        assert profile.postal_code == 'anonymize'
        assert profile.country == 'anonymize'
        assert profile.country_area == 'anonymize'
        assert profile.vat == 'anonymize'
        assert profile.notes == 'anonymize'
        assert profile.invoice_company_name == 'anonymize'
        assert profile.invoice_first_name == 'anonymize'
        assert profile.invoice_last_name == 'anonymize'
        assert profile.invoice_email == 'anonymize'
        assert profile.invoice_street_address_1 == 'anonymize'
        assert profile.invoice_street_address_2 == 'anonymize'
        assert profile.invoice_city == 'anonymize'
        assert profile.invoice_postal_code == 'anonymize'
        assert profile.invoice_country == 'anonymize'

    def test_deletes_user_carts(self, user_factory, cart_factory):
        """Test that all user carts are deleted."""
        user = user_factory(username='testuser', email='<EMAIL>')

        cart1 = cart_factory(owner=user)
        cart2 = cart_factory(owner=user)

        assert Cart.objects.filter(owner=user).count() == 2

        anonymize_user(user)

        assert Cart.objects.filter(owner=user).count() == 0
        assert not Cart.objects.filter(id=cart1.id).exists()
        assert not Cart.objects.filter(id=cart2.id).exists()

    @patch('user_profile.services.anonymize.MarketingExcludeUpdateEvent')
    def test_creates_marketing_exclude_update_event(
        self,
        mock_event,
        user_factory,
    ):
        """Test that MarketingExcludeUpdateEvent is created with correct parameters."""
        user = user_factory(username='testuser', email='<EMAIL>')

        with patch('django.utils.timezone.now') as mock_now:
            mock_time = timezone.now()
            mock_now.return_value = mock_time

            anonymize_user(user)

        mock_event.assert_called_once_with(
            user=user,
            complaint_active=False,
            last_finished_complaint_date=mock_time,
        )

    def test_handles_user_with_no_profile_data(self, user_factory):
        """Test that function works with user that has minimal profile data."""
        user = user_factory(username='testuser')

        anonymize_user(user)

        user.refresh_from_db()

        assert user.username == f'anon_{user.id}'
        assert user.first_name == 'anonymize'
        assert user.last_name == 'anonymize'
        assert user.email == 'anonymize'

    def test_handles_user_with_no_carts(self, user_factory):
        """Test that function works with user that has no carts."""
        user = user_factory(username='testuser', email='<EMAIL>')

        assert Cart.objects.filter(owner=user).count() == 0

        anonymize_user(user)

        user.refresh_from_db()
        assert user.username == f'anon_{user.id}'

    def test_preserves_user_id(self, user_factory):
        """Test that user ID is preserved during anonymization."""
        user = user_factory(username='testuser', email='<EMAIL>')
        original_id = user.id

        anonymize_user(user)

        user.refresh_from_db()

        assert user.id == original_id

    def test_anonymization_is_persistent(self, user_factory):
        """Test that anonymization changes are saved to database."""
        user = user_factory(
            username='testuser',
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
        )

        anonymize_user(user)

        fresh_user = User.objects.get(id=user.id)

        assert fresh_user.first_name == 'anonymize'
        assert fresh_user.last_name == 'anonymize'
        assert fresh_user.email == 'anonymize'
        assert fresh_user.username == f'anon_{user.id}'

    def test_profile_anonymization_is_persistent(self, user_factory):
        """Test that profile anonymization changes are saved to database."""
        user = user_factory(username='testuser', email='<EMAIL>')

        profile = user.profile
        profile.first_name = 'John'
        profile.email = '<EMAIL>'
        profile.phone = '+1234567890'
        profile.save()

        anonymize_user(user)

        fresh_profile = UserProfile.objects.get(user_id=user.id)

        assert fresh_profile.first_name == 'anonymize'
        assert fresh_profile.email == 'anonymize'
        assert fresh_profile.phone == 'anonymize'

    def test_duplicate_fields_in_profile_list_handled_correctly(self, user_factory):
        """Test that duplicate fields in user_profile list don't cause issues."""

        user = user_factory(username='testuser', email='<EMAIL>')

        profile = user.profile
        profile.phone = '+1234567890'
        profile.company_name = 'Test Company'
        profile.save()

        anonymize_user(user)

        profile.refresh_from_db()

        assert profile.phone == 'anonymize'
        assert profile.company_name == 'anonymize'

    @patch('user_profile.services.anonymize.MarketingExcludeUpdateEvent')
    def test_marketing_event_called_even_if_other_operations_fail(
        self,
        mock_event,
        user_factory,
    ):
        """Test that MarketingExcludeUpdateEvent is still called even if cart deletion fails."""
        user = user_factory(username='testuser', email='<EMAIL>')

        with patch.object(user.carts, 'all') as mock_carts:
            mock_queryset = mock_carts.return_value
            mock_queryset.delete.side_effect = Exception('Cart deletion failed')

            try:
                anonymize_user(user)
            except Exception:
                pass  # We expect this to fail due to our mock

        mock_event.assert_called_once()
