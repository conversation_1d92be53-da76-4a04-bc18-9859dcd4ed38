from .common import (
    SITE_URL,
    env,
)

# PRODUCTION SYSTEM
PRODUCTION_SYSTEM_API_URL = env.str('PRODUCTION_SYSTEM_API_URL')
PRODUCTION_SYSTEM_API_URL = PRODUCTION_SYSTEM_API_URL.strip('/')
PRODUCTION_SYSTEM_LOCAL_URL = env.str(
    'PRODUCTION_SYSTEM_LOCAL_URL',
    default=PRODUCTION_SYSTEM_API_URL.split('/api/', 1)[0],
)
PRODUCTION_SYSTEM_REDIRECT_URL = env.str(
    'PRODUCTION_SYSTEM_REDIRECT_URL',
    default=PRODUCTION_SYSTEM_API_URL.replace('/api/v1', ''),
)
PRODUCTION_SYSTEM_TOKEN = env.str('PRODUCTION_SYSTEM_TOKEN')
PRODUCTION_SYSTEM_DEFAULT_TIMEOUT = env.int(
    'PRODUCTION_SYSTEM_DEFAULT_TIMEOUT',
    default=60,
)

CSTM_CALLBACK_URL = env.str('CSTM_CALLBACK_URL', default=SITE_URL)


# LOGISTIC
LOGISTIC_URL = env.str('LOGISTIC_URL', default='')
CSTM_USER_USERNAME = env.str('CSTM_USER_USERNAME', 'cstm')
LOGISTIC_USER_USERNAME = env.str('LOGISTIC_USER_USERNAME', 'logistic')
LOGISTIC_MOCK_REQUEST_STRATEGY = env.bool('LOGISTIC_MOCK_REQUEST_STRATEGY', False)

# BAG
BAG_URL = env.str('BAG_URL', default='')
BAG_TOKEN = env.str('BAG_TOKEN', default='')


OMNIBUS_SIMPLIFIED = env.bool('OMNIBUS_SIMPLIFIED', default=False)

# Google Tag Manager
ATUPALE_HOST_URL = 'https://atupale.tylko.com'

SELENIUM_USER_PASSWORD = env.str('SELENIUM_USER_PASSWORD', default='')

EXPORT_CASH_FLOW_TO_BIG_QUERY = False

# GOOGLE CHROME HEADLESS
GOOGLE_CHROME_URL = env('GOOGLE_CHROME_URL', default='http://127.0.0.1:9222')

GOOGLE_RECAPTCHA_SECRET = env.str('GOOGLE_RECAPTCHA_SECRET', default='')


FPM_API_URL = env.str('FPM_API_URL', default='')
