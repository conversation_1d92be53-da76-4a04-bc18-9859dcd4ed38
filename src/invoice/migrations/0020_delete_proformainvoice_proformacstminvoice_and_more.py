# Generated by Django 4.1.13 on 2024-07-01 12:27

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0019_invoice_source'),
    ]

    operations = [
        migrations.DeleteModel(
            name='ProformaInvoice',
        ),
        migrations.CreateModel(
            name='ProformaCstmInvoice',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.invoice',),
        ),
        migrations.CreateModel(
            name='ProformaCustomerServiceInvoice',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.invoice',),
        ),
        migrations.CreateModel(
            name='ProformaLogisticInvoice',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.invoice',),
        ),
        migrations.CreateModel(
            name='ProformaRetoolInvoice',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.invoice',),
        ),
    ]
