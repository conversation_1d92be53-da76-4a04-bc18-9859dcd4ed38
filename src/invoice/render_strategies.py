import typing

from abc import (
    ABC,
    abstractmethod,
)
from collections import defaultdict
from datetime import datetime
from datetime import timezone as dt_timezone

from django.conf import settings

from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from invoice.dto import InvoiceItemCachedDTO
from invoice.dto_aggregators import InvoiceItemCachedWithServicesPDFAggregator

if typing.TYPE_CHECKING:
    from invoice.models import Invoice
    from orders.models import Order


tzinfo = dt_timezone.utc if settings.USE_TZ else None
WITH_SERVICES_RELEASE_DATETIME = datetime(2025, 5, 28, 9, 30, 0, tzinfo=tzinfo)

TEMPLATE_WITH_SERVICE_NAME = 'invoice_with_services/invoice.html'
CORRECTION_TEMPLATE_WITH_SERVICE_NAME = 'invoice_with_services/invoice_correction.html'

TEMPLATE_NAME = 'invoice/invoice.html'
CORRECTION_TEMPLATE_NAME = 'invoice/invoice_correction.html'

LEGACY_UNITED_KINGDOM_TEMPLATE_NAME = 'invoice/legacy_invoice_united_kingdom.html'
LEGACY_UNITED_KINGDOM_CORRECTION_TEMPLATE_NAME = (
    'invoice/legacy_invoice_correction_united_kingdom.html'
)


def earliest_invoice_with_services_version_not_supported(order: 'Order') -> bool:
    first_invoice = order.invoice_set.order_by('id').first()
    first_invoice_before_exists = bool(
        first_invoice and first_invoice.issued_at < WITH_SERVICES_RELEASE_DATETIME
    )

    return first_invoice_before_exists


def is_correction(invoice_dict):
    return invoice_dict['status'] in InvoiceStatus.correcting_statuses()


class BaseRendererInvoiceStrategy(ABC):
    @abstractmethod
    def get_template_name(self) -> str:
        """Should return name of the template to be used for rendering"""

    @abstractmethod
    def default_invoice_items_grouped(self) -> dict:
        """Initial value for grouped invoice items"""

    @abstractmethod
    def group_by(self, invoice_item_dto: InvoiceItemCachedDTO) -> int:
        """Define group by field for invoice items"""

    def create_context_for_pdf(self) -> dict:
        if not self.invoice.pretty_id:
            raise Exception('Fill out the Invoice first')

        is_invoice_order_complaint_proforma = (
            self.invoice.order.is_to_be_shipped_complaint()
        )
        invoice_dict = self.invoice.to_dict(
            is_complaint_reproduction=is_invoice_order_complaint_proforma
        )
        invoice_items_grouped = self.group_invoice_items(invoice_dict)
        corrected_invoice_items_grouped = self.group_corrected_invoice_items(
            invoice_dict
        )

        return {
            'invoice_object': self.invoice,
            'invoice': invoice_dict,
            'invoice_items': dict(invoice_items_grouped),
            'corrected_invoice_items': dict(corrected_invoice_items_grouped),
            'is_complaint_proforma': is_invoice_order_complaint_proforma,
        }

    def group_invoice_items(self, invoice_dict: dict) -> dict:
        invoice_items_grouped = self.default_invoice_items_grouped()

        for invoice_item in invoice_dict['items']:
            invoice_item_dto = InvoiceItemCachedDTO(**invoice_item)
            invoice_items_grouped[self.group_by(invoice_item_dto)].append(
                invoice_item_dto
            )
        return invoice_items_grouped

    def group_corrected_invoice_items(self, invoice_dict) -> dict:
        if not is_correction(invoice_dict):
            return {}

        corrected_invoice = self.invoice.get_previous_correction()
        if corrected_invoice is None:
            corrected_invoice = self.invoice.corrected_invoice

        corrected_invoice_items_grouped = self.default_invoice_items_grouped()

        if corrected_invoice is None:
            raise Exception
        for invoice_item in corrected_invoice.to_dict()['items']:
            invoice_item_dto = InvoiceItemCachedDTO(**invoice_item)
            corrected_invoice_items_grouped[self.group_by(invoice_item_dto)].append(
                invoice_item_dto
            )

        return corrected_invoice_items_grouped


class ServiceAsSeparateEntryStrategy(BaseRendererInvoiceStrategy):
    def __init__(self, invoice: 'Invoice') -> None:
        self.invoice = invoice

    def get_template_name(self) -> str:
        if self.invoice.order.earliest_invoice_domestic_version_not_supported():
            if self.invoice.is_correction:
                return LEGACY_UNITED_KINGDOM_CORRECTION_TEMPLATE_NAME
            return LEGACY_UNITED_KINGDOM_TEMPLATE_NAME
        if self.invoice.is_correction:
            return CORRECTION_TEMPLATE_NAME
        return TEMPLATE_NAME

    def default_invoice_items_grouped(self) -> dict:
        return {item_type.value: [] for item_type in InvoiceItemType}

    def group_by(self, invoice_item_dto: InvoiceItemCachedDTO) -> int:
        return invoice_item_dto.item_type


class ServiceCombinedWithItemStrategy(BaseRendererInvoiceStrategy):
    def __init__(self, invoice: 'Invoice') -> None:
        self.invoice = invoice

    def get_template_name(self) -> str:
        if self.invoice.is_correction:
            return CORRECTION_TEMPLATE_WITH_SERVICE_NAME
        return TEMPLATE_WITH_SERVICE_NAME

    def default_invoice_items_grouped(self) -> dict:
        return defaultdict(InvoiceItemCachedWithServicesPDFAggregator)

    def group_by(self, invoice_item_dto: InvoiceItemCachedDTO) -> int | None:
        return invoice_item_dto.order_item_id
