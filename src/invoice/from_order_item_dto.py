import logging
import typing

from dataclasses import dataclass
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from custom.enums import ShelfType
from custom.models import Countries
from invoice.choices import InvoiceItemType
from invoice.utils import gross_to_net
from producers.choices import ProductStatus

if typing.TYPE_CHECKING:
    from invoice.models import Invoice
    from orders.models import (
        Order,
        OrderItem,
    )


logger = logging.getLogger('invoice')

DEFAULT_MINIMAL_SAMPLE_ITEM_GROSS_FREE = Decimal('1.00')

MINIMAL_SAMPLE_ITEM_GROSS_FREE_BY_COUNTRY = {
    Countries.united_kingdom.name: Decimal('1.00'),
    Countries.switzerland.name: Decimal('1.00'),
    Countries.norway.name: Decimal('5.00'),
}


def round_half_up(value: Decimal) -> Decimal:
    return value.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)


POLISH_DESCRIPTION_FOR_CUSTOMS_AGENCY_JETTY_MATERIALS = {
    ShelfType.TYPE01: {
        0: ' / Biała sklejka',
        1: ' / <PERSON><PERSON>na sklejka',
        2: ' / <PERSON>lejonka',
        3: ' / Szara sklejka',
        6: ' / Sklejka z laminatem HPL w kolorze classic-red',
        7: ' / Sklejka z laminatem HPL w kolorze yellow',
        8: ' / Sklejka z laminatem HPL w kolorze dusty-pink',
    },
    ShelfType.TYPE02: {
        0: ' / Biała płyta wiórowa',
        1: ' / Płyta wiórowa',
        2: ' / Granatowa płyta wiórowa',
        3: ' / Piaskowa płyta wiórowa',
        4: ' / Płyta wiórowa',
    },
    ShelfType.VENEER_TYPE01: {
        0: ' / Fornir',
        1: ' / Fornir',
    },
}

SAMPLEBOX_NAME = ' / Próbki materiałowe'
WATTY_NAME = ' / Szafa Tylko'
JETTY_NAME = ' / Regał Tylko'
SOTTY_NAME = (
    ' / Modułowy zestaw sof tapicerowanych, rama: drewniana, '
    'tapicerka: {material}, do użytku domowego'
)
CORDUROY_MATERIAL = 'sztruksowa'
WOOL_MATERIAL = 'wełniana'


@dataclass
class InvoiceItemFromOrderItem:
    order_item: 'OrderItem'
    order: 'Order'
    invoice: 'Invoice'
    ignore_promo_on_invoice: bool

    def __post_init__(self) -> None:
        self.item_gross = self.order_item.get_price_number()
        self.item_net = self.order_item.get_price_net_number()
        if self.ignore_promo_on_invoice:
            self.item_gross_promo = Decimal(0.00)
        else:
            self.item_gross_promo = self.order_item.region_promo_value

    @property
    def net_price_per_item(self) -> Decimal:
        if self.order.is_united_kingdom() and not self.invoice.is_domestic:
            return self.item_gross
        return self.item_net

    @property
    def gross_price_per_item(self) -> Decimal:
        return self.item_gross - self.item_gross_promo

    @property
    def gross_price_total(self) -> Decimal:
        gross_price = self.gross_price_per_item * self.order_item.quantity
        return round_half_up(gross_price)

    @property
    def net_value_per_item(self) -> Decimal:
        net_value = gross_to_net(self.gross_price_per_item, self.invoice.get_vat_rate())
        return round_half_up(net_value)

    @property
    def net_value_total(self) -> Decimal:
        net_value = gross_to_net(self.gross_price_total, self.invoice.get_vat_rate())
        return round_half_up(net_value)

    @property
    def vat_amount_total(self) -> Decimal:
        return self.gross_price_total - self.net_value_total

    @property
    def recycle_tax_value(self) -> Decimal:
        return self.order_item.recycle_tax_value * self.order_item.quantity

    @property
    def discount_value_per_item(self) -> Decimal:
        return self.net_price_per_item - self.net_value_per_item

    def get_descriptions(self) -> tuple[str, str, str]:
        order_item = self.order_item
        furniture_item = order_item.order_item
        description = furniture_item.get_item_description()

        material = description['material']
        name = description['name']
        if order_item.is_samplebox():
            dimension = ''
        else:
            dimension = description['dimensions']

        if not self.is_polish_description_required_for_customs_agency():
            return dimension, material, name

        if order_item.is_samplebox():
            name = f'{name}{SAMPLEBOX_NAME}'
        elif order_item.is_watty():
            name = f'{name}{WATTY_NAME}'
        elif order_item.is_sotty():
            material_name = (
                CORDUROY_MATERIAL
                if furniture_item.has_corduroy_material
                else WOOL_MATERIAL
            )
            polish_name = SOTTY_NAME.format(material=material_name)
            name = f'{name}{polish_name}'
        elif order_item.is_jetty():
            name = f'{name}{JETTY_NAME}'
            shelf_type = furniture_item.shelf_type
            try:
                by_shelf_type = (
                    POLISH_DESCRIPTION_FOR_CUSTOMS_AGENCY_JETTY_MATERIALS.get(
                        shelf_type
                    )
                )
                polish_material = by_shelf_type[furniture_item.material]
                material = f'{material}{polish_material}'
            except KeyError:
                logger.info('Missing customs agency translation %s', order_item.id)

        return dimension, material, name

    def is_polish_description_required_for_customs_agency(self):
        return self.order.country and self.order.country.lower() in [
            Countries.switzerland.name,
            Countries.norway.name,
            Countries.united_kingdom.name,
        ]

    def get_weights(self) -> tuple[float, float]:
        order_item = self.order_item
        try:
            if self.order.is_complaint():
                product = order_item.product_set.exclude(
                    status=ProductStatus.ABORTED
                ).first()
                net_weight = round(product.get_weight_netto(), 2)
                gross_weight = round(product.get_weight_brutto(), 2)
            else:
                net_weight = round(
                    order_item.order_item.get_weight() * order_item.quantity,
                    2,
                )
                gross_weight = round(
                    order_item.order_item.get_accurate_weight_gross()
                    * order_item.quantity,
                    2,
                )
        except KeyError:
            # PS failed to provide data
            logger.error(
                'PS failed to provide data for order_item %s',
                order_item.order_item.pk,
            )
            net_weight = 0
            gross_weight = 0
        except ZeroDivisionError:
            logger.error(
                'Zero division error for order_item %s',
                order_item.order_item.pk,
            )
            net_weight = 0
            gross_weight = 0
        except:  # noqa
            logger.error(
                'PS Timeout or something else :( order_item - %s',
                order_item.order_item.pk,
            )
            net_weight = 0
            gross_weight = 0
        return gross_weight, net_weight


@dataclass
class AssemblyInvoiceItemFromOrderItem:
    order_item: 'OrderItem'
    order: 'Order'
    invoice: 'Invoice'

    def __post_init__(self) -> None:
        self.item_gross = self.order_item.region_assembly_price
        self.item_net = gross_to_net(self.item_gross, self.invoice.get_vat_rate())
        self.item_gross_promo = Decimal(0.00)

    @property
    def net_price_per_item(self) -> Decimal:
        if self.order.is_united_kingdom() and not self.invoice.is_domestic:
            return self.item_gross
        return self.item_net

    @property
    def gross_price_per_item(self) -> Decimal:
        return self.item_gross - self.item_gross_promo

    @property
    def gross_price_total(self) -> Decimal:
        gross_price = self.gross_price_per_item * self.order_item.quantity
        return round_half_up(gross_price)

    @property
    def net_value_total(self) -> Decimal:
        net_value = gross_to_net(self.gross_price_total, self.invoice.get_vat_rate())
        return round_half_up(net_value)

    @property
    def vat_amount_total(self) -> Decimal:
        return self.gross_price_total - self.net_value_total

    def get_descriptions(self) -> tuple[str, str, str]:
        if self.gross_price_total:
            suffix = f'Included {self.gross_price_total}{self.invoice.currency_symbol}'
        else:
            suffix = 'Free of charge'
        name = f'{InvoiceItemType.ASSEMBLY.label} {suffix}'
        return '', '', name


@dataclass
class DeliveryItemFromOrderItem:
    order_item: 'OrderItem'
    order: 'Order'
    invoice: 'Invoice'
    ignore_promo_on_invoice: bool

    def __post_init__(self) -> None:
        self.item_gross = self.order_item.region_delivery_price
        self.item_net = gross_to_net(self.item_gross, self.invoice.get_vat_rate())
        if self.ignore_promo_on_invoice:
            self.item_gross_promo = Decimal(0.00)
        else:
            self.item_gross_promo = self.order_item.region_delivery_promo_value

    @property
    def net_price_per_item(self) -> Decimal:
        if self.order.is_united_kingdom() and not self.invoice.is_domestic:
            return self.item_gross
        return self.item_net

    @property
    def gross_price_per_item(self) -> Decimal:
        return self.item_gross - self.item_gross_promo

    @property
    def gross_price_total(self) -> Decimal:
        gross_price = self.gross_price_per_item * self.order_item.quantity
        return round_half_up(gross_price)

    @property
    def net_value_per_item(self) -> Decimal:
        net_value = gross_to_net(self.gross_price_per_item, self.invoice.get_vat_rate())
        return round_half_up(net_value)

    @property
    def net_value_total(self) -> Decimal:
        net_value = gross_to_net(self.gross_price_total, self.invoice.get_vat_rate())
        return round_half_up(net_value)

    @property
    def vat_amount_total(self) -> Decimal:
        return self.gross_price_total - self.net_value_total

    @property
    def discount_value_per_item(self) -> Decimal:
        return self.net_price_per_item - self.net_value_per_item

    def get_descriptions(self) -> tuple[str, str, str]:
        if self.gross_price_total:
            suffix = f'Included {self.gross_price_total}{self.invoice.currency_symbol}'
        else:
            suffix = 'Free of charge'
        name = f'{InvoiceItemType.DELIVERY.label} {suffix}'
        return '', '', name


@dataclass
class AdditionalServiceItemFromOrderItem(InvoiceItemFromOrderItem):
    order_item: 'OrderItem'
    order: 'Order'
    invoice: 'Invoice'
    ignore_promo_on_invoice: bool

    def get_descriptions(self) -> tuple[str, str, str]:
        order_item = self.order_item
        service_item = order_item.order_item
        if self.gross_price_total:
            suffix = f'Included {self.gross_price_total}{self.invoice.currency_symbol}'
        else:
            suffix = 'Free of charge'
        name = f'{service_item.get_kind_display()} {suffix}'
        return '', '', name


@dataclass
class FreeSampleInvoiceItemFromOrderItem(InvoiceItemFromOrderItem):
    def __post_init__(self) -> None:
        self.item_gross = MINIMAL_SAMPLE_ITEM_GROSS_FREE_BY_COUNTRY.get(
            self.invoice.order.country, DEFAULT_MINIMAL_SAMPLE_ITEM_GROSS_FREE
        )

        self.item_net = gross_to_net(self.item_gross, self.invoice.get_vat_rate())
        if self.ignore_promo_on_invoice:
            self.item_gross_promo = Decimal(0.00)
        else:
            self.item_gross_promo = self.order_item.region_promo_value
