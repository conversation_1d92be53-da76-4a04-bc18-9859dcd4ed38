from django.urls import (
    include,
    path,
)

app_name = 'APIv1'

urlpatterns = [
    path('tokens/', include('user_profile.internal_api.urls')),
    path('mailings/', include('mailing.internal_api.urls')),
    path('orders/', include('orders.internal_api.urls')),
    path('free-returns/', include('free_returns.internal_api.urls')),
    path('regions/', include('regions.internal_api.urls')),
    path('complaints/', include('complaints.internal_api.urls')),
    path('products/', include('producers.internal_api.urls')),
    path('invoices/', include('invoice.internal_api.urls')),
]
