import logging

from decimal import Decimal
from typing import Optional

from django.contrib.contenttypes.models import ContentType
from django.db.models import (
    Q,
    QuerySet,
)
from django.db.models.fields.files import ImageFieldFile
from django.utils.translation import get_language

from rest_framework import serializers
from rest_framework.request import Request

from catalogue.models import (
    BoardManualOrder,
    CatalogueEntry,
)
from catalogue.utils import get_catalogue_entry_labels
from custom.enums import (
    Furniture,
    ShelfType,
    Sofa01Color,
)
from gallery.services.prices_for_serializers import (
    RegionCurrencySerializerMixin,
    get_price_in_euro,
    get_region_price,
    get_region_price_with_discount_from_price,
)
from gallery.slugs import (
    get_slug_for_furniture_material,
    get_title_for_grid,
)
from promotions.models import (
    Promotion,
    PromotionConfig,
)

logger = logging.getLogger('cstm')


def http_or_https_hack(original_url):
    """We don't have proper security headers set up yet, so we have to use this hack.

    TODO: Once the security epic is done, test if it's still needed (shouldn't be).

    Maps links to images from http to https, but not on local.
    """
    is_localhost = (
        'localhost' in original_url
        or '127.0.0.1' in original_url
        or '0.0.0.0' in original_url
    )
    if not is_localhost:
        return original_url.replace('http://', 'https://')
    return original_url


def get_image_url(request: Request, image: ImageFieldFile) -> str:
    try:
        url = request.build_absolute_uri(image.url)
    except (ValueError, AttributeError):
        return ''
    # NOTE: this will be fixed once we use actual security headers
    return http_or_https_hack(url)


class SimpleCatalogueEntrySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='furniture.id')
    furniture_type = serializers.CharField(source='content_type.name')
    brand = serializers.CharField(source='furniture.get_pattern_name')
    preview_instagrid = serializers.SerializerMethodField()
    product_unreal_image = serializers.SerializerMethodField()
    lifestyle_unreal_image = serializers.SerializerMethodField()
    material_group = serializers.SerializerMethodField()
    catalogue_id = serializers.IntegerField(source='id')

    height = serializers.SerializerMethodField()
    width = serializers.SerializerMethodField()
    depth = serializers.SerializerMethodField()

    class Meta:
        model = CatalogueEntry
        fields = [
            'id',
            'furniture_type',
            'category',
            'shelf_type',
            'brand',
            'preview_instagrid',
            'product_unreal_image',
            'lifestyle_unreal_image',
            'material_group',
            'width',
            'height',
            'catalogue_id',
        ]

    def get_height(self, instance: CatalogueEntry) -> int:
        if instance.furniture.furniture_type == Furniture.sotty.value:
            return instance.furniture.get_height()
        return instance.height

    def get_width(self, instance: CatalogueEntry) -> int:
        if instance.furniture.furniture_type == Furniture.sotty.value:
            return instance.furniture.get_width()
        return instance.width

    def get_depth(self, instance: CatalogueEntry) -> int:
        if instance.furniture.furniture_type == Furniture.sotty.value:
            return instance.furniture.get_depth()
        return instance.depth

    def get_preview_instagrid(self, obj: CatalogueEntry):
        image = obj.real_lifestyle_image or obj.lifestyle_unreal_image_webp
        return self._get_image_url(image)

    def get_product_unreal_image(self, obj: CatalogueEntry):
        return self._get_image_url(obj.product_unreal_image_webp)

    def get_lifestyle_unreal_image(self, obj: CatalogueEntry):
        return self._get_image_url(obj.lifestyle_unreal_image_webp)

    @staticmethod
    def get_material_group(obj: CatalogueEntry) -> list[str] | str:
        if obj.material == Sofa01Color.MULTICOLOR:
            return [
                ShelfType(obj.shelf_type).colors(material).name.lower()
                for material in obj.furniture.materials
            ]
        return ShelfType(obj.shelf_type).colors(obj.material).name.lower()

    def _get_image_url(self, image):
        return get_image_url(request=self.context.get('request'), image=image)


class AvailableColorSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='object_id')
    material = serializers.IntegerField()
    thumbnail = serializers.SerializerMethodField()

    class Meta:
        model = CatalogueEntry
        fields = ['id', 'material', 'thumbnail']

    def get_thumbnail(self, obj: CatalogueEntry) -> str:
        return get_image_url(
            request=self.context.get('request'),
            image=obj.product_unreal_thumbnail_image_webp or '',
        )


class CatalogueEntrySerializer(
    RegionCurrencySerializerMixin,
    SimpleCatalogueEntrySerializer,
):
    configurator_type = serializers.IntegerField(source='furniture.configurator_type')
    physical_product_version = serializers.IntegerField(
        source='furniture.physical_product_version',
    )
    image_cloudinary = serializers.SerializerMethodField()
    labels = serializers.SerializerMethodField()
    available_colors = serializers.SerializerMethodField()
    seo_slug = serializers.SerializerMethodField()
    slug = serializers.SerializerMethodField()
    region_price = serializers.SerializerMethodField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    discount_value = serializers.SerializerMethodField()

    class Meta:
        model = CatalogueEntry
        fields = [
            'id',
            'furniture_type',
            'category',
            'shelf_type',
            'configurator_type',
            'physical_product_version',
            'brand',
            'preview_instagrid',
            'image_cloudinary',
            'product_unreal_image',
            'lifestyle_unreal_image',
            'labels',
            'material',
            'material_group',
            'available_colors',
            'width',
            'height',
            'depth',
            'seo_slug',
            'slug',
            'order',
            'region_price',
            'region_price_in_euro',
            'region_price_with_discount',
            'region_price_with_discount_in_euro',
            'discount_value',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Prices are stored in the context to avoid multiple counting of the same values
        self._region_prices = {}
        self._discount_region_prices = {}

    @property
    def promo_config(self) -> Optional[PromotionConfig]:
        return self.context.get('promo_config')

    @property
    def current_promo(self) -> Optional[Promotion]:
        return self.context.get('current_promo', None)

    def get_available_colors(self, obj: CatalogueEntry) -> dict[str, str | int]:
        qs = (
            obj.related_entries.all_for_region(region=self.region)
            .exclude(
                Q(product_unreal_thumbnail_image_webp__isnull=True)
                | Q(product_unreal_thumbnail_image_webp='')
            )
            .only('object_id', 'material', 'product_unreal_thumbnail_image_webp')
        )
        return AvailableColorSerializer(qs, many=True, context=self.context).data

    def get_slug(self, obj: CatalogueEntry) -> str:
        lang = self._get_language()
        return get_slug_for_furniture_material(
            obj.furniture,
            lang,
            obj.material,
        )

    def get_region_price(self, obj) -> Decimal:
        return self._get_regional_price(obj)

    def get_region_price_in_euro(self, obj) -> Decimal:
        return get_price_in_euro(
            self._get_regional_price(obj),
            self.currency_rate,
        )

    def get_region_price_with_discount(self, obj) -> Decimal:
        return self._get_discount_regional_price(obj, self.current_promo)

    def get_region_price_with_discount_in_euro(self, obj) -> Decimal:
        return get_price_in_euro(
            self._get_discount_regional_price(obj, self.current_promo),
            self.currency_rate,
        )

    def get_discount_value(self, obj) -> int:
        promotion = self.context['current_promo']
        if promotion and promotion.promo_code:
            return promotion.promo_code.get_discount_value_for_item(obj.furniture)

        return 0

    def get_labels(self, obj: CatalogueEntry):
        voucher = self.current_promo.promo_code if self.current_promo else None
        return get_catalogue_entry_labels(
            item=obj.furniture,
            region=self.region,
            voucher=voucher,
            promotion=self.current_promo,
            catalogue_entry=obj,
            promotion_config=self.promo_config,
        )

    def get_image_cloudinary(self, obj: CatalogueEntry):
        return getattr(obj.image_cloudinary, 'url', '')

    def get_seo_slug(self, obj: CatalogueEntry):
        return get_title_for_grid(obj.furniture, self._get_language())

    def _get_language(self):
        try:
            lang_param = self.context.get('request').query_params.get('lang', None)
        except AttributeError:
            lang_param = None
        return lang_param or get_language()

    def _get_regional_price(self, obj: CatalogueEntry) -> Decimal:
        if obj.id not in self._region_prices:
            region_price = get_region_price(
                furniture=obj.furniture,
                region=self.region,
                region_calculations_object=self.region_calculations_object,
            )
            self._region_prices[obj.id] = region_price
        return self._region_prices[obj.id]

    def _get_discount_regional_price(
        self, obj: CatalogueEntry, promotion: Promotion
    ) -> Decimal:
        if obj.id not in self._discount_region_prices:
            discount_region_price = get_region_price_with_discount_from_price(
                self._region_prices[obj.id],
                obj.furniture,
                promotion,
            )
            self._discount_region_prices[obj.id] = discount_region_price
        return self._discount_region_prices[obj.id]


class EntryVariantSerializer(CatalogueEntrySerializer):
    title = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()
    product_image_url = serializers.SerializerMethodField()
    lifestyle_image_url = serializers.SerializerMethodField()

    class Meta:
        model = CatalogueEntry
        fields = [
            'id',
            'furniture_type',
            'title',
            'material',
            'url',
            'product_image_url',
            'lifestyle_image_url',
            'region_price',
            'region_price_in_euro',
            'region_price_with_discount',
            'region_price_with_discount_in_euro',
            'discount_value',
        ]

    def get_title(self, obj: CatalogueEntry) -> str:
        return self.get_seo_slug(obj=obj)

    def get_url(self, obj: CatalogueEntry) -> str:
        if obj.furniture.furniture_type == Furniture.sotty.value:
            return obj.furniture.get_item_url_with_region(region=self.region)
        return obj.furniture.get_url_with_region(region=self.region)

    def get_product_image_url(self, obj: CatalogueEntry) -> str:
        return self.get_product_unreal_image(obj=obj)

    def get_lifestyle_image_url(self, obj: CatalogueEntry) -> str:
        return self.get_preview_instagrid(obj=obj)


class MinigridCatalogueEntrySerializer(CatalogueEntrySerializer):
    minigrid_order = serializers.SerializerMethodField()

    class Meta(CatalogueEntrySerializer.Meta):
        fields = CatalogueEntrySerializer.Meta.fields + ['minigrid_order']

    def get_minigrid_order(self, obj: CatalogueEntry) -> Optional[int]:
        """Needed in retool"""
        minigrid_name = self.context.get('minigrid_name')
        if not minigrid_name:
            return

        if hasattr(obj, 'minigrid_order') and len(obj.minigrid_order) == 1:
            return obj.minigrid_order[0].order


class BoardManualOrderSerializer(serializers.ModelSerializer):
    furniture_id = serializers.IntegerField(source='entry.object_id')
    furniture_type = serializers.CharField(write_only=True)
    board_name = serializers.CharField(allow_blank=True)
    entry_id = serializers.IntegerField(read_only=True)

    class Meta:
        model = BoardManualOrder
        fields = [
            'id',
            'entry_id',
            'furniture_id',
            'furniture_type',
            'board_name',
            'order',
        ]

    def get_entry_from_input_parameters(self, furniture_type, furniture_id):
        content_type = ContentType.objects.get(
            app_label='gallery',
            model=furniture_type,
        )
        return CatalogueEntry.objects.get(
            object_id=furniture_id, content_type=content_type
        )

    def create(self, validated_data):
        furniture_id = validated_data.pop('entry', {}).get('object_id')
        furniture_type = validated_data.pop('furniture_type')
        validated_data['entry'] = self.get_entry_from_input_parameters(
            furniture_type,
            furniture_id,
        )
        # TODO: if there's an override here already - raise Validation Error
        # TODO: if this entry is somewhere else on this board - raise
        return super().create(validated_data)

    def update(self, instance, validated_data):
        furniture_id = validated_data.pop('entry', {}).get('object_id')
        furniture_type = validated_data.pop('furniture_type')
        validated_data['entry'] = self.get_entry_from_input_parameters(
            furniture_type,
            furniture_id,
        )
        # TODO: if the BoardManualOrder doesnt exist - raise Validation Error
        # TODO: if this entry is somewhere else on this board - raise
        return super().update(instance, validated_data)


class BoardManualDestroySerializer(serializers.ModelSerializer):
    board_name = serializers.CharField(allow_blank=True)

    class Meta:
        model = BoardManualOrder
        fields = ['board_name', 'order']


class TopSellerSerializer(serializers.Serializer):
    furniture_type = serializers.CharField()
    furniture_id = serializers.IntegerField()

    def validate_furniture_type(self, value):
        try:
            return ContentType.objects.get(
                app_label='gallery',
                model=value,
            ).id
        except ContentType.DoesNotExist:
            raise serializers.ValidationError('Incorrect furniture type.')


class BoardCopySerializer(serializers.Serializer):
    board_to_be_copied_name = serializers.CharField()
    regions = serializers.ListField(required=True)

    def validate_board_to_be_copied_name(self, board_to_be_copied_name):
        if not BoardManualOrder.objects.filter(
            board_name=board_to_be_copied_name
        ).exists():
            raise serializers.ValidationError('Board with this name does not exist.')
        return board_to_be_copied_name

    def create_new_boards_names(
        self,
        regions: list[str],
        base_board_name: str,
    ) -> list[str]:
        return [f'{base_board_name}__region={region_name}' for region_name in regions]

    def copy_board(
        self,
        new_board_name: str,
        boards_to_be_copied: QuerySet[BoardManualOrder],
    ) -> None:
        previous_board = BoardManualOrder.objects.filter(board_name=new_board_name)
        if previous_board.exists():
            previous_board.delete()
        for bmo in boards_to_be_copied:
            bmo.id = None
            bmo._state.adding = True
            bmo.board_name = new_board_name
            bmo.save()

    def create(self, validated_data):
        board_to_be_copied_name = validated_data.pop('board_to_be_copied_name')
        boards_to_be_copied = BoardManualOrder.objects.filter(
            board_name=board_to_be_copied_name
        )
        new_board_names = self.create_new_boards_names(
            regions=validated_data.pop('regions'),
            base_board_name=board_to_be_copied_name.split('__region=')[0],
        )

        for new_board_name in new_board_names:
            self.copy_board(
                new_board_name=new_board_name,
                boards_to_be_copied=boards_to_be_copied,
            )
        return {
            'message': f'Board {board_to_be_copied_name} was copied to '
            f'{", ".join(new_board_names)}.'
        }
