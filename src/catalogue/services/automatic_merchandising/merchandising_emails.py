from typing import TYPE_CHECKING

from django.core.mail.message import EmailMessage

if TYPE_CHECKING:
    from catalogue.services.automatic_merchandising import MerchandiserOrchestrator


def send_automerch_finished_email(
    merchandiser: 'MerchandiserOrchestrator', email: str
) -> None:
    message = EmailMessage(
        subject='Automatic merchandising finished',
        body=merchandiser.merchandising_info,
        from_email='<EMAIL>',
        to=[email],
    )
    message.send(fail_silently=False)


def send_automerch_error_email(email: str, error: str) -> None:
    message = EmailMessage(
        subject='Automatic merchandising failed',
        body=f'Automatic merchandising failed with error: {error}',
        from_email='<EMAIL>',
        to=[email],
    )
    message.send(fail_silently=False)
