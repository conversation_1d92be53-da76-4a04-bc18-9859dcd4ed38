from django.db.models import QuerySet

from catalogue.exceptions import (
    EntryRepeatedInOverridesError,
    OverridesImproperlyOrderedForMerchandisingError,
)
from catalogue.models import BoardManualOrder


def validate_overrides_for_merchandising(overrides: QuerySet[BoardManualOrder]):
    """Validates overrides for automatic merchandising."""
    overrides_orders = {override.order for override in overrides}
    expected_orders = {i + 1 for i in range(len(overrides_orders))}
    if overrides_orders != expected_orders:
        raise OverridesImproperlyOrderedForMerchandisingError(
            'Overrides are supposed to be ordered from number 1 to the last one without'
            ' gaps.'
        )
    entries = {override.entry for override in overrides}
    if len(entries) != len(overrides):
        raise EntryRepeatedInOverridesError('Entries cannot be repeated.')
