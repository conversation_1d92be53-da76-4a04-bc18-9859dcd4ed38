import pytest

from catalogue.enums import StrategyEnum
from catalogue.services.automatic_merchandising.categories_filters_getters import (
    EdgeWardrobeFiltersGetter,
    WardrobeFiltersGetter,
)
from catalogue.services.automatic_merchandising.filters_getters import (
    AllShelvesEdgeOnlyFiltersGetter,
    AllShelvesFiltersGetter,
)
from catalogue.services.automatic_merchandising.merchandisers import (
    AllShelvesMerchandiser,
    CategoryMerchandiser,
)
from gallery.enums import FurnitureCategory


@pytest.mark.django_db
class TestAllShelvesMerchandiser:
    def test_merchandiser_without_strategy(self, catalogue_entry_factory):
        catalogue_entry_factory.create_batch(3)
        merchandiser = AllShelvesMerchandiser(strategy=StrategyEnum.NONE, region=None)

        assert merchandiser.shiam_field is None
        assert merchandiser.ordering_field == 'test_order'

    def test_merchandiser_with_strategy(self, catalogue_entry_factory):
        catalogue_entry_factory.create_batch(3)

        merchandiser = AllShelvesMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            region=None,
        )

        assert merchandiser.shiam_field == 'profit_netto'
        assert merchandiser.ordering_field == 'test_profit_netto_order'

    @pytest.mark.parametrize(
        ('region_name', 'code', 'shiam_field', 'ordering_field'),
        [
            ('germany', 'de', 'profit_netto_de', 'test_profit_netto_order_de'),
            ('france', 'fr', 'profit_netto_fr', 'test_profit_netto_order_fr'),
            ('netherlands', 'nl', 'profit_netto_nl', 'test_profit_netto_order_nl'),
            ('united_kingdom', 'uk', 'profit_netto_uk', 'test_profit_netto_order_uk'),
            ('switzerland', 'ch', 'profit_netto_ch', 'test_profit_netto_order_ch'),
        ],
    )
    def test_merchandisers_with_strategy_profit_netto_and_region(
        self,
        region_name,
        code,
        shiam_field,
        ordering_field,
        catalogue_entry_factory,
        country_factory,
    ):
        catalogue_entry_factory.create_batch(3)
        country = country_factory(
            name=region_name,
            region__name=region_name,
            code=code,
        )
        region = country.region
        strategy = StrategyEnum.PROFIT_NETTO

        merchandiser = AllShelvesMerchandiser(strategy=strategy, region=region)

        assert merchandiser.shiam_field == shiam_field
        assert merchandiser.ordering_field == ordering_field


@pytest.mark.django_db
class TestCategoryMerchandiser:
    category = FurnitureCategory.SIDEBOARD

    def test_merchandiser_without_strategy(self, sideboard_entries):
        merchandiser = CategoryMerchandiser(
            strategy=StrategyEnum.NONE,
            category=self.category,
            region=None,
        )

        assert merchandiser.shiam_field is None
        assert merchandiser.ordering_field == 'test_category_order'

    def test_merchandiser_with_strategy(self, sideboard_entries):
        merchandiser = CategoryMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            category=self.category,
            region=None,
        )

        assert merchandiser.shiam_field == 'profit_netto'
        assert merchandiser.ordering_field == 'test_profit_netto_category_order'

    @pytest.mark.parametrize(
        ('region_name', 'code', 'shiam_field', 'ordering_field'),
        [
            ('germany', 'de', 'profit_netto_de', 'test_profit_netto_category_order_de'),
            ('france', 'fr', 'profit_netto_fr', 'test_profit_netto_category_order_fr'),
            (
                'netherlands',
                'nl',
                'profit_netto_nl',
                'test_profit_netto_category_order_nl',
            ),
            (
                'united_kingdom',
                'uk',
                'profit_netto_uk',
                'test_profit_netto_category_order_uk',
            ),
            (
                'switzerland',
                'ch',
                'profit_netto_ch',
                'test_profit_netto_category_order_ch',
            ),
        ],
    )
    def test_merchandisers_with_strategy_profit_netto_and_region(
        self,
        region_name,
        code,
        shiam_field,
        ordering_field,
        sideboard_entries,
        country_factory,
    ):
        country = country_factory(
            name=region_name,
            region__name=region_name,
            code=code,
        )
        region = country.region
        strategy = StrategyEnum.PROFIT_NETTO

        merchandiser = CategoryMerchandiser(
            strategy=strategy,
            category=self.category,
            region=region,
        )

        assert merchandiser.shiam_field == shiam_field
        assert merchandiser.ordering_field == ordering_field


class TestSettingUpEdgeOnlyBoards:
    def test_category_merchandiser_for_edge_only_wardrobe_boards(
        self,
        edge_entries,
        tone_entries,
    ):
        merchandiser = CategoryMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            category=FurnitureCategory.WARDROBE,
            region=None,
            edge_wardrobes_only=True,
        )

        assert merchandiser.ordering_field == 'test_profit_netto_alt_category_order'
        assert merchandiser.queryset.count() == len(edge_entries)
        assert type(merchandiser.filters_getter) is EdgeWardrobeFiltersGetter

    def test_category_merchandiser_for_wardrobes_with_edge_only_false(
        self,
        edge_entries,
        tone_entries,
    ):
        merchandiser = CategoryMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            category=FurnitureCategory.WARDROBE,
            region=None,
            edge_wardrobes_only=False,
        )

        assert merchandiser.ordering_field == 'test_profit_netto_category_order'
        assert merchandiser.queryset.count() == len(edge_entries) + len(tone_entries)
        assert type(merchandiser.filters_getter) is WardrobeFiltersGetter

    def test_all_shelves_merchandiser_for_all_shelves_with_edge_wardrobes_only(
        self,
        edge_entries,
        tone_entries,
        sideboard_entries,
    ):
        merchandiser = AllShelvesMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            region=None,
            edge_wardrobes_only=True,
        )

        assert merchandiser.ordering_field == 'test_profit_netto_alt_order'
        assert merchandiser.queryset.count() == len(edge_entries + sideboard_entries)
        assert type(merchandiser.filters_getter) is AllShelvesEdgeOnlyFiltersGetter

    def test_all_shelves_merchandiser_for_all_shelves_with_edge_wardrobes_only_false(
        self,
        edge_entries,
        tone_entries,
        sideboard_entries,
    ):
        merchandiser = AllShelvesMerchandiser(
            strategy=StrategyEnum.PROFIT_NETTO,
            region=None,
            edge_wardrobes_only=False,
        )

        assert merchandiser.ordering_field == 'test_profit_netto_order'
        assert merchandiser.queryset.count() == len(
            edge_entries + sideboard_entries + tone_entries
        )
        assert type(merchandiser.filters_getter) is AllShelvesFiltersGetter
