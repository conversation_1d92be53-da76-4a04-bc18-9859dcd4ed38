from django.db import migrations


def create_merchandiser_user(apps, schema_editor):
    User = apps.get_model('auth', 'User')
    Token = apps.get_model('authtoken', 'Token')
    UserProfile = apps.get_model('user_profile', 'UserProfile')

    merchandiser_user, _ = User.objects.get_or_create(username='merchandiser')
    UserProfile.objects.get_or_create(user=merchandiser_user)
    Token.objects.get_or_create(user=merchandiser_user)


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0013_add_enabled_and_published_fields'),
        ('user_profile', '0015_remove_userprofile_intercom_registered_id'),
    ]

    operations = [
        migrations.RunPython(
            create_merchandiser_user,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
