#!/bin/bash
set -e
set -a

DOCKER=$1

export PGHOST="${POSTGRES_HOST:-127.0.0.1}"
export PGPORT="${POSTGRES_PORT:-5500}"
export PGUSER="${POSTGRES_USER:-cstm}"
export DATABASE="${POSTGRES_DB:-cstm}"
export PGPASSWORD="${PGPASSWORD:-cstm}"

export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"

if [ "$DOCKER" ]; then
    cd ..
    docker-compose up -d db
    cd src || exit
fi

# Check postgres and aws clients
if ! command -v aws &> /dev/null
then
    echo "You must install aws client"
    exit 1
fi

if ! command -v psql &> /dev/null
then
    echo "You must install postgres client"
    exit 1
fi

if ! pg_isready -d "$DATABASE" -h "$PGHOST" -p "$PGPORT" -U "$PGUSER" &> /dev/null
then
    echo "Could not connect to postgres server. Check if you set proper environment variables and if server is running."
    exit 1
fi


# Download dump from aws bucket
BUCKET=mini-dumps-2
DUMP_TAR="$(aws s3 ls $BUCKET --recursive | sort | tail -n 1 | awk '{print $4}')"

aws s3 cp s3://$BUCKET/"$DUMP_TAR" fixtures/"$DUMP_TAR"
tar -zxvf fixtures/"$DUMP_TAR" -C fixtures/

# Restore dump
psql -U "$PGUSER" -h "$PGHOST" -p "$PGPORT" -c "select pg_terminate_backend(pid) from pg_stat_activity where datname='$DATABASE' and pid <> pg_backend_pid()" "$DATABASE" || echo 1

dropdb -h "$PGHOST" -p "$PGPORT" -e "$DATABASE" --if-exists
createdb -h "$PGHOST" -p "$PGPORT" -e "$DATABASE"

# comment out -j5 if you get broken pipe error on macOS 14+ (Sonoma or newer)
if [[ "$(uname)" == "Darwin" && "$(sw_vers -productVersion | cut -d. -f1)" -ge 14 ]]; then
  echo "macOS $(sw_vers -productVersion) detected – skipping -j5"
  pg_restore -p "$PGPORT" -h "$PGHOST" -Fd --no-owner -d "$DATABASE" fixtures/dump
else
  pg_restore -p "$PGPORT" -h "$PGHOST" -Fd -j5 --no-owner -d "$DATABASE" fixtures/dump
fi

# Load static_files
# add if with docker
if [ "$DOCKER" ]; then
    cd ..
    docker-compose run --rm app python manage.py clear_old_media
    docker-compose run --rm app python manage.py load_static_files --directory fixtures/fixtures/
    docker-compose run --rm app python manage.py regenerate_dna_entries
    docker-compose run --rm app python manage.py create_developer_data
    cd src || exit
else
    python manage.py clear_old_media
    python manage.py load_static_files --directory fixtures/fixtures/
    python manage.py regenerate_dna_entries
    python manage.py create_developer_data
fi;

# Cleanup
rm -r fixtures/dump fixtures/fixtures fixtures/"$DUMP_TAR"
