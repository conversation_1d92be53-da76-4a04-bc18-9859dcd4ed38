from datetime import timed<PERSON><PERSON>
from tempfile import NamedTemporaryFile

from django.conf import settings
from django.utils import timezone

from PIL import Image

from custom.internal_api.enums import AssemblyTypeChoices
from orders.enums import OrderStatus
from vouchers.enums import VoucherType


def create_test_image():
    image = Image.new('RGB', size=(1, 1))
    file = NamedTemporaryFile(suffix='.jpg')
    image.save(file)
    return file


def get_test_jetty(filter=None):
    from gallery.models import Jetty
    from gallery.tests.factories import JettyFactory

    if filter is None:
        filter = {}
    if settings.IS_TESTING:
        jetty = JettyFactory(preview=create_test_image().read())
    else:
        jetty = Jetty.objects.filter(**filter).last()
    return jetty


def get_test_watty():
    from gallery.models import Watty
    from gallery.tests.factories import WattyFactory

    watty = Watty.objects.last()
    if settings.IS_TESTING or not watty:
        preview = create_test_image().read()
        watty = WattyFactory.create(preview=preview)
    return watty


def get_test_order(defaults=None, filters=None, paid=True, assembly=False):
    from django.contrib.auth.models import User

    from orders.models import (
        Order,
        PaidOrders,
    )
    from orders.order_notes import get_order_note
    from orders.tests.factories import OrderFactory

    if defaults is None:
        defaults = {}
    if filters is None:
        filters = {}
    if settings.IS_TESTING:
        defaults['owner'] = User.objects.get(username='admin')
        if paid:
            defaults['paid_at'] = timezone.now()
            defaults['status'] = OrderStatus.IN_PRODUCTION
        order = OrderFactory(
            **defaults,
            items__order_item=get_test_jetty(),
        )
    else:
        order_class = PaidOrders if paid else Order
        order = order_class.objects.filter(**filters).first()
    if assembly:
        order.assembly = True
        order.order_notes = get_order_note(
            assembly_type=AssemblyTypeChoices.ASSEMBLY_PAID.value,
            current_note=order.order_notes,
        )
    return order


def get_test_voucher(is_absolute=True):
    from vouchers.models import Voucher
    from vouchers.tests.factories import VoucherFactory

    if settings.IS_TESTING:
        kind_of = VoucherType.ABSOLUTE
        if is_absolute:
            kind_of = VoucherType.PERCENTAGE
        voucher = VoucherFactory(
            value=25,
            kind_of=kind_of,
            amount_starts=400,
            amount_limit=10000,
            end_date=timezone.now() + timedelta(days=1),
            active=True,
            quantity=1,
            quantity_left=1,
            code=Voucher.generate_code(),
        )
    else:
        voucher = Voucher.objects.last()
    return voucher
