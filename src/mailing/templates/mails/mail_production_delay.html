{% extends 'mails/_base_templates/base_flow_plaintext.html' %}
{% load mailing_tags %}
{% load region_tags %}
{% load voucher_tags %}
{% load util_tags %}
{% load i18n static %}

{# Preheader #}

{% block preheader %}
    {% blocktrans with name=user_name %}production_delay_email_hello_{{ name }}{% endblocktrans %}
{% endblock %}


{% block content %}
    {# Translation Keys Variables #}

        {% trans 'production_delay_email_p2' as paragraph_2 %}
        {% trans 'production_delay_email_p4' as paragraph_4 %}
        {% trans 'production_delay_email_p5' as paragraph_5 %}


    {# Mail Components structure #}

    {% mail_paragraph_plaintext '' join="lower" %}

    {% blocktrans with name=user_name %}production_delay_email_hello_{{ name }}{% endblocktrans %}

    {% mail_paragraph_plaintext '' join="upper" %}

    {# ###### P_1 ###### #}

    {% mail_paragraph_plaintext '' join="lower" %}
        {% blocktrans %}production_delay_email_p1_{{ order_id }}{% endblocktrans %}
    {% mail_paragraph_plaintext '' join="upper" %}

    {% mail_paragraph_plaintext paragraph_2 %}

    {% mail_paragraph_plaintext '' join="lower" %}
        {% blocktrans %}production_delay_email_p3_{{ date }}{% endblocktrans %}
    {% mail_paragraph_plaintext '' join="upper" %}
    {% mail_paragraph_plaintext paragraph_4 %}
    {% mail_whitespace height=10 %}
    {% mail_paragraph_plaintext paragraph_5 %}


{% endblock %}


{% block custom_footer_name %}
    {% mail_paragraph_plaintext '' join='lower' paragraph_color="#949494" font_size=12 line_height=14 %}
        {% if LANGUAGE_CODE != 'fr' %}
            <b>Bernard</b> <br>
            Customer Service Manager
        {% else %}
            <b>Bernard</b> <br>
            Directeur du Service Clients
        {% endif %}
    {% mail_paragraph_plaintext '' join="upper" %}
{% endblock %}
