import abc
import json
import logging

from datetime import timedelta
from os.path import (
    basename,
    splitext,
)
from pathlib import Path
from urllib.parse import urlparse

from django.conf import settings
from django.core.mail import get_connection
from django.template import loader
from django.utils import (
    timezone,
    translation,
)
from django.utils.translation import gettext_lazy as _
from django.utils.translation import trans_null
from django.utils.translation import trans_real as trans

from custom.enums import LanguageEnum
from custom.metrics import metrics_client
from mailing.constants import INVOICE_BCC_EMAILS
from mailing.helpers import get_test_order
from vouchers.models import Voucher

logger = logging.getLogger('cstm')


class BaseMail(abc.ABC):
    """Domain object of email message.

    Attributes
    ----------
    to_address: str, optional
        email address to which mail message will be sent
    topic: str, optional
        email message subject
    data_html: str, optional
        message body in HTML
    data_text: str, optional
        message body in plain text
    file_to_add: List[str | file-like object], optional
        list of file paths to sent as attachments
    template_html: str
        path to mail's body HTML template
    template_text: str
        path to mail's body plain text template
    from_address: str
        email address from which mail messages will be sent
    context_vars: List[str]
        keys of context variable passed to template renderer
    bcc_list: list, optional
        list of email addresses to send bcc
    """

    to_address = None
    bcc_list = None
    topic = None

    data_html = None
    data_text = None

    file_to_add = None

    def __init__(
        self,
        to_address,
        data_html,
        from_address=None,
        data_text=None,
        site=None,
        files_to_add=tuple(),
        topic=None,
        topic_variables=None,
        bcc_list=None,
    ):
        self.to_address = to_address.encode('ascii', 'ignore').decode('ascii')
        if from_address:
            self.from_address = from_address
        if bcc_list:
            self.bcc_list = bcc_list
        self.data_html = data_html
        if data_text is None:
            self.data_text = data_html
        self.site = self._strip_scheme(site or settings.SITE_URL)
        self.files_to_add = files_to_add
        if topic is not None:
            self.topic = topic
        self.topic_variables = topic_variables if topic_variables else {}

    @abc.abstractmethod
    def _prepare_test_data(self):
        """Return context data used to render sample emails."""
        raise NotImplementedError

    def send(self, language=None, tags=None, user_id=None):
        """Send email message.

        Sends email message rendered in ``language`` with mailchimp ``tags``
        attached.

        """
        from mailing.utils import EmailMultiRelated

        if self.to_address is not None:
            if not language:
                language = translation.get_language() or settings.LANGUAGE_CODE

            language = LanguageEnum(language).get_mailing_language()

            translation.activate(language)

            # text version
            t = loader.get_template(self.template_text)
            content = self.data_text
            content['site'] = self.site
            content['LANGUAGE_CODE'] = language
            mc_tags = [self.__class__.__name__, language]
            mc_metadata = dict()
            mc_metadata['user_id'] = str(user_id or '')
            if isinstance(self.from_address, tuple):
                self.from_address = ' '.join(str(a) for a in self.from_address)
            if tags is not None:
                for tag in tags:
                    mc_tags.append(str(tag))

            with get_connection(**settings.MAILCHIMP_EMAIL_PROVIDER_SETTINGS) as conn:
                headers = {
                    'X-MC-Tags': ','.join(mc_tags),
                    'X-MC-Metadata': json.dumps(mc_metadata),
                }
                #  fix for ECOM-1431, if more domains will be problematic, please
                #  move to more generic solution. This header disable clicking tracking
                if 't-online.de' in self.to_address:
                    headers['X-MC-Track'] = 'None'

                msg = EmailMultiRelated(
                    self.get_topic(),
                    t.render(content),
                    self.from_address,
                    [self.to_address],
                    headers=headers,
                    connection=conn,
                    bcc=self.bcc_list,
                )

                # html version
                if hasattr(self, 'template_html'):
                    t = loader.get_template(self.template_html)
                    content = self.data_html
                    content['site'] = self.site
                    content['LANGUAGE_CODE'] = language
                    msg.attach_alternative(t.render(content), 'text/html')
                for file_to_add in self.files_to_add:
                    if hasattr(file_to_add, 'read'):
                        self.attach_file_by_file_obj(msg, file_to_add)
                    else:
                        self.attach_file_by_path(msg, file_to_add)
                msg.send()
                metrics_client().increment(
                    'backend.mail.send', 1, tags=['language:{}'.format(language)]
                )

    @staticmethod
    def attach_file_by_path(msg, file_to_add):
        try:
            msg.attach_file(file_to_add)
        except IOError:
            logger.error('missing file: %s', file_to_add)

    @staticmethod
    def attach_file_by_file_obj(msg, file_to_add):
        content = file_to_add.read()
        path = Path(file_to_add.name)
        msg.attach(path.name, content)

    def render(self, language=None):
        """Render email message body."""
        if not language:
            language = translation.get_language() or settings.LANGUAGE_CODE
        #  'xx' as language iso-code will be used to disable translation at all
        #  It will allow to see keys is raw form, usable for testing
        #  Only used internally
        if language == 'xx':
            translation._trans = trans_null
            translation.activate(None)
        else:
            translation._trans = trans
            translation.activate(language)
        t_text = loader.get_template(self.template_text)
        c_text = self.data_text
        c_text['site'] = self.site
        c_text['LANGUAGE_CODE'] = language
        c_text['template_filename'] = self.template_filename
        c_text['mail_class'] = self.__class__.__name__
        c_text['subject'] = self.topic
        render_text = t_text.render(c_text)
        t_html = loader.get_template(self.template_html)
        c_html = self.data_html
        c_html['site'] = self.site
        c_html['LANGUAGE_CODE'] = language
        c_html['template_filename'] = self.template_filename
        c_html['mail_class'] = self.__class__.__name__
        c_html['subject'] = self.topic
        render_html = t_html.render(c_html)
        translation.deactivate()
        return {'text': render_text, 'html': render_html}

    def get_topic(self):
        """``topic`` with added prefix."""
        topic = self.topic % self.topic_variables
        if settings.EMAIL_SPECIAL_PREFIX:
            return ' '.join((settings.EMAIL_SPECIAL_PREFIX, topic))
        return topic

    def set_test_data(self):
        """Update ``data_html`` and ``data_text`` with test data."""
        test_data = self._prepare_test_data()
        self.data_html = test_data
        self.data_text = test_data

    def send_test_mail(self, language=None):
        """Send email with test data."""
        self.set_test_data()
        self.send(language=language, user_id=123)

    @property
    def template_filename(self):
        """``template_text`` mail template filename."""
        return splitext(basename(self.template_text))[0]

    @staticmethod
    def _strip_scheme(url):
        """Strip scheme and trailing slashes from URL."""
        parsed_url = urlparse(url)
        parsed_url = parsed_url._replace(scheme='')
        return parsed_url.geturl().strip('/')


# 2017-07 New Design Version
class AccountCreatedFlowDesignMail(BaseMail):
    template_html = 'mails/system/mail_account_created_flowdesign.html'
    template_text = 'mails/system/mail_account_created_flowdesign.html'
    topic = _('mail_system_welcome_desktop_new_subject_1')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = []

    def _prepare_test_data(self):
        return {}


# 2017-07 New Design Version
class ForgottenPasswordMail(BaseMail):
    template_html = 'mails/system/mail_password_reset_flowdesign.html'
    template_text = 'mails/system/mail_password_reset_flowdesign.html'
    topic = _('mail_system_password_change_subject_line_1')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'token',
    ]

    def _prepare_test_data(self):
        return {
            'token': 'abc123',
        }


# new version
class OrderPlacedMail(BaseMail):
    template_html = 'mails/transactions/mail_order_placed.html'
    template_text = 'mails/transactions/mail_order_placed.html'

    topic = _('mail_transaction_new_order_placed_subject_1_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'blacklist_token']

    def _prepare_test_data(self):
        return {
            'blacklist_token': 'zyx098',
            'order': get_test_order(assembly=True),
            'login_access_token': 'abc123',
        }


# sample version
class OrderPlacedMailSampleSet(BaseMail):
    template_html = 'mails/transactions/mail_order_placed_sample.html'
    template_text = 'mails/transactions/mail_order_placed_sample.html'

    topic = _('mail_transaction_new_order_placed_subject_1_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'blacklist_token']

    def _prepare_test_data(self):
        return {
            'blacklist_token': 'zyx098',
            'order': get_test_order(assembly=True),
            'login_access_token': 'abc123',
        }


class OrderPlacedReportMail(BaseMail):
    template_html = 'mails/transactions/mail_order_placed_report.html'
    template_text = 'mails/transactions/mail_order_placed_report.html'

    topic = 'Someone placed an order (not yet payed)'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order']

    def _prepare_test_data(self):
        return {
            'order': get_test_order(assembly=True),
        }


# new version
class OrderInvoiceMail(BaseMail):
    template_html = 'mails/transactions/mail_payment_confirmation.html'
    template_text = 'mails/transactions/mail_payment_confirmation.html'
    topic = _('mail_transaction_new_invoice_attached_subject_1_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'invoice']
    bcc_list = INVOICE_BCC_EMAILS

    def _prepare_test_data(self):
        return {
            'order': get_test_order(),
        }


# proforma
class OrderInvoiceProformaMail(BaseMail):
    template_html = 'mails/transactions/mail_payment_confirmation_proforma.html'
    template_text = 'mails/transactions/mail_payment_confirmation_proforma.html'
    topic = _('mail_transaction_proforma_invoice_subject_1_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'invoice']
    bcc_list = INVOICE_BCC_EMAILS

    def _prepare_test_data(self):
        return {
            'order': get_test_order(),
        }


# correction invoice
class OrderCorrectionInvoiceMail(BaseMail):
    template_html = 'mails/transactions/mail_correction_invoice.html'
    template_text = 'mails/transactions/mail_correction_invoice.html'
    topic = _(
        'mail_transaction_correction_invoice_attached_subject_1%(order_pretty_id)s'
    )
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'invoice']
    bcc_list = INVOICE_BCC_EMAILS

    def _prepare_test_data(self):
        return {
            'order': get_test_order(),
        }


class OrderCorrectionInvoiceFreeReturnNormalMail(BaseMail):
    template_html = 'mails/transactions/mail_correction_invoice_free_return_normal.html'
    template_text = 'mails/transactions/mail_correction_invoice_free_return_normal.html'
    topic = _('mail_correction_after_free_return_normal_subject_%(order)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'invoice']
    bcc_list = INVOICE_BCC_EMAILS

    def _prepare_test_data(self):
        order = get_test_order()
        return {
            'user': 'Test User',
            'order': order.id,
            'order_items_id': ', '.join(
                str(id) for id in order.items.values_list('id', flat=True)
            ),
        }


class OrderCorrectionInvoiceFreeReturnKlarnaMail(BaseMail):
    template_html = 'mails/transactions/mail_correction_invoice_free_return_klarna.html'
    template_text = 'mails/transactions/mail_correction_invoice_free_return_klarna.html'
    topic = _('mail_correction_after_free_return_klarna_subject_%(order)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'invoice', 'user']
    bcc_list = INVOICE_BCC_EMAILS

    def _prepare_test_data(self):
        order = get_test_order()
        return {
            'user': 'Test User',
            'order': order.id,
            'order_items_id': ', '.join(
                str(id) for id in order.items.values_list('id', flat=True)
            ),
        }


class OrderPaymentAuthorisedMail(BaseMail):
    template_html = 'mails/transactions/mail_payment_authorised.html'
    template_text = 'mails/transactions/mail_payment_authorised.html'
    topic = _('mail_transaction_new_payment_confirmation_subject_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'blacklist_token']

    def _prepare_test_data(self):
        order = get_test_order(
            defaults={
                'chosen_payment_method': 'klarna',
            },
            filters={'estimated_delivery_time__isnull': False},
            assembly=True,
        )
        return {
            'blacklist_token': 'zyx098',
            'order': order,
            'delivery_range': '09 - 11.09.2022',
            'amount': 'RAFCASHBACK',
            'value': 'RAFTEST',
            'minimal_order_value': 800,
            'double_cashback_value': 150,
        }


class OrderPaymentAuthorisedMailSampleSet(BaseMail):
    template_html = 'mails/transactions/mail_payment_authorised_sample.html'
    template_text = 'mails/transactions/mail_payment_authorised_sample.html'
    topic = _('mail_transaction_new_payment_confirmation_subject_%(order_pretty_id)s')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order', 'blacklist_token']

    def _prepare_test_data(self):
        order = get_test_order(
            defaults={'estimated_delivery_time': timezone.now() + timedelta(days=14)},
            filters={'estimated_delivery_time__isnull': False},
            assembly=True,
        )
        return {
            'blacklist_token': 'zyx098',
            'order': order,
            'delivery_date': order.estimated_delivery_time,
            'amount': 'RAFCASHBACK',
            'value': 'RAFTEST',
        }


class MissingSampleBoxStocksReportMail(BaseMail):
    template_html = 'mails/mail_missing_sample_stock_report.html'
    template_text = 'mails/mail_missing_sample_stock_report.html'
    topic = _('Sample stock low level')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'stocks',
    ]

    def _prepare_test_data(self):
        from warehouse.models import StockSampleBox

        return {'stocks': StockSampleBox.objects.all}


class SalesReport(BaseMail):
    template_html = 'mails/mail_sales_report.html'
    template_text = 'mails/mail_sales_report.html'
    topic = 'Sales report'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('data',)

    def _prepare_test_data(self):
        from admin_customization.anomaly_checks import SalesReportCheck

        return {'data': SalesReportCheck().check()}


class PaymentFailedInfoForTylko(BaseMail):
    template_html = 'mails/payment_failed_info_for_tylko.html'
    template_text = 'mails/payment_failed_info_for_tylko.html'
    topic = 'There was a failed payment!'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('order', 'notification')

    def _prepare_test_data(self):
        order = get_test_order()
        return {
            'order': order,
        }


class PaymentChargebackForTylko(BaseMail):
    template_html = 'mails/payment_chargeback_for_tylko.html'
    template_text = 'mails/payment_chargeback_for_tylko.html'
    topic = 'There was a chargeback!'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('order', 'notification')

    def _prepare_test_data(self):
        order = get_test_order()
        return {
            'order': order,
        }


class ProductManufacturedOneMail(BaseMail):
    template_html = 'mails/transactions/mail_product_manufactured_1.html'
    template_text = 'mails/transactions/mail_product_manufactured_1.html'
    topic = _('mail_transaction_new_product_manufacturedA_subject_1')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('order',)

    def _prepare_test_data(self):
        order = get_test_order(
            defaults={'estimated_delivery_time': timezone.now() + timedelta(days=14)},
            filters={'estimated_delivery_time__isnull': False},
        )
        return {
            'order': order,
            'delivery_left': order.get_estimated_delivery_time_left().weeks
            if order
            else 3,
            'order_status_query_dict': {
                'topic': 'order_status',
                'order_id': order.id,
                'postal_code': order.postal_code,
            },
        }


class ProductManufacturedTwoMail(BaseMail):
    template_html = 'mails/transactions/mail_product_manufactured_2.html'
    template_text = 'mails/transactions/mail_product_manufactured_2.html'
    topic = _('mail_transaction_new_product_manufacturedB_subject_1')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('order',)

    def _prepare_test_data(self):
        order = get_test_order(
            defaults={'estimated_delivery_time': timezone.now() + timedelta(days=14)},
            filters={'estimated_delivery_time__isnull': False},
        )
        return {
            'order': order,
            'delivery_left': order.get_estimated_delivery_time_left().weeks
            if order
            else 3,
            'order_status_query_dict': {
                'topic': 'order_status',
                'order_id': order.id,
                'postal_code': order.postal_code,
            },
        }


class ProductManufacturedZeroMail(BaseMail):
    template_html = 'mails/transactions/mail_product_manufactured_0.html'
    template_text = 'mails/transactions/mail_product_manufactured_0.html'
    topic = _(
        'mail_transaction_new_product_manufactured0_subject_1_%(order_pretty_id)s'
    )
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ('order',)

    def _prepare_test_data(self):
        order = get_test_order(
            defaults={'estimated_delivery_time': timezone.now() + timedelta(days=14)},
            filters={'estimated_delivery_time__isnull': False},
        )
        return {
            'order': order,
            'delivery_left': order.get_estimated_delivery_time_left().weeks
            if order
            else 3,
            'order_status_query_dict': {
                'topic': 'order_status',
                'order_id': order.id,
                'postal_code': order.postal_code,
            },
        }


class AnomalyDeliveredButBadStatusMail(BaseMail):
    template_html = 'mails/anomalies/delivered_bad_status.html'
    template_text = 'mails/anomalies/delivered_bad_status.html'
    topic = 'Anomaly Report'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['data']

    def _prepare_test_data(self):
        from admin_customization.anomaly_checks import DeliveredButBadStatusCheck

        return {'data': DeliveredButBadStatusCheck().check()}


class AnomalyItemsProductsAmountMail(BaseMail):
    template_html = 'mails/anomalies/items_products_amount.html'
    template_text = 'mails/anomalies/items_products_amount.html'
    topic = 'Anomaly Report'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['data']

    def _prepare_test_data(self):
        from admin_customization.anomaly_checks import (
            OrdersInProductionCheckWithDifferentProductItems,
        )

        return {'data': OrdersInProductionCheckWithDifferentProductItems().check()}


class AnomalyInvoiceMail(BaseMail):
    template_html = 'mails/anomalies/invoice.html'
    template_text = 'mails/anomalies/invoice.html'
    topic = 'Anomaly Invoice Report'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['data']

    def _prepare_test_data(self):
        from admin_customization.anomaly_checks import InvoiceNumbering

        return {'data': InvoiceNumbering().check()}


class ConfirmContactMail(BaseMail):
    template_html = 'mails/mail_confirm_contact.html'
    template_text = 'mails/mail_confirm_contact.html'
    topic = _('contact_form_emailconfirmation_header')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['contact_id', 'first_name']

    def _prepare_test_data(self):
        return {
            'first_name': 'TestName',
            'contact_id': 1234,
        }


class KlarnaNotification(BaseMail):
    template_text = 'mails/mail_klarna_notification.html'
    template_html = 'mails/mail_klarna_notification.html'
    topic = 'Tylko notification'
    from_address = 'Tylko <<EMAIL>>'

    def _prepare_test_data(self):
        return {'lines': ('line 1', 'line 2')}


class ProductionDelayEmail(BaseMail):
    template_text = 'mails/mail_production_delay.html'
    template_html = 'mails/mail_production_delay.html'
    topic = _('production_delay_email_subject_%(order_id)s')
    from_address = (
        _('mail_transaction_new_dedicated_transport_sender'),
        '<<EMAIL>>',
    )
    context_vars = ('sender_name',)

    def _prepare_test_data(self):
        from orders.models import PaidOrders

        order = PaidOrders.objects.all().first()
        return {
            'date': (order.paid_at + timedelta(days=60)).date().isoformat(),
            'user_name': order.owner.username,
            'order': PaidOrders.objects.all().first(),
            'order_id': order.order_pretty_id,
        }


class BankTransferReminderMail(BaseMail):
    template_html = 'mails/bank_transfer_reminder.html'
    template_text = 'mails/bank_transfer_reminder.html'
    topic = _('mail_transaction_new_bank_transfer_reminder_subject_line')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['blacklist_token', 'login_access_token', 'order']

    def _prepare_test_data(self):
        return {
            'blacklist_token': 'zyx098',
            'login_access_token': 'abc123',
            'order': get_test_order(),
        }


class SwitchOrderTeamLeaderInfo(BaseMail):
    template_text = 'mails/mail_cs_switch_exceptional_release.html'
    template_html = 'mails/mail_cs_switch_exceptional_release.html'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order_id', 'cs_user_email']

    def _prepare_test_data(self):
        return {
            'order_id': 123456,
            'cs_user_email': '<EMAIL>',
        }


class QualityControlNeededInfo(BaseMail):
    template_html = 'mails/mail_qc_info.html'
    template_text = 'mails/mail_qc_info.html'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['product_id']

    def _prepare_test_data(self):
        return {
            'product_id': 111222,
            'get_priority_display': 'Big Orders',
            'manufactor__name': 'Meble',
        }


class CodenamesReportReadyMail(BaseMail):
    template_html = 'mails/mail_codenames_report_ready.html'
    template_text = 'mails/mail_codenames_report_ready.html'
    topic = _('Codenames report ready')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['filename', 'pricing_factors', 'errors']

    def _prepare_test_data(self):
        return {
            'filename': 'test.csv',
            'pricing_factors': 'Use the current pricing factors',
            'errors': [11, 22, 33],
        }


class EcotaxReportReadyMail(BaseMail):
    template_html = 'mails/mail_tax_report_ready.html'
    template_text = 'mails/mail_tax_report_ready.html'
    topic = _('Ecotax report ready')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['filename', 'errors']

    def _prepare_test_data(self):
        return {
            'filename': 'test.csv',
            'errors': [11, 22, 33],
        }


class ElectrotaxReportReadyMail(BaseMail):
    template_html = 'mails/mail_tax_report_ready.html'
    template_text = 'mails/mail_tax_report_ready.html'
    topic = _('Electrotax report ready')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['filename', 'errors']

    def _prepare_test_data(self):
        return {
            'filename': 'test.csv',
            'errors': [11, 22, 33],
        }


class NotifyAccountingOrderAbortedKlarnaMail(BaseMail):
    template_text = 'mails/mail_notify_accounting_order_aborted_klarna.html'
    template_html = 'mails/mail_notify_accounting_order_aborted_klarna.html'
    topic = 'Tylko notification - Order aborted with Klarna'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['order_id']

    def _prepare_test_data(self):
        return {'order_id': 123444}


class NotifyOnAbortOrderMail(BaseMail):
    template_text = 'mails/mail_notify_on_abort.html'
    template_html = 'mails/mail_notify_on_abort.html'
    topic = 'Tylko notification - Anulowanie mebla'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['product_id', 'batch_id']

    def _prepare_test_data(self):
        return {'product_id': 123444, 'batch_id': 12222}


class ShelfMarketReportCreatedInfoMail(BaseMail):
    template_html = 'mails/mail_shelf_market_report_created.html'
    template_text = 'mails/mail_shelf_market_report_created.html'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = ['report_id', 'product_count', 'product_ids']

    def _prepare_test_data(self):
        return {'report_id': 111222, 'product_count': 3, 'product_ids': '1,2'}


def get_referral_context_for_email(region=None):
    cashback = Voucher.objects.filter(code='RAFCASHBACK').first()
    double_cashback = Voucher.objects.filter(code='DOUBLECASHBACK').first()
    cashback_amount = (
        cashback.get_regionalized_value_display(region) if cashback else ''
    )

    double_cashback_value = (
        double_cashback.get_regionalized_value_display(region)
        if double_cashback
        else ''
    )

    return {
        'amount': cashback_amount,
        'value': '27%',
        'double_cashback_value': double_cashback_value,
    }


class NotifyLogisticAbortingComplaintDone(BaseMail):
    """
    Email is sent when production team abort Complaint on status Done
    """

    template_text = 'mails/complaints/notify_logistic_about_aborting_done_complaint_email_template.html'  # noqa: E501
    template_html = 'mails/complaints/notify_logistic_about_aborting_done_complaint_email_template.html'  # noqa: E501
    topic = 'Production team has aborted Done Complaint'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'order_id',
    ]

    def _prepare_test_data(self):
        return {
            'order_id': '*********',
        }


class NotifyLogisticDamageFormReady(BaseMail):
    """
    Email is sent when damage form was created with status Ready
    """

    template_text = 'mails/complaints/' + 'notify_logistic_about_damage_form_ready.html'
    template_html = 'mails/complaints/' + 'notify_logistic_about_damage_form_ready.html'

    topic = 'complaint: %(order_id)s damage form ready'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'damage_form_id',
        'order_id',
    ]

    def _prepare_test_data(self):
        return {
            'damage_form_id': '*********',
            'order_id': '*********',
        }


class NotifyAccountingOrderMissingInvoice(BaseMail):
    """
    Email is sent when Free Return is created (from CS panel)
     and order is missing invoice (most probably Klarna case)
    """

    template_text = 'mails/mail_notify_accounting_missing_invoice.html'
    template_html = 'mails/mail_notify_accounting_missing_invoice.html'
    topic = 'Tylko notification - Missing order invoice after free return creation'
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'order_id',
        'free_return_id',
    ]

    def _prepare_test_data(self):
        return {
            'order_id': '*********',
            'free_return_id': '555',
        }


class NotifyClientReproductionCreated(BaseMail):
    template_text = 'mails/complaints/mail_notify_client_reproduction_created.html'
    template_html = 'mails/complaints/mail_notify_client_reproduction_created.html'
    topic = _('mail_complaint_created_after_service_subject')
    from_address = 'Tylko <<EMAIL>>'
    context_vars = [
        'order_id',
        'user',
        'blacklist_token',
    ]

    def _prepare_test_data(self):
        return {
            'user': 'Test User',
            'order_id': '*********',
            'blacklist_token': 'zyx098',
        }
