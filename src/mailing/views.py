import logging

from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext as _
from django.views.generic.base import View

from mailing.models import RetargetingBlacklist
from user_profile.models import RetargetingBlacklistToken

logger = logging.getLogger('cstm')


class RetargetingBlacklistView(View):
    def get(self, request, token, **kwargs):
        try:
            precreated_token = RetargetingBlacklistToken.objects.get(
                token=token, used_at__isnull=True
            )
            precreated_token.used_at = timezone.now()
            blacklist, created = RetargetingBlacklist.objects.get_or_create(
                email=precreated_token.email
            )
            source = request.GET.get('source')
            if created and source:
                blacklist.source = source
                blacklist.save(update_fields=('source',))
            precreated_token.save(update_fields=('used_at',))
            messages.info(request, _('unsubscription_subpage_confirmation_header_1'))
        except RetargetingBlacklistToken.DoesNotExist:
            messages.error(request, _('unsubscription_subpage_error_header_1'))
        except Exception:
            messages.error(request, _('unsubscription_subpage_error_header_1'))
            logger.exception('Error in retargeting blacklist view.')
        return redirect(reverse('front-homepage'))
