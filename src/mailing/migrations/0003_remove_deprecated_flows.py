from __future__ import unicode_literals

from django.db import migrations

deprecated_flows = [
    'NewsletterWithPromoReminderFlow',
    'CartRetargetingMailFourFlow',
    'CartRetargetingMailThreeFlow',
    'CartRetargetingMailTwoFlow',
    'CartRetargetingMailOneFlow',
    'CartRetargetingMailZeroFlow',
    'MailDayOneFlow',
    'AfterAbandonedCartFlow',
    'SavedItemFiveFlow',
    'DiscontinuedProductFlow',
    'FailedPaymentFlow',
]


def remove_unused_flows(apps, schema_editor):
    MailingFlowSettings = apps.get_model('mailing', 'MailingFlowSettings')
    MailingFlowSettings.objects.filter(flow_designation__in=deprecated_flows).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('mailing', '0002_newsletter_confirm_token'),
    ]

    operations = [
        migrations.RunPython(
            remove_unused_flows,
            migrations.RunPython.noop,
            elidable=True,
        )
    ]
