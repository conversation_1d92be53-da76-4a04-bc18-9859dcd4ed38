from __future__ import unicode_literals

from django.db import migrations

deprecated_flows = [
    'OrderToBeCancelledFlow',
    'PaymentFailedFlow',
]


def remove_unused_flows(apps, schema_editor):
    MailingFlowSettings = apps.get_model('mailing', 'MailingFlowSettings')
    MailingFlowSettings.objects.filter(flow_designation__in=deprecated_flows).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('mailing', '0004_update_verbose_name_fields'),
    ]

    operations = [
        migrations.RunPython(
            remove_unused_flows,
            migrations.RunPython.noop,
            elidable=True,
        )
    ]
