import pytest

from customer_service import filter_sets
from orders.models import Order


@pytest.mark.django_db
class TestSynonymousCharFieldsFilter:
    filter_class = filter_sets.SynonymousCharFieldsFilter

    def test_filter_returns_properly_filtered_queryset(
        self,
        order_factory,
    ):
        field_names = ('invoice_email', 'email')
        filter_ = self.filter_class(
            field_names,
            name='email',
            lookup_expr='icontains',
        )
        n = 10
        needle = 'foobar'
        orders = list()
        for i in range(n):
            orders.append(
                order_factory(email='{}{}@gmail.com'.format(needle, i)),
            )
            orders.append(
                order_factory(
                    invoice_email='{}{}@foo.bar'.format(needle, i),
                ),
            )
            order_factory(
                email='test{}@me.like'.format(i),
                invoice_email='you@do{}.test'.format(i),
            )
        queryset = filter_.filter(Order.objects.all(), needle)
        assert queryset.count() == 2 * n
        assert len(queryset.values_list('id', flat=True)) == len(
            [order.id for order in orders]
        )
