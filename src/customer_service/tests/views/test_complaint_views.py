import datetime

from copy import deepcopy
from decimal import Decimal
from unittest import mock

from django.http import HttpRequest
from django.urls import reverse

import pytest

from rest_framework import status

from complaints.choices import NotificationTypes
from complaints.elements_extractor import ProductElementInfo
from complaints.enums import ElementCategory
from complaints.models import (
    Complaint,
    ComplaintCosts,
)
from complaints.services.post_save_complaints_service import PostCreateComplaintService
from complaints.views import (
    AddComplaintView,
    DeleteComplaintView,
    UpdateComplaintView,
)
from customer_service.enums import CSCorrectionRequestStatus
from customer_service.models import CSCorrectionRequest
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from invoice.enums import InvoiceItemDiscountTag
from invoice.tests.factories import InvoiceItemFactory
from orders.models import Order
from producers.models import Product
from producers.tests.factories import ProductBatchFactory


@pytest.mark.django_db
@pytest.mark.nbp
class TestComplaintView:
    view_class = None
    view_name = None

    @pytest.fixture(autouse=True)
    def mock_external_dependencies(self, mocker, logistic_order_dto):
        mocker.patch('producers.tasks.generate_product_files')
        mocker.patch(
            'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
        )
        mocker.patch(
            'complaints.models.Complaint.get_reproduction_time_calculated',
            return_value=1,
        )
        mocker.patch(
            'complaints.forms.ProductElementsExtractor.__call__',
            return_value=[],
        )
        mocker.patch(
            'producers.models.Product.get_front_view_svg_from_ps',
            return_value='',
        )
        mocker.patch(
            'complaints.forms.BaseComplaintForm.get_fittings_queryset',
            return_value=[],
        )
        mocker.patch(
            'complaints.elements_extractor.ProductElementsExtractor.__call__',
            return_value=self.get_elements_extractor_mock(),
        )
        mocker.patch(
            'regions.mixins.RegionalizedMixin.display_regionalized', return_value='100'
        )
        mock_complaintpushedtoproductionevent = mocker.patch(
            'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
        )
        mocker.patch('complaints.forms.LogisticOrderDeleteEvent')

        mock_complaintpushedtoproductionevent.return_value.logistic_order = (
            logistic_order_dto
        )
        mocker.patch(
            'complaints.services.post_save_complaints_service.ComplaintAssemblyTeamInterventionRequiredEvent'
        )

    @pytest.fixture
    def invoice(self, invoice_factory, order):
        return invoice_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='test/DE',
            order=order,
        )

    def get_elements_extractor_mock(self):
        return [
            ProductElementInfo(
                ElementCategory.REGULAR,
                code='b3',
                box_number=3,
                cost=Decimal('93.75'),
                weight=Decimal('6.91'),
            ),
            ProductElementInfo(
                ElementCategory.REGULAR,
                code='b5',
                box_number=8,
                cost=Decimal('61.00'),
                weight=Decimal('4.62'),
            ),
            ProductElementInfo(
                ElementCategory.FEATURE,
                code='f10',
                box_number=19,
                cost=Decimal('96.14'),
                weight=Decimal('2.64'),
            ),
            ProductElementInfo(
                ElementCategory.FEATURE,
                code='f1',
                box_number=14,
                cost=Decimal('222.85'),
                weight=Decimal('9.41'),
            ),
        ]

    @pytest.fixture
    def base_form_data(self, admin_user, typical_issues, complaint_type, product):
        return {
            'reported_date': datetime.date(2033, 3, 3),
            'reproduction': False,
            'refund': False,
            'typical_issues': typical_issues.id,
            'complaint_type': complaint_type.id,
            'reporter': admin_user.id,
            'notification_type': NotificationTypes.NO_INFO,
            'additional_info': 'test',
            'refund_amount': 0,
            'refund_reason': '---------',
            'refund_currency': 'EUR',
            'product': product.id,
        }

    def get_form_data(self, base_form_data, reproduction=None, refund=None):
        base_form_data = deepcopy(base_form_data)
        if reproduction:
            base_form_data['assembly_team_intervention'] = True
            base_form_data['reproduction'] = True
            base_form_data['elements_regular'] = ['b3 - BOX:3 - 1', 'b5 - BOX:8 - 2']
            base_form_data['elements_feature'] = [
                'f1 - BOX:14 - 1',
                'f10 - BOX:19 - 2',
            ]

        if refund:
            base_form_data['refund'] = True
            base_form_data['refund_amount'] = Decimal('113.10')
            base_form_data['refund_currency'] = 'EUR'
            base_form_data[
                'refund_reason'
            ] = InvoiceItemDiscountTag.DELIVERY_DELAY.value
            if not reproduction:
                del base_form_data['typical_issues']
        return base_form_data


class TestAddComplaintView(TestComplaintView):
    view_class = AddComplaintView
    view_name = 'cs_create_complaint'

    @pytest.fixture()
    def staff_admin_client(self, admin_user, client):
        admin_user.is_staff = True
        admin_user.save()
        client.force_login(admin_user)
        return client

    def test_get_form_kwargs_contains_product_instance(self, product):
        view = self.view_class()
        view.request = mock.Mock()
        view.object = product
        form_kwargs = view.get_form_kwargs()
        assert form_kwargs['product'] == product

    def test_post_creates_proper_complaint_costs_instance(
        self,
        staff_admin_client,
        base_form_data,
        invoice,
        product_factory,
    ):
        product = product_factory(order=invoice.order)
        refund_form_data = self.get_form_data(base_form_data, refund=True)
        assert ComplaintCosts.objects.count() == 0
        response = staff_admin_client.post(
            reverse(self.view_name, args=[product.id]),
            data=refund_form_data,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        assert ComplaintCosts.objects.count() == 1
        assert ComplaintCosts.objects.first().refund_amount == Decimal('113.10')

    @mock.patch(
        'complaints.services.express_replacement.is_express_replacement_possible',
        return_value=True,
    )
    def test_post_set_Complaint_express_replacement_field_to_true_when_express_replacement_possible(
        self,
        _,
        staff_admin_client,
        invoice,
        base_form_data,
        product_factory,
    ):
        reproduction_form_data = self.get_form_data(base_form_data, reproduction=True)
        product = product_factory(order=invoice.order)
        staff_admin_client.post(
            reverse(self.view_name, args=[product.id]),
            data=reproduction_form_data,
        )
        complaint = Complaint.objects.get(product=product)
        assert complaint.express_replacement

    @pytest.mark.parametrize(
        ('init_data'),
        [
            {'refund': True, 'reproduction': True, 'expect_correction_request': True},
            {'refund': True, 'reproduction': False, 'expect_correction_request': True},
            {'refund': False, 'reproduction': True, 'expect_correction_request': False},
            {
                'refund': False,
                'reproduction': False,
                'expect_correction_request': False,
            },
        ],
    )
    def test_complaint_and_correction_request_created(
        self,
        init_data,
        staff_admin_client,
        base_form_data,
        invoice,
        product_factory,
    ):
        product = product_factory(order=invoice.order)
        form_data = self.get_form_data(
            base_form_data,
            reproduction=init_data['reproduction'],
            refund=init_data['refund'],
        )

        assert (
            CSCorrectionRequest.objects.filter(
                invoice__order_id=product.order_id
            ).count()
            == 0
        )
        staff_admin_client.post(
            reverse(self.view_name, args=[product.id]),
            data=form_data,
        )
        assert CSCorrectionRequest.objects.filter(
            invoice__order_id=product.order_id
        ).count() == (1 if init_data['expect_correction_request'] else 0)

    @mock.patch(
        'orders.models.Order.is_klarna_payment',
        return_value=True,
    )
    def test_complaint_created_but_correction_request_not_created_when_klarna_payment(
        self,
        _,
        staff_admin_client,
        invoice,
        base_form_data,
        product_factory,
    ):
        invoice.save()
        refund_form_data = self.get_form_data(base_form_data, refund=True)

        product = product_factory(order=invoice.order)
        assert (
            CSCorrectionRequest.objects.filter(
                invoice__order_id=product.order_id
            ).count()
            == 0
        )
        staff_admin_client.post(
            reverse(self.view_name, args=[product.id]),
            data=refund_form_data,
        )
        assert Complaint.objects.filter(product=product).exists()
        assert (
            CSCorrectionRequest.objects.filter(
                invoice__order_id=product.order_id
            ).count()
            == 0
        )


class TestUpdateComplaintView(TestComplaintView):
    view_class = UpdateComplaintView
    view_name = 'cs_update_complaint'

    @pytest.fixture(autouse=True)
    def mock_external_update_dependencies(self, mocker, logistic_order_dto):
        mock_complaintpushedtoproductionevent = mocker.patch(
            'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
        )

        mock_complaintpushedtoproductionevent.return_value.logistic_order = (
            logistic_order_dto
        )
        self.as_team_intervention_event_mock = mocker.patch(
            'complaints.services.post_save_complaints_service.ComplaintAssemblyTeamInterventionRequiredEvent'
        )
        self.change_to_reproduction_event_mock = mocker.patch(
            'complaints.services.post_save_complaints_service.ComplaintChangeToReproductionServiceEvent'
        )
        self.update_shipping_price_event_mock = mocker.patch(
            'complaints.services.post_save_complaints_service.ComplaintUpdateShippingPriceServiceEvent'
        )

    @staticmethod
    def create_batched_reproduction_complaint(complaint):
        complaint.process_complaint_to_production()
        reproduction_product = complaint.reproduction_product
        manufactor = reproduction_product.manufactor
        reproduction_product.batch = ProductBatchFactory(manufactor=manufactor)
        reproduction_product.save()
        return complaint

    @staticmethod
    def assert_form_errors(expected_errors, response):
        form_errors = set(
            [
                error.message
                for error in response.context_data['form'].errors['__all__'].data
            ]
        )
        assert form_errors == expected_errors

    def test_cant_edit_refund_related_fields(
        self,
        admin_client,
        base_form_data,
        complaint_with_refund,
        invoice,
    ):
        new_refund_form_data = self.get_form_data(base_form_data, refund=True)
        new_refund_amount = Decimal('6.23')

        new_refund_form_data['refund_amount'] = new_refund_amount
        new_refund_reason = (
            InvoiceItemDiscountTag.POOR_ASSEMBLY_SERVICE_EXPERIENCE.value
        )
        new_refund_form_data['refund_reason'] = new_refund_reason
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_refund.id]),
            data=new_refund_form_data,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        complaint_with_refund.refresh_from_db()
        assert complaint_with_refund.refund_reason != new_refund_reason
        assert complaint_with_refund.complaint_costs.refund_amount != new_refund_amount

    def test_cant_edit_elements_of_already_batched_product(
        self,
        admin_client,
        base_form_data,
        complaint_with_reproduction,
    ):
        complaint = self.create_batched_reproduction_complaint(
            complaint_with_reproduction
        )
        new_elements = ['b5 - BOX:8 - 2']
        reproduction_form_data = self.get_form_data(base_form_data, reproduction=True)
        reproduction_form_data['elements_regular'] = new_elements
        response = admin_client.post(
            reverse(self.view_name, args=[complaint.id]),
            data=reproduction_form_data,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        expected_errors = {'Product already batched, edition is blocked'}
        self.assert_form_errors(expected_errors, response)
        complaint.refresh_from_db()
        assert complaint.elements['elements'] != new_elements

    def test_edit_reproduction_elements(
        self,
        admin_client,
        base_form_data,
        complaint_with_reproduction,
    ):

        reproduction_form_data = self.get_form_data(base_form_data, reproduction=True)
        reproduction_form_data['elements_regular'] = ['b3 - BOX:3 - 1']
        complaint_with_reproduction.process_complaint_to_production()
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_reproduction.id]),
            data=reproduction_form_data,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        complaint_with_reproduction.refresh_from_db()
        assert complaint_with_reproduction.reproduction is True
        self.update_shipping_price_event_mock.assert_called_once()
        assert complaint_with_reproduction.elements['elements'] == ['b3 - BOX:3']

    def test_cant_edit_reproduction_to_non_reproduction(
        self,
        admin_client,
        base_form_data,
        complaint_with_reproduction,
        invoice,
    ):

        refund_form_data = self.get_form_data(
            base_form_data, reproduction=False, refund=True
        )
        complaint_with_reproduction.assembly_team_intervention = False
        complaint_with_reproduction.save()
        complaint_with_reproduction.process_complaint_to_production()
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_reproduction.id]),
            data=refund_form_data,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        error_msg = 'Reproduction complaint - please select elements'
        assert (
            response.context_data['form'].errors['__all__'].data[0].message == error_msg
        )
        complaint_with_reproduction.refresh_from_db()
        assert complaint_with_reproduction.reproduction is True
        assert complaint_with_reproduction.refund is False

    def test_added_refund_to_reproduction(
        self,
        admin_client,
        base_form_data,
        complaint_with_reproduction,
        invoice,
    ):
        refund_with_reproduction = self.get_form_data(
            base_form_data, reproduction=True, refund=True
        )
        complaint_with_reproduction.process_complaint_to_production()
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_reproduction.id]),
            data=refund_with_reproduction,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        complaint_with_reproduction.refresh_from_db()
        assert complaint_with_reproduction.reproduction is True
        assert complaint_with_reproduction.refund is True

    def test_added_refund_to_already_batched_reproduction(
        self,
        admin_client,
        base_form_data,
        invoice,
        complaint_with_reproduction,
    ):

        complaint = self.create_batched_reproduction_complaint(
            complaint_with_reproduction
        )
        refund_with_reproduction = self.get_form_data(
            base_form_data, reproduction=True, refund=True
        )
        response = admin_client.post(
            reverse(self.view_name, args=[complaint.id]),
            data=refund_with_reproduction,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        error_msg = 'Product already batched, edition is blocked'
        assert (
            response.context_data['form'].errors['__all__'].data[0].message == error_msg
        )

    def test_added_reproduction_to_refund(
        self,
        admin_client,
        base_form_data,
        complaint_with_refund,
        invoice,
    ):
        refund_with_reproduction = self.get_form_data(
            base_form_data, reproduction=True, refund=True
        )
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_refund.id]),
            data=refund_with_reproduction,
            follow=True,
        )
        assert response.status_code == status.HTTP_200_OK
        complaint_with_refund.refresh_from_db()
        assert complaint_with_refund.reproduction is True
        assert complaint_with_refund.refund is True
        self.as_team_intervention_event_mock.assert_called_once()


class TestDeleteComplaintView(TestComplaintView):
    view_class = DeleteComplaintView
    view_name = 'cs_delete_complaint'

    @pytest.fixture(autouse=True)
    def mock_external_delete_dependencies(self, mocker, logistic_order_dto):
        mock_complaintpushedtoproductionevent = mocker.patch(
            'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
        )

        mock_complaintpushedtoproductionevent.return_value.logistic_order = (
            logistic_order_dto
        )
        self.service_delete_event = mocker.patch(
            'complaints.forms.ComplaintServiceDeleteEvent'
        )

    @pytest.fixture
    def mock_request(self, user):
        request = HttpRequest()
        request.user = user
        return request

    def test_delete_complaint_and_all_related_elements(
        self,
        admin_client,
        complaint_with_reproduction,
    ):
        complaint_with_reproduction.process_complaint_to_production()
        response = admin_client.post(
            reverse(self.view_name, args=[complaint_with_reproduction.id]), follow=True
        )
        assert response.status_code == status.HTTP_200_OK
        assert Complaint.objects.filter(id=complaint_with_reproduction.id).count() == 0
        assert (
            Order.objects.filter(
                id=complaint_with_reproduction.reproduction_order_id
            ).count()
            == 0
        )
        assert (
            Product.objects.filter(
                id=complaint_with_reproduction.reproduction_product_id
            ).count()
            == 0
        )

    def test_cant_delete_when_correction_request_already_processed(
        self,
        admin_client,
        invoice,
        complaint_with_refund,
        base_form_data,
        mock_request,
    ):
        self.create_related_invoice_item(invoice, complaint_with_refund)
        refund_form_data = self.get_form_data(base_form_data, refund=True)

        self.create_related_correction_request(
            mock_request, complaint_with_refund, refund_form_data, invoice
        )

        CSCorrectionRequest.objects.update(
            status=CSCorrectionRequestStatus.STATUS_ACCEPTED
        )
        admin_client.post(
            reverse(self.view_name, args=[complaint_with_refund.id]), follow=True
        )
        assert Complaint.objects.filter(id=complaint_with_refund.id).exists() is True

    @staticmethod
    def create_related_invoice_item(invoice, complaint):
        invoice_item = InvoiceItemFactory.create(
            invoice=invoice,
            net_value=200,
            gross_price=99,
            item_type=InvoiceItemType.ITEM,
        )
        invoice_item.order_item = complaint.product.order_item
        invoice_item.save()

    @staticmethod
    def create_related_correction_request(
        mock_request, complaint, refund_data, invoice
    ):
        elements: dict
        refund_amount: Decimal
        refund: bool
        refund_reason: str
        post_save_actions = PostCreateComplaintService(
            mock_request,
            complaint,
            '',
            elements={},
            refund_amount=refund_data['refund_amount'],
            refund=refund_data['refund'],
            refund_reason=refund_data['refund_reason'],
        )
        post_save_actions.create_correction_request(invoice)
