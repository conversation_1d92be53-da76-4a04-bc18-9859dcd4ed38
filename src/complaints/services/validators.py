from invoice.choices import InvoiceStatus


class ReproductionValidator:
    def __init__(self):
        self.errors = []

    def validate_feature_elements(self, elements_feature):
        legs = [element for element in elements_feature if element.startswith('l')]
        if len(legs) % 2 != 0:
            self.errors.append('Please choose even number of legs')

    def validate_elements_or_fittings_chosen_when_typical_issue(
        self, has_elements, has_fittings, typical_issue, assembly_team_intervention
    ):
        if (
            not (has_elements or has_fittings or assembly_team_intervention)
            and typical_issue
        ):
            self.errors.append('Please choose elements or fittings')

    def validate_edited_reproduction(self, has_elements, is_batched):
        if is_batched:
            self.errors.append('Product already batched, edition is blocked')
        if not has_elements:
            self.errors.append('Reproduction complaint - please select elements')


class RefundValidator:
    def __init__(self):
        self.errors = []

    def validate_refund_checked_when_value(self, refund_amount, refund_reason, refund):
        if (not refund_reason.startswith('-') or (refund_amount > 0)) and not refund:
            self.errors.append('Remember to check refund checkbox')

    def validate_invoice(self, invoice):
        if invoice.status == InvoiceStatus.PROFORMA:
            self.errors.append('Cant create refund with proforma')

        if invoice.pending_correction_requests.exists():
            self.errors.append('Cant create refund with pending correction requests')

    def validate_refund_reason(self, refund_reason):
        if refund_reason.startswith('-'):
            self.errors.append('Please choose refund reason')

    def validate_refund_amount(self, refund_amount, invoice):
        if refund_amount <= 0:
            self.errors.append('Please choose refund amount')
            return
        invoice_sum_price = invoice.sum_invoice_items_gross_price()
        if refund_amount > invoice_sum_price:
            self.errors.append(f'Max possible refund: {invoice_sum_price}')


class TypicalIssuesValidator:
    def __init__(self):
        self.errors = []

    def validate_typical_issues_is_chosen(self, typical_issues, refund):
        if not refund and not typical_issues:
            self.errors.append('Remember to select typical issues')

    def validate_typical_issues_and_elements(
        self,
        typical_issues,
        has_elements,
        has_fittings,
        refund,
        assembly_team_intervention,
    ):
        if (
            typical_issues
            and refund
            and not (has_elements or has_fittings or assembly_team_intervention)
        ):
            self.errors.append(
                "Can't select typical issues without reproduction "
                'or assembly team intervention'
                'For refund, select refund reason'
            )
        if (
            has_elements or has_fittings or assembly_team_intervention
        ) and not typical_issues:
            self.errors.append('Please select typical issues')
