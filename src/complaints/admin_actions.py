from datetime import datetime
from decimal import Decimal
from io import BytesIO
from zipfile import ZipFile

from django import forms
from django.contrib import (
    admin,
    messages,
)
from django.contrib.admin import ModelAdmin
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.http import (
    FileResponse,
    HttpRequest,
    HttpResponseRedirect,
)
from django.shortcuts import render

from celery import shared_task

from complaints.admin_mixins import ComplaintCostDisplayAdminMixin
from complaints.enums import (
    ComplaintPriorityChoices,
    ComplaintStatus,
)
from complaints.exceptions import (
    ComplaintNotReproductionException,
    NotFoundFrontViewException,
    NotFoundInstructionAndFrontViewException,
    NotFoundInstructionException,
    NotFoundPackagingLabelsException,
)
from complaints.models import (
    AllComplaintCosts,
    Complaint,
)
from complaints.reports.free_return_prevention import FreeR<PERSON>urn<PERSON>reventionReport
from complaints.services.export_production_files import (
    add_front_view_packaging_instruction_and_labels_to_zipfile,
)
from complaints.services.post_save_complaints_service import PostCreateComplaintService
from complaints.utils import generate_production_order_as_html
from custom.context_managers import DocumentRequestWithStatus
from custom.export_file_strategies import csv_export_strategy
from custom.utils.decorators import (
    CSVObject,
    csv_export,
    with_document_async_message,
)
from producers.choices import ProductStatus


@admin.action(description='Generate report')
@csv_export
def generate_complaint_report(modeladmin, request, queryset):
    header_row = [
        'ID',
        'Status',
        'Manufactor name',
        'Order ID',
        'Product ID',
        'Elements description',
        'Reported date',
        'Production ordered date',
        'Production released date',
    ]
    rows = []
    for page in Paginator(queryset, 100):
        for complaint in page.object_list:
            manufactor = getattr(
                getattr(
                    getattr(
                        complaint,
                        'product',
                        {},
                    ),
                    'manufactor',
                    {},
                ),
                'name',
                '',
            )
            rows.append(
                [
                    complaint.id,
                    complaint.get_status_display(),
                    manufactor,
                    complaint.get_order_id(),
                    complaint.product_id,
                    complaint.get_elements_description(),
                    complaint.reported_date,
                    complaint.production_ordered_date,
                    complaint.production_released_date,
                ]
            )
    return CSVObject(
        file_name='complaints_report',
        header_row=header_row,
        rows=rows,
    )


def prepare_complaints_costs_report(complaint_ids: list[int]):
    queryset = (
        AllComplaintCosts.objects.get_queryset(
            statuses=(ProductStatus.SENT_TO_CUSTOMER,)
        )
        .filter(pk__in=complaint_ids)
        .select_related('product', 'reporter')
        .order_by('-id')
    )

    header_row = (
        'Product ID',
        'Order',
        'Reproduction Product ID',
        'Manufactor',
        'Shelf type',
        'ProdVersion',
        'Get order id',
        'Complaint type',
        'Typical issues',
        'Refund reason',
        'Free Return reason tag',
        'Area',
        'Responsibility',
        'Element list',
        'Reproduction element categories',
        'Reproduction elements count',
        'To be shipped date',
        'Delivered date',
        'Reproduction Delivered date',
        'Complaint created date',
        'Carrier',
        'Complaint carrier',
        'Complaint COGS',
        'Complaint LOGS',
        'AS team intervention value',
        'Discount value',
        'COGS shelf costs',
        'LOGS shipping costs',
        'LOGS free return shipping costs',
        'Packaging kit COGS',
        'Total costs',
        'DT prediction',
        'TNT prediction',
        'UPS prediction',
        'DPD prediction',
        'Reporter',
        'Additional info',
        'Conversation link',
        'Order status changed to "Sent to customer"',
        'Original product sent date'
    )
    cost_displayer = ComplaintCostDisplayAdminMixin()
    cost_displayer.load_current_rates()
    rows = []

    for page in Paginator(queryset, 100):
        for complaint in page.object_list:
            product_status_history = (
                complaint.product.prefetched_status_history[0]
                if complaint.product.prefetched_status_history
                else None
            )

            row = [
                complaint.product.id,
                complaint.product.order.id,
                cost_displayer.get_reproduction_product(complaint),
                complaint.product.manufactor or '-',
                complaint.product.cached_shelf_type,
                cost_displayer.admin_get_physical_product_version(complaint),
                complaint.get_order_id(),
                complaint.complaint_type,
                complaint.typical_issues,
                complaint.refund_reason,
                cost_displayer.get_free_refund_reason_tag(complaint),
                complaint.area,
                complaint.responsibility,
                complaint.get_elements_description(),
                ', '.join(complaint.reproduction_element_categories),
                cost_displayer.get_reproduction_elements_count(complaint),
                cost_displayer.get_product_to_be_shipped_date(complaint),
                cost_displayer.get_product_delivered_date(complaint),
                cost_displayer.get_reproduction_product_delivered_date(complaint),
                complaint.created_at.strftime('%Y-%m-%d %H%M%S'),
                cost_displayer.get_carrier(complaint),
                cost_displayer.get_complaint_carrier(complaint),
                complaint.complaint_costs.complaint_cost,
                cost_displayer.reproduction_logistic_cost(complaint),
                cost_displayer.get_as_team_intervention_value(complaint),
                cost_displayer.get_refund_in_euro(complaint),
                cost_displayer.get_cogs_product_cost(complaint),
                cost_displayer.get_logistic_order_cost(complaint),
                cost_displayer.get_logs_free_return_shipping_costs(complaint),
                cost_displayer.get_packaging_kit_cogs(complaint),
                cost_displayer.total_costs(complaint),
                cost_displayer.get_dedicated_transport_prediction(complaint),
                cost_displayer.get_tnt_prediction(complaint),
                cost_displayer.get_ups_prediction(complaint),
                cost_displayer.get_dpd_prediction(complaint),
                complaint.reporter.get_full_name() or complaint.reporter.get_username(),
                complaint.additional_info,
                complaint.conversation_link,
                (
                    product_status_history.created_at.strftime('%Y-%m-%d %H%M%S')
                    if product_status_history
                    else ''
                ),
                complaint.product.get_sent_to_customer_date(),
            ]
            rows.append(row)

    return {
        'header': header_row,
        'content': rows,
    }


@shared_task
def prepare_complaints_costs_report_task(
    document_request_id: int, complaint_ids: list[int]
):
    with DocumentRequestWithStatus(
        document_request_id,
        f'complaints_report_{datetime.now():%Y-%m-%d}.csv',
    ) as document_with_status:
        document_with_status.export(
            prepare_complaints_costs_report,
            args=(complaint_ids,),
            export_strategy=csv_export_strategy,
        )


@admin.action(description='Generate complaints costs report')
@with_document_async_message
def generate_complaints_costs_report(modeladmin, document_request, request, queryset):
    prepare_complaints_costs_report_task.delay(
        document_request.pk,
        complaint_ids=list(queryset.values_list('id', flat=True)),
    )


@admin.action(description='Export front views for selected complaints as zip')
def export_frontviews_as_zip(self, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for page in Paginator(queryset, 100):
            for complaint in page.object_list:
                priority = (
                    'priority'
                    if complaint.priority == ComplaintPriorityChoices.PRIORITY
                    else 'normal'
                )
                name = f'COMPLAINT_ID_{complaint.product_id}_{priority}.pdf'

                try:
                    front_view = (
                        complaint.reproduction_product.details.front_view.read()
                    )
                except ValueError:
                    self.message_user(
                        request,
                        f'Complaint(id={complaint.id}) cannot be exported because '
                        f'front view does not exists',
                        level=messages.WARNING,
                    )
                    continue

                zipfile.writestr(name, data=front_view)

    stream.seek(0)
    response = FileResponse(stream, as_attachment=True, filename='production_files.zip')
    return response


@admin.action(
    description=(
        'Export front views, packaging instructions and labels '
        'for selected complaints as zip'
    )
)
def export_frontview_and_packaging_instruction_as_zip(self, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for page in Paginator(queryset, 100):
            for complaint in page.object_list:
                priority = (
                    'priority'
                    if complaint.priority == ComplaintPriorityChoices.PRIORITY
                    else 'normal'
                )
                try:
                    add_front_view_packaging_instruction_and_labels_to_zipfile(
                        product=complaint.product,
                        zip_file=zipfile,
                        complaint=complaint,
                        priority=priority,
                        view='complaints',
                    )
                except (
                    NotFoundInstructionException,
                    NotFoundFrontViewException,
                    NotFoundInstructionAndFrontViewException,
                    NotFoundPackagingLabelsException,
                ) as exc:
                    messages.add_message(
                        request, level=messages.WARNING, message=str(exc)
                    )
    stream.seek(0)
    return FileResponse(stream, as_attachment=True, filename='production_files.zip')


@admin.action(description='Process complaints to production')
def process_complaints_to_production(self, request, queryset):
    processed_count = 0
    for complaint in queryset:
        try:
            complaint.process_complaint_to_production()
            processed_count += 1
        except ComplaintNotReproductionException:
            self.message_user(
                request,
                f'Complaint(id={complaint.id}) cannot be processed to production '
                f'because it is not reproduction type',
                level=messages.WARNING,
            )

    if processed_count:
        self.message_user(
            request, f'Processed {processed_count} complaints in production'
        )


@admin.action(description='Confirm complaints created by assembly team')
def confirm_complaints_created_by_assembly_team(self, request, queryset):
    if queryset.filter(is_confirmed=True).exists():
        self.message_user(
            request,
            'Some of the complaints are already confirmed, please select only '
            'complaints that are not confirmed',
            level=messages.ERROR,
        )
        return
    if queryset.filter(created_by_assembly_team=False).exists():
        self.message_user(
            request,
            'Some of the complaints are not created by assembly team, please select '
            'only complaints created by assembly team',
            level=messages.ERROR,
        )
        return
    message = 'Complaints created by assembly team are confirmed'
    for complaint in queryset:
        post_create_actions = PostCreateComplaintService(
            request,
            complaint,
            message,
            elements=complaint.elements,
            refund=False,
            refund_amount=Decimal(0),
            refund_reason='',
        )
        post_create_actions.run()
    queryset.update(is_confirmed=True)


@admin.action(description='Delete complaints created by assembly team')
def delete_complaints_created_by_assembly_team(self, request, queryset):
    if queryset.filter(is_confirmed=True).exists():
        self.message_user(
            request,
            'Some of the complaints are already confirmed, please select only '
            'complaints that are not confirmed',
            level=messages.ERROR,
        )
        return
    if queryset.filter(created_by_assembly_team=False).exists():
        self.message_user(
            request,
            'Some of the complaints are not created by assembly team, please select '
            'only complaints created by assembly team',
            level=messages.ERROR,
        )
        return
    for obj in queryset:
        obj.delete()
    self.message_user(request, 'Successfully deleted selected complaints.')


@admin.action(description='Mass change status')
def change_status_to_specific_one(self, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = SelectOneStatusForm(request.POST)
        if form.is_valid():
            for complaint in queryset:
                complaint.change_status(
                    status_new=form.cleaned_data['what_status'],
                    status_previous=complaint.status,
                )
                complaint.save()
            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = SelectOneStatusForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/producers/change_delivery_date.html',
        {
            'stacks': queryset,
            'form': form,
            'opts': opts,
            'app_label': app_label,
            'action_name': 'change_status_to_specific_one',
        },
    )


class SelectOneDateForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    what_date = forms.ChoiceField(
        choices=(
            ('production_ordered_date', 'production_ordered_date'),
            ('production_released_date', 'production_released_date'),
            ('shipment_date', 'shipment_date'),
            ('delivered_date', 'delivered_date'),
        )
    )
    one_date = forms.DateField(widget=forms.TextInput(attrs={'type': 'date'}))


class SelectOneStatusForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    what_status = forms.ChoiceField(choices=ComplaintStatus.choices())


@admin.action(description='Create production order from selected')
def create_production_order(self, request, queryset):
    return generate_production_order_as_html(queryset)


@admin.action(description='Mass change any date')
def change_date_to_specific_date(self, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = SelectOneDateForm(request.POST)
        if form.is_valid():
            for item in queryset:
                setattr(
                    item, form.cleaned_data['what_date'], form.cleaned_data['one_date']
                )
                item.save()
            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = SelectOneDateForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/producers/change_delivery_date.html',
        {
            'stacks': queryset,
            'form': form,
            'opts': opts,
            'app_label': app_label,
            'action_name': 'change_date_to_specific_date',
        },
    )


@admin.action(description='Generate free return prevention report for previous month')
@csv_export
def generate_free_return_prevention_report(
    self: ModelAdmin, request: HttpRequest, queryset: QuerySet[Complaint]
) -> CSVObject:
    report = FreeReturnPreventionReport(queryset)
    return report.to_csv_object()
