# Generated by Django 3.2.12 on 2022-07-26 16:55

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0035_complaint_deleted_by_cascade'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplaintCosts',
            fields=[],
            options={
                'verbose_name_plural': 'Complaint costs',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('complaints.complaint',),
        ),
        migrations.AddField(
            model_name='complaint',
            name='product_cost',
            field=models.DecimalField(
                decimal_places=2,
                default=0.0,
                max_digits=7,
                verbose_name='COGS Product (€)',
            ),
        ),
    ]
