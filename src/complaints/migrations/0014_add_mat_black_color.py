# Generated by Django 3.1.8 on 2021-05-27 11:54

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0013_add_type03_shelf_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='replaceableshelfverticalparts',
            name='shelf_color',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'T01_WHITE'),
                    (1, 'T01_BLACK'),
                    (3, 'T01_GREY'),
                    (4, 'T01_AUBERGINE'),
                    (5, 'T01_NATURAL'),
                    (6, 'T01_RED'),
                    (7, 'T01_YELLOW'),
                    (8, 'T01_PINK'),
                    (1000, 'T02_WHITE'),
                    (1001, 'T02_TERRACOTTA'),
                    (1002, 'T02_MIDNIGHT_BLUE'),
                    (1003, 'T02_SAND'),
                    (1004, 'T02_MINT'),
                    (1006, 'T02_MAT_BLACK'),
                    (2000, 'T01_ASH'),
                    (2001, 'T01_OAK'),
                ]
            ),
        ),
    ]
