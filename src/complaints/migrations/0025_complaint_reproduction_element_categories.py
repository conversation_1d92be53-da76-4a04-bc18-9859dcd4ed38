# Generated by Django 3.2.9 on 2021-11-22 11:30

import django.contrib.postgres.fields
from django.db import migrations, models

from complaints.reproduction_days_elements import get_elements_categories


def insert_element_categories(apps, schema_editor):
    complaint_model = apps.get_model('complaints', 'Complaint')
    complaints_to_update = []
    for complaint in complaint_model.objects.all():
        elements_dict = getattr(complaint, 'elements', {})
        categories = get_elements_categories(elements_dict)
        complaint.reproduction_element_categories = categories
        complaints_to_update.append(complaint)

    complaint_model.objects.bulk_update(
        complaints_to_update,
        ['reproduction_element_categories'],
        batch_size=1000,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0024_complaint_priority'),
    ]

    operations = [
        migrations.AddField(
            model_name='complaint',
            name='reproduction_element_categories',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=20), default=list, size=None),
        ),
        migrations.RunPython(
            insert_element_categories,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
