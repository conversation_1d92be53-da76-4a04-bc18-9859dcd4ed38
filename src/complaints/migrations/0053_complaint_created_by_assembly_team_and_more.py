# Generated by Django 4.1.9 on 2024-05-22 00:11

import django.core.files.storage
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0052_complaint_closed_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='complaint',
            name='created_by_assembly_team',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='complaint',
            name='is_confirmed',
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name='AssemblyTeamComplaintPhoto',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('file_name', models.CharField(blank=True, max_length=255)),
                (
                    'photo',
                    models.FileField(
                        blank=True,
                        max_length=400,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to=producers.utils.RandomizedUploadTo(
                            'complaints/photo/%Y/%m'
                        ),
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'complaint',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='assembly_team_photos',
                        to='complaints.complaint',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Assembly Team Complaint Photo',
            },
        ),
    ]
