#!/usr/bin/env python
"""
Test script to verify the WishlistView union implementation
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cstm.settings')
django.setup()

from gallery.views import WishlistView
from django.contrib.auth.models import User
from gallery.enums import FurnitureStatusEnum

def test_wishlist_union():
    """Test the union-based wishlist implementation"""
    print("Testing WishlistView union implementation...")

    # Get a user with wishlist items
    user = User.objects.filter(jetty__furniture_status=FurnitureStatusEnum.SAVED).first()

    if not user:
        print("No user with jetty wishlist items found, trying watty...")
        user = User.objects.filter(watty__furniture_status=FurnitureStatusEnum.SAVED).first()

    if not user:
        print("No user with watty wishlist items found, trying sotty...")
        user = User.objects.filter(sotty__furniture_status=FurnitureStatusEnum.SAVED).first()

    if not user:
        print("No user with wishlist items found")
        return

    print(f"Found user: {user.id}")

    # Test the union method
    view = WishlistView()
    try:
        items = view._get_combined_wishlist_items(user)
        print(f"Successfully retrieved {len(items)} wishlist items")

        if items:
            print(f"First item type: {type(items[0])}")
            print(f"First item id: {items[0].id}")
            print(f"First item furniture_type: {items[0].furniture_type}")

            # Test serialization
            from gallery.serializers.furniture.wishlist import FurnitureWishlistSerializer
            from regions.cached_region import get_region_data_from_request
            from gallery.services.prices_for_serializers import get_currency_rate
            from promotions.utils import strikethrough_promo

            # Create a mock context
            region = user.profile.cached_region_data
            context = {
                'region': region,
                'currency_rate': get_currency_rate(region),
                'strikethrough_promotion': strikethrough_promo(region),
            }

            serializer = FurnitureWishlistSerializer(items[:1], context=context, many=True)
            data = serializer.data
            print(f"Successfully serialized first item: {data[0]['id']}")
            print("Test passed!")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wishlist_union()
