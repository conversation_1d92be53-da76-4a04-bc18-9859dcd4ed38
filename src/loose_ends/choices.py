from django.db import models


class LooseEndCategory(models.TextChoices):
    FREE_RETURN = (
        'free_return',
        'Free Return',
    )
    FREE_RETURN_KIT = (
        'free_return_kit',
        'Free Return Packaging Kit',
    )
    KLARNA_CANCEL = (
        'klarna_cancel',
        'Klarna Cancellation',
    )
    KLARNA_FREE_RETURN = (
        'klarna_free_return',
        'Klarna Free Return',
    )
    ASS_SERVICE = (
        'ass_service',
        'Additional service - Assembly - service fee',
    )
    ASS_CUSTOMER = (
        'ass_customer',
        'Additional service - Assembly - customer fee',
    )
    CANCEL_PROD = (
        'cancel_prod',
        'Cancellation - before production',
    )
    CANCEL_SEND = (
        'cancel_send',
        'Cancellation - before sendout',
    )
    CANCEL_DELIV = (
        'cancel_deliv',
        'Cancellation - after delivery',
    )
    PROJECT_CHANGE = (
        'project_change',
        'Project changes',
    )
    INVOICE_CHANGE = (
        'invoice_change',
        'Invoice changes',
    )
    VOUCHER_CHANGE = (
        'voucher_change',
        'Voucher changes',
    )
    COMPLAINS_TRANS = (
        'complains_trans',
        'Complains - transportation',
    )
    COMPLAINS_PROD_ERR = (
        'complains_prod_err',
        'Complains - production errors',
    )
    COMPLAINS_PROD_DELAY = (
        'complains_prod_delay',
        'Complains - production delays',
    )
    INSURANCE_DISCOUNT = (
        'insurance_discount',
        'TNT Complaint case result - discount for service',
    )
    INSURANCE = (
        'insurance',
        'TNT Insurance',
    )
    RETURN_PREVENTION = (
        'return_prevention',
        'Free return prevention',
    )
    COMPLAINT_PREVENTION = (
        'complaint_prevention',
        'Complain action prevention',
    )
    REPRODUCTION = (
        'reproduction',
        'Elements reproduction',
    )
    OTHER = (
        'other',
        'Other - check description',
    )


class Department(models.TextChoices):
    PRODUCTION = 'PRODUCTION', 'Production'
    LOGISTICS = 'LOGISTICS', 'Logistics'
    ACCOUNTING = 'ACCOUNTING', 'Accounting'
    CUSTOMER_SERVICE = 'CUSTOMER_SERVICE', 'Customer Service'
    OTHER = 'OTHER', 'Other'
