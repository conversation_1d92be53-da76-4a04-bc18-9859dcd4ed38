import { doorDirection } from "../../constants.js";

const findHingeAxes = (selectedColumn, door, hingeSettings) => {
  const hingeHeight = hingeSettings.height;
  const halfHingeHeight = Math.ceil(hingeHeight / 2);
  // const maxHingeYOffset = 200;  // sorry, no way to enforce this
  const minClearanceUnderASlab = 50 - halfHingeHeight;  // to the hinge axis
  const minClearanceOverASlab = 100 - halfHingeHeight;  // to the hinge axis

  // find hinge y axes
  let hingeYAxes = [];
  // number of hinges depends on door size: equals ((y*x*2.25)+1) rounded up
  let door_height = door.y2 - door.y1;
  let numberOfHinges = Math.ceil(
    door_height / 1000.0 * (door.x2 - door.x1) / 1000.0 * 2.25 + 1
  );
  // an extra hinge for doors taller than 700
  if (door_height > 700) numberOfHinges += 1;
  // number of hinges should be between 2 and 6
  numberOfHinges = Math.min(6, Math.max(2, numberOfHinges));


  // find invalid ranges
  let invalidRanges = [
    {from: door.y1, to: door.y1 + hingeSettings.yOffsetBottom},
    {from: door.y2 - hingeSettings.yOffsetTop, to: door.y2},
  ];

  let slabsBehindDoors = selectedColumn.slabs.filter(s => (
    !s.removed &&
    s.y1 < door.y2 &&
    s.y2 > door.y1 &&
    !hingeSettings.excludeSlabSubtypes.includes(s.subtype)
  ));
  slabsBehindDoors.forEach(slab => {
    invalidRanges.push({
      from: slab.y1 - minClearanceUnderASlab,
      to: slab.y2 + minClearanceOverASlab,
    });
  });

  let drawersBehindDoors = selectedColumn.drawers.filter(d => (
    !d.removed &&
    d.y1 < door.y2 &&
    d.y2 > door.y1
  ));
  drawersBehindDoors.forEach(drawer => {
    invalidRanges.push({
      from: drawer.y1,
      to: drawer.y2,
    });
  });

  invalidRanges.sort((a, b) => (a.from - b.from));
  invalidRanges = invalidRanges.reduce((ranges, range) => {
    // if last range is overlapping with current range, merge them
    if (ranges.length && ranges[ranges.length - 1].to > range.from) {
      ranges[ranges.length - 1].to = range.to;
    } else {
      ranges.push(range);
    }
    return ranges;
  }, []);


  // calculate hinge y axes
  let bottomHingeYOffset = hingeSettings.yOffsetBottom;
  // if an invalid range extends over the minimal offset (because of a drawer), increase it
  // this will result in a more even distribution of hinges over the available height
  let bottomInvalidRange = invalidRanges.find(range => (
    range.from <= door.y1 + bottomHingeYOffset && range.to >= door.y1 + bottomHingeYOffset
  ));
  if (bottomInvalidRange) bottomHingeYOffset = bottomInvalidRange.to - door.y1;

  let hingeYOffset = (door_height - hingeSettings.yOffsetTop - bottomHingeYOffset) / (numberOfHinges - 1);
  for (let i = 0; i < numberOfHinges; i++) {
    let possibleHingeAxis = door.y1 + bottomHingeYOffset + i * hingeYOffset;
    let invalidRange = invalidRanges.find(range => (
      possibleHingeAxis >= range.from && possibleHingeAxis <= range.to
    ));
    if (!invalidRange) {
      hingeYAxes.push(possibleHingeAxis);
      continue;
    }

    // check if the closest valid position is up or down
    let downIsCloserThanUp = (
      Math.abs(possibleHingeAxis - invalidRange.from) <
      Math.abs(possibleHingeAxis - invalidRange.to)
    )

    // force direction for first and last hinge
    if (i === 0) downIsCloserThanUp = false;
    if (i === numberOfHinges - 1) downIsCloserThanUp = true;

    if (downIsCloserThanUp) {  // move hinge down
      let previousInvalidRange = invalidRanges.toReversed().find(range => (
        range.to < invalidRange.from
      ));
      if (previousInvalidRange && invalidRange.from - previousInvalidRange.to <= hingeHeight) {
        // in case of drawers, position the hinge in the middle if the valid space
        possibleHingeAxis = (invalidRange.from + previousInvalidRange.to) / 2;
      } else {
        // otherwise, move hinge axis to the top of the invalid range
        possibleHingeAxis = invalidRange.from - halfHingeHeight;
      }
    } else {  // move hinge up
      let nextInvalidRange = invalidRanges.find(range => (
        range.from > invalidRange.to
      ));
      if (nextInvalidRange && nextInvalidRange.from - invalidRange.to <= hingeHeight) {
        // in case of drawers, position the hinge in the middle if the valid space
        possibleHingeAxis = (invalidRange.to + nextInvalidRange.from) / 2;
      } else {
        // otherwise, move hinge axis to the bottom of the invalid range
        possibleHingeAxis = invalidRange.to + halfHingeHeight;
      }
    }
    hingeYAxes.push(possibleHingeAxis);
  }
  return hingeYAxes;
}
export const distributeHinges = (selectedColumn, hingeSettings) => {
  const hingeHeight = hingeSettings.height;
  const hingeWidth = hingeSettings.width;
  const hingeDepth = hingeSettings.depth;

  let doors = selectedColumn.doors.filter(door => !door.removed);
  let hingeYAxes = [];
  doors.forEach(door => {
    hingeYAxes.push(...findHingeAxes(selectedColumn, door, hingeSettings));
    // provide default door direction if not set
    if (door.direction === undefined)
      door.direction = doorDirection.left;
  });

  // make actual hinges
  let hinges = []

  // double doors make each axis twice
  let uniqueHingeAxes = Array.from(new Set(hingeYAxes));
  uniqueHingeAxes.forEach(yAxis => {
    const hinge = {
      x1: selectedColumn.x1,
      x2: selectedColumn.x2,
      y1: yAxis - hingeHeight / 2,
      y2: yAxis + hingeHeight / 2,
      z1: selectedColumn.z2 - hingeSettings.zOffset - hingeDepth,
      z2: selectedColumn.z2 - hingeSettings.zOffset,
    };

    // if any of the doors have left direction, add a right hinge
    if (selectedColumn.doors.some(door => (
      !door.removed && door.direction === doorDirection.left
    ))) {
      hinges.push({
        ...hinge,
        x1: selectedColumn.x2 - hingeWidth,
        x2: selectedColumn.x2,
        direction: doorDirection.left,
      });
    }
    // if any of the doors have right direction, add a left hinge
    if (selectedColumn.doors.some(door => (
      !door.removed && door.direction === doorDirection.right
    ))) {
      hinges.push({
        ...hinge,
        x1: selectedColumn.x1,
        x2: selectedColumn.x1 + hingeWidth,
        direction: doorDirection.right,
      });
    }
  })
  watty.hinges.push(...hinges);
}
