import {
  drawBack,
  drawSlab,
  drawWall,
  drawDrawer,
  drawDoor,
  drawBar,
  drawLedStrip,
  drawHinge,
  drawFrame,
  drawGrommet,
  drawSlabFromAbove,
  drawBackFromAbove,
  drawWallFromAbove,
  drawDoorFromAbove,
  drawLeg,
  drawDrawerFromAbove,
  drawLegFromAbove,
} from "./watty_geometry.js";

export const renderWatty = (drawing, watty) => {
  let offsetX = watty.width / 2;
  let shelfType = watty['shelf_type']

  watty.legs.forEach(l => {
    drawLeg(l, drawing, offsetX);
  });
  watty.backs.forEach(s => {
    drawBack(s, drawing, offsetX);
  });
  watty.cable_management.forEach(g => {
    drawGrommet(g, drawing, offsetX);
  })
  watty.slabs.forEach(s => {
    drawSlab(s, drawing, offsetX);
  });
  watty.walls.forEach(w => {
    drawWall(w, drawing, offsetX);
  });
  watty.frame.forEach(f => {
    drawFrame(f, drawing, offsetX);
  });
  watty.drawers.forEach(d => {
    drawDrawer(d, drawing, offsetX);
  });
  watty.doors.forEach(d => {
    drawDoor(d, drawing, offsetX, shelfType);
  });
  watty.bars.forEach(b => {
    drawBar(b, drawing, offsetX);
  });
  watty.lighting.forEach(led => {
    drawLedStrip(led, drawing, offsetX);
  });
  watty.hinges.forEach(h => {
    drawHinge(h, drawing, offsetX);
  });
}

export const renderWattyFromAbove = (drawing, watty) => {
  let offsetX = watty.width / 2;
  let shelfType = watty['shelf_type']
  watty.legs.forEach(d => {
    drawLegFromAbove(d, drawing, offsetX);
  });
  watty.backs.forEach(s => {
    drawBackFromAbove(s, drawing, offsetX);
  });
  watty.slabs.forEach(s => {
    drawSlabFromAbove(s, drawing, offsetX, shelfType);
  });
  watty.walls.forEach(w => {
    drawWallFromAbove(w, drawing, offsetX);
  });
  watty.doors.forEach(d => {
    drawDoorFromAbove(d, drawing, offsetX);
  });
  watty.drawers.forEach(d => {
    drawDrawerFromAbove(d, drawing, offsetX);
  });
  // watty.bars.forEach(b => {
  //   drawBarFromAbove(b, drawing, offsetX);
  // });
}
