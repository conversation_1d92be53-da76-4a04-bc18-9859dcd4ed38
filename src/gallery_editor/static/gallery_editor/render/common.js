import { longLegHeight } from "../jetty/constants.js";
import { shelfTypeValues } from "../constants.js";

let padding = 10
const viewportWidth = 1000;
const viewportHeight = 800;
let textScale = 15;


const getTextScale = (furniture) => {
  const maxTextSize = 40;
  const baseTextSize = 15;
  let scale = Math.max(
    1,
    (furniture.width + padding) / viewportWidth,
    (furniture.height + padding) / viewportHeight,
  );
  return Math.min(maxTextSize, baseTextSize * scale);
}

export const initDrawing = (drawingID, furniture) => {
  textScale = getTextScale(furniture);
  if (furniture.shelf_type === shelfTypeValues.TYPE_25)
    padding += 80;  // legs to the sides
  return SVG().addTo('#' + drawingID)
    .size(viewportWidth, viewportHeight)
    .viewbox(
      0,
      -longLegHeight,
      furniture.width + padding,
      furniture.height + longLegHeight + padding,
    )
    .matrix(1, 0, 0, -1, 0, 0)
    .css({
      'display': 'block',
      'margin': 'auto',
      'font-size': `${textScale}px`,
      'font-family': 'sans-serif',
    });
}

export const initCornerDrawing = (drawingID, leftFurniture, rightFurniture) => {
  let maxDim = Math.max(
    leftFurniture.width + rightFurniture.depth,
    rightFurniture.width + leftFurniture.depth,
  );
  return SVG().addTo('#' + drawingID)
    .size(400, 400)
    .viewbox(
      0,
      0,
      maxDim + padding,
      maxDim + padding,
    )
    .matrix(1, 0, 0, -1, 0, 0)
    .css({
      'display': 'block',
      'margin': 'auto',
      'font-size': `120px`,
      'font-family': 'sans-serif',
    });
}

export const initDrawingTop = (drawingID, furniture) => {
  textScale = getTextScale(furniture);
  let sizeY = viewportHeight / (furniture.height + longLegHeight + padding) * (furniture.depth + padding);
  return SVG().addTo('#' + drawingID)
    .size(viewportWidth, sizeY)
    .viewbox(
      0, 0,
      furniture.width + padding,
      furniture.depth + padding,
    )
    .matrix(1, 0, 0, 1, 0, 0)
    .css({
      'display': 'block',
      'margin': 'auto',
      'margin-top': '20px',
      'font-size': `${textScale}px`,
      'font-family': 'sans-serif',
    });
}

export const updateDrawingSize = (drawing, furniture) => {
  drawing.viewbox(
    -padding / 2,
    -9,
    furniture.width + padding,
    furniture.height + longLegHeight + padding,
  );
}

export const updateTopDrawingSize = (drawing, furniture) => {
  drawing.viewbox(
    -padding / 2,
    0,
    furniture.width + padding,
    furniture.depth + padding,
  );
  let sizeY = viewportHeight / (furniture.height + longLegHeight + padding) * (furniture.depth + padding);
  drawing.size(
    viewportWidth,
    sizeY,
  )
}
