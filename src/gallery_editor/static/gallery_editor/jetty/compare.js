import { doorDirection, doorFlip, thickness } from "../constants.js";
import { geometryFields } from './constants.js';
import { findAddedAndChangedElements, findMovedElements, findRemovedElements } from "../common_compare_utils.js";


const isNeighbouringHorizontal = (element, horizontal, onlyAbove) => {
  let hasOverlapX = (horizontal.x1 < element.x2 && horizontal.x2 > element.x1)
  let isBelow = !onlyAbove && (
    horizontal.y2 === element.y1 ||
    horizontal.y2 === element.y1 - thickness / 2
  )
  let isAbove = (
    horizontal.y1 === element.y2 ||
    horizontal.y1 === element.y2 + thickness / 2
  )
  return hasOverlapX && (isBelow || isAbove)
}

const isNeighbouringVertical = (element, vertical, byDirection) => {
  let hasOverlapY = (vertical.y1 < element.y2 && vertical.y2 > element.y1)
  let isLeft = (
    vertical.x2 === element.x1 ||
    vertical.x2 === element.x1 - thickness / 2
  )
  let isRight = (
    vertical.x1 === element.x2 ||
    vertical.x1 === element.x2 + thickness / 2
  )
  if (byDirection) {
    if (element.direction === doorDirection.right || element.flip === doorFlip.right)
      return hasOverlapY && isLeft  // hinges on the left side
    if (element.direction === doorDirection.left || element.flip === doorFlip.left)
      return hasOverlapY && isRight  // hinges on the right side
  }
  return hasOverlapY && (isLeft || isRight)
}

const markNeighbouringHorizontalsAsChanged = (
  changedJetty,
  originalJetty,
  element,
  onlyAbove = false,
) => {
  changedJetty['horizontals'].filter(
    h => isNeighbouringHorizontal(element, h, onlyAbove)
  ).forEach(h => {
    h['hasChangedFittings'] = true;
    h['wasMoved'] = false;
  })
  originalJetty['horizontals'].filter(
    h => isNeighbouringHorizontal(element, h, onlyAbove)
  ).forEach(h => {
    h['hasChangedFittings'] = true;
    h['wasMoved'] = false;
  })
}

const markNeighbouringVerticalsAsChanged = (
  changedJetty,
  originalJetty,
  element,
  byDirection = false,
) => {
  changedJetty['verticals'].filter(
    v => isNeighbouringVertical(element, v, byDirection)
  ).forEach(v => {
    v['hasChangedFittings'] = true;
    v['wasMoved'] = false;
  })
  originalJetty['verticals'].filter(
    v => isNeighbouringVertical(element, v, byDirection)
  ).forEach(v => {
    v['hasChangedFittings'] = true;
    v['wasMoved'] = false;
  })
}

const typesAttachedToHorizontals = [
  'doors', 'backs', 'supports', 'inserts', 'legs', 'long_legs', 'plinth', 'verticals', 'cable_management',
]
const typesAttachedToVerticals = [
  'drawers', 'doors', 'inserts',
]

export const compareJetty = (changedJetty, originalJetty) => {

  geometryFields.forEach(objectType => {
    findAddedAndChangedElements(objectType, originalJetty, changedJetty, propagateChanges);
    findRemovedElements(objectType, originalJetty, changedJetty, propagateChanges);
    findMovedElements(objectType, changedJetty, originalJetty, propagateChanges);
  })
}

const propagateChanges = (element, objectType, changedJetty, originalJetty) => {
  if (
    element['wasAdded']
    || element['wasMoved']
    || element['wasChanged']
    || element['wasDeleted']
  ) {
    if (typesAttachedToHorizontals.includes(objectType)) {
      markNeighbouringHorizontalsAsChanged(changedJetty, originalJetty, element, objectType === 'doors');
    }
    if (typesAttachedToVerticals.includes(objectType)) {
      markNeighbouringVerticalsAsChanged(changedJetty, originalJetty, element, objectType === 'doors');
    }
  }
}
