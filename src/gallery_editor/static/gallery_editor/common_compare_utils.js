export const getChangedElementsList = (changedGallery, originalGallery, geometryFields) => {
  let changedElements = {};
  let addedElements = {};
  let deletedElements = {};
  let movedElements = {};
  let objectsWithChangedFittings = {};
  geometryFields.forEach(objectType => {
    changedElements[objectType] = 0;
    addedElements[objectType] = 0;
    deletedElements[objectType] = 0;
    movedElements[objectType] = 0;
    objectsWithChangedFittings[objectType] = 0;

    changedGallery[objectType].forEach(element => {
      if (element['wasChanged'])
        changedElements[objectType] += 1;
      if (element['wasAdded'])
        addedElements[objectType] += 1;
      if (element['wasMoved'])
        movedElements[objectType] += 1;
    })
    originalGallery[objectType].forEach(element => {
      if (element['wasDeleted'])
        deletedElements[objectType] += 1;
      if (element['hasChangedFittings'])
        objectsWithChangedFittings[objectType] += 1;
    })
  })
  let changesList = document.createElement("ul");
  geometryFields.forEach(type => {
    appendToChangesList(changesList, type, deletedElements[type], 'removed');
    appendToChangesList(changesList, type, addedElements[type], 'added');
    appendToChangesList(changesList, type, movedElements[type], 'moved');
    appendToChangesList(changesList, type, changedElements[type], 'changed');
    appendToChangesList(changesList, type, objectsWithChangedFittings[type], 'with changed fittings');
  })
  return changesList;
}

const appendToChangesList = (changesList, type, amount, changesType) => {
  if (amount === 0) return;
  let listItem = document.createElement("li");
  let typeName = type;
  if (type === 'cable_management')
    typeName = 'grommets';
  if (type === 'long_legs')
    typeName = 'long legs';
  if (amount === 1 && typeName.slice(-1) === 's')
    typeName = typeName.slice(0, -1);
  listItem.innerHTML = `${amount} ${typeName} ${changesType}`;
  if (type === 'inserts' && changesType === 'changed')
    // inserts might change z coordinates, which are unified by PS later on
    listItem.innerHTML += ' <em> (possibly)</em>';
  changesList.appendChild(listItem);
}


export const getChangedDimensions = (changedGallery, originalGallery) => {
  let changedDimensions = document.createElement("p");
  ['width', 'depth', 'height'].forEach(dimension => {
    if (changedGallery[dimension] !== originalGallery[dimension])
      changedDimensions.innerHTML += `${dimension}: ${originalGallery[dimension]} -> ${changedGallery[dimension]}<br>`;
  })
  if (changedDimensions.innerHTML !== '')
    changedDimensions.innerHTML = `<b>Changed dimensions:</b><br>${changedDimensions.innerHTML}`;
  return changedDimensions;
}


export const findAddedAndChangedElements = (
  objectType,
  originalJetty,
  changedJetty,
  propagateChanges,
) => {
  let changedGeometry = changedJetty[objectType];
  changedGeometry.forEach(element => {
    let originalElement = originalJetty[objectType].find(
      e => (
        e.x1 === element.x1 &&
        e.y1 === element.y1 &&
        e.z1 === element.z1
      )
    );
    if (!originalElement) {
      element['wasAdded'] = true;
    } else if (
      originalElement.x2 !== element.x2 ||
      originalElement.y2 !== element.y2 ||
      originalElement.z2 !== element.z2
    ) {
      element['wasChanged'] = true;
      originalElement['wasChanged'] = true;
    } else if (
      (objectType === 'doors' || objectType === 'drawers') &&
      element.flip !== originalElement.flip
    ) {
      element['wasChanged'] = true;
      originalElement['wasChanged'] = true;
      // in case of flipped doors we want to propagate changes to original neighbour too
      propagateChanges(originalElement, objectType, changedJetty, originalJetty);
    } else if (
      objectType === 'drawers' &&
      (
        element.merge?.includes('left') !== originalElement.merge?.includes('left')
        || element.merge?.includes('right') !== originalElement.merge?.includes('right')
      )
    ) {
      element['wasChanged'] = true;
      originalElement['wasChanged'] = true;
    }
    propagateChanges(element, objectType, changedJetty, originalJetty);
  });
};


export const findRemovedElements = (
  objectType,
  originalJetty,
  changedJetty,
  propagateChanges,
) => {
  let originalGeometry = originalJetty[objectType];
  originalGeometry.forEach(element => {
    let changedElement = changedJetty[objectType].find(
      e => (
        e.x1 === element.x1 &&
        e.y1 === element.y1 &&
        e.z1 === element.z1
      )
    );
    if (!changedElement) {
      element['wasDeleted'] = true;
    }
    propagateChanges(element, objectType, changedJetty, originalJetty);
  });
};


export const findMovedElements = (
  objectType,
  originalJetty,
  changedJetty,
  propagateChanges,
) => {
  changedJetty[objectType]
    .filter(element => element.wasAdded && !element.hasChangedFittings)
    .forEach(element => {
      // calculate dimensions of added element
      let width = element.x2 - element.x1;
      let height = element.y2 - element.y1;
      let depth = element.z2 - element.z1;
      // find original element
      let originalElement = originalJetty[objectType].find(
        e => (
          e.wasDeleted &&
          !e.hasChangedFittings &&
          e.x2 - e.x1 === width &&
          e.y2 - e.y1 === height &&
          e.z2 - e.z1 === depth
        ));
      if (originalElement) {
        element['wasMoved'] = true;
        element['wasAdded'] = false;
        originalElement['wasMoved'] = true;
        originalElement['wasDeleted'] = false;
      }
      propagateChanges(element, objectType, changedJetty, originalJetty);
    });
}
