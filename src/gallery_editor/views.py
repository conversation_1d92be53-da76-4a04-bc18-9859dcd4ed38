import json

from decimal import Decimal
from typing import (
    ClassVar,
    Optional,
    Type,
)

from django.conf import settings
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.db import transaction
from django.http import HttpResponse
from django.shortcuts import (
    get_object_or_404,
    redirect,
)
from django.urls import reverse
from django.views.generic import (
    TemplateView,
    View,
)

from gallery.models import (
    Jetty,
    Watty,
)
from gallery.serializers import (
    JettySerializerForProduction,
    WattySerializer,
)
from gallery.serializers.furniture.base import FurnitureBaseSerializer
from gallery_editor.service import (
    GalleryEditorService,
    JettyEditorService,
    WattyEditorService,
)
from producers.production_system_utils.client import ProductionSystemClient


def get_gallery_object_context(
    editor_service: GalleryEditorService,
    original_price: Optional[int] = None,
) -> dict:
    product = editor_service.product
    price = editor_service.price_as_number
    original_price = original_price or price
    errors = editor_service.ps_errors or []
    description = editor_service.gallery_object.description or ''
    edit_link = reverse(
        f'{editor_service.model.__name__.lower()}_editor',
        kwargs={'object_id': editor_service.gallery_object.id},
    )
    context = {
        'gallery_object': editor_service.gallery_object_as_json,
        'product_id': product.id if product else -1,
        'price': price,
        'original_price': original_price,
        'price_change': price - Decimal(original_price),
        'errors': errors,
        'description': description,
        'is_draft': description.startswith('Draft'),
        'is_production': settings.IS_PRODUCTION,
        'edit_link': edit_link,
    }
    return context


class GalleryEditorView(PermissionRequiredMixin, TemplateView):
    permission_required = 'gallery.change_jetty'
    template_name: ClassVar[str]
    model: ClassVar[Type[Jetty | Watty]]
    service: ClassVar[Type[GalleryEditorService]]
    url_name: ClassVar[str]

    def get(self, request, *args, **kwargs):
        object = get_object_or_404(self.model, id=kwargs.get('object_id'))
        context = get_gallery_object_context(editor_service=self.service(object))
        return self.render_to_response(context=context)

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        changed_geometry = json.loads(request.POST.get('changed_geometry', '{}'))
        saving_object = 'save_original_object' in request.POST
        saving_draft_object = 'save_draft_object' in request.POST
        object_id = kwargs.get('object_id')
        changed_object = get_object_or_404(self.model, id=object_id)

        editor_service = self.service(changed_object)
        editor_service.process(
            changed_geometry=changed_geometry,
            save_original=saving_object,
            save_draft=saving_draft_object,
        )
        if saving_draft_object:
            return redirect(self.url_name, object_id=changed_object.id)

        original_price = request.POST.get('original_price', None)
        context = get_gallery_object_context(
            editor_service=editor_service,
            original_price=original_price,
        )
        return self.render_to_response(context=context)


class JettyEditorView(GalleryEditorView):
    model = Jetty
    service = JettyEditorService
    url_name = 'jetty_editor'
    template_name = 'jetty_editor.html'


class WattyEditorView(GalleryEditorView):
    model = Watty
    service = WattyEditorService
    url_name = 'watty_editor'
    template_name = 'watty_editor.html'


class GalleryApplyChangesView(PermissionRequiredMixin, TemplateView):
    permission_required = 'gallery.change_jetty'
    template_name = 'gallery_swap.html'
    model: ClassVar[Type[Jetty | Watty]]
    service: ClassVar[Type[GalleryEditorService]]

    def get(self, request, *args, **kwargs):
        draft_gallery = get_object_or_404(self.model, id=kwargs.get('draft_id'))
        target_gallery = get_object_or_404(self.model, id=kwargs.get('target_id'))
        return self.render_comparison(draft_gallery, target_gallery)

    def post(self, request, *args, **kwargs):
        draft_gallery = get_object_or_404(self.model, id=kwargs.get('draft_id'))
        target_gallery = get_object_or_404(self.model, id=kwargs.get('target_id'))
        if 'apply_draft' in self.request.POST:
            self.service(target_gallery).apply_draft(draft_gallery)
        return self.render_comparison(draft_gallery, target_gallery)

    def render_comparison(self, draft_gallery, target_gallery):
        draft_gallery_context = get_gallery_object_context(
            editor_service=self.service(draft_gallery),
        )
        target_gallery_context = get_gallery_object_context(
            editor_service=self.service(target_gallery),
        )
        price_change = draft_gallery_context['price'] - target_gallery_context['price']
        return self.render_to_response(
            context={
                'draft': draft_gallery_context,
                'target': target_gallery_context,
                'price_change': price_change,
                'model': self.model.__name__.lower(),
            },
        )


class GalleryApplyChangesViewJetty(GalleryApplyChangesView):
    permission_required = 'gallery.change_jetty'
    model = Jetty
    service = JettyEditorService


class GalleryApplyChangesViewWatty(GalleryApplyChangesView):
    permission_required = 'gallery.change_jetty'
    model = Watty
    service = WattyEditorService


class FrontViewPDFView(PermissionRequiredMixin, View):
    permission_required = 'gallery.change_jetty'
    serializer = Type[FurnitureBaseSerializer]
    furniture_class = ClassVar[Type[Jetty | Watty]]

    def post(self, request, *args, **kwargs):
        furniture_type = self.furniture_class.product_type
        changed_geometry = json.loads(request.POST.get('changed_geometry', '{}'))
        item_id = kwargs.get(f'{furniture_type}_id')

        changed_item = get_object_or_404(self.furniture_class, id=item_id)
        for field, value in changed_geometry.items():
            changed_item.__setattr__(field, value)

        item_serialized = self.serializer(changed_item).data

        with ProductionSystemClient(suppress_errors=True) as ps_client:
            frontview = ps_client.get_gallery_test_files(
                gallery_json=item_serialized,
                furniture_type=f'{furniture_type}',
                file_type='front-view-pdf',
            )
        response = HttpResponse(
            frontview,
            content_type='application/pdf',
        )
        response['Content-Disposition'] = 'inline;filename=front_view.pdf'
        return response


class FrontViewPDFViewJetty(FrontViewPDFView):
    permission_required = 'gallery.change_jetty'
    serializer = JettySerializerForProduction
    furniture_class = Jetty


class FrontViewPDFViewWatty(FrontViewPDFView):
    permission_required = 'gallery.change_watty'
    serializer = WattySerializer
    furniture_class = Watty


class GalleryCornerMergeView(PermissionRequiredMixin, TemplateView):
    permission_required = 'gallery.change_jetty'
    template_name = 'corner_merge.html'
    model: ClassVar[Type[Jetty | Watty]]
    service: ClassVar[Type[GalleryEditorService]]

    def get(self, request, *args, **kwargs):
        left_gallery = get_object_or_404(self.model, id=kwargs.get('left_id'))
        right_gallery = get_object_or_404(self.model, id=kwargs.get('right_id'))
        return self.render_comparison(left_gallery, right_gallery)

    def post(self, request, *args, **kwargs):
        left_gallery = get_object_or_404(self.model, id=kwargs.get('left_id'))
        right_gallery = get_object_or_404(self.model, id=kwargs.get('right_id'))
        merged_geometry = json.loads(request.POST.get('merged_geometry', '{}'))
        left_gallery.description = f'Merged into a corner with {right_gallery.id}.'
        merged_id = self.service(left_gallery).process(
            changed_geometry=merged_geometry,
            save_original=False,
            save_draft=True,
        )
        return redirect('jetty_editor', object_id=merged_id)

    def render_comparison(self, left_gallery, right_gallery):
        left_gallery_context = get_gallery_object_context(
            editor_service=self.service(left_gallery),
        )
        right_gallery_context = get_gallery_object_context(
            editor_service=self.service(right_gallery),
        )
        return self.render_to_response(
            context={
                'left': left_gallery_context,
                'right': right_gallery_context,
                'model': self.model.__name__.lower(),
            },
        )


class GalleryCornerMergeViewJetty(GalleryCornerMergeView):
    permission_required = 'gallery.change_jetty'
    model = Jetty
    service = JettyEditorService
