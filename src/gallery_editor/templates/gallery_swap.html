{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Comparing {{ model }} {{ target.id }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous"></head>
<body>
<div class="container-fluid">
<div class="row justify-content-center">
    <div id="edited" class="col-4 flex-grow-1">
        <div id="shelf_box" class="row">
            <div
                {% if draft.is_draft is True %}
                class="alert alert-secondary col-10 offset-1"
                {% elif draft.product_id == -1 %}
                class="alert alert-info col-10 offset-1"
                {% else %}
                class="alert alert-warning col-10 offset-1"
                {% endif %}
                role="alert"
            >
                <h3>Draft Shelf Info:</h3>
                <p id="draftShelfInfo"></p>
                <p><em>{{ draft.description }}</em></p>
                <p>
                    Price: {{ draft.price }} PLN<br>
                </p>
                <a
                    href="{{ draft.edit_link }}"
                    target="_blank" class="btn btn-primary"
                    style="position: absolute; right: 15px; top: 15px;"
                >Edit</a>
            </div>
        </div>
        <div class="row">
            {% if draft.errors %}
                <div class="alert alert-danger col-10 offset-1" role="alert">
                    <h4>Errors:</h4>
                    <ul>
                        {% for error in draft.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                    </ul>
                </div>
            {% else %}
                <div class="alert alert-success col-10 offset-1" role="alert">
                    <p>No errors</p>
                </div>
            {% endif %}
        </div>
    </div>
    <div id="divider" class="col-2">
    </div>
    <div id="original" class="col-4 flex-grow-1">
        <div id="shelf_box" class="row">
            <div
                {% if target.is_draft is True %}
                class="alert alert-secondary col-10 offset-1"
                {% elif target.product_id == -1 %}
                class="alert alert-info col-10 offset-1"
                {% else %}
                class="alert alert-warning col-10 offset-1"
                {% endif %}
                role="alert"
            >
                <h3>Target Shelf Info:</h3>
                <img src="{% static "gallery_editor/pan_szafka.svg" %}" width="150px" class="float-right">
                <p id="targetShelfInfo"></p>
                <p><em>{{ target.description }}</em></p>
                <p>
                    Original Price: {{ target.price }} PLN<br>
                </p>
            </div>
        </div>
        <div class="row">
            {% if target.errors %}
                <div class="alert alert-danger col-10 offset-1" role="alert">
                    <h4>Errors:</h4>
                    <ul>
                        {% for error in target.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                    </ul>
                </div>
            {% else %}
                <div class="alert alert-success col-10 offset-1" role="alert">
                    <p>No errors</p>
                </div>
            {% endif %}
        </div>
</div>
</div>
<div class="row justify-content-center">
    <div class="col-4 flex-grow-1" id="editedDrawing"></div>
    <div id="divider" class="col-2">
        <div class="row alert alert-warning" role="alert">
        <span>
            <h3>Changes:</h3>
            <p>
                <b>Price: {{ price_change }} PLN</b>
            </p>
            <span id="changesList">
                <b>Changed elements: </b><br>
            </span>
        </span>
        </div>
        <div class="row justify-content-center pt-4">
            <form id="swapGalleryForm" method="post">
            {% csrf_token %}

            <div class="input-group input-group-lg">
              <div class="input-group-prepend">
                <span class="input-group-text" id="inputGroup-sizing-lg">Apply Draft</span>
              </div>
            <button name="apply_draft" id="applyButton" type="submit" class="btn btn-warning">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-arrow-right-square" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M15 2a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2zM0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zm4.5 5.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"></path>
                </svg>
            </button>
            </div>
            </form>
        </div>
    </div>
    <div class="col-4 flex-grow-1" id="targetDrawing"></div>
</div>
</div>
<script type="text/javascript">
    let editedGallery = {{ draft.gallery_object|safe }};
    let targetGallery = {{ target.gallery_object|safe }};
    let editedProductID = {{ draft.product_id|safe }};
    let targetProductID = {{ target.product_id|safe }};
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/svg.js/3.1.2/svg.min.js" type="text/javascript"></script>

{% if model == "jetty" %}
    <script src="{% static "gallery_editor/jetty/swap.js" %}" type="module"></script>
{% else %}
    <script src="{% static "gallery_editor/watty/swap.js" %}" type="module"></script>
{% endif %}

</body>
</html>
