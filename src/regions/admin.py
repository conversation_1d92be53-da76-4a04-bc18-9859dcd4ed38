from django.contrib import (
    admin,
    messages,
)
from django.db.models import QuerySet
from django.http import HttpRequest

from regions.forms import CountryRegionVatAdminForm
from regions.models import (
    Country,
    CountryRegionVat,
    Currency,
    CurrencyRate,
    Region,
    RegionRate,
)
from regions.tasks import update_carts_with_given_currency


class CountryRegionVatAdmin(admin.ModelAdmin):
    list_display = ('country', 'vat', 'valid_date_range')
    list_filter = (
        'country',
        'country__region',
    )
    form = CountryRegionVatAdminForm


class CountryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'vat', 'language_code', 'locale', 'region')
    list_filter = (
        'vat',
        'region',
    )


class CurrencyAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'code',
        'symbol',
    )


class CurrencyRateAdmin(admin.ModelAdmin):
    date_hierarchy = 'time'
    list_display = (
        'time',
        'currency',
        'rate',
    )
    list_display_links = ('time',)
    list_filter = ('currency',)

    actions = ('recalculate_related_carts',)

    @admin.action(description='Recalculate related carts')
    def recalculate_related_carts(
        self,
        request: HttpRequest,
        queryset: QuerySet[CurrencyRate],
    ) -> None:
        for currency_id in queryset.values_list('currency__id', flat=True):
            update_carts_with_given_currency.delay(currency_id)
        messages.info(request, 'Carts are being recalculated')


class RegionAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'currency',
        'default_for_language',
    )
    list_filter = ('currency',)


class RegionRateAdmin(admin.ModelAdmin):
    date_hierarchy = 'time'
    list_display = (
        'time',
        'region',
        'rate',
    )
    list_display_links = ('time',)
    list_filter = ('region',)


admin.site.register(Country, CountryAdmin)
admin.site.register(CountryRegionVat, CountryRegionVatAdmin)
admin.site.register(Currency, CurrencyAdmin)
admin.site.register(CurrencyRate, CurrencyRateAdmin)
admin.site.register(Region, RegionAdmin)
admin.site.register(RegionRate, RegionRateAdmin)
