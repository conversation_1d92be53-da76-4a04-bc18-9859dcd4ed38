# Generated by Django 3.1.12 on 2021-09-07 14:25

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0014_change_estimated_delivery_time_verbose_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='source_order_item',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='order_source',
                to='orders.orderitem',
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='source_region_total_price',
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=12, null=True
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='source_region_total_price_net',
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=12, null=True
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='source_total_price',
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=12, null=True
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='source_total_price_net',
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=12, null=True
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='switch_status',
            field=django_fsm.FSMField(
                choices=[
                    ('blank', 'Blank'),
                    ('item_replacement', 'Item Replacement'),
                    ('cost_recalculation', 'Cost Recalculation'),
                    (
                        'waiting_for_additional_payment',
                        'Waiting for additional payment',
                    ),
                    ('completed', 'Completed'),
                    ('cancelled', 'Cancelled'),
                ],
                db_index=True,
                default='blank',
                max_length=50,
                protected=True,
            ),
        ),
        migrations.AddField(
            model_name='order',
            name='target_order_item',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='order_target',
                to='orders.orderitem',
            ),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='deleted',
            field=models.DateTimeField(editable=False, null=True),
        ),
    ]
