import logging

from django.db import transaction
from django.utils import timezone

from carts.services.cart_service import CartService
from custom.shelf_states_interactor import add_jetty_state_to_redis
from custom.utils.slack import (
    notify_about_influ_order_on_slack,
    notify_about_vip_order_on_slack,
)
from customer_service.models import CSOrder
from events.domain_events.marketing_events import OrderSurfaceCalculationEvent
from gallery.enums import ShelfStatusSource
from orders.enums import OrderStatus
from orders.internal_api.events import (
    LogisticOrderEstimatedAssemblyCostsRequestEvent,
    OrderPushedToProductionEvent,
    OrderRefreshEvent,
)
from orders.order_notes import (
    ADDITIONAL_PACKAGE_NOTE,
    INFLU_NOTE,
    VIP_NOTE,
    get_order_note,
)
from orders.services.sample_costs_calculator import SampleCostsCalculator
from orders.utils import (
    add_order_context,
    get_assembly_type,
)
from pricing_v3.recycle_tax_rates import get_recycle_tax_value
from producers.choices import ProductStatus
from producers.services.create_product import CreateProductFromOrderItem
from regions.constants import OTHER_REGION_NAME
from warehouse.tools import SampleManager

logger = logging.getLogger('orders')


class ProcessOrderToProduction:
    def __init__(self, order_to_production):
        self.original_order = order_to_production.original_order
        self.order_to_production = order_to_production

    def process(self):
        logistic_orders_by_order_type = MoveOrderToProduction(
            self.original_order
        ).move()
        self.calculate_recycle_tax()
        self.calculate_sample_box_costs()
        self.order_to_production.process_at = timezone.now()
        self.order_to_production.save(update_fields=['process_at'])
        self.original_order.update_values_if_invoice_ignored_promo_used()
        self.get_or_create_invoice()
        OrderRefreshEvent(self.original_order)

        if logistic_orders_by_order_type:
            for logistic_order in logistic_orders_by_order_type.values():
                if logistic_order['assembly_type']:
                    LogisticOrderEstimatedAssemblyCostsRequestEvent(
                        self.original_order,
                        logistic_order['id'],
                    )

        # TODO: Uncomment after releasing EDD for new orders
        # TODO: also this: test_is_estimated_delivery_date_set_according_to_country
        # if self.is_eligible_for_estimated_delivery_date:
        #     self.original_order.is_estimated_delivery_date = True
        #     self.original_order.save(update_fields=['is_estimated_delivery_date'])

    def calculate_recycle_tax(self):
        if not self.original_order.is_france():
            return

        for order_item in self.original_order.items.exclude(
            content_type__model='samplebox',
        ).exclude(
            content_type__model='additionalservice',
        ):
            self.update_recycle_tax_value(order_item)

    def calculate_sample_box_costs(self):
        calculator = SampleCostsCalculator(self.original_order)
        calculator.calculate()

    @classmethod
    def update_recycle_tax_value(cls, order_item):
        order_item.recycle_tax_value = get_recycle_tax_value(
            order_item.product_set.exclude(
                status=ProductStatus.ABORTED,
            )
            .first()
            .get_weight_netto()
        )
        order_item.save(update_fields=['recycle_tax_value'])

    def get_or_create_invoice(self):
        if (
            self.original_order.is_free()
            and not self.original_order.is_full_barter_deal()
        ):
            return

        if self.original_order.is_klarna_payment():
            proforma = self.original_order.create_proforma()
            return proforma

        invoice = self.original_order.create_invoice()
        return invoice

    @property
    def is_eligible_for_estimated_delivery_date(self):
        if self.original_order.country is not None:
            return self.original_order.country.lower() == 'netherlands'
        return False


class MoveOrderToProduction:
    def __init__(self, order):
        self.order = order

    def move(self):
        from customer_service.tasks import update_or_create_cs_order

        if not self.order.items.exists():
            logger.error(
                add_order_context('Order has no items.', order=self.order),
                extra={'order_id': self.order.id},
            )
            return
        self.log_if_in_production()

        self.order.change_status(
            OrderStatus.IN_PRODUCTION,
            should_callback_logistic=False,
        )

        if cart := self.order.get_cart():
            CartService(cart).handle_paid_order()

        for suborder in self.order.suborders.all():
            suborder.change_status(OrderStatus.IN_PRODUCTION)

        pushed_to_production_event = OrderPushedToProductionEvent(self.order)
        self.update_order_fields(
            pushed_to_production_event.logistic_orders_by_order_type
        )
        self.move_order_items_to_production(
            pushed_to_production_event.logistic_orders_by_order_type,
        )
        self.notify_about_special_order()

        OrderSurfaceCalculationEvent(
            user=self.order.owner,
            email=self.order.email,
            last_order_surface=self._calculate_order_surface(),
        )

        if CSOrder.should_update_or_create_from_order(self.order):
            transaction.on_commit(
                lambda: update_or_create_cs_order.delay(self.order.pk)
            )
        return pushed_to_production_event.logistic_orders_by_order_type

    def log_if_in_production(self):
        in_production = self.order.order_or_product_in_status_production_or_above()
        if in_production:
            logger.critical(in_production)

    def move_order_items_to_production(self, logistic_order_dict):
        for order_item in self.order.material_items.all():
            furniture_item = order_item.order_item
            if order_item.product_set.exclude(status=ProductStatus.ABORTED).exists():
                logger.exception(
                    add_order_context(
                        f'OrderItem {order_item.id} already has not aborted Product',
                        order=self.order,
                    ),
                    extra={'order_id': self.order.id},
                )
                continue
            if order_item.is_samplebox():
                SampleManager.take_sample_from_stock(
                    furniture_item,
                    self.order.pk,
                    self.order.owner,
                    order_item.quantity,
                )
                continue
            self.export_to_bigquery(furniture_item, order_item)

            for counter in range(order_item.quantity):
                logistic_order = logistic_order_dict[
                    (
                        order_item.content_type.model,
                        get_assembly_type(self.order, order_item.order_item),
                    )
                ]
                CreateProductFromOrderItem(order_item).create(logistic_order['id'])

    def export_to_bigquery(self, furniture_item, order_item):
        try:
            add_jetty_state_to_redis(
                user_id=self.order.owner.id,
                jetty=furniture_item,
                source=ShelfStatusSource.TRANSACTION,
            )
        except ValueError:
            logger.exception(
                add_order_context(
                    (
                        'Something went wrong while'
                        ' exporting to BQ.[furniture_id={}]'
                    ).format(order_item),
                    order=self.order,
                ),
                extra={'order_id': self.order.id},
            )

    def notify_about_special_order(self):
        self.notify_about_other_region()
        if self.order.is_influ_order:
            notify_about_influ_order_on_slack(self.order.id)
        if self.order.is_vip_order:
            notify_about_vip_order_on_slack(self.order.id)
        if self.order.is_big_order:
            self.order.notify_about_big_order()
        if self.order.is_b2b_order:
            self.order.notify_about_b2b_order()

    def notify_about_other_region(self):
        from orders.tasks import send_other_region_notification

        if self.order.get_region().name == OTHER_REGION_NAME:
            transaction.on_commit(
                lambda: send_other_region_notification.delay(self.order.id)
            )

    def update_order_fields(self, logistic_orders_by_order_type):
        self.order.estimated_delivery_time = self.order.get_estimated_delivery_time()
        self._set_order_notes(logistic_orders_by_order_type)
        self.order.currency = self.order.get_region().currency
        self.order.save(
            update_fields=['order_notes', 'currency', 'estimated_delivery_time']
        )

    def _set_order_notes(self, logistic_orders_by_order_type):
        self.order.order_notes = get_order_note(
            order=self.order,
            current_note=self.order.order_notes,
        )
        self.order.order_notes = self.get_order_note_base_on_promo_code()
        self.order.set_order_notes(self.order.notes)
        if self.order.is_influ_order:
            self.order.cs_notes += INFLU_NOTE
            self.order.set_order_notes(INFLU_NOTE, commit=False)
            self.order.save(update_fields=['cs_notes', 'order_notes'])
        if self.order.is_vip_order:
            self.order.cs_notes += VIP_NOTE
            self.order.set_order_notes(VIP_NOTE, commit=False)
            self.order.save(update_fields=['cs_notes', 'order_notes'])
        if self.order.get_region().name == OTHER_REGION_NAME:
            self.order.set_order_notes(ADDITIONAL_PACKAGE_NOTE)

    def _calculate_order_surface(self) -> float:
        return sum(product.get_m2() for product in self.order.product_set.all())

    def get_order_note_base_on_promo_code(self):
        promo_dict = {
            'tylko4minimum': ' Showroom BERLIN!!! Wysłać BUSEM! '
            'Nanieść notatkę na etykietach transportowych o treści: '
            'OWNER: MINIMUM',
            'tylko4lendis': ' Klient od Bena "Wypożyczalnia (Lendis)". '
            'Nanieść dodatkową nazwę identyfikacyjną '
            'na CMR oraz etykietach transportowych o '
            'treści wskazanej przez Lendis w '
            'dodatkowym komentarzu do orderu"',
            'tylko4vc': 'TYLKO4VC WYSŁAĆ KONIECZNIE DT!!!! '
            'Zamówienie jedzie do naszego inwestora!!!',
        }
        if not self.order.promo_text:
            return self.order.order_notes
        note = promo_dict.get(self.order.promo_text.lower())
        if note:
            current_note = self.order.order_notes
            return current_note + note
        return self.order.order_notes
