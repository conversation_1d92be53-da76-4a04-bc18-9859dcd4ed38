import logging

from dataclasses import (
    dataclass,
    field,
)
from typing import (
    TYPE_CHECKING,
    Optional,
)
from unittest.mock import MagicMock

from django.conf import settings

from events.domain_events.internal_events import InternalEventBase
from orders.internal_api.clients import (
    ClientError,
    LogisticOrderAPIClient,
)

if TYPE_CHECKING:
    from django.contrib.auth.models import User

    from custom.internal_api.dto import LogisticOrderDTO
    from orders.models import Order
    from producers.models import Product


logger = logging.getLogger('orders')


@dataclass
class OrderPushedToProductionEvent(InternalEventBase):
    order: 'Order'
    user: Optional['User'] = None
    logistic_orders_by_order_type: dict = field(default_factory=dict)

    def __post_init__(self) -> None:
        with_response = super().__post_init__()
        if with_response:
            self.logistic_orders_by_order_type = with_response

    @property
    def properties(self) -> dict:
        properties = {'order': self.order.id}
        return properties

    def execute(self):
        if settings.LOGISTIC_MOCK_REQUEST_STRATEGY:
            mock_logistic_orders_by_order_type = MagicMock()
            mock_logistic_orders_by_order_type.__getitem__.return_value = {'id': None}
            return mock_logistic_orders_by_order_type

        logistic_order_api = LogisticOrderAPIClient()
        logistic_orders = logistic_order_api.get_or_create_logistic_order(self.order)

        logistic_orders_by_order_type = {}

        for logistic_order in logistic_orders:
            self.order.set_serialized_logistic_info(logistic_order)
            logistic_orders_by_order_type[
                (logistic_order['order_type'], logistic_order['assembly_type'])
            ] = logistic_order

        return logistic_orders_by_order_type


@dataclass
class OrderAssemblyAddedEvent(InternalEventBase):
    order: 'Order'
    logistic_order: 'LogisticOrderDTO'

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order': self.logistic_order.id,
            'order': self.order.id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order = logistic_order_api.set_assembly_paid(self.logistic_order.id)
        self.order.set_serialized_logistic_info(logistic_order)
        return self.order


@dataclass
class OrderRefreshEvent(InternalEventBase):
    order: 'Order'

    @property
    def properties(self) -> dict:
        properties = {
            'order': self.order.id,
        }
        return properties

    def execute(self):
        if settings.LOGISTIC_MOCK_REQUEST_STRATEGY:
            return None

        logistic_order_api = LogisticOrderAPIClient()
        for logistic_order in self.order.logistic_info:
            try:
                serialized_logistic_order = logistic_order_api.refresh_serialized_order(
                    logistic_order.id,
                    self.order,
                )
            except ClientError:
                logger.exception('Error during refreshing logistic order')
                continue
            self.order.set_serialized_logistic_info(serialized_logistic_order)
        return self.order


@dataclass
class OrderStatusChangedEvent(OrderRefreshEvent):
    """Triggered when status is updated"""


@dataclass
class LogisticOrderToBeShippedStatusChangedEvent(InternalEventBase):
    order: 'Order'
    logistic_order_id: int

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order': self.logistic_order_id,
            'order': self.order.id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order = logistic_order_api.set_to_be_shipped(self.logistic_order_id)
        self.order.set_serialized_logistic_info(logistic_order)
        return self.order


@dataclass
class LogisticOrderToBeShippedStatusRollbackEvent(InternalEventBase):
    order: 'Order'
    logistic_order_id: int

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order': self.logistic_order_id,
            'order': self.order.id,
        }
        return properties

    def execute(self) -> 'Order':
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order = logistic_order_api.rollback_to_be_shipped(
            self.logistic_order_id
        )
        self.order.set_serialized_logistic_info(logistic_order)
        return self.order


@dataclass
class LogisticOrderSplittedEvent(InternalEventBase):
    order: 'Order'

    @property
    def properties(self) -> dict:
        properties = {
            'order': self.order.id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order_api.split(self.order)


@dataclass
class LogisticOrderUndeliveredEvent(InternalEventBase):
    logistic_order_id: int

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order_id': self.logistic_order_id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order_api.set_undelivered(self.logistic_order_id)


@dataclass
class LogisticOrderEstimatedAssemblyCostsRequestEvent(InternalEventBase):
    order: 'Order'
    logistic_order_id: int

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order_id': self.logistic_order_id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order = logistic_order_api.request_estimated_cost_assembly(
            self.logistic_order_id
        )
        self.order.set_serialized_logistic_info(logistic_order)
        return self.order


@dataclass
class LogisticOrderDeleteEvent(InternalEventBase):
    order: 'Order'
    product: 'Product'
    logistic_order: 'LogisticOrderDTO'

    @property
    def properties(self) -> dict:
        properties = {
            'order': self.order.id,
            'product': self.product.id,
            'logistic_order': self.logistic_order.id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order_api.delete_logistic_order(self.logistic_order.id)
        self.order.delete_serialized_logistic_info(self.logistic_order.id)
        self.product.logistic_order = None
        self.product.save_serialization_only(update_fields=['logistic_order'])


@dataclass
class LogisticOrderGetSimilarOrdersInvalidateCacheEvent(InternalEventBase):
    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order_api.invalidate_get_similar_orders_cache()


@dataclass
class OrderFullRefreshEvent(InternalEventBase):
    order: 'Order'

    @property
    def properties(self) -> dict:
        properties = {
            'order': self.order.id,
        }
        return properties

    def execute(self) -> None:
        if settings.LOGISTIC_MOCK_REQUEST_STRATEGY:
            return None

        logistic_order_api = LogisticOrderAPIClient()
        serialized_logistic_orders = (
            logistic_order_api.refresh_serialized_order_for_all_logistic_orders(
                self.order,
            )
        )
        self.order.set_serialized_logistic_info_list(
            serialized_logistic_orders,
        )
