{% load i18n %}
{% trans placeholder as translated_placeholder %}

<li class="account-input bg-white {{ wrapper_class }}">
    <label
        for="{{ input_name }}"
        class="account-input__label normal-14 text-offblack-800 mb-4 mt-8 px-16 text-capitalize-first-letter"
        data-testid="input-{{ input_name }}-label"
    >
        {% trans label %}
    </label>
    <input
        data-testid="input-{{ input_name }}"
        value="{{ value }}"
        type="{{ type }}"
        placeholder="{{ translated_placeholder }}"
        class="account-input__input px-16 bg-white"
        id="{{ id }}"
        name="{{ input_name }}"
        {% if required %}required{% endif %}
        {% if autofocus %}autofocus{% endif %}
    >
</li>
