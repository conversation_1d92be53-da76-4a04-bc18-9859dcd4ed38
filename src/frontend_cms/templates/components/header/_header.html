{% load i18n static util_tags components  %}
{% load user_agents %}

{% spaceless %}
    <header
        class="header bg-white w-full z-30"
        data-section="header"
    >
        <div class="container-cstm-fluid">
            <ul class="row middle-xs header__main-container">
                <!-- mobile menu -->
                <li class="col-xs-4 mobile-visible">
                    {% include './components/_mobile-menu.html' %}
                </li>

                <!-- logo -->
                <li class="col-xs-4 col-md-2">
                    <a href="{% url 'front-homepage' %}" class="flex center-xs start-md w-full header__link header__logo-container" data-testid="logo">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 81 29" fill="#FF3C00" class="header__logo" alt="tylko">
                            <polygon points="26.5,5.7 22,18.5 17.7,5.7 13.8,5.7 20.2,23 19.3,25.8 15.3,25.8 15.3,29 21.7,29 30.3,5.7 "/>
                            <path d="M71.6,20.4c-3.2,0-5.9-2.6-6-5.8c-0.1-3.3,2.5-6,5.8-6.1l0.2,0c3.2,0,5.9,2.6,6,5.8c0.1,3.3-2.5,6-5.8,6.1 L71.6,20.4z M71.4,5.2c-5.2,0.2-9.2,4.4-9.1,9.6c0.2,5.1,4.3,9,9.3,9h0c0.1,0,0.2,0,0.3,0c2.5-0.1,4.8-1.1,6.5-2.9c1.7-1.8,2.6-4.2,2.5-6.7C80.8,9.1,76.6,5.1,71.4,5.2"/>
                            <polygon points="45.9,0 45.9,23.4 49.5,23.4 49.5,13.6 56.7,23.3 56.8,23.4 61.3,23.4 54,13.6 61.3,5.7 56.7,5.7 49.5,13.5 49.5,0 "/>
                            <polygon points="32.5,0 32.5,3.3 35.6,3.3 35.6,20.2 32.5,20.2 32.5,23.4 42.5,23.4 42.5,20.2 39.3,20.2 39.3,0 "/>
                            <polygon points="2.8,0 2.8,5.8 0,5.8 0,9 2.8,9 2.8,23.4 10.4,23.4 10.4,20.2 6.4,20.2 6.4,9 10.4,9 10.4,5.8 6.4,5.8 6.4,0 "/>
                        </svg>
                    </a>
                </li>

                <!-- desktop links -->
                <li class="col-xs-4 col-md-10 flex end-xs between-md middle-xs">
                    <div class="flex middle-xs start-xs mobile-hidden">
                        <label for="megaMenuDesktop" data-testid="toggle-products-tab"
                               class="text-grey-900 lg:text-offblack-600 lg:normal-14 xl:normal-16 link--arrow-down middle-xs flex header__desktop-label header__mega-menu-label">
                            {% trans "menu_bar_shop_header_smallcaps" %}
                        </label>
                        <label for="inspirationMenuDesktop" data-testid="toggle-inspiration-tab"
                               class="text-grey-900 lg:text-offblack-600 lg:normal-14 xl:normal-16 link--arrow-down middle-xs flex header__desktop-label header__inspiration-menu-label">
                            {% trans "menu_bar_inspiration_header_smallcaps" %}
                        </label>
                        <a href="{% url "front-samples" %}"
                            class="text-grey-900 lg:text-offblack-600 normal-16 lg:normal-14 xl:normal-16 px-16 lg:px-12 xl:px-16 link flex header__link" data-testid="redirect-samples">
                            {% trans "ola_comparison_sample_menu" %}
                        </a>
                        <a href="{% url "front-tylko-for-business" %}"
                           class="text-grey-900 lg:text-offblack-600 normal-16 lg:normal-14 xl:normal-16 px-16 lg:px-12 xl:px-16 link flex header__link" data-testid="redirect-reviews">
                            {% trans "menu_bar_b2b_header_smallcaps" %}
                        </a>
                        <a href="/review-list"
                           class="text-grey-900 lg:text-offblack-600 normal-16 lg:normal-14 xl:normal-16 px-16 lg:px-12 xl:px-16 link flex header__link" data-testid="redirect-reviews">
                            {% trans "common_reviews" %}
                        </a>
                    </div>

                    <div class=" flex end-xs middle-xs">

                        {% include './components/_sign-in.html' with is_desktop=True %}

                        <label for="regionsDesktop" data-testid="change-region"
                               class="text-grey-900 lg:text-offblack-600 normal-16 link--arrow-down middle-xs flex position-relative header__desktop-label header-regions__desktop-label ml-12 mobile-hidden">
                            <i class="regions__sprite {% if not user_region.name == "_other" %}regions__sprite--{{ user_region.name }}{% else %}regions__sprite--other-regions{% endif %} regions__sprite--user-region"></i>
                        </label>

                        {% include './components/_wishlist-icon.html' %}

                        <a href="{% url 'front-cart' %}" class="header__icon-container header__icon-cart bg-transparent flex middle-xs end-xs position-relative ml-4" data-testid="open-cart">
                            <img src="{% static 'cart.svg' %}"
                                 alt="{% trans "Cart" %}"
                                 class="header__cart-icon mt-4"
                            />
                            <span class="header__cart-number flex middle-xs center-xs bold-10 text-offwhite-600 bg-orange visually-hidden"></span>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </header>

    {% include './components/_desktop-menu-containers.html' %}

{% endspaceless %}
