{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "name": "T03_bar"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "T03_bar"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 768, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 768, "max": [5.0, 20.0, 10.0], "min": [-5.0, -20.0, -10.0], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 9216, "componentType": 5126, "count": 768, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 768, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 6144, "componentType": 5126, "count": 768, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 1536, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 1536, "byteLength": 18432, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 19968, "byteLength": 12288, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "T03_bar.bin", "byteLength": 32256}]}