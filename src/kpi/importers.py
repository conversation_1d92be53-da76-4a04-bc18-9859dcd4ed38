from rest_framework import serializers

from kpi.models import MarketingNonperformanceCost
from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.importers import BaseImporter

MARKETING_NON_PERFORMANCE_COST_COLUMNS: list[str] = [
    'name',
    'date_at',
    'description',
    'cost_amount',
    'countries',
    'category',
    'author',
]


class MarketingNonperformanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketingNonperformanceCost
        fields = MARKETING_NON_PERFORMANCE_COST_COLUMNS


class MarketingNonperformanceCostExporter(BaseSerializedItemsExporter):
    dict_export_fields = MARKETING_NON_PERFORMANCE_COST_COLUMNS[1:]
    dict_export_key = MARKETING_NON_PERFORMANCE_COST_COLUMNS[0]
    serializer = MarketingNonperformanceSerializer
    export_item_prefetch_related = ()

    @property
    def default_export_queryset(self):
        return MarketingNonperformanceCost.objects.all()


class MarketingNonperformanceCostImporter(BaseImporter):
    encoding = 'utf-8-sig'

    serializer = MarketingNonperformanceSerializer
    dict_export_key = MARKETING_NON_PERFORMANCE_COST_COLUMNS[0]
