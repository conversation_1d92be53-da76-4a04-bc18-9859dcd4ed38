{% extends "admin/base.html" %}

{% load i18n static %}

{% block title %}
    {% if opts.object_name %}
        {{ opts.object_name }}
    {% else %}
        {{ site_title }}
    {% endif %}
{% endblock %}

{% block extrastyle %}
    <link rel="shortcut icon" href="{% static 'icons/be_icon.ico' %}"/>
    <link rel="stylesheet" href="{% static 'css/no-dark-mode.css' %}">
    <link rel="stylesheet" href="{% static 'css/font_size.css' %}">

    <script src="{% static 'js/jquery-1.11.0.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">
    <link href="{% static 'css/sb-admin-2-extras.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <style>
        #changelist-form .vTextField {
            width: 10em !important;
        }
        div .navbar-header {
            width: 100%
        }
        div .navbar-notifications {
            width: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        div .notifications-trigger-btn {
                display: flex;
                background: none;
                border: none;
                margin-right: 30px;
        }
        div .open>.dropdown-menu {
            margin-left: -298px;
            display: flex;
            width: 800px;
            flex-direction: column;
        }
        div .notifications-trigger-btn .badge {
            margin-right: 5px;
        }
        .dropdown-item {
            display: block;
            width: 100%;
            padding: .25rem 1.5rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            font-size: 12px;
        }
        .dropdown-divider {
            height: 0;
            margin: .5rem 0;
            overflow: hidden;
            border-top: 1px solid #e9ecef;
        }

        #changelist-form input[type=number] {
            width: 5em !important;
        }

        .model-logisticorder table#result_list > tbody > tr {
            border-bottom: 2px solid black;
        }

        .model-tobeshippedlogisticorder table#result_list > tbody > tr {
            border-bottom: 2px solid black;
        }

        i.circle-with-background {
            border-radius: 100%;
            border: 1px solid #d9dcdc;
            width: 15px;
            height: 15px;
            border-radius: 100%;
            display: inline-block;
            margin-left: 0;
            margin-bottom: 0
        }

        i.green {
            background: #3cc85a;
        }

        i.yellow {
            background: #ffd20f;
        }

        i.red {
            background: #FF2020;
        }

        i.black {
            background: #000000;
        }

        i.gray {
            background: #9a9a9a;
        }

        i.purple {
            background: purple;
        }

        /* Tooltip container */
        .tooltip {
            position: relative;
            display: inline-block;
            border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
        }

        .app-logistic, .app-logistic_custom, .app-tnt, .app-courier_self_service {
            display: none;
        }
        /* Tooltip text */
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: black;
            color: #fff;
            text-align: center;
            padding: 5px 0;
            border-radius: 6px;

            /* Position the tooltip text - see examples below! */
            position: absolute;
            z-index: 1;
        }

        /* Show the tooltip text when you mouse over the tooltip container */
        .tooltip:hover .tooltiptext {
            visibility: visible;
        }

        #changelist-form .results {
            overflow-x: auto;
        }


        /* Main Table */

        .module table th,
        .module table td {
            border-color: #f1f1f1;
            border-left: 1px solid #ddd;
            border-bottom: 1px solid #f3f3f3;
            vertical-align: middle;
            text-align: left;
            font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
        }

        .module table tr td {
            padding: 8px 5px;
            line-height: 15px;
            font-size: 12px;
        }

        .module table th {
            padding-left: 5px;
            line-height: 1.5;
            text-transform: none;
        }

        /* END - Main Table */


        /* Columns */

        .vDateField {
            max-width: 6.85em;
        }

        /* END - Columns */


        /* Side Filter  */

        #changelist #changelist-filter {
            width: 205px; /* 135px for condensed side filter*/
            border: 1px solid #ddd;
            margin: 0;
        }

        .change-list #changelist #changelist-filter h3 {
            margin-bottom: 10px;
            font-size: 12px;
            font-weight: bold;

        }

        #changelist-filter p {
            font-size: 12px;
        }

        #changelist-filter ul.errorlist {
            padding: 2px 5px 2px 15px;
        }

        .change-list #changelist #changelist-filter ul li {
            font-size: 12px;
            line-height: 16px;
        }

        #content #changelist-filter li.selected {
            border-left: 5px solid #ccc;
        }

        #changelist-filter input[type=submit],
        #changelist-filter .submit-row input {
            padding: 3px 6px;
            margin-right: 2px;
        }

        #changelist-filter p.datetime {
            margin: 0 0 0 16px;
        }

        #changelist-filter .datetime input {
            margin-left: 0;
        }

        #changelist-filter .vTimeField {
            max-width: 6.85em;
        }

        #changelist-filter .submit-row input {
            height: 25px;
        }

        /* END - Side Filter  */


        /* Links  */

        .change-list #changelist #changelist-filter ul li.selected a {
            color: #029eee !important;
        }

        #content #result_list tbody a,
        #content #result_list tbody a:visited,
        #content #result_list tbody a:link,
        #content #result_list tbody a:link:visited {
            color: #029eee;
        }

        #content a:hover {
            text-decoration: underline;
        }


        /* END - Links  */
        {% if IS_PRODUCTION %}
            body > div#container div#header {
            background: rgba(255, 60, 0, 0.4);
            }
        {% else %}
            #page-wrapper{
                background: #ccc;
            }
            #header{
                background: black;
                color: #ffffff;
            }
        {% endif %}
    </style>
    <script type="application/javascript">
        $(document).ready(function () {
            $('.whole-td-opacity').closest('tr').css('opacity', 0.3);
            $('.whole-td-opacity-half').closest('tr').css('background', 'rgba(228, 9, 9, 0.2)');
            $('.whole-td-display-none').closest('tr').css('display', 'none');
        });
    </script>
{% endblock %}

{% block usertools %}
    {% if has_permission %}
        <div id="user-tools">
            {% block welcome-msg %}
                {% trans 'Welcome,' %}
                <strong>{% firstof user.get_short_name user.get_username %}</strong>.
            {% endblock %}
            {% block userlinks %}
                {% if site_url %}
                    <a href="{{ site_url }}">{% trans 'View site' %}</a> /
                {% endif %}
                {% if user.is_active and user.is_staff %}
                    {% url 'django-admindocs-docroot' as docsroot %}
                    {% if docsroot %}
                        <a href="{{ docsroot }}">{% trans 'Documentation' %}</a> /
                    {% endif %}
                {% endif %}
                {% if user.has_usable_password %}
                    <a href="{% url 'admin:password_change' %}">{% trans 'Change password' %}</a> /
                {% endif %}
                <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
            {% endblock %}
        </div>

    {% endif %}
{% endblock %}
