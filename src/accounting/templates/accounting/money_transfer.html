{% extends "admin/base_site.html" %}
{% load humanize %}
{% load admin_tags %}
{% load static %}
{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css'%} " rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
{% endblock %}

{% block title %}Paypal import{% endblock %}

{% block content %}

    <div id="wrapper">
        <div class="container" style="width:95% !important">
            <div class="row">
            <h3> Paypal import </h3>
            <form action="" method="post" enctype="multipart/form-data">{% csrf_token %}
                {{ form.as_p }}
                <input type="submit" value="Send file" />
            </form>
        </div>
            Loaded</br>
        <table class="table table-striped table-bordered table-sm">
          <thead>
          <tr>
          {% if not response_rest %} Did not found any PayPal match{% endif %}
          {% for item in response_keys %}
          <th>{{ item }}</th>
          {% endfor %}
          </tr>
          </thead>
          <tbody>
          {% for item in response_rest %}
          <tr>
          {% for value in item %}
              {% if forloop.last %}
              <th >{{ value|safe }}</th>
              {% else %}
              <th >{{ value }}</th>
              {% endif %}
          {% endfor %}
          </tr>
          {% endfor %}
          </tbody>
        </table>
        {% if not response_rest_bank %} Did not found any Bank match{% endif %}
            <table class="table table-striped table-bordered table-sm">
          <thead>
          <tr>
          {% for item in response_keys_bank %}
          <th>{{ item }}</th>
          {% endfor %}
          </tr>
          </thead>
          <tbody>
          {% for item in response_rest_bank %}
          <tr>
          {% for value in item %}
              {% if forloop.last %}
              <th >{{ value|safe }}</th>
              {% else %}
              <th >{{ value }}</th>
              {% endif %}
          {% endfor %}
          </tr>
          {% endfor %}
          </tbody>
        </table>
    </div>
{% endblock %}
