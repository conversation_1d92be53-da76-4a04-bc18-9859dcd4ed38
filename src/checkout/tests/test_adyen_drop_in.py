from copy import copy
from unittest.mock import patch

from django.urls import reverse

import pytest

from rest_framework import status

from orders.enums import OrderStatus
from payments.models import Transaction

adyen_success_response = {
    'amount': {'currency': 'EUR', 'value': 1000},
    'countryCode': 'NL',
    'expiresAt': '2021-08-24T13:35:16+02:00',
    'id': 'CSD9CAC34EBAE225DD',
    'merchantAccount': 'CSTMCO',
    'reference': 'order-123',
    'returnUrl': 'https://your-company.com/checkout?shopperOrder=12xy.',
    'sessionData': 'Ab02b4c...',
}
adyen_webhook_response = {
    'currency': 'GBP',
    'eventCode': 'AUTHORISATION',
    'eventDate': '2022-10-17T10:32:30.28Z',
    'live': 'false',
    'merchantAccountCode': 'CSTMCO',
    'merchantReference': '****************',
    'operations': 'CANCEL,CAPTURE,REFUND',
    'originalReference': '',
    'paymentMethod': 'visa',
    'pspReference': 'order-123',
    'reason': '1234:7777:12/2012',
    'success': 'true',
    'value': '20100',
}


@pytest.fixture()
def validated_order(order_factory, user):
    return order_factory(
        first_name='Test1',
        last_name='Test',
        email='<EMAIL>',
        phone='*********',
        street_address_1='test street',
        city='test city',
        postal_code='12345',
        country='PL',
        phone_prefix='+48',
        status=OrderStatus.DRAFT,
        owner=user,
    )


@pytest.mark.django_db
class TestAdyenSession:
    @patch(
        'checkout.services.adyen_session.AdyenSessionHandler'
        '.get_session_data_from_adyen',
        return_value=adyen_success_response,
    )
    def test_create_adyen_session(
        self,
        mocked_adyen,
        api_client,
        validated_order,
    ):
        transactions_before = Transaction.objects.count()
        url = reverse('payment-session')
        api_client.force_authenticate(validated_order.owner)
        response = api_client.post(url)
        transactions_after = Transaction.objects.count()
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data.get('session', {}).get(
            'sessionId'
        ) == adyen_success_response.get('id')
        assert response.data.get('session', {}).get(
            'sessionData'
        ) == adyen_success_response.get('sessionData')
        assert transactions_before + 1 == transactions_after
        assert Transaction.objects.filter(
            order=validated_order,
        ).exists()

    @patch(
        'checkout.services.adyen_session.AdyenSessionHandler'
        '.get_session_data_from_adyen',
        return_value=adyen_success_response,
    )
    @patch('orders.services.process_to_production.MoveOrderToProduction.move')
    def test_create_adyen_session_for_multiple_existing_transactions_end_to_end(
        self,
        mocked_adyen,
        mocked_process_to_production,
        api_client,
        validated_order,
        transaction_factory,
    ):
        transaction_factory(
            order=validated_order, merchant_reference=validated_order.adyen_reference
        )
        transaction_factory(
            order=validated_order, merchant_reference=validated_order.adyen_reference
        )
        url = reverse('payment-session')
        api_client.force_authenticate(validated_order.owner)
        api_client.post(url)
        adyen_webhook_response['merchantReference'] = validated_order.adyen_reference
        api_client.post(reverse('payment-notifications'), data=adyen_webhook_response)

        validated_order.refresh_from_db()

        assert mocked_process_to_production.call_count == 1
        assert validated_order.paid_at
        assert adyen_webhook_response[
            'eventCode'
        ] in validated_order.transactions.values_list('status', flat=True)

    @patch(
        'checkout.services.adyen_session.AdyenSessionHandler'
        '.get_session_data_from_adyen',
        return_value=adyen_success_response,
    )
    @patch('orders.services.process_to_production.MoveOrderToProduction.move')
    def test_success_end_to_end_payment(
        self,
        mocked_adyen,
        mocked_process_to_production,
        api_client,
        validated_order,
    ):
        """
        1. Create session and transaction
        2. Hit notification webhook
        3. Check order, transaction and mocked process_to_production
        """
        url = reverse('payment-session')
        api_client.force_authenticate(validated_order.owner)
        api_client.post(url)
        adyen_webhook_response['merchantReference'] = validated_order.adyen_reference
        api_client.post(reverse('payment-notifications'), data=adyen_webhook_response)

        validated_order.refresh_from_db()

        assert mocked_process_to_production.call_count == 1
        assert validated_order.paid_at
        assert validated_order.transactions.count() == 1
        assert (
            validated_order.transactions.first().status
            == adyen_webhook_response['eventCode']
        )

    @patch(
        'checkout.services.adyen_session.AdyenSessionHandler'
        '.get_session_data_from_adyen',
        return_value=adyen_success_response,
    )
    @patch('orders.services.process_to_production.MoveOrderToProduction.move')
    def test_end_to_end_first_failed_second_success(
        self,
        mocked_adyen,
        mocked_process_to_production,
        api_client,
        validated_order,
    ):
        url = reverse('payment-session')
        api_client.force_authenticate(validated_order.owner)
        api_client.post(url)
        negative_adyen_response = copy(adyen_webhook_response)
        negative_adyen_response['success'] = 'false'
        negative_adyen_response['merchantReference'] = validated_order.adyen_reference
        api_client.post(reverse('payment-notifications'), data=negative_adyen_response)

        validated_order.refresh_from_db()

        assert not validated_order.paid_at

        adyen_webhook_response['merchantReference'] = validated_order.adyen_reference
        api_client.post(reverse('payment-notifications'), data=adyen_webhook_response)
        validated_order.refresh_from_db()
        assert validated_order.paid_at
        assert mocked_process_to_production.call_count == 1

    @patch(
        'checkout.services.adyen_session.AdyenSessionHandler'
        '.get_session_data_from_adyen',
        return_value=adyen_success_response,
    )
    @patch('orders.services.process_to_production.MoveOrderToProduction.move')
    def test_end_to_end_first_success_second_failed(
        self,
        mocked_adyen,
        mocked_process_to_production,
        api_client,
        validated_order,
    ):
        url = reverse('payment-session')
        api_client.force_authenticate(validated_order.owner)
        api_client.post(url)

        adyen_webhook_response['merchantReference'] = validated_order.adyen_reference
        api_client.post(reverse('payment-notifications'), data=adyen_webhook_response)
        validated_order.refresh_from_db()
        assert validated_order.paid_at

        negative_adyen_response = copy(adyen_webhook_response)
        negative_adyen_response['success'] = 'false'
        api_client.post(reverse('payment-notifications'), data=negative_adyen_response)
        validated_order.refresh_from_db()
        assert validated_order.paid_at
        assert mocked_process_to_production.call_count == 1
