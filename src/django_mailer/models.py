import datetime

from django.db import models

from django_mailer import (
    constants,
    managers,
)
from django_mailer.managers import (
    MessageQuerySet,
    QueueQuerySet,
)


class Priorities(models.IntegerChoices):
    HIGH = constants.PRIORITY_HIGH, 'high'
    NORMAL = constants.PRIORITY_NORMAL, 'normal'
    LOW = constants.PRIORITY_LOW, 'low'


class ResultCodes(models.IntegerChoices):
    SUCCESS = constants.RESULT_SENT, 'success'
    NOT_SENT_BLACKLISTED = constants.RESULT_SKIPPED, 'not sent (blacklisted)'
    FAILURE = constants.RESULT_FAILED, 'failure'


class Message(models.Model):
    """
    An email message.

    The ``to_address``, ``from_address`` and ``subject`` fields are merely for
    easy of access for these common values. The ``encoded_message`` field
    contains the entire encoded email message ready to be sent to an SMTP
    connection.

    """

    to_address = models.Char<PERSON><PERSON>(max_length=200)
    from_address = models.Char<PERSON><PERSON>(max_length=200)
    subject = models.CharField(max_length=998)

    encoded_message = models.TextField()
    date_created = models.DateTimeField(default=datetime.datetime.now)
    extra_headers = models.JSONField(default=dict)

    objects = managers.QueueManager.from_queryset(MessageQuerySet)()

    class Meta(object):
        ordering = ('date_created',)

    def __str__(self):
        return '{}: {}'.format(self.to_address, self.subject)


class QueuedMessage(models.Model):
    """
    A queued message.

    Messages in the queue can be prioritised so that the higher priority
    messages are sent first (secondarily sorted by the oldest message).

    """

    message = models.OneToOneField(
        Message,
        editable=True,
        on_delete=models.CASCADE,
    )
    priority = models.PositiveSmallIntegerField(
        choices=Priorities.choices,
        default=constants.PRIORITY_NORMAL,
    )
    deferred = models.DateTimeField(null=True, blank=True)
    retries = models.PositiveIntegerField(default=0)
    date_queued = models.DateTimeField(default=datetime.datetime.now)

    objects = managers.QueueManager.from_queryset(QueueQuerySet)()

    class Meta(object):
        ordering = ('priority', 'date_queued')

    def defer(self):
        self.deferred = datetime.datetime.now()
        self.save()


class Blacklist(models.Model):
    """
    A blacklisted email address.

    Messages attempted to be sent to e-mail addresses which appear on this
    blacklist will be skipped entirely.

    """

    email = models.EmailField(max_length=200)
    date_added = models.DateTimeField(default=datetime.datetime.now)

    class Meta(object):
        ordering = ('-date_added',)
        verbose_name = 'blacklisted e-mail address'
        verbose_name_plural = 'blacklisted e-mail addresses'


class Log(models.Model):
    """
    A log used to record the activity of a queued message.

    """

    message = models.ForeignKey(
        Message,
        editable=False,
        on_delete=models.CASCADE,
    )
    result = models.PositiveSmallIntegerField(choices=ResultCodes.choices)
    date = models.DateTimeField(default=datetime.datetime.now)
    log_message = models.TextField()

    class Meta(object):
        ordering = ('-date',)
