from django.contrib.auth.models import User
from django.utils import timezone

import simplejson as json

from custom.enums import (
    Furniture,
    ShelfType,
)
from orders.models import Order


def create_survey_data(user: User, input_data: dict) -> dict:
    data = {
        'created_at': timezone.now().isoformat(),
        'user_id': 'anonymous',
        'user_email': '',
        'country': '',
        'city': '',
        'user_returning': None,
        'rate': input_data['score'],
        'comment': input_data['feedback'],
        'language': input_data['lang'],
    }
    if user.is_authenticated:
        data['user_id'] = user.id
        data['user_email'] = user.email or ''
        if user.profile:
            data['country'] = user.profile.country
            data['city'] = user.profile.city

    return data


def dumps_checkout_survey_data(user: User, data: dict, device_type: str) -> str:
    order = Order.objects.get(id=data['order_id'])
    categories = []
    product_lines = []
    sizes = []
    for item in order.items.all():
        furniture = item.sellable_item
        product_line = ShelfType(furniture.shelf_type).product_line
        if (
            hasattr(furniture, 'shelf_category')
            and furniture.shelf_category not in categories
        ):
            categories.append(furniture.shelf_category)
        if product_line not in product_lines:
            product_lines.append(product_line)
        sizes.append(furniture.get_size())

    survey_data = create_survey_data(user, input_data=data) | {
        'device': device_type,
        'payment_method': order.chosen_payment_method,
        'total_region_price': order.region_total_price,
        'currency': order.region.currency.code,
        'amount_of_product_purchased': order.items.count(),
        'furniture_categories': categories,
        'product_lines': product_lines,
        'furniture_sizes': sizes,
    }

    return json.dumps(survey_data, use_decimal=True)


def dumps_configurator_survey_data(user: User, data: dict, ab_tests: dict) -> str:
    model = Furniture(data['item_content_type']).model
    furniture = model.objects.get(id=data['item_id'])
    furniture_category = (
        furniture.shelf_category if hasattr(furniture, 'shelf_category') else ''
    )
    survey_data = create_survey_data(user=user, input_data=data) | {
        'furniture_id': data['item_id'],
        'furniture_type': data['item_content_type'],
        'furniture_category': furniture_category,
        'shelf_type': ShelfType(furniture.shelf_type).name,
        'configurator_type': furniture.configurator_type,
        'ab_tests': ab_tests,
    }

    return json.dumps(survey_data)
