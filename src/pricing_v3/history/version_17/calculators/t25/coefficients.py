from decimal import Decimal
from typing import Optional

base_coefs = {
    't25_base_unit': Decimal('41.3206'),
    't25_base_area': Decimal('26'),
}


element_coefs = {
    't25_wall_unit': Decimal('40.89'),
    't25_wall_area': Decimal('129.1596'),
    't25_slab_unit': <PERSON><PERSON><PERSON>('26.8695'),
    't25_slab_area': <PERSON><PERSON><PERSON>('107.7191'),
    't25_door_unit': Decimal('52.2804'),
    't25_door_perimeter': Decimal('42.2355'),
    't25_backs_unit': Decimal('29.016'),
    't25_backs_area': Decimal('72.883'),
    't25_drawer_width': Decima<PERSON>('102.2505'),
    't25_drawer_unit': <PERSON><PERSON><PERSON>('279.5522'),
    't25_logs_vol': Decimal('583.0266'),
    't25_logs_drawers': Decimal('20'),
    't25_logs_base': Decimal('88.0644'),
    't25_cable_management_unit': Decimal('25.5068'),
    't25_kielbasa_legs_length': Decima<PERSON>('312.227'),
    't25_kielbasa_legs_unit': Decimal('29.75'),
}


margin_coefs = {
    't25_margin_sideboard': Decimal('0'),
    't25_margin_tvstand': Decimal('0'),
    't25_margin_chest': Decimal('0'),
    't25_margin_bedside_table': Decimal('0'),
    't25_margin_wardrobe': Decimal('0'),
}

additional_factors = {
    't25_additional_increase': Decimal('1.6324'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']

    if overrides:
        coefficients.update(overrides)

    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **margin_coefs,
        **element_coefs,
        **additional_factors,
    }

    return {
        'coefficients': coefficients,
    }
