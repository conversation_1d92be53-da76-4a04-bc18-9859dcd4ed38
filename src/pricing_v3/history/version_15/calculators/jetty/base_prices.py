import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import Jetty


def _get_jetty_front_area(jetty: 'Jetty') -> Decimal:
    width_m = Decimal(jetty.width) / 1000
    height_m = Decimal(jetty.height) / 1000
    return height_m * width_m


def calculate_base_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like legs, joints etc."""
    front_area = _get_jetty_front_area(jetty)
    return {
        'base': front_area * price_coefficients['base'],
    }


def calculate_logistics_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    front_area = _get_jetty_front_area(jetty)
    logistic_cost = (
        front_area * price_coefficients['logistic_front_area']
        + len(jetty.drawers) * price_coefficients['logistic_drawers']
        + price_coefficients['logistic_base']
    )
    return {'logistic': logistic_cost}
