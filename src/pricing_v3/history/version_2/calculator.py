from pricing_v3.history.unified_interface import UnifiedPricingInterface

from .jetty_pricing import calculate_price as jetty_calculator
from .t03_pricing import calculate_price as t03_calculator
from .t13_pricing import calculate_price as t13_calculator


class PricingCalculator(UnifiedPricingInterface):
    def jetty_calculator(self, geometry):
        return jetty_calculator(geometry, self.region_name, self.coefficients)

    def t03_calculator(self, geometry):
        return t03_calculator(geometry, self.region_name, self.coefficients)

    def t13_calculator(self, geometry):
        return t13_calculator(geometry, self.region_name, self.coefficients)
