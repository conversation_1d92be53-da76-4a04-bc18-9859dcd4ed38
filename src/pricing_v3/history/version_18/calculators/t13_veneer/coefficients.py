from decimal import Decimal
from typing import Optional

FACTOR_EURO = Decimal('4.3')

base_coefs = {
    'type13_veneer_base_unit': Decimal('54.9059'),
    'type13_veneer_base_area': Decimal('16.1588'),
}

type13_veneer_coefs = {
    'type13_veneer_wall_unit': Decimal('22.6979'),
    'type13_veneer_t01_wall_area': Decimal('253.87'),
    'type13_veneer_t02_wall_area': Decimal('73.93'),
    'type13_veneer_slab_unit': Decimal('19.3694'),
    'type13_veneer_slab_area': Decimal('78.9872'),
    'type13_veneer_door_unit': Decimal('48.0713'),
    'type13_veneer_door_perimeter': Decimal('20.1187'),
    'type13_veneer_backs_unit': Decimal('20.1504'),
    'type13_veneer_backs_area': Decimal('73.0940'),
    'type13_veneer_bar_unit': Decimal('13.1048'),
    'type13_veneer_bar_length': Decima<PERSON>('30.1818'),
    'type13_veneer_crossbar_unit': Decimal('55.3800'),
    'type13_veneer_drawer_width': Decimal('69.7642'),
    'type13_veneer_internal_drawer_unit': Decimal('170.8268'),
    'type13_veneer_external_drawer_unit': Decimal('145.7164'),
    'type13_veneer_logs_vol': Decimal('85.0000'),
    'type13_veneer_logs_drawers': Decimal('0'),
    'type13_veneer_logs_base': Decimal('0'),
    'type13_veneer_cable_management_unit': Decimal('16.61902'),
}

margin_coefs = {
    # categories for Wood Egde
    'type13_veneer_margin_wardrobe': Decimal('2.472'),
    'type13_veneer_margin_bookcase': Decimal('2.5961'),
    'type13_veneer_margin_wallstorage': Decimal('2.5961'),
    'type13_veneer_margin_chest': Decimal('2.3586'),
}

additional_factors = {
    'type_13_veneer_additional_increase': Decimal('0.4432'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']

    if overrides:
        coefficients.update(overrides)

    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **margin_coefs,
        **type13_veneer_coefs,
        **additional_factors,
    }

    return {
        'coefficients': coefficients,
    }
