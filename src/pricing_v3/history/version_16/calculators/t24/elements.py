import typing

from decimal import Decimal

from custom.enums import (
    Axis,
    ShelfType,
)

if typing.TYPE_CHECKING:
    from gallery.models import Watty

Element = dict[str, int]
ElementsList = list[Element]


def compute_length(element: Element, axis: Axis) -> Decimal:
    """Compute element length along an axis and convert it to meters."""
    return abs(Decimal(element[f'{axis}2']) - Decimal(element[f'{axis}1'])) / 1000


def calculate_area(elements: ElementsList, axis1: Axis, axis2: Axis) -> Decimal:
    return sum(
        compute_length(element, axis1) * compute_length(element, axis2)
        for element in elements
    )


def sum_lengths(elements: ElementsList, axis: Axis) -> Decimal:
    return sum(compute_length(element, axis) for element in elements)


class T24ElementsPriceCalculator:
    def __init__(self, watty: 'Watty', price_coefficients: dict) -> None:
        self.watty = watty
        self._pc = price_coefficients

    def calculate_walls_price(self) -> Decimal:
        walls = self.watty.walls
        if not walls:
            return Decimal()

        return (
            len(walls) * self._pc['t24_wall_unit']
            + calculate_area(walls, Axis.Y, Axis.Z) * self._pc['t24_wall_area']
        )

    def calculate_slabs_price(self) -> Decimal:
        slabs = self.watty.slabs
        if not slabs:
            return Decimal()

        return (
            len(slabs) * self._pc['t24_slab_unit']
            + calculate_area(slabs, Axis.X, Axis.Z) * self._pc['t24_slab_area']
        )

    def calculate_doors_price(self) -> Decimal:
        doors = self.watty.doors
        if not doors:
            return Decimal()

        doors_perimeter = sum(
            (compute_length(door, Axis.X) + compute_length(door, Axis.Y)) * 2
            for door in doors
        )
        return (
            len(doors) * self._pc['t24_door_unit']
            + doors_perimeter * self._pc['t24_door_perimeter']
        )

    def calculate_backs_price(self) -> Decimal:
        backs = self.watty.backs
        if not backs:
            return Decimal()

        return (
            len(backs) * self._pc['t24_backs_unit']
            + calculate_area(backs, Axis.X, Axis.Y) * self._pc['t24_backs_area']
        )

    def calculate_kielbasa_price(self) -> Decimal:
        if self.watty.shelf_type != ShelfType.TYPE25:
            return Decimal()
        legs = self.watty.legs
        legs_height = sum_lengths(legs, Axis.Y)
        return (
            len(legs) * self._pc['t24_kielbasa_legs_unit']
            + legs_height * self._pc['t24_kielbasa_legs_length']
        )

    def calculate_drawers_price(self) -> Decimal:
        drawers = self.watty.drawers
        drawers_length = sum_lengths(drawers, Axis.X)

        drawers_price = (
            len(drawers) * self._pc['t24_drawer_unit']
            + drawers_length * self._pc['t24_drawer_width']
        )

        return drawers_price

    def calculate_cable_managements_price(self) -> Decimal:
        """Cable management price is constant."""
        cable_management = self.watty.cable_management
        return len(cable_management) * self._pc.get(
            't24_cable_management_unit', Decimal('0')
        )
