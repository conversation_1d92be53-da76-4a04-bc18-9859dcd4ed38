# Generated by Django 3.2.12 on 2022-03-31 14:49

from django.db import migrations

from pricing_v3.calculators.coefficients import get_coefficients


def create_initial_pricing_version(apps, schema_editor):
    PricingVersion = apps.get_model('pricing_v3', 'PricingVersion')
    Region = apps.get_model('regions', 'Region')
    for region in Region.objects.filter(name__in={'switzerland', 'germany'}):
        PricingVersion(coefficients=get_coefficients(region), region=region).save()
    PricingVersion(coefficients=get_coefficients(region_name=None)).save()


class Migration(migrations.Migration):

    dependencies = [
        ('pricing_v3', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(
            create_initial_pricing_version,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
