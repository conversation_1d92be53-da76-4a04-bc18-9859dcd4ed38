import pytest

from custom.enums import Sample<PERSON>oxVariantEnum
from warehouse.enums import (
    SampleBoxElementType,
    StockSampleBoxType,
)
from warehouse.models import (
    SampleBoxElement,
    StockSampleBox,
    StockSampleBoxEventLog,
)
from warehouse.tools import SampleManager


@pytest.mark.django_db
class TestSampleBoxElementsDelivery:
    def test_make_delivery_creates_stock_sample_box(
        self,
        sample_box_element,
        sample_box_elements_delivery_factory,
        sample_box_elements_delivery_position_factory,
    ):
        delivery = sample_box_elements_delivery_factory(is_booked=False)
        sample_box_elements_delivery_position_factory(
            quantity=100,
            delivery=delivery,
            element_type=sample_box_element,
        )
        stocks_in = StockSampleBox.objects.filter(
            stock_type=StockSampleBoxType.IN_STOCK.value
        ).first()

        assert stocks_in.quantity == 0

        delivered = delivery.make_delivery(user=None)
        stocks_in.refresh_from_db()

        assert delivered is True
        assert stocks_in.quantity == 100
        assert stocks_in.type_resource == sample_box_element


@pytest.mark.django_db
class TestStockSampleBox:
    def test_add_element_to_in_stock_adds_to_stock_quantity_and_creates_event_log(
        self,
        sample_box_element,
    ):
        stock = StockSampleBox.objects.filter(
            type_resource=sample_box_element,
            stock_type=StockSampleBoxType.IN_STOCK.value,
        ).last()
        value = 50
        StockSampleBox.add_element_to_in_stock(
            element=sample_box_element,
            value=value,
            user=None,
            source='TEST_IN',
        )
        event_log = StockSampleBoxEventLog.objects.filter(
            stock=stock,
            value=value,
            source='TEST_IN',
            user=None,
        )
        stock.refresh_from_db()

        assert stock.quantity == value
        assert event_log.count() == 1

    def test_take_from_in_stock_subtracts_from_stock_quantity_and_creates_event_log(
        self,
        sample_box_element,
    ):
        value = 50
        StockSampleBox.take_from_in_stock(
            element=sample_box_element,
            value=value,
            source='TEST_TAKE',
            user=None,
        )
        stock = StockSampleBox.objects.get(
            type_resource=sample_box_element,
            stock_type=StockSampleBoxType.IN_STOCK.value,
        )
        event_log = StockSampleBoxEventLog.objects.filter(
            stock=stock,
            value=-value,
            source='TEST_TAKE',
            user=None,
        )

        assert stock.quantity == -value
        assert event_log.count() == 1

    def test_put_to_out_stock_adds_to_stock_quantity(
        self,
        sample_box_element,
    ):
        stock = StockSampleBox.objects.filter(
            type_resource=sample_box_element,
            stock_type=StockSampleBoxType.TO_SEND.value,
        ).last()
        value = 50
        StockSampleBox.add_element_to_out_stock(
            element=sample_box_element,
            value=value,
            user=None,
            source='TEST_OUT',
        )
        stock.refresh_from_db()
        assert stock.quantity == value

    def test_take_from_out_stock_subtracts_from_stock_quantity(
        self,
        sample_box_element,
    ):
        stock = StockSampleBox.objects.filter(
            type_resource=sample_box_element,
            stock_type=StockSampleBoxType.TO_SEND.value,
        ).last()
        value = 50
        StockSampleBox.take_from_out_stock(
            element=sample_box_element,
            value=value,
            user=None,
            source='TEST_OUT',
        )
        stock.refresh_from_db()
        assert stock.quantity == -value


@pytest.mark.django_db
class TestSampleManager:
    @pytest.mark.parametrize(
        'element_type',
        [
            SampleBoxElementType.MATERIAL.value,
            SampleBoxElementType.STICKER.value,
            SampleBoxElementType.OTHERS.value,
        ],
    )
    def test_sample_manager_takes_elements_from_stock_every_element_type(
        self,
        user,
        sample_box_element_factory,
        sample_box_variant_factory,
        sample_box_factory,
        element_type,
    ):
        created_elements = 4
        sample_box_variant = SampleBoxVariantEnum.TYPE01_CLASSIC.value
        box_variant = sample_box_variant_factory(variant_type=sample_box_variant)
        sample_box_element_factory.create_batch(
            created_elements,
            box_variants=(box_variant,),
            element_type=element_type,
        )
        sample_box_gallery = sample_box_factory(owner=user, box_variant=box_variant)
        SampleManager.take_sample_from_stock(
            sample_box=sample_box_gallery, order_id=100, user=user, quantity=1
        )
        sample_box_elements = SampleBoxElement.objects.filter(box_variant=box_variant)
        stocks_in = StockSampleBox.objects.filter(
            stock_type=StockSampleBoxType.IN_STOCK.value,
            type_resource__in=sample_box_elements,
        )
        stocks_out = StockSampleBox.objects.filter(
            stock_type=StockSampleBoxType.TO_SEND.value,
            type_resource__in=sample_box_elements,
        )

        assert sample_box_elements.count() == created_elements
        assert all(stock.quantity == -1 for stock in stocks_in)
        assert all(stock.quantity == 1 for stock in stocks_out)
