# Generated by Django 4.1.13 on 2024-09-13 14:24

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0013_alter_sampleboxvariant_variant_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='sampleboxelementsdeliveryposition',
            name='price_gross',
        ),
        migrations.RemoveField(
            model_name='sampleboxelementsdeliveryposition',
            name='price_net',
        ),
        migrations.AddField(
            model_name='sampleboxelement',
            name='codename',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='sampleboxvariant',
            name='current_cost',
            field=models.DecimalField(
                decimal_places=2,
                max_digits=5,
                null=True,
                verbose_name='Current cost (PLN)',
            ),
        ),
        migrations.AddField(
            model_name='sampleboxvariant',
            name='status',
            field=models.CharField(
                choices=[
                    ('archived', 'Archived'),
                    ('active', 'Active'),
                    ('on_hold', 'On Hold'),
                    ('implementation', 'Implementation'),
                ],
                default='active',
                max_length=128,
            ),
        ),
        migrations.CreateModel(
            name='SampleBoxElementCost',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'cost',
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name='Element cost (PLN)',
                    ),
                ),
                ('date_from', models.DateField()),
                ('date_to', models.DateField(blank=True, null=True)),
                (
                    'sample_box_element',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='warehouse.sampleboxelement',
                    ),
                ),
            ],
        ),
    ]
