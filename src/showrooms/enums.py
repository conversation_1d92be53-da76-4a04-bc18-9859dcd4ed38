from django.db import models


class ShowRoomItemEnum(models.TextChoices):
    EDGE = 'edge', 'Edge'
    TONE = 'tone', 'Tone'
    ORIGINAL_CLASSIC = 'original_classic', 'Original Classic'
    ORIGINAL_MODERN = 'original_modern', 'Original Modern'
    SMOOTH_SOFA = 'smooth_sofa', 'Smooth Sofa'


class ShowRoomKind(models.TextChoices):
    SHOWROOM = 'showroom'
    FRIENDS = 'friends'


def get_default_show_room_items():
    return list(choice.value for choice in ShowRoomItemEnum)
