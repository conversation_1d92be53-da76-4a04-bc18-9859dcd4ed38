from django import forms

from showrooms.enums import ShowRoomItemEnum
from showrooms.models import ShowRoom


class ShowRoomForm(forms.ModelForm):
    showroom_items = forms.MultipleChoiceField(
        choices=ShowRoomItemEnum.choices,
        widget=forms.SelectMultiple,
        required=False,
        label='Showroom Items',
        initial=[choice.value for choice in ShowRoomItemEnum],
    )

    class Meta:
        model = ShowRoom
        fields = (
            'postal_code',
            'city',
            'street_address',
            'phone',
            'phone_prefix',
            'email',
            'latitude',
            'longitude',
            'name',
            'kind',
            'link',
            'display_image',
            'region',
            'mon_open_time',
            'mon_close_time',
            'tue_open_time',
            'tue_close_time',
            'wed_open_time',
            'wed_close_time',
            'thu_open_time',
            'thu_close_time',
            'fri_open_time',
            'fri_close_time',
            'sat_open_time',
            'sat_close_time',
            'sun_open_time',
            'sun_close_time',
            'showroom_items',
        )
