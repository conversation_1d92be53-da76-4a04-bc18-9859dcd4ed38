export default defineNuxtPlugin(() => {
  if (typeof window === 'undefined') { return; }
  const {
    public: { googleMapsApiKey }
  } = useRuntimeConfig();
  // <PERSON><PERSON><PERSON> ju<PERSON>, nie ładuj ponownie
  if (document.getElementById('google-maps-script')) { return; }
  const script = document.createElement('script');
  script.id = 'google-maps-script';
  script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
  script.async = true;
  document.head.appendChild(script);
});
