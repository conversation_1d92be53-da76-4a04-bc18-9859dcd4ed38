import Toast, { type PluginOptions, POSITION, TYPE } from 'vue-toastification';
import 'vue-toastification/dist/index.css';
import CloseToast from '@/assets/icons/close-toast.svg';
import ErrorIcon from '@/assets/icons/error.svg';
import SuccessIcon from '@/assets/icons/success.svg';
import InfoIcon from '@/assets/icons/info.svg';

const options: PluginOptions = {
  position: POSITION.BOTTOM_CENTER,
  timeout: 5000,
  transition: 'Vue-Toastification__fade',
  maxToasts: 10,
  newestOnTop: true,
  // @ts-ignore
  class: 'ty-toast',
  closeButton: CloseToast,
  toastDefaults: {
    [TYPE.INFO]: {
      icon: InfoIcon
    },
    [TYPE.SUCCESS]: {
      icon: SuccessIcon
    },
    [TYPE.WARNING]: {
      icon: InfoIcon
    },
    [TYPE.ERROR]: {
      icon: ErrorIcon
    }
  }
};

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(Toast, { ...options });
});
