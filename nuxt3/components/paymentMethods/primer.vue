<template>
  <div>
    <p
      v-if="errorMessage"
      v-html="$t('checkout.payment_methods.technical_error')"
    />
    <p
      v-if="isCheckoutLoading"
      v-html="$t('checkout.payment_methods.please_wait')"
    />
    <div
      v-show="!errorMessage"
      id="primer-container"
      ref="primerContainer"
    />
  </div>
</template>

<script setup lang="ts">
import { Primer, PaymentMethodType } from '@primer-io/checkout-web';
import { useToast } from 'vue-toastification';

import {
  CHANGE_STATUS_TO_PENDING,
  CHANGE_STATUS_TO_DRAFT,
  PAYMENTS_PRIMER
} from '~/api/checkout';

const locale = {
  fr: 'fr_FR',
  de: 'de-DE',
  en: 'en-GB',
  es: 'es-ES'
};

const toast = useToast();
const { $i18n, $logException } = useNuxtApp();
const isCheckoutLoading = ref(true);
const errorMessage = ref(false);
const primerContainer = ref(null);
const global = useGlobal();
const { locale: lang } = useLocale();
let redirectTimeout;

const props = defineProps({
  isBusinessClient: {
    type: Boolean,
    default: false
  }
});

const changeStatusToPending = async () => {
  const { error, execute } = CHANGE_STATUS_TO_PENDING(global);
  await execute();

  if (error.value) {
    toast.error($i18n.t('common.error.connection'));
    $logException(error.value);
  }
};

const bootstrapPrimerCheckout = async (clientToken: any) => {
  try {
    await Primer.showUniversalCheckout(
      clientToken,
      {
        container: primerContainer.value,
        locale: locale[lang.value],
        allowedPaymentMethods: getAllowedPaymentMethods(),
        async onBeforePaymentCreate (data, handler) {
          // Notifies you that a payment will be created
          // Update your UI accordingly
          // e.g. Show custom loading UI, etc.
          // Abort or continue with payment creation
          // Primer will continue with payment creation if onBeforePaymentCreate is not implemented

          // ⚠️ You MUST call one of the functions of the `handler` if `onBeforePaymentCreate` is implemented
          if (data.paymentMethodType !== 'PAYPAL') {
            const { error, execute } = await CHANGE_STATUS_TO_PENDING(global);
            await execute();

            if (error.value) {
              toast.error($i18n.t('common.error.connection'));
              $logException(error.value);
              return handler.abortPaymentCreation();
            } else {
              return handler.continuePaymentCreation();
            }
          } else {
            return handler.continuePaymentCreation();
          }
        },
        async onCheckoutComplete () {
          await changeStatusToPending();
          redirectTimeout = setTimeout(() => {
            window.location.href = `${window.location.origin}/${$i18n.locale.value}/confirmation/${global.orderId}/?uuid=${global.userId}`;
            // await router.push({ name: `confirmation-orderId___${i18n.locale}`, params: { orderId: orderId.value }, query: { uuid: userId.value } });
          }, 1000);
        },
        async onCheckoutFail (_, __, handler) {
          const { error, execute } = CHANGE_STATUS_TO_DRAFT(global);
          await execute();

          if (error.value) {
            toast.error($i18n.t('common.error.connection'));
            $logException(error.value);
            return handler?.showErrorMessage();
          } else {
            return handler?.showErrorMessage();
          }
        }
      }
    );
  } catch (e: any) {
    toast.error($i18n.t('common.error.connection'));
    errorMessage.value = true;
    $logException(e);
  }
};

const getAllowedPaymentMethods = () => {
  const paymentMethods = { ...PaymentMethodType };

  if (props.isBusinessClient) {
    delete paymentMethods[PaymentMethodType.KLARNA];
    delete paymentMethods[PaymentMethodType.ADYEN_KLARNA];
  }

  paymentMethods.ADYEN_IDEAL = 'ADYEN_IDEAL';

  return Object.values(paymentMethods);
};

onMounted(async () => {
  const { error, execute, data } = PAYMENTS_PRIMER(global);
  await execute();

  if (error.value) {
    toast.error($i18n.t('common.error.connection'));
    $logException(error.value);
  } else if (data.value) {
    await bootstrapPrimerCheckout(data.value?.clientToken);
    isCheckoutLoading.value = false;
  }
});
onBeforeUnmount(() => {
  if (redirectTimeout) { clearTimeout(redirectTimeout); }
});
</script>
