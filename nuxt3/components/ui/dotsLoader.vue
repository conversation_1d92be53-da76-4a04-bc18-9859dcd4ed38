<template>
  <div
    class="dots-spinner
            left-1/2 top-1/2 absolute transform -translate-x-1/2 -translate-y-1/2 opacity-100
            transition-opacity duration-300 ease-out delay-[1500ms]"
  >
    <div
      class="bounce1"
      v-bind:class="bounceClass"
    />
    <div
      class="bounce2"
      v-bind:class="bounceClass"
    />
    <div
      class="bounce3"
      v-bind:class="bounceClass"
    />
  </div>
</template>

<script setup lang="ts">
defineProps({
  bounceClass: {
    type: String,
    default: 'bg-orange'
  }
});
</script>
