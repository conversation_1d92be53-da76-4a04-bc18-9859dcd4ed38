<template>
  <Component
    v-bind:is="selector"
    v-bind:class="[selectorClasses, { 'is-checked': isChecked , 'with-plus-icon': withPlusIcon }, `variant-${variant}`]"
    class="relative"
  >
    <span v-html="content" />
    <span
      v-if="badge.length"
      class="absolute top-0 ml-4 text-orange uppercase bold-10 !leading-1"
      v-html="badge"
    />
  </Component>
</template>

<script setup lang="ts">
defineProps({
  selector: {
    type: String,
    default: 'h3'
  },
  badge: {
    type: String,
    default: ''
  },
  selectorClasses: {
    type: String,
    default: 'relative'
  },
  isChecked: {
    type: Boolean,
    default: false
  },
  withPlusIcon: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    required: true
  },
  variant: {
    type: String,
    default: 'right',
    validation: variant => ['right', 'left'].includes(variant)
  }
});
</script>

<style lang="scss">

.with-plus-icon {
  padding-right: 10%;
  width: 100%;

  &::before,
  &::after {
    @apply absolute bg-grey-900 top-1/2;

    content: '';

    @apply ease-in-out duration-300;
  }

  &.variant-right {
    &::before {
      @apply w-[15px] lg:w-[21px] h-[1px] lg:h-[1px] right-0;

      opacity: 1;
    }

    &::after {
      @apply w-[1px] lg:w-[1px] h-[15px] lg:h-[21px] right-[7px] lg:right-[10px];

      margin-top: -7px;

      @screen lg {
        margin-top: -10px;
      }
    }

    &.is-checked {
      &::before,
      &::after {
        transform: rotate(90deg);
      }

      &::before {
        opacity: 0;
      }
    }
  }

  &.variant-left {
    @apply duration-300 ease-out transition-padding;

    &::before,
    &::after {
      @apply bg-black;
    }

    &:hover {
      @apply pl-32;
    }

    @apply pl-24;

    &::before {
      @apply w-2 h-[14px] left-[6px];

      margin-top: -8px;
    }

    &::after {
      @apply w-[14px] h-2 left-0;

      margin-top: -2px;
    }

    &.is-checked {
      &::before,
      &::after {
        transform: rotate(135deg);
      }
    }
  }
}
</style>
