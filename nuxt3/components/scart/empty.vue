<template>
  <div class="mx-16 py-12 flex-grow flex flex-col justify-center">
    <div class="text-center mb-32">
      <span
        class="block text-16 text-grey-900"
        data-testid="scart-empty-text-upper"
        v-html="$t('scart.empty_cart')"
      />
      <span
        class="block text-16 text-grey-900"
        data-testid="scart-empty-text-lower"
        v-html="$t('scart.empty_add_new_design')"
      />
    </div>
    <BaseLink
      variant="accent"
      v-bind="{ href: $addLocaleToPath('plp'), trackData: {} }"
      class="uppercase w-full"
      data-testid="scart-empty-link"
    >
      {{ $t('scart.shop_now') }}
    </BaseLink>
  </div>
</template>

<script setup lang="ts">
const { $addLocaleToPath } = useNuxtApp();
</script>
