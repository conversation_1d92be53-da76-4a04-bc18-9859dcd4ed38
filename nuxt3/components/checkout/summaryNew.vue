<template>
  <Transition name="fade">
    <div
      v-if="!isNil(cart.cartItems) && cart.cartItems.length"
    >
      <div class="lg-max:hidden flex items-center justify-between">
        <h2
          class="bold-20 text-offblack-600"
          data-testid="summary-title"
        >
          {{ $t('cart.order_summary') }}
        </h2>
        <button
          v-if="displayItemsList"
          class="normal-14 text-orange border-b border-orange"
          data-testid="summary-show-hide-items"
          v-on:click="showProducts = !showProducts"
        >
          {{ $t(`checkout.${ showProducts ? 'show' : 'hide' }.products_info`) }}
        </button>
      </div>
      <template v-if="displayItemsList">
        <Transition name="slide">
          <CheckoutItemsListNew
            v-if="displayItemsList && isDesktopViewport && !showProducts"
            class="pt-32"
            data-testid="summary-items-list"
            v-bind="{
              cartItems: cart.cartItems
            }"
          />
        </Transition>
      </template>
      <CheckoutSummaryDetailsNew
        v-if="!pending"
        data-testid="summary-details"
        v-bind:class=" [{ 'md-max:order-2': showItemListOnMobile }, summaryDetailsClasses]"
        v-bind="{
          disableHeading: !isDesktopViewport && showItemListOnMobile,
          displayNewsletter,
          displayDeliveryNotice,
          displayAssemblyPrice,
          displayCartButton,
        }"
      >
        <template #promocode>
          <CheckoutPromocodeNew
            v-if="displayPromoCode && formDisplay"
            data-testid="summary-promo"
            class="md-max:hidden py-16"
            v-bind="{
              displayPromoCodeRemoveButton,
              disablePromocodeAddition,
              eventCategory: 'checkout',
            }"
          />
        </template>
        <template #assembly>
          <CheckoutAssemblyTemporary
            v-if="formDisplay && displayAssembly"
            class="pt-24 border-t border-neutral-400 mt-24 mb-24 md-max:hidden"
          />
        </template>
      </CheckoutSummaryDetailsNew>
      <div
        v-else
        class="relative mt-24 h-48"
      >
        <UiDotsLoader
          class="w-full flex justify-center"
          bounce-class="bg-orange !w-16 !h-16 !mr-8"
        />
      </div>
      <div
        v-if="displayPaymentButton"
        class="mt-24"
      >
        <CheckoutPaymentButton
          v-if="paymentButtonLoader"
        />
        <div
          v-else-if="!paymentButtonLoader"
          class="relative h-48"
        >
          <UiDotsLoader
            bounce-class="bg-orange !w-16 !h-16 !mr-8"
          />
        </div>
      </div>
      <CheckoutTermsNote
        v-if="displayTerms"
        class="md-max:hidden"
      />
      <div
        v-bind:class="{ 'md-max:order-3': showItemListOnMobile }"
      >
        <slot />
      </div>

      <slot name="additional" />
      <div
        class="mt-24"
        v-bind:class="{ 'hidden md:block': mobileHidden }"
      >
        <CheckoutFeaturesDesktopNew
          v-if="!hasS01"
          class="text-neutral-900"
        />
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { isNil } from 'lodash-es';
import useMq from '~/composables/useMq';

import { useScartStore } from '~/stores/scart';
const cart = useScartStore();
const { isDesktopViewport } = useMq('lg');
const { paymentButtonLoader, formDisplay } = useAdyenDropin();
const showProducts = ref(true);
const global = useGlobal();
const { hasS01 } = storeToRefs(global);

defineProps({
  displayPromoCode: {
    type: Boolean,
    default: true
  },
  displayAssembly: {
    type: Boolean,
    default: true
  },
  displayPaymentButton: {
    type: Boolean,
    default: false
  },
  displayTerms: {
    type: Boolean,
    default: true
  },
  displayNewsletter: {
    type: Boolean,
    default: false
  },
  displayPromoCodeRemoveButton: {
    type: Boolean,
    default: true
  },
  disablePromocodeAddition: {
    type: Boolean,
    default: false
  },
  displayAssemblyRemoveButton: {
    type: Boolean,
    default: true
  },
  displayDeliveryNotice: {
    type: Boolean,
    default: false
  },
  disableAssemblyAddition: {
    type: Boolean,
    default: false
  },
  displayAssemblyPrice: {
    type: Boolean,
    default: false
  },
  showItemListOnMobile: {
    type: Boolean,
    default: false
  },
  displayItemsList: {
    type: Boolean,
    default: true
  },
  mobileHidden: {
    type: Boolean,
    default: true
  },
  summaryDetailsClasses: {
    type: String,
    default: 'p-0'
  },
  pending: {
    type: Boolean,
    default: false
  },
  displayCartButton: {
    type: Boolean,
    default: false
  }
});

</script>

<style scoped>
.slide-enter-active, .slide-leave-active {
  transition: max-height 0.5s ease, opacity 0.5s ease;
  overflow: hidden;
}

.slide-enter-from, .slide-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-enter-to, .slide-leave-from {
  max-height: 1000px;
  opacity: 1;
}
</style>
