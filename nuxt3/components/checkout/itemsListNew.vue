<template>
  <div class="border-b border-neutral-400 flex flex-col-reverse">
    <BaseLink
      v-if="isDesktopViewport"
      variant="underlined"
      class="mb-16 mt-8"
      v-bind="{
        href: `${$addLocaleToURL('/cart')}${userId ? '?uuid='+userId : ''}`,
        trackData: { eventLabel: 'view-cart' },
        'data-testid': 'view-cart'
      }"
    >
      {{ $t('checkout_edit_cart_button') }}
    </BaseLink>
    <div>
      <div
        v-for="(item, index) in cartItems"
        v-bind:key="index"
        class="last:pb-0"
        v-bind:class="{ 'pb-8': (!item.lighting && displayReminder) || !displayReminder }"
      >
        <CheckoutSampleItemNew
          v-if="item.itemFurnitureType === 'sample_box'"
          v-bind="{
            color: item.color,
            promotion: item.promotion,
            shelf_type: item.shelf_type,
            variant_type: item.variant_type,
            itemDescriptionMaterial: item.itemDescriptionMaterial,
            item_price_regionalized_number: item.item_price_regionalized_number,
            item_price_without_discount_regionalized_number: item.item_price_without_discount_regionalized_number
          }"
        >
          <template #price>
            <CheckoutPriceItem
              v-bind="{
                cartItem: item
              }"
            />
          </template>
        </CheckoutSampleItemNew>
        <template v-else>
          <CheckoutFurnitureItemNew
            v-bind="{
              contentType: item.content_type,
              itemId: item.itemId,
              width: item.width,
              depth: item.depth,
              height: item.height,
              promotion: item.promotion,
              item_price_without_discount_regionalized_number: item.item_price_without_discount_regionalized_number,
              item_price_regionalized_number: item.item_price_regionalized_number,
              region_price: item.region_price,
              itemFurnitureType: item.itemFurnitureType,
              patternName: item.pattern_name,
              category: item.category,
              shelfType: item.shelf_type,
              itemImageUrl: item.itemImageUrl,
              material: item.material,
              hasLighting: item.lighting,
              omnibusPrice: item.omnibus_price ? item.omnibus_price : false,
              quantity: item.quantity,
              is_strikethrough_promo: item.is_strikethrough_promo,
              region_price_with_discount: item.region_price_with_discount
            }"
          >
            <template #price>
              <CheckoutPriceItem
                v-bind="{
                  cartItem: item
                }"
              />
            </template>
          </CheckoutFurnitureItemNew>
          <ConfirmationReminder
            v-if="item.lighting && displayReminder"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobal } from '~/stores/global';
import useMq from '~/composables/useMq';
const { isDesktopViewport } = useMq('lg');
defineProps({
  cartItems: {
    type: Array,
    required: true
  },
  displayReminder: {
    type: Boolean,
    default: false
  }
});
const { userId } = storeToRefs(useGlobal());
</script>
