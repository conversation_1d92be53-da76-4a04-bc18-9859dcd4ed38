<template>
  <div
    ref="mobileSummaryBar"
    class="bg-neutral-200 mobile-summary-bar sticky top-48 flex w-screen
  py-[12px] px-[20px] justify-between z-2"
    data-testid="mobile-summary-bar"
    v-bind:onClick="onCtaClick"
  >
    <div class="flex w-full justify-between items-center ">
      <p class="semibold-16 text-offblack-800 whitespace-nowrap">
        {{ $t('cart.order_summary') }}&nbsp;
      </p>
      <IconCaretDown
        class="w-24 h-24 aspect-square transition-transform basic-transition"
        data-testid="mobile-summary-caret-icon"
        v-bind:class="isDrawerOpen && 'rotate-180'"
      />
    </div>
  </div>

  <TransitionRoot
    v-bind:show="isDrawerOpen"
    as="template"
  >
    <Dialog
      class="fixed inset-0 top-96 z-9
              border-t border-neutral-400
              overflow-y-scroll overflow-x-hidden overscroll-none"
      v-on:close="isDrawerOpen = false"
    >
      <TransitionChild
        enter="duration-300 cubic-bezier(0, 0, 0.58, 1)"
        leave="duration-200 cubic-bezier(0.25, 0.1, 0.25, 1)"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div
          class="absolute inset-0 bg-black bg-opacity-40"
          aria-hidden="true"
        />
      </TransitionChild>
      <TransitionChild
        class="absolute top-0 left-0 w-full"
        enter="duration-300 cubic-bezier(0, 0, 0.58, 1)"
        leave="duration-200 cubic-bezier(0.25, 0.1, 0.25, 1)"
        enter-from="-translate-y-full"
        enter-to="translate-y-0"
        leave-from="translate-y-0"
        leave-to="-translate-y-full"
      >
        <DialogPanel class="w-full h-full bg-white">
          <CheckoutItemsListNew
            class="pt-16 pr-16 bg-neutral-200"
            v-bind="{
              cartItems: cart.cartItems
            }"
          />
          <CheckoutSummaryNew
            class="bg-neutral-200 px-16 pb-16"
            v-bind:display-cart-button="true"
            v-bind:display-assembly-price="true"
          />
        </DialogPanel>
      </TransitionChild>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { useScartStore } from '~/stores/scart';

const mobileSummaryBar = ref();
const cart = useScartStore();
const isDrawerOpen = ref(false);

function onCtaClick () {
  isDrawerOpen.value = !isDrawerOpen.value;
}

</script>
