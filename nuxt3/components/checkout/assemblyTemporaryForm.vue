<template>
  <div>
    <div
      class="assembly-item mb-16"
      v-bind:class="{ active: cartUsedAssembly }"
      v-on:click="cartUsedAssembly = true"
    >
      <div class="flex items-center justify-center">
        <div class="w-full">
          <p class="bg-neutral-900 text-white inline px-8 py-2 rounded-4 normal-12">
            {{ $t('checkout.recommended_label') }}
          </p>
          <p class="mt-8 semibold-16 text-neutral-900">
            {{ $t('checkout.delivery.assembly_headline') }}
            <BaseBadge
              v-if="assemblyDiscount"
              variant="attribute"
              class="inline-block"
            >
              -{{ assemblyDiscount }}%
            </BaseBadge>
          </p>
          <p class="mt-2 text-neutral-700 normal-12">
            {{ $t('checkout.delivery.assembly_info') }}
          </p>
        </div>
        <p
          class="normal-16 text-neutral-900"
          v-bind:class="{ 'text-orange': assemblyDiscount }"
        >
          {{ format(cart.orderPricing?.assembly_price) }}
        </p>
      </div>
    </div>
    <div
      class="assembly-item"
      v-bind:class="{ active: !cartUsedAssembly }"
      v-on:click="cartUsedAssembly = false"
    >
      <div class="flex items-center justify-center">
        <div class="w-full">
          <p
            class="mt-8 semibold-16 text-neutral-900"
          >
            {{ $t('checkout.delivery.home_headline') }}
          </p>
          <p
            class="mt-2 text-neutral-700 normal-12"
          >
            {{ $t('checkout.free.delivery_subheadline') }}
          </p>
        </div>
        <p class="normal-16 text-neutral-900">
          {{ format(0) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';

const $gtm = useGtm();
const global = useGlobal();
const cart = useScartStore();

const { format } = usePrice();
const { handleAssembly } = useCart();
const { checkout2025Event } = checkoutAnalytics();

const { regionName } = storeToRefs(global);
const { cartUsedAssembly } = storeToRefs(cart);

const toBoolean = (value: string | boolean) => !!(value === 'true' || value === true);

const assemblyDiscount = computed(() => {
  if (regionName.value === 'france' || regionName.value === 'netherlands' || regionName.value === 'switzerland') {
    return 50;
  } else if (regionName.value === 'germany' || regionName.value === 'belgium') {
    return 30;
  }

  return 0;
});

watch(cartUsedAssembly, async (value, oldValue) => {
  // there is bug in formkit that it emits boolen value as string on initial which triggers this watch
  // so we need to check if value is actually changed
  const booleanValue = toBoolean(value);
  const booleanOldValue = toBoolean(oldValue);

  if (booleanValue !== booleanOldValue) {
    await handleAssembly(booleanValue);

    if (booleanValue) {
      $gtm.push({ ecommerce: null });
      $gtm.push(checkout2025Event('add_shipping_info', null, 'Home delivery'));
    }
  }
});
</script>

<style lang="scss" scoped>
    .assembly-item {
        @apply cursor-pointer rounded-8 border border-neutral-500 px-16 py-24;
        &.active {
            @apply border-neutral-900 bg-transparent relative overflow-hidden;
            &::before {
                    @apply absolute right-0 bottom-0 w-20 h-20 bg-neutral-900 bg-no-repeat rounded-tl-6;
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="1447.4 687.6 17.6 13.4"><g id="group" transform="translate(1421 687)"><path id="path" fill="%23FFFFFF" class="cls-1" d="M9,16.2,4.8,12,3.4,13.4,9,19,21,7,19.6,5.6Z" transform="translate(23 -5)"/></g></svg>');
                    background-size: 8px 8px;
                    background-position: 6px 6px;
                    content: '';
                }
        }
    }
</style>
