<template>
  <div class="normal-16 text-grey-900">
    <p>{{ firstName }} {{ lastName }}</p>
    <p>{{ streetAddress1 }}</p>
    <p v-if="streetAddress2">
      {{ streetAddress2 }}
    </p>
    <p>{{ postalCode }} {{ city }}</p>
    <p>{{ $t(regionKey) }}</p>
  </div>
</template>

<script setup lang="ts">

import { regions } from '~/utils/regions';

const props = defineProps({
  firstName: {
    type: String,
    required: true
  },
  lastName: {
    type: String,
    required: true
  },
  streetAddress1: {
    type: String,
    required: true
  },
  streetAddress2: {
    type: String,
    default: null
  },
  postalCode: {
    type: String,
    required: true
  },
  city: {
    type: String,
    required: true
  },
  region: {
    type: String,
    required: true
  }
});

const regionKey = regions[props.region].nameKey;

</script>
