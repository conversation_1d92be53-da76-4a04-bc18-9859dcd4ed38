<template>
  <div
    ref="stickyScroll"
    class="sticky-scroll left-0"
    v-bind:class="[
      isStatic ? 'static' : 'sticky'
    ]"
    v-bind:style="{ 'top': heightCorrection + 'px' }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core';

const heightCorrection = 80;
const stickyScroll = ref();
const isStatic = ref(false);
const { height } = useWindowSize();

onMounted(() => {
  isStatic.value = height.value < stickyScroll.value?.clientHeight + heightCorrection;
});
</script>
