<template>
  <header
    class="sticky top-0 w-full header"
    data-section="header"
  >
    <div class="relative will-change-transform z-1 shadow-md bg-white">
      <div
        class="grid-container flex items-center transition-min-height min-h-[48px] lg:min-h-[61px] relative"
      >
        <BaseLink
          variant="custom"
          data-testid="logo"
          class="-ml-4"
          v-bind="{ href: $addLocaleToPath('homepage'), trackData: {} }"
        >
          <IconTylkoLogo
            class="lg-max:w-[47px] text-orange h-[16px] lg:h-[29px] box-content px-4 py-8"
          />
        </BaseLink>
        <IconSecureCheckout class="lg-max:hidden -mt-2 ml-12 w-[10px]" />
        <span
          class="ml-4 text-offblack-600 normal-12"
          v-html="$t('cart.header.secure_checkout')"
        />
        <div class="flex items-center ml-auto mr-8 lg-max:hidden text-offblack-600">
          <span
            class="mx-4 normal-14"
            v-html="$t('cart.header.help')"
          />
          <a
            class="hover:text-orange normal-14"
            v-bind:href="`tel:${$t('common.phone_number')}`"
          >
            {{ $t('common.phone_number') }}
          </a>
          <BaseTooltip
            class="lg-max:hidden ml-8 justify-center items-center"
            popover-classes="max-w-[240px] w-[240px] text-grey-900"
          >
            <p
              class="text-offblack-600 normal-14"
              v-html="$t('hotline_annotation')"
            />
          </BaseTooltip>
          <span
            v-if="(cookieStore?.consents?.functional && isDixaVisible)"
            class="ml-8 normal-14"
          >
            {{ $t('cart.header.or') }}
          </span>
        </div>
        <BaseButton
          v-if="cookieStore?.consents?.functional && isDixaVisible"
          variant="outlined"
          class="lg-max:ml-auto ty-btn--s"
          v-bind="{ trackData: cartEvent('chatnow', 'open') }"
          v-on="{ click: openLiveChat }"
        >
          {{ $t('cart.header.chat_now') }}
        </BaseButton>
      </div>
    </div>
  </header>
</template>

<script setup>
import { pageName } from '~/helpers/pageName';
import { CART_ANALYTICS } from '~/composables/useCart';

const cookieStore = useTyCookieStore();
const route = useRoute();
const { $dixa, $logException } = useNuxtApp();

const { cartEvent } = CART_ANALYTICS(pageName(route) === 'cart' ? 'cart_edit' : 'checkout');

const openLiveChat = () => {
  try {
    $dixa.toggleWidget(true);
  } catch (e) {
    console.error(e);
    $logException(e);
  }
};

const isDixaVisible = useState('isDixaEnabled', () => false);

</script>
