<template>
  <ModalBasicCart
    v-if="mergedCartItems.length"
    v-model="isCartMerged"
    modal-classes="!p-0 w-[90%] max-w-[560px] rounded-[24px]"
    v-bind="{
      showProductImage: false,
    }"
  >
    <template #default="{ closeModal }">
      <div class="pt-12 mb-12">
        <h1 class="semibold-24 text-neutral-900 text-center pr-48">
          {{ $t('scart.item.label.cart_merge_modal_title') }}
        </h1>
        <div class="relative mt-32">
          <div
            class="overflow-y-scroll
                      after:w-full after:h-32 after:absolute after:bottom-[-1px]
                      after:bg-gradient-to-t after:from-white after:to-transparent"
          >
            <ul class="max-h-[300px] w-full">
              <li
                v-for="(item, id) in mergedCartItems"
                v-bind:key="id"
                class="flex gap-x-16 mt-16 first:mt-0 last:pb-32"
              >
                <BaseLink
                  v-bind:href="item?.itemUrl"
                >
                  <CartItemImage
                    class="!w-[120px]"
                    v-bind="{
                      cartItem: item,
                    }"
                  />
                </BaseLink>
                <CartItemDescription
                  class="flex-1 normal-14 text-neutral-700 first:[&>li]:normal-16 first:[&>li]:text-neutral-900"
                  name-visible
                  quantity-visible
                  v-bind="{
                    cartItem: item,
                  }"
                >
                  <CheckoutPriceItem
                    v-bind="{
                      cartItem: item,
                    }"
                  />
                </CartItemDescription>
              </li>
            </ul>
          </div>
        </div>
        <BaseButton
          variant="accent"
          class="w-full"
          v-bind="{
            trackData: { eventLabel: 'view-cart' },
            'data-testid': 'view-cart'
          }"
          v-on:click="closeModal"
        >
          {{ $t('scart.label.view_cart') }}
        </BaseButton>
      </div>
    </template>
  </ModalBasicCart>
</template>

<script setup lang="ts">

const route = useRoute();

const isCartMerged = ref(true);

const { cartItems } = storeToRefs(useScartStore());

const mergedCartItems = computed(() =>
  route.query?.addedCartItemIds?.split(',').map(id => cartItems.value.find(item => item.id === parseInt(id))).filter(Boolean));

</script>
