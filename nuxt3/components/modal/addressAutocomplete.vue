<template>
  <ModalBasic
    v-bind:model-value="modelValue"
    description-classes="h-full overflow-hidden"
    modal-classes="
    relative
    md:px-24
    !pb-0
    md-max:px-16
    md:w-[704px]
    lg:w-[635px]
    lg2:w-[806px]
    xl:w-[890px]
    xl2:w-[1018px]
    h-[673px]
    md:h-[651px]
    lg:h-[574px]
    lg2:h-[651px]"
    v-on:update:model-value="$emit('update:modelValue', $event);"
    v-on:after-close="cleanData"
  >
    <template #default>
      <div class="text-left h-full overflow-hidden">
        <h2 class="text-neutral-900 semibold-20 mb-16">
          {{ $t('checkout_address_input_placeholder') }}
        </h2>
        <p class="text-neutral-900 normal-16 mb-24">
          {{ $t('checkout_address_modal_description') }}
        </p>
        <label class="relative cursor-pointer w-full">
          <IconSearch
            class="absolute top-1/2 left-12 transform -translate-y-1/2 z-1"
          />
          <input
            ref="addressInput"
            v-model="query"
            type="text"
            v-bind:placeholder="$t('checkout_address_input_placeholder_info')"
            class="bg-beige-100 w-full rounded-8 pl-[44px] pr-12 py-16 text-left relative normal-16 text-neutral-900 border border-beige-200 placeholder-neutral-900"
            v-on:input="fetchSuggestions"
          >
        </label>
        <div class="border-t border-neutral-400 w-full mt-24 pt-24" />
        <div
          class="
            h-[calc(100%-293px)]
            md:h-[calc(100%-272px)]
            lg2:h-[calc(100%-272px)]
            overflow-y-scroll"
        >
          <div
            v-for="(result, key) in results"
            v-bind:key="key"
            class="h-64 flex justify-start items-center cursor-pointer"
            v-on:click="() => handleDispatchAddress(result)"
          >
            <p
              v-if="!result.fullStreet && !result.street"
              class="cursor-pointer text-neutral-900 normal-16 mb-8"
            >
              {{ `${result.city}, ${result.country}` }}
            </p>
            <p
              v-else
              class="cursor-pointer"
            >
              <span class="text-neutral-900 normal-16 mb-8">
                {{ `${result.fullStreet || result.street || ''}${result.city ? ', ' + result.city : ''}${result.country ? ', ' + result.country : ''}` }}
              </span>
            </p>
            <IconCaretRight
              class="h-24 ml-auto text-neutral-900 cursor-pointer"
            />
          </div>
        </div>

        <div class="bg-white absolute bottom-0 left-0 right-0 h-80 flex justify-center items-center">
          <BaseButton
            variant="outlined"
            class="w-[207px]"
            data-testid="modal-address-close-button"
            v-bind="{
              trackData: {}
            }"
            v-on="{ click: () => closeModal() }"
          >
            {{ $t('common.cancel') }}
          </BaseButton>
        </div>
      </div>
    </template>
  </ModalBasic>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';
const { $i18n } = useNuxtApp();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});
const { regionCode } = storeToRefs(useGlobal());
const { locale } = useLocale();
const addressInput = ref<HTMLInputElement | null>(null);
const emits = defineEmits<{
  'update:modelValue': [value: false],
  'dispatchAddress': [value: any]
}>();

const handleDispatchAddress = (result: any) => {
  emits('dispatchAddress', result);
  closeModal();
};

const query = ref('');
const results = ref<any[]>([]);
const AutocompleteSuggestion = ref<any>(null);
const AutocompleteSessionToken = ref<any>(null);
let sessionToken: any = null;

function parseAddress (components: any[]): Record<string, string> {
  const parts: Record<string, string> = {};

  for (const c of components) {
    const types = c.types;
    const longName = c.longText || c.long_name;
    const shortName = c.shortText || c.short_name;

    if (types.includes('street_number')) {
      parts.streetNumber = longName;
    }

    if (types.includes('route')) {
      parts.street = longName;
    }

    if (types.includes('postal_code')) {
      parts.postalCode = longName;
    }

    if (types.includes('locality')) {
      parts.city = longName;
    } else if (types.includes('postal_town')) {
      parts.city = longName;
    }

    if (types.includes('country')) {
      parts.country = longName;
      parts.countryCode = shortName;
    }
  }

  if (parts.street && parts.streetNumber) {
    parts.fullStreet = `${parts.street} ${parts.streetNumber}`;
  }

  return parts;
}

const fetchSuggestions = debounce(async () => {
  if (!query.value) { return; }

  try {
    sessionToken = new AutocompleteSessionToken.value();

    const request = {
      input: query.value,
      sessionToken,
      language: locale.value,
      region: regionCode.value,
      includedRegionCodes: [regionCode.value]
    };

    const { suggestions } = await AutocompleteSuggestion.value.fetchAutocompleteSuggestions(request);
    if (!suggestions.length) { return; }

    const parsedResults = [];

    for (let i = 0; i < Math.min(10, suggestions.length); i++) {
      const suggestion = suggestions[i];
      if (!suggestion.placePrediction) { continue; }

      const place = suggestion.placePrediction.toPlace();
      await place.fetchFields({
        fields: ['addressComponents', 'formattedAddress']
      });
      const parsed = parseAddress(place.addressComponents);

      // Only add if it contains a valid full postal code (not just prefix)
      if (parsed.postalCode && parsed.postalCode.length >= 4 && /\d/.test(parsed.postalCode)) {
        parsedResults.push(parsed);
      }
    }

    results.value = parsedResults;
  } catch (err) {
    console.error('❌ Error during autocomplete:', err);
  }
}, 100);

const closeModal = () => {
  emits('update:modelValue', false);
};

const cleanData = () => {
  query.value = '';
  results.value = [];
};

let stopWatching: () => void;

onMounted(async () => {
  const lib = await google.maps.importLibrary('places') as google.maps.PlacesLibrary;
  AutocompleteSuggestion.value = lib.AutocompleteSuggestion;
  AutocompleteSessionToken.value = lib.AutocompleteSessionToken;
  stopWatching = watch(
    () => props.modelValue,
    (val: boolean) => {
      if (val) {
        setTimeout(() => {
          addressInput.value?.focus();
        }, 100);
      }
    }
  );
});
onUnmounted(() => {
  stopWatching?.();
});
</script>
