<template>
  <BasePicture
    v-if="isSampleBox"
    class="w-full aspect-square"
    img-classes="object-cover w-full aspect-square"
    type="A"
    data-testid="cart-item-image"
    v-bind="{
      alt: $t(cartItem.name_key),
      path: imagePath,
      pictureClasses: 'w-full',
      isRetinaUploaded: false
    }"
  />
  <img
    v-else
    class="block w-full aspect-square"
    data-testid="cart-item-image"
    v-bind:src="imagePath"
  >
</template>

<script setup lang="ts">
import type { CartItem } from '~/types/userStatus';

const { getCartItemImage, isItSampleBox } = useCart();
const props = defineProps<{cartItem: CartItem}>();

const isSampleBox = computed(() => isItSampleBox(props.cartItem));
const imagePath = computed(() => getCartItemImage(props.cartItem));

</script>
