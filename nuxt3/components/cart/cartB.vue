<template>
  <div class="bg-beige-100 flex-1">
    <div class="md:grid-container text-neutral-900">
      <header
        v-if="cartItemsCount"
        class="px-16 md:px-0 xl:px-32 pt-32 pb-16 md:py-32 md-max:bg-white"
      >
        <h1
          class="semibold-20 lg:semibold-24"
          data-testid="cart-header"
        >
          {{ $t('scart.your_cart') }}
          <span
            class="normal-12 lg:normal-16 text-neutral-700"
            data-testid="cart-counter"
          >
            ({{ $t('scart.items_count', { count: cartItemsCount }) }})
          </span>
        </h1>
      </header>
      <div
        v-if="!isCartFetched"
        class="relative flex min-h-[50vh]"
      >
        <UiDotsLoader
          class="mt-2 w-full flex justify-center"
          bounce-class="bg-orange !w-20 !h-20 !mr-8"
        />
      </div>
      <div
        v-else-if="cartItems?.length"
        class="lg:flex items-start xl:px-32 md:mb-96 gap-y-8 gap-x-16 lg:gap-x-24"
      >
        <!-- ITEMS -->
        <div class="relative flex-1 flex flex-col-reverse gap-y-8">
          <template
            v-for="(item, index) in cartItems"
            v-bind:key="item.id"
          >
            <CartFurnitureItemNew2025
              class="bg-white"
              data-testid="cart-item"
              v-bind="{
                cartItem: item,
              }"
              v-on:save-to-wishlist="() => onSaveToWishlist(index)"
              v-on:remove-from-cart="() => onRemoveItemFromCart(index)"
            />
          </template>
        </div>
        <div
          class="w-full lg:bg-white lg:w-[304px] lg2:w-[360px] xl:w-[400px] xl2:w-[424px]
                lg:px-16 xl:px-32 lg:py-24
                sticky lg:top-[calc(var(--current-header-height)+32px)]
                transition-[top] basic-transition"
        >
          <h2 class="lg-max:hidden bold-20 text-offblack-600">
            {{ $t('cart.order_summary') }}
          </h2>
          <CartSummary />

          <CheckoutFeaturesDesktopNew
            v-if="!hasS01"
            class="hidden lg:block mt-24 text-neutral-900 "
          />
        </div>
      </div>
      <!--      Empty Cart-->
      <div
        v-else
        class="grid-container pt-32 pb-48"
      >
        <div class="flex flex-col justify-center items-center w-full max-w-[326px] mx-auto text-center min-h-[50vh]">
          <div
            class="bg-beige-200/50 aspect-square w-[200px] mt-24 flex justify-center items-center"
            data-testid="empty-cart-img"
          >
            <IconCartShelf class="w-[82px] text-[#D6D1C5]" />
          </div>
          <p
            class="semibold-20 text-offblack-900 mt-32"
            data-testid="empty-cart-text"
          >
            {{ $t('scart.empty_cart') }}
          </p>
          <p
            v-if="!isSignedIn"
            class="normal-16 text-offblack-900 mt-4"
            data-testid="empty-cart-text-not-signed-in"
          >
            {{ $t('scart.empty_cart_login') }}
          </p>
          <BaseLink
            v-if="!isSignedIn"
            variant="accent"
            class="w-full mb-16 [p+&]:mt-32"
            v-bind="{
              href: $addLocaleToPath('register-or-login'),
              trackData: { eventLabel: 'cart-empty-login' },
              'data-testid': 'cart-empty-login'
            }"
          >
            {{ $t('common.login') }}
          </BaseLink>
          <BaseLink
            variant="outlined"
            class="w-full mb-16 [p+&]:mt-32"
            v-bind="{
              href: $addLocaleToPath('plp'),
              trackData: { eventLabel: 'cart-empty-shop' },
              'data-testid': 'cart-empty-shop'
            }"
          >
            {{ $t('library.continue_shopping_button') }}
          </BaseLink>
        </div>
      </div>
    </div>

    <LazyModalWishlist
      v-if="isWishlistModalOpen"
      v-model="isWishlistModalOpen"
      v-bind="{
        wishlistItem
      }"
    />

    <LazyModalBasicCart
      v-if="isModalLoaded"
      v-model="isModalVisible"
      v-bind="{
        cartItem: selectedItem,
      }"
      v-on:after-close="() => {
        isModalVisible = false;
        isRemoveItemModalVisible = false;
        isSaveForLaterModalOpen = false;
        selectedItem = null;
      }"
    >
      <Transition
        name="fade"
        mode="out-in"
      >
        <LazyModalViewRemoveItem
          v-if="isRemoveItemModalVisible"
          v-bind="{
            cartItem: selectedItem,
          }"
          v-on:move-to-wishlist="onMoveToWishlist"
          v-on:close="() => {
            isModalVisible = false;
          }"
        />

        <LazyModalViewSaveItem
          v-else-if="isSaveForLaterModalOpen"
          v-bind="{
            isMoveToWishlist,
            furnitureId: selectedItem?.itemId,
            model: selectedItem?.itemFurnitureType,
          }"
          v-on:close="() => {
            isModalVisible = false;
          }"
        />
      </Transition>
    </LazyModalBasicCart>

    <LazyModalCartMergedItems
      v-if="isCartMerged"
    />
  </div>
</template>

<script setup lang="ts">
import { useEventBus, watchOnce } from '@vueuse/core';
import { CART_ITEM_NAME } from '~/composables/useCart';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';
import { useScartStore } from '~/stores/scart';
import type { CartItem } from '~/types/userStatus';
import useCartStatus from '~/composables/useCartStatus';

const gtm = useGtm();
const route = useRoute();
const global = useGlobal();
const cartStore = useScartStore();
const bus = useEventBus<string>('cart');

const { isSignedIn, hasS01 } = storeToRefs(global);
const { cartItemsCount, cartItems } = storeToRefs(cartStore);

const { getItemName } = CART_ITEM_NAME();
const { fetchUserStatus } = useCartStatus();
const { checkout2025Event } = checkoutAnalytics();

const isModalLoaded = ref(false);
const isModalVisible = ref(false);
const isMoveToWishlist = ref(false);
const isWishlistModalOpen = ref(false);
const isSaveForLaterModalOpen = ref(false);
const isRemoveItemModalVisible = ref(false);
const selectedItem = ref<CartItem | null>(null);
const isCartMerged = ref(!!route.query?.addedCartItemIds?.length);

const wishlistItem = ref({
  preview: null,
  title: null,
  region_price: null,
  region_price_with_discount: null
});

const isCartFetched = useState('isCartFetched', () => false);

const onMoveToWishlist = () => {
  isMoveToWishlist.value = true;
  addToWishlist();
};

const onSaveToWishlist = (id: number) => {
  isMoveToWishlist.value = false;
  selectedItem.value = cartItems.value[id];
  addToWishlist();
};

const addToWishlist = () => {
  if (!isSignedIn.value) {
    isRemoveItemModalVisible.value = false;
    isSaveForLaterModalOpen.value = true;
    isModalVisible.value = true;
  }
};

const onRemoveItemFromCart = (index: number) => {
  selectedItem.value = cartItems.value[index];
  isRemoveItemModalVisible.value = true;
  isModalVisible.value = true;
};

watchOnce([isRemoveItemModalVisible, isSaveForLaterModalOpen], () => {
  isMoveToWishlist.value = false;
  isModalLoaded.value = true;
});

callOnce(async () => {
  await fetchUserStatus();

  isCartFetched.value = true;
}, { mode: 'navigation' });

onMounted(() => {
  bus.on((event, item:CartItem) => {
    if (event === 'showWishlistBubble') {
      isModalVisible.value = false;
      isWishlistModalOpen.value = true;

      wishlistItem.value = {
        preview: item?.itemImageUrl || '',
        title: getItemName(item?.itemFurnitureType, item?.pattern_name, item?.category, item?.shelf_type),
        region_price: item?.region_price
      };

      setTimeout(() => {
        isWishlistModalOpen.value = false;
      }, 3000);
    }
  });

  gtm?.push({ ecommerce: null });
  gtm?.push(checkout2025Event('view_cart'));
});
</script>
