<template>
  <PdpProductDetailsUiParam label="pdp.product_details.params.color.label">
    <template v-if="colorInfo">
      <div class="flex items-center">
        <p v-html="getTranslatedColor(colorInfo.nameKey)" />
        <img
          v-bind:src="getAssetPath(`common/swatch/${colorInfo.iconPath}/small/A`, 'svg')"
          class="w-20 h-20 ml-8 rounded-full"
          alt=""
        >
      </div>

      <div
        v-if="ralCodeDescriptions && ralCodeDescriptions.length"
        class="mt-4 normal-12 text-neutral-750"
      >
        <button
          class="flex items-center"
          v-on:click="isOpen = !isOpen"
        >
          <span v-html="$t('pdp.product_details.params.color.ral_codes.label')" />
          <IconChevronDown
            class="w-16 h-16 aspect-square"
            v-bind:class="isOpen && 'rotate-180'"
          />
        </button>

        <Transition name="pdp-product-details-param-color__transition">
          <div
            v-show="isOpen"
            class="grid grid-rows-[1fr] overflow-hidden"
          >
            <div class="min-h-0">
              <div class="mt-4">
                <p
                  v-for="description in ralCodeDescriptions"
                  v-bind:key="description.id"
                  v-html="$t(description.translationKey, description.values)"
                />
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </template>
  </PdpProductDetailsUiParam>
</template>

<script setup lang="ts">
import getAssetPath from '~/utils/assets';
import IconChevronDown from '~/assets/icons/caret-down.svg';

import useColors from '~/composables/useColors';
import useDetails from '~/composables/pdp/useDetails';
import { getTranslatedColor } from '~/composables/useColorName';

import { FURNITURE_TYPES_KEYS } from '~/consts/types';

const props = defineProps({
  furnitureType: {
    type: String as PropType<FURNITURE_TYPES_KEYS>,
    required: true
  },
  shelfType: {
    type: Number,
    required: true
  },
  value: {
    type: Number,
    required: true
  }
});

const { getColor } = useColors();
const { ralCodes } = useDetails();

const isOpen = ref(false);

const colorInfo = computed(() => getColor(props.furnitureType, props.value, '', props.shelfType));
const ralCodeDescriptions = computed(() => ralCodes(colorInfo));
</script>

<style lang="scss">
.pdp-product-details-param-color__transition {
  &-enter-active,
  &-leave-active {
    transition: grid-template-rows 200ms;
  }

  &-enter,
  &-leave-to {
    @apply grid-rows-[0fr];
  }
}
</style>
