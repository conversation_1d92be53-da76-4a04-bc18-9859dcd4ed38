<template>
  <div class="absolute inset-0 bg-offwhite-700 z-2 lg:flex lg:flex-row lg:items-start modern-navigation-page-padding">
    <div
      class="configurator-loader-wrapper flex items-center justify-center flex-col lg-max:translate-y-[10px] pt-32 lg:pt-96"
      data-testid="configurator-loader"
    >
      <div class="img-wrapper rounded-6 overflow-hidden border border-solid border-grey-800 relative bg-[#f0f0f0] w-[150px] h-[150px] lg:w-[300px] lg:h-[300px]">
        <img
          v-if="preview"
          v-bind="{
            src: preview,
            alt: title
          }"
        >
        <div class="absolute inset-0 gradient-wrapper">
          <IconLoader class="gradient-wrapper__svg" />
        </div>
      </div>
      <section>
        <h1 class="text-grey-900 bold-14 text-center max-w-[300px] mx-auto mt-16">
          {{ title }}
        </h1>
      </section>
      <p class="pt-16 lg-max:hidden text-grey-900 normal-14">
        {{ $t('common.width') }} {{ dimensions.width }}cm • {{ $t('common.height') }} {{ dimensions.height }}cm • {{ $t('common.depth') }} {{ dimensions.depth }}cm
      </p>
      <p class="normal-10 lg:normal-14 text-grey-900 lg-max:py-8 lg:pb-16 relative after:content-[''] after:absolute after:w-[12px] after:h-[1px] after:bg-grey-800 after:top-full after:left-1/2">
        {{ $t('common.configurator.colour') }} {{ colorName }}
      </p>

      <div
        data-nosnippet="data-nosnippet"
        class="mx-64 mt-8 text-center lg:mt-12 normal-16 lg:normal-20"
      >
        {{ $t('common.configurator.loader_copy') }}
      </div>
    </div>
    <div class="configurator-loader-skeleton bg-white py-8 lg:py-16 xl2:p-24 px-16 lg:w-[393px] xl:w-[420px] lg:mr-32 xl:mr-[74px] lg:rounded-b-12">
      <div class="grid w-full data-wrapper lg-max:absolute lg-max:top-0 lg-max:left-0 lg-max:pt-16 lg-max:px-16 lg:pb-12 lg:border-b lg:border-grey-700">
        <header>
          <h1
            class="title normal-12 lg:text-offblack-600 one-line"
            v-html="title"
          />
        </header>
        <p class="taxes lg-max:hidden normal-12 text-offblack-600">
          <IconAccept class="inline-block mr-8 w-20" />
          {{ $t('common.configurator.taxes') }}
        </p>
        <p class="pt-4 shipping lg-max:hidden normal-12 text-offblack-600">
          <IconAccept class="inline-block mr-8 w-20" />
          {{ $t('common.shipping', { min: minDelivery, max: maxDelivery }) }}
        </p>
      </div>
      <PdpConfiguratorSkeleton class="pb-16 border-b border-grey-700 lg:py-16" />
      <div class="flex flex-col pt-16 lg:flex-col-reverse">
        <div
          data-nosnippet="data-nosnippet"
          class="ty-btn-outlined ty-btn-outlined--disabled h-[48px] lg-max:mb-8"
        >
          {{ $t('common.configurator.save_for_later') }}
        </div>
        <div
          data-nosnippet="data-nosnippet"
          class="ty-btn-accent ty-btn-accent--disabled h-[48px] mb-8"
        >
          <IconCart class="h-[20px] w-[20px] box-content mr-8 text-offwhite-600" />
          {{ $t('common.configurator.add_to_cart') }}
        </div>
      </div>
      <div
        data-nosnippet="data-nosnippet"
        class="uppercase text-grey-700 text-center lg-max:hidden mt-16 font-bold text-12 leading-1 tracking-0_5 flex justify-center items-center"
      >
        {{ $t('common.configurator.payment_information') }}
      </div>
      <p
        class="pb-16 text-center normal-12 text-offblack-600 lg:hidden"
        v-html="$t('common.shipping', { min: minDelivery, max: maxDelivery })"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import IconCart from '../../../assets/icons/cart.svg';
import IconAccept from '../../../assets/icons/accept.svg';
import IconLoader from '../../../assets/icons/pdpLoader.svg';

defineProps({
  preview: {
    type: String,
    default: null
  },
  dimensions: {
    type: Object as PropType<{
      width: number;
      height: number;
      depth: number;
    }>,
    required: true
  },
  colorName: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  maxDelivery: {
    type: Number,
    default: 0
  },
  minDelivery: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: ''
  }
});
</script>

<style lang="scss">
.configurator-loader-wrapper {
  @screen lg {
    flex-grow: 1;
  }

  @screen lg-max {
    @apply w-full;

    height: 100vw;
  }

  .img-wrapper {
    @screen lg-max {
      --imgSize: 150px;
    }

    @screen lg {
      --imgSize: 300px;
    }
  }

  @keyframes loader-shiny-self {
    0% {
      opacity: 0;
      transform: translate(0%, -50%);
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0;
      transform: translate(-200%, -50%);
    }
  }

  .gradient-wrapper {
    &__svg {
      @apply opacity-0 mix-blend-overlay absolute left-full top-1/2 w-[75%];

      animation: loader-shiny-self 1.3s infinite ease-in-out;
    }
  }
}

.configurator-loader-skeleton {
  .data-wrapper {
    grid-template-columns: 1fr fit-content(100%);
    grid-template-rows: 1fr fit-content(100%);

    .dim {
      grid-column: 1;
      grid-row: 2;
    }

    .title {
      grid-column: 1;
      grid-row: 1;
    }

    .price {
      grid-column: 2;
      grid-row: 1 / span 2;

      @screen lg {
        grid-column: 1;
        grid-row: 2;
      }
    }

    .taxes {
      grid-column: 2;
      grid-row: 1;
    }

    .shipping {
      @screen lg {
        grid-column: 2;
        grid-row: 2;
      }
    }
  }
}

.configurator-loader--modern{
  .configurator-loader-skeleton{
    @apply lg:rounded-t-12 lg:mt-24;
  .data-wrapper{
    @apply lg-max:pt-96;
  }
  }
}
</style>
