<template>
  <section class="relative z-0 bg-beige-100">
    <header class="grid-container pt-48 pb-32 md:pt-64 lg:pt-64 lg:pb-64">
      <p
        class="mb-16 md:mb-24 uppercase text-neutral-800 semibold-14 md:semibold-18"
        v-html="lineHero.videoSubtitle"
      />
      <div class="grid-cols">
        <h2
          class="col-span-12 lg:col-span-9 text-neutral-900 semibold-28 md:semibold-32 lg:semibold-44 xl:semibold-54"
          v-html="lineHero.videoTitle"
        />
      </div>
    </header>
    <div class="relative">
      <SectionVideoParallax
        data-observe-view="Line hero"
        class="mt-16"
        data-section="video-quality"
        v-bind="{
          size: 'small',
          videoParams:{
            videoId: {
              mobile: videoID.mobile,
              desktop: videoID.desktop
            },
            embedOptions: {
              fitStrategy: 'cover',
              hasSound: false
            },
          },
          imageAlt: lineHero.videoTitle,
          backgroundColor: '#F2EFEE',
          headerColorClass: 'text-white',
          additionalPadding: 52,
          videoAspectRatio: lineHero.videoAspectRatio,
          hasSoundControl: true,
          isStaticVideo: toneShelf
        }"
      />
      <SectionAnimatedBorders
        v-if="!toneShelf && !isOriginalShelf"
        data-observe-view="Line hero bottom text"
        v-bind="{
          headingCopy: lineHero.descriptionBody,
          subheadingCopy: lineHero.descriptionSubheadline,
          headerClass: 'semibold-24 xl:semibold-32',
          headerColorClass: 'text-neutral-900',
          hideButton: true,
          hideBlend: true,
          usps
        }"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
import { CATEGORIES } from '~/consts/categories';
import { FURNITURE_TYPES_KEYS, TYPE_TO_LINE } from '~/consts/types';

import useLineHero from '~/composables/pdp/useLineHero';

const props = defineProps({
  furnitureCategory: {
    type: String as PropType<'bookcase' | 'wallstorage' | 'desk' | 'vinylstorage' | 'sideboard' | 'chest' | 'bedsidetable' | 'tvstand' | 'wardrobe' | 'shoerack'>,
    required: true
  },
  furnitureType: {
    type: String as PropType<FURNITURE_TYPES_KEYS>,
    required: true
  },
  shelfType: {
    type: Number as PropType<1 | 2 | 3 | 4 | 5>,
    required: true
  },
  material: {
    type: Number,
    required: true
  }
});

const { t } = useI18n();
const { locale } = useLocale();
const { mossGreenVideos, reisingerVideos, uspsData, lineHeroData } = useLineHero();

const usps = computed(() => {
  if (TYPE_TO_LINE[props.furnitureType] === 'edge' && props.furnitureCategory !== 'wardrobe') {
    return props.shelfType === 5 ? uspsData('edge-cleaf') : uspsData('edge');
  } else {
    return props.shelfType === 3 ? uspsData('tone') : uspsData();
  }
});

const categories = CATEGORIES();
const categoryTranslation = t(categories[props.furnitureCategory].nameKey);

const lineHero = computed(() => {
  if (props.furnitureCategory === 'wardrobe') {
    return props.shelfType === 3 ? lineHeroData('tone-wardrobe') : lineHeroData('edge-wardrobe');
  } else if (TYPE_TO_LINE[props.furnitureType] === 'edge') {
    return lineHeroData('edge-shelves', undefined, categoryTranslation, props.furnitureCategory);
  } else if (TYPE_TO_LINE[props.furnitureType] === 'tone') {
    return lineHeroData('tone-shelves');
  }

  return lineHeroData('', undefined, categoryTranslation, props.furnitureCategory);
});

const reisingerVideo = computed(() => {
  const lang = locale.value;

  return {
    mobile: reisingerVideos.mobile[lang as keyof typeof reisingerVideos.mobile],
    desktop: reisingerVideos.desktop[lang as keyof typeof reisingerVideos.desktop]
  };
});

const videoID = computed(() => {
  if (props.furnitureType === FURNITURE_TYPES_KEYS.t02 && props.material === 15) {
    return reisingerVideo.value;
  } else if (props.furnitureType === FURNITURE_TYPES_KEYS.t01p && props.material === 11) {
    return mossGreenVideos;
  } else {
    return lineHero.value.videoID;
  }
});

const toneShelf = TYPE_TO_LINE[props.furnitureType] === 'tone' && props.furnitureCategory !== 'wardrobe';
const isOriginalShelf = TYPE_TO_LINE[props.furnitureType] === 'original';
</script>
