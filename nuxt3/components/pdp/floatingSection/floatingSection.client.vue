<template>
  <div ref="floatingSection">
    <PdpFloatingSectionMobile
      v-if="isSm && shouldRenderSection && localStorageData && Object.keys(localStorageData).length"
      v-bind="{
        screenshot: localStorageData.screenshot,
        geom: localStorageData.geom,
        furnitureModel: localStorageData.endpoint,
        isSignedIn,
        isSaved,
        headerCopy,
        bgColor: localStorageData.bgColor
      }"
      v-on:update-is-saved="handleIsSaved"
    />
  </div>
</template>

<script lang="ts" setup>
import { useIntersectionObserver } from '@vueuse/core';
import scrollToElement from '~/helpers/scrollToElement';

interface LocalStorageData {
  screenshot: string;
  geom: Object;
  endpoint: string;
  bgColor: string;
  isWardrobe: boolean;
}

const {
  t03Available,
  isSalesEnabled,
  waitingListTokenExpired,
  waitingListTokenActive,
  isSignedIn
} = storeToRefs(useGlobal());

const { isSm } = useMq();

const isWardrobeSaleIsLimited = () => {
  const isActiveInviteLink = waitingListTokenExpired.value && waitingListTokenActive.value;
  return !t03Available.value || (!isSalesEnabled.value && !isActiveInviteLink);
};

const shouldRenderSection = ref(false);
const isSaved = ref(false);
const floatingSection = ref();
const targetIsVisible = ref(false);

const localStorageData = shallowRef<LocalStorageData | null>();

useIntersectionObserver(floatingSection, ([{ isIntersecting }]) => {
  targetIsVisible.value = isIntersecting;
});

const buildMobileExitSection = () => {
  const newData = window.localStorage.getItem('exitMobileSection');

  if (!isSm || !newData) {
    shouldRenderSection.value = false;
    return;
  }

  const newParsedData = JSON.parse(newData);

  if (newParsedData?.isWardrobe && isWardrobeSaleIsLimited()) {
    shouldRenderSection.value = false;
    return;
  }

  localStorageData.value = newParsedData;
  shouldRenderSection.value = true;
};

onMounted(() => {
  window?.PubSub.subscribe('rebuildMobileExitSection', () => {
    if (!floatingSection.value && targetIsVisible.value) {
      buildMobileExitSection();
    }
  });
});

onBeforeUnmount(() => window?.PubSub.unsubscribe('rebuildMobileExitSection'));

watch(targetIsVisible, () => {
  if (isSm.value && targetIsVisible.value) {
    buildMobileExitSection();
  }
}, { immediate: true });

const headerCopy = computed(() => (isSignedIn.value
  ? (isSaved.value ? 'floating_section.title_confirmation_wishlist' : 'floating_section.title')
  : (isSaved.value ? 'floating_section.title_confirmation' : 'floating_section.title')
));

const handleIsSaved = () => {
  isSaved.value = true;
  window.localStorage.removeItem('exitMobileSection');
  scrollToElement({ element: floatingSection.value, offset: 64 });
};

</script>
