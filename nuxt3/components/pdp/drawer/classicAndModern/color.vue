<template>
  <BaseDrawer
    ref="toneDepthDrawer"
    v-model="isDrawerOpen"
    v-bind:modern-drawer="true"
    v-on:update:model-value="$emit('update:modelValue', $event);"
  >
    <aside>
      <h2 class="semibold-24 md:semibold-28 xl:semibold-32 md:mr-32 px-16">
        {{ $t('pdp.configurator.modal.color.title') }}
      </h2>
      <div class="px-16 mt-32">
        <BaseToggle
          v-model="activeTab"
          class="mb-16 mt-8 flex gap-24"
          v-bind="{
            variant: 'custom',
            toggleActiveButtonClasses: 'text-orange',
            toggleButtonClasses: 'uppercase',
            options: toggleOptions
          }"
        />
        <div
          v-for="(dataItem, dataKey, indexGroup) in data"
          v-show="activeTab === dataKey"
          v-bind:key="indexGroup"
          class="flex flex-col"
        >
          <div
            v-if="isModern && dataKey === 'classic' || !isModern && dataKey === 'modern'"
            class="flex justify-center items-center mt-24 md:justify-start"
          >
            <Warning class="min-w-20 w mr-8" />
            <UiInterpolatedLinkText
              class="normal-12 text-neutral-800"
              v-bind="{
                link: `${$addLocaleToPath('plp')}${$t(categoryPathKey)}?productLines=${isModern ? [0, 2] : 1 }`,
                textKey: 'pdp.configurator.modal.color.line.hint',
                textLinkKey: isModern ? 'common.original_classic' : 'common.original_modern',
                linkClasses: 'text-orange hover:underline bold-12',
                interpolationKey: '0',
                newCard: true
              }"
            />
          </div>

          <div
            v-for="(item, indexItem) in dataItem"
            v-bind:key="`${indexGroup}-${indexItem}`"
            class="md:grid md:grid-cols-2 md:gap-x-16 pt-24"
            v-bind:class="{
              'order-first': isVeneer
                && item.shelfTypeTitle === '01v'
                && furnitureCategory !== 'vinyl_storage',
              'order-last': item.shelfTypeTitle === '01v'
                && furnitureCategory === 'vinyl_storage'
            }"
          >
            <PdpDrawerColorImageAndSwatches
              v-bind="{
                defaultImage: item.defaultImage,
                variant: 's',
                groupIndex: `${indexGroup}-${indexItem}`,
                title: item.title,
                colors: item.colors,
                shelfTypeTitle: item.shelfTypeTitle,
                swatchWrapperClasses: 'gap-x-4 mt-8 mb-32',
                imgClasses: 'aspect-[244/150]'
              }"
            />
            <div class="md:col-start-2 md:row-start-1 md:row-span-2 md-max:mt-16">
              <h3
                class="bold-20 text-neutral-900"
                v-html="item.title"
              />
              <h4
                v-if="'additionalTitle1' in item"
                class="mt-8 bold-14 text-neutral-900"
                v-html="item.additionalTitle1"
              />
              <p
                v-if="'description' in item"
                class="mt-8 normal-14 text-neutral-800"
                v-html="item.description"
              />
              <BaseLink
                v-if="'additionalLink' in item"
                v-bind="{
                  href: $addLocaleToPath(item.additionalLink),
                  variant: 'link-color',
                  arrow: 'right',
                  trackData: { eventLabel: 'cta', eventPath: $addLocaleToPath(item.additionalLink) }
                }"
                class="mt-4 block bold-14"
                target="_blank"
                v-html="item.additionalLinkText"
              />
              <h4
                v-if="'additionalTitle2' in item"
                class="mt-16 bold-14 text-neutral-900"
                v-html="item.additionalTitle2"
              />
              <p
                v-if="'additionalDescription' in item"
                class="mt-8 normal-14 text-neutral-800"
                v-html="item.additionalDescription"
              />
              <span
                v-if="'note' in item"
                class="block normal-10 mt-8 text-neutral-800"
                v-html="item.note"
              />
            </div>
          </div>
          <PdpDrawerClassicAndModernSamplesLink
            v-if="isModern && dataKey === 'modern' || !isModern && dataKey === 'classic'"
            class="mt-24 md-max:pt-24 !md:pt-0 border-grey-700 md-max:border-t text-center md:justify-center"
            v-bind:class="{
              'border-b md-max:border-t-0 !pt-0 pb-24': furnitureCategory === 'vinyl_storage'
            }"
            caption-classes="normal-14"
          />
          <div
            v-if="indexGroup === 0 && furnitureCategory === 'vinyl_storage'"
            class="flex justify-center items-center mt-24 md:justify-start"
          >
            <Warning class="min-w-20 w mr-8" />
            <UiInterpolatedLinkText
              v-if="indexGroup === 0 && furnitureCategory === 'vinyl_storage'"
              class="normal-12 text-neutral-800"
              v-bind="{
                link: `${$addLocaleToPath('plp')}${$t(categoryPathKey)}?productLines=2`,
                textKey: 'pdp.configurator.modal.colors.go_to_another_configurator_venner',
                textLinkKey: 'common.original_classic_sideboard',
                linkClasses: 'text-orange hover:underline bold-12',
                interpolationKey: '0',
                newCard: true
              }"
            />
          </div>
        </div>
      </div>
    </aside>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { CATEGORIES } from '~/consts/categories';
import Warning from '~/assets/icons/warning.svg';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  shelfType: {
    type: Number,
    required: true
  },
  furnitureCategory: {
    type: String,
    required: true
  }
});

const i18n = useI18n();
const isModern = props.shelfType === 1;
const isVeneer = props.shelfType === 2;
const categories = CATEGORIES();

const categoryPathKey = categories[props.furnitureCategory === 'vinyl_storage' ? 'sideboard' : props.furnitureCategory]?.urlPathKey + '';

interface DataItem {
    shelfTypeTitle: string;
    colors: string[];
    defaultImage: string;
    title: string;
    description: string;
    badgeText: string;
    additionalTitle1?: string;
    additionalTitle2?: string;
    additionalDescription?: string;
    additionalLink?: string;
    additionalLinkText?: string;
    note?: string;
    excludedByCategory?: string[];
    excludedColorsByCategory?: { [key: string]: string[] };
}

type DataType = {
  [key: string]: DataItem[];
};

const dataRaw: DataType = {
  classic: [
    {
      shelfTypeTitle: '01p',
      colors: ['01p-white', '01p-grey', '01p-black'],
      defaultImage: 't01p/white',
      title: i18n.t('pdp.configurator.modal.color.section1.title'),
      description: i18n.t('pdp.configurator.modal.color.section1.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section1.label')
    },
    {
      shelfTypeTitle: '01p',
      colors: ['01p-dusty-pink', '01p-yellow', '01p-red', '01p-blue'],
      defaultImage: 't01p/yellow',
      title: i18n.t('pdp.configurator.modal.color.section2.title'),
      description: i18n.t('pdp.configurator.modal.color.section2.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section2.label'),
      excludedByCategory: ['desk']
    },
    {
      shelfTypeTitle: '01v',
      colors: ['01v-ash', '01v-oak'],
      defaultImage: 't01v/ash',
      title: i18n.t('pdp.configurator.modal.color.section3.title'),
      description: i18n.t('pdp.configurator.modal.color.section3.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section3.label'),
      note: i18n.t('pdp.configurator.modal.color.section3.note'),
      excludedByCategory: ['desk']
    }
  ],
  modern: [
    {
      shelfTypeTitle: '02',
      colors: ['02-white', '02-cotton', '02-grey', '02-stone-grey'],
      defaultImage: 't02/cotton',
      title: i18n.t('pdp.configurator.modal.color.section4.title'),
      description: i18n.t('pdp.configurator.modal.color.section4.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section4.label')
    },
    {
      shelfTypeTitle: '02',
      colors: ['02-sand', '02-grey-darkgrey', '02-stone-grey-walnut'],
      defaultImage: 't02/beige',
      excludedColorsByCategory: {
        desk: ['02-stone-grey-walnut']
      },
      title: i18n.t('pdp.configurator.modal.color.section5.title'),
      description: i18n.t('pdp.configurator.modal.color.section5.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section5.label')
    },
    {
      shelfTypeTitle: '02',
      colors: ['02-terracotta', '02-burgundy', '02-sage-green', '02-sky-blue', '02-midnight-blue'],
      defaultImage: 't02/sky-blue',
      title: i18n.t('pdp.configurator.modal.color.section6.title'),
      description: i18n.t('pdp.configurator.modal.color.section6.description'),
      badgeText: i18n.t('pdp.configurator.modal.color.section6.label'),
      excludedColorsByCategory: {
        desk: ['02-burgundy', '02-sky-blue', '02-midnight-blue']
      }
    },
    {
      shelfTypeTitle: '02',
      colors: ['02-reisingers-pink', '02-matte-black'],
      defaultImage: 't02/reisingers-pink',
      title: i18n.t('pdp.configurator.modal.color.section7.titile'),
      badgeText: i18n.t('pdp.configurator.modal.color.section7.label'),
      description: i18n.t('pdp.configurator.modal.color.section7.description1'),
      additionalTitle1: i18n.t('pdp.configurator.modal.color.section7.subtitile1'),
      additionalLinkText: i18n.t('pdp.configurator.modal.color.section7.link1'),
      additionalLink: ('lp.colab-andy'),
      additionalTitle2: i18n.t('pdp.configurator.modal.color.section7.subtitile2'),
      additionalDescription: i18n.t('pdp.configurator.modal.color.section7.description2'),
      excludedColorsByCategory: {
        desk: ['02-matte-black']
      }
    }
  ]
};

function filterByCategory (data: DataType, category: string): DataType {
  const filteredData: DataType = {};

  for (const key in data) {
    if (Array.isArray(data[key])) {
      filteredData[key] = data[key]
        .filter((item) => {
          return !item.excludedByCategory?.includes(category);
        })
        .map((item) => {
          if (item.colors && item.excludedColorsByCategory && item.excludedColorsByCategory[category]) {
            const filteredColors = item.colors.filter(color => !item.excludedColorsByCategory![category].includes(color));
            return {
              ...item,
              colors: filteredColors
            };
          }

          return item;
        });
    }
  }

  return filteredData;
}

const data = filterByCategory(dataRaw, props.category);

const activeTab = ref<keyof typeof data>(isModern ? 'modern' : 'classic');

const toggleOptions = [
  { label: i18n.t('pdp.configurator.modal.color.tab1'), value: 'classic' },
  { label: i18n.t('pdp.configurator.modal.color.tab2'), value: 'modern' }
];

const isDrawerOpen = ref(props.modelValue || false);

defineEmits<{ 'update:modelValue': [value: false]}>();

watch(() => props.modelValue, (value) => {
  isDrawerOpen.value = value;
});
</script>
