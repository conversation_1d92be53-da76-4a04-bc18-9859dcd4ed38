<template>
  <section class="relative aspect-[0.72/1] md:aspect-[45/23] bg-beige-100">
    <BasePicture
      img-classes="absolute block object-cover object-bottom w-full h-full"
      type="M T SD LD"
      v-bind="{
        path: `pdp/section-instagram/${furnitureType}/${furnitureCategory}`,
        alt: $t('pdp.instagram_hero.headline'),
        isRetinaUploaded: false
      }"
    />

    <div class="sticky top-0 py-64 grid-container z-1 md:py-48 lg:py-64 xl:py-96">
      <h2
        ref="subheading"
        class="mb-16 uppercase semibold-14 md:semibold-16 lg:semibold-18 md:mb-24 text-neutral-100"
        v-html="$t('pdp.instagram_hero.subheadline')"
      />
      <h1
        ref="heading"
        class="semibold-28 md:semibold-32 lg:semibold-46 xl:semibold-54 text-neutral-100"
        v-html="$t('pdp.instagram_hero.headline')"
      />

      <BaseLink
        variant="filled-dark"
        target="_blank"
        v-bind="{
          href: $addLocaleToPath('lp.gallery'),
          trackData: {
            eventType: 'NOEEC',
            eventCategory: 'pdp_clicks',
            eventAction: 'Get inspired',
            eventLabel: 'Discover more'
          }
        }"
        class="inline-flex mt-24 md:mt-32 xl:mt-48"
      >
        {{ $t('pdp.instagram_hero.cta') }}
      </BaseLink>
    </div>

    <BaseLink
      v-if="INSTAGRAM_AUTHORS[furnitureCategory as keyof Object][furnitureType as keyof Object]"
      target="_blank"
      v-bind="{
        variant: 'custom',
        href: $t(`pdp.instagram_hero.author.${furnitureCategory}.${furnitureType}.url`),
        trackData: {
          eventType: 'NOEEC',
          eventCategory: 'pdp_clicks',
          eventAction: 'Get inspired',
          eventLabel: 'Insta'
        }
      }"
      class="absolute left-0 right-0 flex justify-start md:justify-end grid-container bottom-16 md:bottom-40"
    >
      <Instagram class="w-[24px] h-[24px] mr-8" />
      <h3
        class="text-neutral-100 semibold-16"
        v-html="INSTAGRAM_AUTHORS[furnitureCategory as keyof Object][furnitureType as keyof Object]"
      />
    </BaseLink>
  </section>
</template>

<script setup lang="ts">
import { INSTAGRAM_AUTHORS } from '~/consts/productPage';

import Instagram from '~/assets/icons/social/instagram.svg';

const props = defineProps({
  furnitureCategory: {
    type: String as PropType<'bookcase' | 'wallstorage' | 'desk' | 'vinylstorage' | 'sideboard' | 'chest' | 'bedsidetable' | 'tvstand' | 'wardrobe' | 'shoerack'>,
    required: true
  },
  shelfType: {
    type: Number,
    required: true
  }
});

const furnitureType = computed(() => GET_SHELF_TYPE(props.shelfType as SHELF_TYPE));
</script>
