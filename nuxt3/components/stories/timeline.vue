<template>
  <div class="absolute left-0 top-0 w-full flex gap-8 md:gap-16 px-24 md:px-32 pt-16">
    <div
      v-for="(story, index) in stories"
      v-bind:key="index"
      class="flex-1 h-[3px] bg-white/30 rounded-full"
    >
      <div
        class="bg-white rounded-full h-full max-w-full"
        v-bind:style="{ width: `${ index === currentIndex ? progress : (index < currentIndex ? 100 : 0)}%` }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StoryOptions } from '~/components/stories/index.vue';

defineProps<{
  stories: Array<StoryOptions>,
  currentIndex: number,
  progress: number,
}>();

</script>
