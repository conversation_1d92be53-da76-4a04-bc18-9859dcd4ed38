<template>
  <section class="py-48 lg:pb-[104px] overflow-hidden lg:pt-96">
    <div class="grid-container grid-cols">
      <p class="col-span-12 lg:col-span-1 ty-tagline ty-tagline--s lg:ty-tagline--l lg-max:mb-16 relative">
        <span
          class="inline-block top-left lg:absolute whitespace-nowrap"
          v-html="title"
        />
      </p>
      <div class="col-span-12 lg:col-span-5 xl:col-span-4 xl2:col-span-3 lg:col-start-2 lg-max:mb-24 lg-max:h-40">
        <ToggleScroll
          v-if="filters.length"
          v-model="activeFilter"
          v-bind="{
            options: filters,
            toggleButtonClasses: 'semibold-20 lg:semibold-44 xl:semibold-54 text-neutral-900 lg-max:mr-16 lg-max:last:mr-0 hover:active',
            toggleWrapperClasses: 'flex lg:flex-col lg:items-start lg:gap-24 whitespace-nowrap',
            scrollAreaClasses: 'lg:overflow-hidden',
            toggleActiveButtonClasses: 'active',
            overlayClasses: 'hidden',
            variant: 'underlined',
          }"
        />
        <div class="mt-96">
          <slot
            v-if="!isMobileOrTabletViewport"
          />
        </div>
      </div>

      <CarouselGeneric
        ref="carousel"
        class="col-span-12 lg:col-span-5 lg:col-start-7 overflow-left-hidden"
        v-bind="{
          columnsOnDesktop: 3,
          swiperDefaultControls: false
        }"
      >
        <div
          v-for="(item, index) in data[activeFilter]"
          v-bind:key="index"
          class="swiper-slide"
          data-testid="half-carousel-swiper-slide"
        >
          <div class="group/plp-product-card block w-full h-full">
            <div class="relative">
              <BasePicture
                class="!bg-transparent"
                data-testid="half-carousel-image"
                v-bind="{
                  imgClasses: ['md:aspect-[4/3]'],
                  isRetinaUploaded: false,
                  forceSourceReverse: true,
                  alt: '',
                  path: item.imgPath,
                  type: 'M D'
                }"
              />
              <p
                v-if="item.fotoCaption"
                class="semibold-16 text-white absolute bottom-12 right-20"
                data-testid="half-carousel-photo-caption"
              >
                {{ item.fotoCaption }}
              </p>
            </div>
            <div
              class="origin-top-left scale-[0.8]"
            >
              <BasePicture
                class="!bg-transparent mt-24 lg:mt-32"
                data-testid="half-carousel-logo"
                v-bind="{
                  isRetinaUploaded: false,
                  forceSourceReverse: true,
                  imgClasses: '!w-auto',
                  alt: '',
                  path: item.secondaryImgPath,
                  type: 'M D'
                }"
              />
            </div>
          </div>
        </div>
      </CarouselGeneric>
      <div
        v-if="isMobileOrTabletViewport"
        class="col-span-12 flex justify-center mt-24"
      >
        <slot />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">

import useMq from '~/composables/useMq';

const carousel = ref(null);

const props = defineProps({
  filters: {
    type: Array,
    default: () => []
  },
  data: {
    type: Object,
    required: true
  },
  title: {
    type: String,
    required: true
  }
});

const activeFilter = ref(props.filters[0]?.value);

watch(activeFilter, () => {
  carousel.value.handleGoToFirstSlide();
  setTimeout(() => {
    carousel.value.swiperInstance.update();
  }, 500);
});
const { isMobileOrTabletViewport } = useMq();

</script>

<style lang="scss" scoped>
.overflow-left-hidden {
  @screen lg {
    clip-path: inset(-100vw -100vw -100vw 0);
  }
}

:deep(.swiper-slide) {
  @apply w-full mr-[var(--gutter)] mr-0 pr-16;
}

:deep(.swiper-scrollbar) {
  @apply lg:hidden;
}

:deep(.swiper-button-prev) {
  @screen lg {
    left: calc(50% + 24px) !important;
  }
}

.top-left {
    @screen lg {
        transform-origin: top left;
        left: 0;
        top: 0;
        transform: rotate(-90deg) translateX(-100%);
    }
}

.two-liner {
  -webkit-box-orient: vertical; /* stylelint-disable-line property-no-vendor-prefix */
  display: -webkit-box; /* stylelint-disable-line value-no-vendor-prefix */
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>
