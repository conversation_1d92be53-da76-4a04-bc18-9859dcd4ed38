<template>
  <section
    class="relative w-full flex items-end
            aspect-[--video-aspect-ratio-mobile] md:aspect-[--video-aspect-ratio-desktop] video-fill-viewport__max-height"
    v-bind:class="{
      'video-fill-viewport': fillViewport
    }"
    v-bind:style="`--video-aspect-ratio-mobile: ${videoParams?.aspectRatio?.mobile}; --video-aspect-ratio-desktop: ${videoParams?.aspectRatio?.desktop};`"
  >
    <div class="absolute inset-0 z-0 w-full h-full">
      <BasePicture
        v-if="videoPlaceHolder"
        picture-classes="w-full h-full object-cover"
        img-classes="w-full h-full object-cover object-center h-full"
        v-bind="{
          type: placeholderType,
          disableLazy,
          alt: '',
          path: videoPlaceHolder
        }"
      />
      <BaseVideoWistia
        class="!absolute inset-0 object-cover w-full h-full"
        v-bind="{
          videoId: videoParams?.videoId,
          showPlaceholder: !videoPlaceHolder,
          embedOptions: {
            fitStrategy: 'cover',
            externalMuteControl: hasSound,
            externalMuteValue: isMuted
          },
        }"
      />
    </div>
    <aside class="grid-container w-full relative z-1 py-32 md:py-48 lg:py-64 xl:py-96">
      <h2
        v-if="tagline"
        class="uppercase semibold-12 md:semibold-14 lg:semibold-16 text-neutral-200 text-pretty"
        v-html="tagline"
      />
      <h1
        v-if="title"
        class="semibold-32 md:semibold-44 lg:semibold-54 mt-4 md:mt-8 text-white text-balance"
        v-html="title"
      />
      <p
        v-if="description"
        class="normal-16 lg:normal-18 mt-4 md:mt-8 text-white text-balance"
        v-html="description"
      />
      <BaseLink
        v-if="ctaCopy && ctaUrl"
        class="mt-24 md:mt-32 ty-btn--xl"
        v-bind="{
          ...ctaUrl && { href: ctaUrl },
          variant: 'filled-dark',
          trackData: {
            eventLabel: 'cta',
            eventPath: ctaUrl
          }
        }"
      >
        {{ ctaCopy }}
      </BaseLink>
    </aside>
    <button
      v-if="hasSound"
      class="wistia__button text-offwhite-700 pointer-events-auto absolute z-1
            right-16 md:right-32 lg:right-48 xl:right-56
            bottom-28 md:bottom-48 lg:bottom-64 xl:bottom-96"

      v-on:click="isMuted = !isMuted"
      v-html="isMuted ? IconSound : IconMute"
    />
  </section>
</template>

<script setup lang="ts">
import IconSound from '~/assets/icons/video/sound.svg?raw';
import IconMute from '~/assets/icons/video/mute.svg?raw';

interface VideoParams {
  videoId: {
    mobile: string;
    desktop: string;
  };
  aspectRatio: {
    mobile: string;
    desktop: string;
  };
}

defineProps({
  tagline: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  videoParams: {
    type: Object as PropType<VideoParams>,
    default: () => ({})
  },
  fillViewport: {
    type: Boolean,
    default: false
  },
  hasSound: {
    type: Boolean,
    default: false
  },
  videoPlaceHolder: {
    type: String,
    default: ''
  },
  disableLazy: {
    type: Boolean,
    default: false
  },
  placeholderType: {
    type: String,
    default: 'M T SD LD XLD'
  },
  ctaCopy: {
    type: String,
    default: ''
  },
  ctaUrl: {
    type: String,
    default: ''
  }
});

const isMuted = ref<boolean>(true);

</script>

<style lang="scss">
.video-fill-viewport {

  &__max-height {
    @apply max-h-screen md:min-h-[30vw];
    @apply max-h-[calc((100svh)-var(--ribbon-height)-var(--old-navbar-height))];
  }

  &__min-height {
    @apply min-h-[calc((100vw)*var(--video-aspect-ratio-mobile))];
    @apply md:min-h-[calc((100vw)*var(--video-aspect-ratio-desktop))];
  }
}
</style>
