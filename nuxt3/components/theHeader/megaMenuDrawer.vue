<template>
  <div
    id="mega-menu"
    ref="megaMenuEl"
    data-testid="mega-menu"
    class="grid-container mx-auto pb-24
           lg-max:absolute top-0 w-full overflow-y-auto
           lg:max-h-[calc(100vh-var(--ribbon-height)-var(--header-height))]
           lg-max:h-[calc(100vh-var(--ribbon-height))]"
  >
    <TabGroup
      as="div"
      v-bind:selected-index="selectedIndex"
      v-on:change="onChangeTab"
    >
      <TabList class="lg-max:hidden bg-white flex items-center space-x-1 rounded-xl bg-blue-900/20 pb-8 sticky top-0 z-1 pt-16 lg:h-80">
        <Tab
          v-for="(tabNameKey, index) in ['menu.labels.category', 'menu.labels.spaces']"
          v-bind:key="index"
          class="relative pb-8 mr-32 last:mr-0
                   bold-14 text-grey-900 hover:text-offblack-900 uppercase
                   transition-colors duration-300 ease-in-out
                   after:absolute after:left-0 after:bottom-0 after:w-[70%] after:h-[1px] after:bg-offblack-900
                   after:transform after:scale-x-0 after:origin-left
                   after:transition-transform after:duration-300 after:ease-in-out
                   ui-selected:text-offblack-900 ui-selected:after:!scale-x-100"
        >
          {{ $t(tabNameKey) }}
        </Tab>
        <Tab class="hidden">
          Links
        </Tab>
      </TabList>
      <div
        ref="megaMenuTabsEl"
        class="basic-transition"
        style="transition-property: height;"
        v-bind:style="{ 'height': isTabSwitchAnimationActive ? `${preservedTabHeight}px`: 'auto' }"
      >
        <div
          ref="megaMenuToggleTabsEl"
          class="grid items-start grid-cols-1 lg:grid-cols-12 gap-x-32 auto-rows-min"
        >
          <div class="lg:col-span-10">
            <Transition
              v-bind="transitionProps"
              v-on="transitionEvents"
            >
              <TheHeaderNavigationTiles
                v-if="selectedIndex == 0"
                event-action="category"
                title="Categories"
                tab-name="categories"
                v-bind="{
                  title: $t('menu.labels.categories'),
                  tilesList: categories
                }"
                v-on:on-close="selectedIndex = 2"
              />
              <TheHeaderNavigationTiles
                v-else-if="selectedIndex == 1"
                event-action="rooms"
                tab-name="rooms"
                v-bind="{
                  title: $t('menu.labels.spaces'),
                  tilesList: linkList.linkListSpaces
                }"
                v-on:on-close="selectedIndex = 2"
              />
              <TheHeaderMegaMenuNavigationLinks
                v-else-if="selectedIndex == 2"
                class="lg:!hidden"
                v-on:on-sub-menu-clicked="showDrawer"
              />
              <LazyTheHeaderLangDrawer
                v-else-if="selectedIndex == 3"
                v-on:on-close="selectedIndex = 2"
              />
            </Transition>
          </div>
          <TheHeaderMegaMenuNavigationLinks class="lg-max:hidden lg:col-span-2" />
        </div>
      </div>
    </TabGroup>
  </div>
</template>

<script lang="ts" setup>
import { Tab, TabGroup, TabList } from '@headlessui/vue';
import useMq from '~/composables/useMq';
import type { MobileNestedDrawerType } from '~/composables/useHeader';

const megaMenuEl = ref<HTMLElement>();
const { isMegaMenuOpened, linkList, isLangMenuOpened, changeHeaderState } = useHeader();
const { isDesktopViewport } = useMq();

const categories = linkList.linkListByCategory.map(category => ({
  navigationImagePath: `common/menu/categories/${category.name}`,
  ...category
}));
const selectedIndex = ref(isDesktopViewport.value ? 0 : 2);

watch([isDesktopViewport, isMegaMenuOpened], () => {
  selectedIndex.value = isDesktopViewport.value ? 0 : 2;
  isMegaMenuOpened.value && resetMegaMenuScrollTop();
  !isDesktopViewport.value && isLangMenuOpened.value && changeHeaderState('isLangMenuOpened', false);
});

const preservedTabHeight = ref(0);
const isTabSwitchAnimationActive = ref(false);
const megaMenuToggleTabsEl = ref();
const megaMenuTabsEl = ref();

const showDrawer = (drawerName: MobileNestedDrawerType) => {
  switch (drawerName) {
    case MobileNestedDrawerName.CATEGORIES:
      selectedIndex.value = 0;
      break;
    case MobileNestedDrawerName.ROOMS:
      selectedIndex.value = 1;
      break;
    case MobileNestedDrawerName.REGION:
      selectedIndex.value = 3;
      break;
    default:
      selectedIndex.value = 2;
      break;
  }
};

const updatePreservedTabHeight = (el?:HTMLElement) => {
  preservedTabHeight.value = el
    ? megaMenuToggleTabsEl.value?.offsetHeight // + el.offsetHeight
    : megaMenuTabsEl.value?.offsetHeight;
};

const resetMegaMenuScrollTop = () => {
  megaMenuEl.value && (megaMenuEl.value.scrollTop = 0);
};

const onChangeTab = (index:number) => {
  isTabSwitchAnimationActive.value = true;
  selectedIndex.value = index;
};

const transitionProps = {
  name: 'fade-tab',
  mode: 'out-in'
};
const transitionEvents = {
  beforeLeave: () => {
    updatePreservedTabHeight();
    isTabSwitchAnimationActive.value = true;
  },
  afterLeave: resetMegaMenuScrollTop,
  enter: (el:HTMLElement) => updatePreservedTabHeight(el),
  afterEnter: () => (isTabSwitchAnimationActive.value = false)
};
</script>
