<template>
  <nav class="lg-max:pt-64 grid grid-cols-1 gap-16 transition-transform md:grid-cols-2 auto-rows-min lg:flex lg:flex-col lg:gap-24 lg:sticky lg:top-80 lg-max:transform basic-transition">
    <TheHeaderMegaMenuLink
      v-for="(link, index) in links"
      v-bind:key="index"
      v-bind="{
        ...link.attrs,
        class: link.className
      }"
      v-on="link.listeners || {}"
    />
    <BaseLink
      class="relative"
      v-bind="{
        href: `${$addLocaleToPath('plp')}?productLines=1,3,6,7,8,0,2,4,5`,
        variant: 'custom',
        trackData: { eventLabel: 'cta', eventPath: `${$addLocaleToPath('plp')}?productLines=1,3,6,7,8,0,2,4,5` }
      }"
    >
      <aside
        v-if="extraData && extraData.navBanner"
        class="w-full relative rounded-20 border-2 aspect-[404/600] custom-badge"
        v-bind:style="{
          backgroundColor: bannerBackgroundColor,
          borderColor: bannerBorderColor,
          color: bannerTextColor
        }"
      >
        <p
          class="absolute top-0 left-0 w-full py-12 px-20 bold-24 md:bold-32"
          v-html="$t('menu.mega.banner.winterSale', { value: promoValue })"
        />
      </aside>
      <data v-else>
        <BasePicture
          img-classes="object-cover object-center w-full"
          v-bind="{
            type: 'M T SD LD XLD',
            path: '/common/menu/megamenu/banner25062025',
            disablePlaceholder: true,
            alt: $t('menu.banner.springsale')
          }"
        />
        <div class="absolute top-0 left-0 w-full p-16 xl:p-20 md:p-12 text-balance text-offwhite-700">
          <h1
            class="bold-24 md:bold-16 lg2:bold-18 xl:bold-20 text-balance"
            v-html="$t('menu.banner.sofas.headline')"
          />
        </div>
      </data>
    </BaseLink>
  </nav>
</template>

<script lang="ts" setup>
import useHeader, { MobileNestedDrawerName, type MobileNestedDrawerType } from '~/composables/useHeader';

const props = defineProps({
  isDesktop: {
    type: Boolean,
    default: false
  }
});

const i18n = useI18n();

const { value } = usePromoStore();
const { regionChangeLabel } = useHeader();
const { $addLocaleToPath } = useNuxtApp();
const { extraData, IS_SMOOTH_AVAILABLE } = useGlobal();

const isSamplePromoActive = useFeatureFlag('isSamplePromoActive');

const emit = defineEmits<{(e: 'onSubMenuClicked', drawerName: MobileNestedDrawerType): void }>();

const showDrawer = (drawerName: MobileNestedDrawerType) => {
  emit('onSubMenuClicked', drawerName);
};

const links = [
  // CATEGORIES
  {
    attrs: {
      'data-testid': 'mobile-menu-shop-0',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'category',
        eventLabel: 'opened'
      },
      drawerName: MobileNestedDrawerName.CATEGORIES,
      label: i18n.t('menu.labels.category'),
      imagePath: 'common/menu/megamenu/categories',
      imageAlt: i18n.t('menu.labels.category')
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden',
    listeners: { openDrawer: showDrawer }
  },
  // INFLUENCERS
  {
    attrs: {
      'data-testid': 'mobile-menu-influencers',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'influencers',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.influencers'),
      label: i18n.t('menu.labels.influencers'),
      imagePath: 'common/menu/megamenu/influencers',
      imageAlt: i18n.t('menu.labels.influencers')
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden',
    listeners: {}
  },
  // INSPIRATION
  {
    attrs: {
      'data-testid': 'mobile-menu-inspiration',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'inspiration',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.gallery'),
      label: i18n.t('menu.labels.inspiration'),
      imagePath: 'common/menu/megamenu/inspiration',
      imageAlt: i18n.t('menu.labels.category')
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden',
    listeners: {}
  },
  // SALE
  {
    attrs: {
      'data-testid': 'redirect-sale',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'sale',
        eventLabel: 'opened'
      },
      href: `${$addLocaleToPath('plp')}?productLines=1,3,6,7,8,0,2,4,5`,
      label: 'SALE'
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden text-orange semibold-16 mt-16',
    listeners: {}
  },
  // ROOMS
  {
    attrs: {
      'data-testid': 'mobile-menu-shop-1',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'rooms',
        eventLabel: 'opened'
      },
      drawerName: MobileNestedDrawerName.ROOMS,
      label: i18n.t('menu.labels.spaces')
    },
    className: 'md:col-span-2 lg:hidden',
    listeners: { openDrawer: showDrawer }
  },
  // ABOUT TYLKO
  {
    attrs: {
      'data-testid': 'redirect-about-tylko',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'about-tylko',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.about-tylko'),
      label: i18n.t('menu.banner.whytylko')
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden',
    listeners: {}
  },
  // TYLKO FOR BUSINESS
  {
    attrs: {
      'data-testid': 'redirect-tylko-pro',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'tylko_pro',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.tylkopro'),
      label: i18n.t('menu.labels.tylko_pro')
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden',
    listeners: {}
  },
  // PRODUCT LINES
  {
    attrs: {
      'data-testid': 'product-lines',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'product_lines',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('product-lines.index'),
      label: i18n.t('menu.labels.product-lines')
    },
    className: 'md:col-span-2',
    listeners: {}
  },
  // MATERIAL SAMPLES
  {
    attrs: {
      'data-testid': 'redirect-samples',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'material_samples',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.samples'),
      label: i18n.t('menu.labels.material-samples'),
      badgeLabel: isSamplePromoActive ? i18n.t('promo_label_sale') : ''
    },
    className: 'md:col-span-2',
    listeners: {}
  },
  // reviews
  {
    attrs: {
      'data-testid': 'redirect-reviews',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'reviews',
        eventLabel: 'opened'
      },
      href: $addLocaleToPath('lp.reviews'),
      label: i18n.t('menu.labels.reviews')
    },
    className: 'md:col-span-2',
    listeners: {}
  },
  // regions
  {
    attrs: {
      'data-testid': 'change-region',
      trackData: {
        eventCategory: 'megamenu',
        eventAction: 'language_regions',
        eventLabel: 'opened'
      },
      drawerName: MobileNestedDrawerName.REGION,
      label: regionChangeLabel.value
    },
    isMobileOnly: true,
    className: 'md:col-span-2 lg:hidden pt-16 border-t border-grey-600',
    listeners: { openDrawer: showDrawer }
  }
].filter(link => props.isDesktop ? !link?.isMobileOnly : true);

const bannerTextColor = computed(() => extraData?.textColor);
const bannerBackgroundColor = computed(() => extraData?.backgroundColor);
const bannerBorderColor = computed(() => extraData?.borderColor);
const bannerBadgeTextColor = computed(() => extraData?.badgeTextColor);

const promoValue = parseInt(value);
</script>

<style lang="scss">
.custom-badge {
  :deep(span) {
    @apply rounded-8 px-8 py-4 inline-block;
    background-color: v-bind(bannerTextColor);
    color: v-bind(bannerBadgeTextColor);
  }
}
</style>
