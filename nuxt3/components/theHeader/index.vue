<template>
  <header
    ref="header"
    class="lg:sticky top-0 z-4 lg-max:transform lg-max:basic-transition lg-max:transition-transform"
    data-section="header"
    v-bind:class="[
      { 'pointer-events-none': isNavBarHidden },
      (isHeaderHidingAvailable && isMounted) ? (isConfiguratorVisible ? 'relative' : 'sticky') : 'sticky',
      isHeaderHidingAvailable && (headerIsVisible ? 'lg-max:translate-y-0' : 'lg-max:-translate-y-[calc(var(--ribbon-height))]')
    ]"
  >
    <Transition name="fade">
      <div
        v-show="isAnyMenuOpened"
        class="fixed h-screen bg-black/60 inset-0"
        v-on="{ click: setHeaderDefaultState }"
      />
    </Transition>
    <TheHeaderRibbon v-if="ribbonData && ribbonData.enabled" />
    <TheHeaderNavigationBar v-bind="{ isConfiguratorVisible }" />
  </header>
</template>

<script setup lang="ts">
import { useElementVisibility, useMounted } from '@vueuse/core';
import { useScartStore } from '~/stores/scart';

defineProps({
  hidePromo: {
    type: Boolean,
    default: false
  }
});

const {
  isNavBarHidden,
  isMegaMenuOpened,
  setHeaderDefaultState,
  isAnyMenuOpened
} = useHeader();
const { ribbon: ribbonData } = storeToRefs(useGlobal());
const { $dixa } = useNuxtApp();
const { isMobileOrTabletViewport } = useMq();
const cart = useScartStore();
const route = useRoute();
const {
  isOriginalRowABTest,
  isOriginalColABTest,
  shelfType,
  configuratorType,
  isConfiguratorVisible
} = useConfigurator();

const getRouteBaseName = useRouteBaseName();
const routeBaseName = getRouteBaseName(route);
isConfiguratorVisible.value = routeBaseName === 'pdp';
const header = ref<HTMLElement | null>(null);
const isMounted = useMounted();
const headerIsVisible = useElementVisibility(header);

const isHeaderHidingAvailable = computed(() => (
  ([6, 7, 8].includes(shelfType!) ||
(isOriginalRowABTest.value && configuratorType === 1) ||
(isOriginalColABTest.value && configuratorType === 2)
  ) && isConfiguratorVisible.value));

watch([() => cart.isCartOpen, isMegaMenuOpened], () => {
  if (
    isMegaMenuOpened.value ||
    isMobileOrTabletViewport.value ||
    cart.isCartOpen
  ) {
    $dixa.setWidgetVisibility(false);
  } else {
    $dixa.setWidgetVisibility(true);
  }
});
</script>
