<template>
  <div
    class="h-[--header-height] transition-transform basic-transition relative"
    v-bind:class="[
      isNavBarHidden ? '-translate-y-[--header-height]' : '-translate-y-0',
    ]"
  >
    <div class="h-full bg-white border-b lg:relative lg:z-4 border-offwhite-900">
      <nav
        aria-label="Main Navigation"
        class="flex items-center h-full mx-auto main-nav-bar grid-container text-offblack-600 normal-14 xl:normal-16"
      >
        <ul class="flex items-center flex-1 lg-max:hidden">
          <li class="lg-max:hidden">
            <BaseLink
              variant="custom"
              data-testid="toggle-sale-tab"
              class="text-orange semibold-16"
              v-bind="{
                href: `${$addLocaleToPath('plp')}?productLines=1,3,6,7,8,0,2,4,5`,
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'sale',
                  eventLabel: 'opened'
                },
              }"
            >
              SALE
            </BaseLink>
          </li>
          <li>
            <!--        SHOP -->
            <BaseButton
              variant="custom"
              data-testid="toggle-products-tab"
              v-bind="{
                class: { '!text-orange': isMegaMenuOpened },
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'shop',
                  eventLabel: 'opened'
                },
              }"
              v-on:click="toggleMegaMenu"
            >
              {{ $t('menu.labels.shop') }}
              <IconCaretDown
                width="20"
                height="20"
                class="ml-4 transition-transform basic-transition"
                v-bind:class="isMegaMenuOpened ? 'rotate-180' : 'rotate-0'"
              />
            </BaseButton>
          </li>
          <li class="lg-max:hidden">
            <BaseLink
              variant="custom"
              data-testid="toggle-influencers-tab"
              v-bind="{
                href: $addLocaleToPath('lp.influencers'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'influencers',
                  eventLabel: 'opened'
                },
              }"
            >
              {{ $t('menu.labels.influencers') }}
            </BaseLink>
          </li>
          <li class="lg-max:hidden">
            <BaseLink
              variant="custom"
              data-testid="toggle-inspirations-tab"
              v-bind="{
                href: $addLocaleToPath('lp.gallery'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'inspiration',
                  eventLabel: 'opened'
                },
              }"
            >
              {{ $t('menu.labels.inspiration') }}
            </BaseLink>
          </li>
          <li class="lg-max:hidden">
            <BaseLink
              variant="custom"
              data-testid="redirect-about-tylko"
              v-bind="{
                href: $addLocaleToPath('lp.about-tylko'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'about_tylko',
                  eventLabel: 'opened'
                },
              }"
            >
              {{ $t('menu.banner.whytylko') }}
            </BaseLink>
          </li>
          <li class="lg-max:hidden">
            <BaseLink
              variant="custom"
              data-testid="redirect-tylko-pro"
              v-bind="{
                href: $addLocaleToPath('lp.tylkopro'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'tylko_for_business',
                  eventLabel: 'opened'
                },
              }"
            >
              {{ $t('menu.labels.tylko_pro') }}
            </BaseLink>
          </li>
        </ul>

        <!--        LOGO CENTRED ON DESKTOP -->
        <BaseLink
          variant="custom"
          data-testid="logo"
          v-bind="{ href: $addLocaleToPath('homepage'), trackData: {} }"
        >
          <IconTylkoLogo class="h-24 text-orange lg-max:-mx-8" />
        </BaseLink>

        <!--      INDICATORS -->
        <ul class="flex justify-end items-center flex-1 -mr-12 md:mr-[-6px] xl:mr-[-12px]">
          <!--        CHANGE REGION-->
          <li class="lg-max:hidden">
            <BaseButton
              variant="custom"
              data-testid="change-region"
              v-bind="{
                class: { '!text-orange': isLangMenuOpened },
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'language_regions',
                  eventLabel: 'opened'
                },
              }"
              v-on:click="toggleLangMenu"
            >
              {{ regionChangeLabel }}
              <IconCaretDown
                width="20"
                height="20"
                class="ml-4 transition-transform basic-transition"
                v-bind:class="isLangMenuOpened ? 'rotate-180' : 'rotate-0'"
              />
            </BaseButton>
          </li>
          <!-- CONTENT CARDS BY BRAZE -->
          <li>
            <BaseButton
              id="content-card-show"
              class="relative"
              variant="custom"
              v-bind:track-data="{
                eventAction: 'Content Card Show',
                eventCategory: 'Content Card',
                eventLabel: 'Content Card Label'
              }"
              v-on:click="() => $braze?.showFeedDrawer()"
            >
              <IconNotification
                width="24"
                height="24"
              />
              <ClientOnly>
                <BadgeCounter
                  v-if="brazeNotificationCount && brazeNotificationCount > 0"
                  class="navbar-item-counter"
                  v-bind="{
                    count: brazeNotificationCount,
                  }"
                />
              </ClientOnly>
            </BaseButton>
          </li>
          <li>
            <BaseLink
              variant="custom"
              data-testid="redirect-account"
              v-bind="{
                href: $addLocaleToURL('/account'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'account',
                  eventLabel: 'opened'
                },
              }"
              as-nuxt-link
              v-on:click="toggleAccountMenu"
            >
              <IconProfile
                width="24"
                height="24"
              />
            </BaseLink>
          </li>
          <li>
            <BaseLink
              variant="custom"
              class="relative"
              data-testid="redirect-wishlist"
              v-bind="{
                href: $addLocaleToPath('library'),
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'favourites',
                  eventLabel: 'opened'
                }
              }"
            >
              <IconHeart
                width="24"
                height="24"
              />
              <ClientOnly>
                <BadgeCounter
                  v-if="libraryItemsCount && libraryItemsCount > 0"
                  class="navbar-item-counter"
                  v-bind="{
                    count: libraryItemsCount,
                  }"
                />
              </ClientOnly>
            </BaseLink>
          </li>
          <li>
            <BaseLink
              as-nuxt-link
              class="relative"
              variant="custom"
              data-testid="open-cart"
              v-bind="{
                href: `${$addLocaleToURL('/cart')}${(userId && isMounted) ? '?uuid='+userId : ''}`,
                trackData: {
                  eventCategory: 'navigation',
                  eventAction: 'basket',
                  eventLabel: 'opened'
                }
              }"
              v-on:click="setHeaderDefaultState"
            >
              <IconTrolley
                width="24"
                height="24"
              />
              <ClientOnly>
                <BadgeCounter
                  v-if="cartItemsCount && cartItemsCount > 0"
                  class="navbar-item-counter"
                  v-bind="{ count: cartItemsCount }"
                />
              </ClientOnly>
            </BaseLink>
          </li>
          <li class="lg:hidden relative z-[49]">
            <TheHeaderHamburger v-on:click="onHamburgerClick" />
          </li>
        </ul>
      </nav>
    </div>
    <div
      id="nav-portal"
      class="pointer-events-none"
    />
    <Transition name="navigation-reveal">
      <div
        v-show="isMegaMenuOpened"
        class="navigation-drawer"
        data-testid="mega-menu"
      >
        <LazyTheHeaderMegaMenuDrawer hydrate-on-idle/>
      </div>
    </Transition>
    <Transition name="navigation-reveal">
      <div
        v-show="isLangMenuOpened"
        class="navigation-drawer"
        data-testid="lang-menu"
      >
        <LazyTheHeaderLangDrawer hydrate-on-idle/>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import { useCssVar, useScrollLock, useMounted } from '@vueuse/core';
import { useTrackingDOM } from '~/composables/useTracking';
import useHeader from '~/composables/useHeader';

defineProps({
  isConfiguratorVisible: {
    type: Boolean,
    default: false
  }
});

const { $braze } = useNuxtApp();
const {
  brazeNotificationCount,
  cartItemsCount,
  libraryItemsCount,
  userId
} = storeToRefs(useGlobal());
const { track } = useTrackingDOM();
const { regionChangeLabel } = useHeader();
const isMounted = useMounted();

const {
  headerHeight,
  ribbonHeight,
  isNavBarHidden,
  isAnyMenuOpened,
  isMegaMenuOpened,
  isLangMenuOpened,
  changeHeaderState,
  setHeaderDefaultState
} = useHeader();

if (import.meta.browser) {
  const isLocked = useScrollLock(window);

  watch(isAnyMenuOpened, (value: boolean) => (isLocked.value = value));

  const currentHeaderHeightCssVar = useCssVar('--current-header-height');

  watch([isNavBarHidden, headerHeight, ribbonHeight], () => {
    currentHeaderHeightCssVar.value = `${headerHeight.value}px`;
  }, { immediate: true });
}

const trackDrawerOpen = (element:HTMLElement, name: string) => {
  track(element, {
    eventCategory: 'navigation',
    eventAction: name,
    eventLabel: 'opened'
  });
};

const onHamburgerClick = (event:PointerEvent) => {
  if (isMegaMenuOpened.value || isLangMenuOpened.value) {
    changeHeaderState('isLangMenuOpened', false);
    changeHeaderState('isMegaMenuOpened', false);
  } else {
    changeHeaderState('isMegaMenuOpened', true);
  }

  isMegaMenuOpened.value && trackDrawerOpen(event.target as HTMLElement, 'megamenu');
};

const toggleMegaMenu = (event:PointerEvent) => {
  changeHeaderState('isLangMenuOpened', false);
  changeHeaderState('isMegaMenuOpened', !isMegaMenuOpened.value);
  isMegaMenuOpened.value && trackDrawerOpen(event.target as HTMLElement, 'megamenu');
};

const toggleLangMenu = (event:PointerEvent) => {
  changeHeaderState('isMegaMenuOpened', false);
  changeHeaderState('isLangMenuOpened', !isLangMenuOpened.value);
  isLangMenuOpened.value && trackDrawerOpen(event.target as HTMLElement, 'language_regions');
};

const toggleAccountMenu = () => {
  changeHeaderState('isLangMenuOpened', false);
  changeHeaderState('isMegaMenuOpened', false);
};

</script>
