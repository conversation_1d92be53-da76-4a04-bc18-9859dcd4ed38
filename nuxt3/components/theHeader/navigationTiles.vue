<template>
  <div>
    <TheHeaderMegaMenuTab
      v-bind="{
        title,
        tabName,
      }"
      v-on:on-back="$emit('onClose')"
    />
    <nav class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 xl2:grid-cols-6 gap-16 bg-white lg-max:pb-16 lg:auto-rows-min">
      <BaseLink
        v-for="tile in tilesList"
        v-bind:key="`tile-${eventAction}-${tile.name}`"
        variant="custom"
        class="block normal-14 text-offblack-800 group bg-offwhite-600
             hover:text-offblack-900 text-center hover:bg-grey-600
             transition-colors base-transition"

        v-bind="{
          href: eventAction === 'category' ? `${$addLocaleToPath('plp')}${!isNil(tile.urlPath) ? tile.urlPath : $t(tile.urlPathKey)}` : $addLocaleToPath(tile.urlPathKey),
          trackData: {
            eventCategory: 'megamenu',
            eventAction,
            eventLabel: tile.trackDataEventLabel
          },
          'data-testid': tile['data-testid']
        }"
      >
        <div class="overflow-hidden relative">
          <BasePicture
            class="object-cover w-full aspect-[981/481]"
            img-classes="object-cover w-full aspect-[981/481] group-hover:scale-[1.02] transition-transform base-transition"
            type="A"
            disable-placeholder
            v-bind="{
              path: tile.navigationImagePath,
              alt: $t(tile.nameKey),
            }"
          />
          <BaseBadge
            v-if="extraData?.springSale && springSaleBadges[tile.categoryIndex]"
            variant="springsale"
            class="absolute top-8 right-8 rounded-30"
          >
            {{ springSaleBadges[tile.categoryIndex] }}
          </BaseBadge>
          <BaseBadge
            v-else-if="eventAction === 'category' && extraData?.navigationBadges"
            variant="generic"
            class="absolute top-8 right-8 rounded-30"
            v-bind:style="{ color: badgeTextColor, backgroundColor: badgeBackgroundColor }"
          >
            <span v-html="`-${parseInt(value)}%`" />
          </BaseBadge>
          <BaseBadge
            v-else-if="tile.labelKey"
            variant="attribute"
            class="absolute top-8 right-8 rounded-30"
          >
            <span v-html="$t(tile.labelKey)" />
          </BaseBadge>
        </div>
        <span
          class="inline-block my-8"
          v-html="$t(tile.pluralNameKey || tile.nameKey)"
        />
      </BaseLink>
    </nav>
  </div>
</template>

<script lang="ts" setup>
import { isNil } from 'lodash-es';
import usePromoBadgeAddon from '~/composables/usePromoBadgeAddon';

defineProps({
  tilesList: {
    type: Array,
    required: true
  },
  eventAction: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  tabName: {
    type: String,
    required: true
  }
});

const { extraData } = useGlobal();
const { value } = usePromoStore();
const { promoCategories } = usePromoBadgeAddon();
const springSaleBadges = promoCategories();
defineEmits(['onClose']);

const badgeTextColor = computed(() => extraData?.textColorcustomBadgeTextColor);
const badgeBackgroundColor = computed(() => extraData?.customBadgeBackgroundColor);
</script>
