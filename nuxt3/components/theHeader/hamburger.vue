<template>
  <BaseButton
    variant="custom"
    v-bind="{
      'data-testid': `mobile-menu-burger`,
      trackData: {}
    }"
    class="hoverable:group-hover:text-orange group"
  >
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.75 6.5L20.25 6.5"
        stroke="currentColor"
        stroke-width="1.5"
        class="basic-transition transition-transform origin-center"
        v-bind:class="{ '-rotate-45 translate-y-[4.4px] translate-x-[4px]': isAnyMenuOpened }"
      />
      <path
        d="M3.75 12.5L20.25 12.5"
        stroke="currentColor"
        stroke-width="1.5"
        class="basic-transition transition-opacity origin-center"
        v-bind:class="{ 'opacity-0': isAnyMenuOpened }"
      />
      <path
        d="M3.75 18.5L20.25 18.5"
        stroke="currentColor"
        stroke-width="1.5"
        class="basic-transition transition-transform origin-center"
        v-bind:class="{ 'rotate-45 translate-y-[-4.4px] translate-x-[4px]': isAnyMenuOpened }"
      />
    </svg>
  </BaseButton>
</template>

<script setup lang="ts">
import useHeader from '~/composables/useHeader';

const { isAnyMenuOpened } = useHeader();

</script>
