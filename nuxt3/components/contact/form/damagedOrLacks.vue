<template>
  <div
    class="complaint-form grid grid-cols-1 md:grid-cols-2 gap-16 lg:gap-y-32"
    data-testid="contact-form-damaged-or-lacks"
  >
    <ContactFormInputTooltipWrapper class="col-start-1">
      <template #default>
        <FormKit
          type="text"
          name="orderNumber"
          data-testid="contact-order-input"
          validation="required"
          validation-behavior="live"
          v-bind="{
            label: $t('contact.forms.change_order.order_id'),
            validationMessages: {
              required: $t('common.validation.required'),
            }
          }"
        />
      </template>
      <template #tooltipBody>
        <p data-testid="contact-tooltip-body">
          <b v-html="$t('contact.forms.change_order.order_id_tooltip')" /><br>
          <span v-html="$t('contact.forms.change_order.order_id_tooltip_body')" />
        </p>
      </template>
    </ContactFormInputTooltipWrapper>

    <!-- DAMAGED PACKAGE  -->
    <aside
      class="col-start-1"
      data-testid="contact-form-pack-damaged-choice"
    >
      <FormKit
        outer-class="complaint-form-radio-group"
        data-testid="contact-form-pack-damaged-choice"
        name="isPackagingDamaged"
        validation="required"
        validation-behavior="live"
        type="radio"
        v-bind="{
          validationMessages: {
            required: $t('common.validation.required'),
          },
          options:{ 'yes': $t('common.forms.yes'), 'no': $t('common.forms.no') },
          label: $t('contact.forms.damaged_or_lacks.is_packaging_damaged')
        }"
      />
    </aside>

    <Transition name="fade">
      <template v-if="formValues.isPackagingDamaged === 'yes'">
        <div class="col-start-1 md:col-span-2 grid grid-cols-1 md:grid-cols-2">
          <aside
            class="col-start-1"
            data-testid="contact-form-pack-damaged-report-choice"
          >
            <FormKit
              class="complaint-form-radio-group"
              name="isPackagingDamageReportedToDeliveryCompany"
              validation="required"
              validation-behavior="live"
              type="radio"
              v-bind="{
                validationMessages: {
                  required: $t('common.validation.required'),
                },
                options:{ 'yes': $t('common.forms.yes'), 'no': $t('common.forms.no') },
                label: $t('contact.forms.damaged_or_lacks.report_delivery_company')
              }"
            />
            <p
              class="bold-18 my-16 lg:mt-32"
              data-testid="contact-form-pack-damaged-photo"
              v-html="$t('contact.forms.damaged_or_lacks.upload_damaged_package_photo')"
            />
            <p
              class="mb-16 normal-12 lg:normal-14 text-grey-900"
              data-testid="contact-form-pack-damaged-description"
              v-html="$t('contact.forms.damaged_or_lacks.upload_damaged_package_photo_decription')"
            />
          </aside>
          <ContactFormInputImageUpload
            class="col-start-1 md:col-span-2"
            data-testid="contact-form-pack-damaged-img-upload"
            name="damagedPackageFiles"
          />
        </div>
      </template>
    </Transition>

    <!-- DAMAGED ELEMENTS  -->
    <aside
      class="col-start-1"
      data-testid="contact-form-elem-damaged-choice"
    >
      <FormKit
        outer-class="complaint-form-radio-group"
        name="areAnyShelfElementsDamaged"
        type="radio"
        validation="required"
        validation-behavior="live"
        v-bind="{
          validationMessages: {
            required: $t('common.validation.required'),
          },
          options:{ 'yes': $t('common.forms.yes'), 'no': $t('common.forms.no') },
          label: $t('contact.forms.damaged_or_lacks.are_elements_damaged')
        }"
      />
    </aside>

    <Transition name="fade">
      <div
        v-if="formValues.areAnyShelfElementsDamaged === 'yes'"
        class="col-start-1 md:col-span-2 grid grid-cols-1 md:grid-cols-2"
      >
        <div class="col-start-1">
          <p
            class="bold-18 mb-16"
            data-testid="contact-form-elem-damaged-which"
            v-html="$t('contact.forms.damaged_or_lacks.missing_elements_which')"
          />
          <p
            class="my-16 bold-12 lg:bold-14 text-grey-900"
            data-testid="contact-form-elem-damaged-p-1"
            v-html="$t('contact.forms.damaged_or_lacks.are_elements_damaged_which_1')"
          />
          <p
            class="mb-16 normal-12 lg:normal-14 text-grey-900"
            data-testid="contact-form-elem-damaged-p-2"
            v-html="$t('contact.forms.damaged_or_lacks.are_elements_damaged_which_2')"
          />
          <p
            class="mb-16 normal-12 lg:normal-14 text-grey-900"
            data-testid="contact-form-elem-damaged-p-3"
            v-html="$t('contact.forms.damaged_or_lacks.are_elements_damaged_which_3')"
          />
          <p
            class="bold-18 my-16 lg:mt-32"
            data-testid="contact-form-elem-damaged-img-upload-text"
            v-html="$t('contact.forms.damaged_or_lacks.upload_damaged_elements_photo')"
          />
        </div>
        <ContactFormInputImageUpload
          name="damagedElementsFiles"
          data-testid="contact-form-elem-damaged-img-upload"
          class="md:col-span-2"
        />
        <div class="col-start-1 md:col-span-2">
          <p
            class="bold-18 mb-16 my-16 lg:mt-32"
            data-testid="contact-form-elem-damaged-description-text"
            v-html="$t('contact.forms.damaged_or_lacks.describe_damage')"
          />
          <FormKit
            type="textarea"
            data-testid="contact-form-elem-damaged-description-input"
            validation="required"
            validation-behavior="live"
            name="shelfElementsDamageDescription"
            v-bind="{
              label: $t('contact.forms.damaged_or_lacks.describe_damage_label'),
              validationMessages: {
                required: $t('common.validation.required')
              }
            }"
          />
        </div>
      </div>
    </Transition>

    <!-- MISSING ELEMENTS -->
    <aside
      class="col-start-1"
      data-testid="contact-form-elem-missing-choice"
    >
      <FormKit
        outer-class="complaint-form-radio-group"
        name="areAnyElementsMissing"
        validation="required"
        validation-behavior="live"
        type="radio"
        v-bind="{
          validationMessages: {
            required: $t('common.validation.required'),
          },
          options:{ 'yes': $t('common.forms.yes'), 'no': $t('common.forms.no') },
          label: $t('contact.forms.damaged_or_lacks.missing_elements')
        }"
      />
    </aside>

    <Transition name="fade">
      <div
        v-if="formValues.areAnyElementsMissing === 'yes'"
        class="md:col-span-2"
      >
        <p
          class="bold-18 mb-16"
          data-testid="contact-form-elem-missing-which-text"
          v-html="$t('contact.forms.damaged_or_lacks.missing_elements_which')"
        />
        <FormKit
          type="textarea"
          data-testid="contact-form-elem-missing-input"
          validation="required"
          validation-behavior="live"
          name="missingElementsDescription"
          v-bind="{
            label: $t('contact.forms.damaged_or_lacks.missing_elements_which_label'),
            validationMessages: {
              required: $t('common.validation.required')
            }
          }"
        />
      </div>
    </Transition>

    <FormKit
      type="textarea"
      name="message"
      outer-class="md:col-span-2"
      data-testid="contact-form-damaged-or-lacks-additional-input"
      v-bind="{
        label: $t('contact.forms.damaged_or_lacks.any_additional'),
      }"
    />
    <ContactFormInputCommonFormInputs v-bind="{ isMessageInputVisible: false }" />
  </div>
</template>

<script lang="ts" setup>
import ContactFormInputImageUpload from '~/components/contact/form/input/imageUpload.vue';

defineProps({
  formValues: {
    type: Object,
    required: true
  }
});
</script>

<style lang="scss">
.complaint-form {
  .formkit-options {
    @apply grid grid-cols-2 mt-16;

    .formkit-inner {
      @apply p-0;
    }

    .formkit-label {
      @apply block;
    }
  }
}
</style>
