<template>
  <section class="flex">
    <IconDeliveryTruck
      class="min-w-32"
      alt="delivery-truck"
    />
    <div class="ml-8">
      <h3
        class="bold-20 text-offblack-800 pb-4"
        v-html="$t('contact.forms.order_status.delivery_dates_chosen')"
      />
      <span
        v-for="(slot, id) in selectedTimeSlots"
        v-bind:key="`${slot.date}-${id}`"
        class="normal-20 text-offblack-800"
      >
        {{ changeDateFormat(slot.date) }} {{ getSlotRange(slot) }}
        <br>
      </span>
      <p
        class="normal-14 text-offblack-800 pt-4 pb-16"
        v-html="$t('contact.forms.order_status.delivery_dates_caption')"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { format } from 'date-fns';
import IconDeliveryTruck from '~/assets/icons/contact/deliveryTruck.svg';

defineProps({
  selectedTimeSlots: {
    type: Array,
    default: () => []
  }
});

const getSlotRange = (slot:any) => (slot.all_day ? '6:00 - 23.00' : `${slot.start_hour} - ${slot.end_hour}`);

const changeDateFormat = (dateString:string) => {
  const [year, month, day] = dateString.split('-');
  const transformedDate = new Date(
    Number(year),
    Number(month) - 1,
    Number(day)
  );

  return (format(transformedDate, 'dd/MM/yyyy'));
};
</script>
