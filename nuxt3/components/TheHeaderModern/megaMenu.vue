<template>
  <Transition
    appear
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
    enter-active-class="transition-opacity duration-300 ease-out"
    leave-active-class="transition-opacity duration-[150ms] ease-in"
  >
    <div
      v-show="isMegaMenuOpened && isMounted"
      id="mega-menu-backdrop"
      class="transition-opacity pt-80 w-screen h-screen max-h-[calc(100dvh-var(--ribbon-height))]
              select-none
              backdrop-blur-xl backdrop-brightness-[.25]"
      v-on:click="toggleMegaMenu(false)"
    >
      <div
        ref="megaMenuScrollArea"
        class="pt-12 h-full overflow-y-scroll"
        v-on:mouseleave="toggleMegaMenu(false)"
      >
        <div class="grid-container h-full">
          <nav
            class="text-white pb-32 lg:py-32 relative grid
                    lg:grid-cols-[auto_auto_auto_minmax(0px,_1fr)_minmax(260px,_auto)]
                    lg:gap-64 xl:gap-96"
          >
            <!-- Main/Home links -->

            <Transition
              enter-from-class="opacity-0"
              leave-to-class="opacity-0"
              enter-active-class="transition-opacity lg-max:absolute duration-300 ease-in-out"
              leave-active-class="transition-opacity lg-max:absolute duration-300 ease-on-out"
            >
              <div
                v-show="isDesktopViewport || activeMegaMenuTab === 'home'"
                class="space-y-48"
              >
                <!-- Pages Categories -->
                <ul class="semibold-32 flex flex-col gap-16">
                  <li class="lg:hidden">
                    <BaseButton
                      variant="underline-link"
                      v-bind="{
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: menuSections?.main?.eventAction,
                          eventLabel: menuSections?.main?.items?.[0]?.eventLabel
                        },
                        'data-testid': menuSections?.main?.items?.[0]?.testId
                      }"
                      v-on:click="toggleTab(menuSections?.main?.items?.[0]?.tabName!)"
                    >
                      <template v-if="menuSections?.main?.items?.[0]?.mobileTitleKey">
                        <span class="lg:hidden">{{ $t(menuSections?.main?.items?.[0]?.mobileTitleKey) }}</span>
                        <span class="lg-max:!hidden">{{ $t(menuSections?.main?.items?.[0]?.titleKey) }}</span>
                      </template>
                      <template v-else>
                        {{ $t(menuSections?.main?.items?.[0]?.titleKey) }}
                      </template>
                    </BaseButton>
                  </li>
                  <li
                    v-for="item in menuSections?.main?.items"
                    v-bind:key="item.testId"
                    v-bind:class="item.classes"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: item.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: item.eventAction,
                          eventLabel: item.eventLabel
                        },
                        'data-testid': item.testId
                      }"
                    >
                      {{ $t(item.titleKey ) }}
                    </Baselink>
                    <span
                      v-if="item.badgeKey"
                      class="align-super text-[#FF3C00] semibold-12"
                    >
                      &nbsp; {{ $t(item.badgeKey) }}
                    </span>
                  </li>
                </ul>

                <!-- Pages links -->
                <ul class="semibold-20 space-y-12">
                  <li
                    v-for="item in menuSections?.pages?.items"
                    v-bind:key="item.testId"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: item.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: item.eventAction,
                          eventLabel: item.eventLabel
                        },
                        'data-testid': item.testId,
                      }"
                      class="leading-1_3"
                    >
                      {{ $t(item.titleKey ) }}
                    </Baselink>
                    <span
                      v-if="item.badgeKey"
                      class="align-super text-[#FF3C00] semibold-12"
                    >
                      &nbsp; {{ $t(item.badgeKey) }}
                    </span>
                  </li>
                </ul>

                <!-- Promo image-->
                <BaseLink
                  class="lg:hidden relative block w-full place-self-start"
                  variant="custom"
                  v-bind="{
                    href: IS_SMOOTH_AVAILABLE ? `${$addLocaleToPath('plp')}${$t(SOTTY_CATEGORIES.sofas.urlPathKey)}/` : `${$addLocaleToPath('plp')}${$t('common.category.dressing_table_url_path')}`,
                    trackData: { eventLabel: 'cta', eventPath: IS_SMOOTH_AVAILABLE ? `${$addLocaleToPath('plp')}${$t(SOTTY_CATEGORIES.sofas.urlPathKey)}/`
                      : `${$addLocaleToPath('plp')}${$t('common.category.dressing_table_url_path')}` }
                  }"
                >
                  <BasePicture
                    class="overflow-hidden rounded-8 "
                    v-bind="{
                      alt: 'promo banner',
                      type: 'M T SD LD XLD',
                      path: '/common/menu/megamenu/banner25062025',
                    }"
                  />
                  <div
                    class="absolute inset-0 p-16 xl:p-20 md:p-12 space-y-4 text-offwhite-700
                            flex flex-col justify-between"
                  >
                    <div>
                      <span class="normal-14 text-balance">
                        <h1
                          class="bold-24 md:bold-16 lg2:bold-18 xl:bold-20 text-balance"
                          v-html="$t('menu.banner.sofas.headline')"
                        />
                      </span>
                    </div>
                    <div class="ml-auto ty-btn-outlined ty-btn-outlined--dark">
                      {{ $t('common.explore') }}
                    </div>
                  </div>
                </BaseLink>
              </div>
            </Transition>

            <!-- Mobile expanded menu -->
            <Transition
              enter-from-class="opacity-0"
              leave-to-class="opacity-0"
              enter-active-class="transition-opacity lg-max:absolute duration-300 ease-in-out"
              leave-active-class="transition-opacity lg-max:absolute duration-300 ease-on-out"
            >
              <ul
                v-show="activeMegaMenuTab === 'products'"
                class="semibold-32 space-y-16 w-full lg:hidden"
              >
                <template
                  v-for="(section, index) in mobileProductsSections"
                  v-bind:key="index"
                >
                  <li
                    v-if="!section?.items"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: section.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: section.eventAction,
                          eventLabel: section.eventLabel
                        },
                        'data-testid': section.testId,
                      }"
                      class="leading-1_3"
                    >
                      {{ $t(section.titleKey ) }}
                    </Baselink>
                    <span
                      v-if="section.badgeKey"
                      class="align-super text-[#FF3C00] semibold-12"
                    >
                      &nbsp; {{ $t(section.badgeKey) }}
                    </span>
                  </li>
                  <BaseAccordion
                    v-else
                    as="li"
                    v-bind="{
                      'data-testid': `accordion-tab-${section.testId}`,
                    }"
                  >
                    <template #title="{ isExpanded }">
                      <div class="flex justify-between text-white">
                        <span>
                          {{ $t(section.titleKey) }}
                          <span
                            v-if="section.badgeKey"
                            class="align-super text-[#C1FF00] semibold-12"
                          >
                            &nbsp; {{ $t(section.badgeKey) }}
                          </span></span>
                        <component
                          v-bind:is="isExpanded ? 'IconButtonMinus' : 'IconButtonPlus'"
                          class="w-40 h-40"
                        />
                      </div>
                    </template>
                    <template #content>
                      <ul class="mt-16 pb-32 space-y-8">
                        <li
                          v-for="item in section.items"
                          v-bind:key="item.testId"
                        >
                          <BaseLink
                            variant="underline-link"
                            v-bind="{
                              href: item.href,
                              trackData: {
                                eventCategory: 'megamenu',
                                eventAction: item.eventAction,
                                eventLabel: item.eventLabel
                              },
                              'data-testid': item.testId,
                            }"
                            class="leading-1_3 normal-18"
                          >
                            {{ $t(item.titleKey ) }}
                          </Baselink>
                          <span
                            v-if="item.badgeKey"
                            class="align-super text-[#C1FF00] semibold-12"
                          >
                            &nbsp; {{ $t(item.badgeKey) }}
                          </span>
                        </li>
                      </ul>
                    </template>
                  </BaseAccordion>
                </template>
              </ul>
            </Transition>

            <!-- High storage-->
            <div class="lg-max:hidden">
              <BaseLink
                variant="custom"
                class="semibold-32 mb-16 block"
                v-bind="{
                  href: menuSections?.highStorage?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.highStorage?.eventAction,
                    eventLabel: menuSections?.highStorage?.eventLabel
                  },
                  'data-testid': menuSections?.highStorage?.testId
                }"
              >
                {{ $t(menuSections?.highStorage?.titleKey) }}
              </Baselink>
              <ul class="normal-16 space-y-8">
                <li
                  v-for="item in menuSections?.highStorage?.items"
                  v-bind:key="item.testId"
                >
                  <BaseLink
                    class="relative"
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                  </Baselink>
                  <span
                    v-if="item.badgeKey"
                    class="align-super text-[#C1FF00] semibold-12"
                  >
                    &nbsp;{{ $t(item.badgeKey) }}
                  </span>
                </li>
              </ul>
              <BaseLink
                variant="custom"
                class="semibold-32 mb-16 mt-48 block"
                v-bind="{
                  href: menuSections?.lowStorage?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.lowStorage?.eventAction,
                    eventLabel: menuSections?.lowStorage?.eventLabel
                  },
                  'data-testid': menuSections?.lowStorage?.testId
                }"
              >
                {{ $t(menuSections?.lowStorage?.titleKey) }}
              </Baselink>
              <ul class="normal-16 space-y-8">
                <li
                  v-for="item in menuSections?.lowStorage?.items"
                  v-bind:key="item.testId"
                  class="relative"
                >
                  <BaseLink
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                  </Baselink>
                  <span
                    v-if="item.badgeKey"
                    class="align-super text-[#C1FF00] semibold-12"
                  >
                    &nbsp;{{ $t(item.badgeKey) }}
                  </span>
                </li>
              </ul>
            </div>

            <!-- Wardrobes-->
            <div class="lg-max:hidden">
              <BaseLink
                variant="custom"
                class="semibold-32 mb-16 block"
                v-bind="{
                  href: menuSections?.wardrobes?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.wardrobes?.eventAction,
                    eventLabel: menuSections?.wardrobes?.eventLabel
                  },
                  'data-testid': menuSections?.wardrobes?.testId
                }"
              >
                {{ $t(menuSections?.wardrobes?.titleKey) }}
              </Baselink>
              <ul class="normal-16 space-y-8">
                <li
                  v-for="item in menuSections?.wardrobes?.items"
                  v-bind:key="item.testId"
                >
                  <BaseLink
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                  </Baselink>
                  <span
                    v-if="item.badgeKey"
                    class="align-super text-[#C1FF00] semibold-12"
                  >
                    &nbsp;{{ $t(item.badgeKey) }}
                  </span>
                </li>
              </ul>
              <BaseLink
                v-if="IS_SMOOTH_AVAILABLE"
                variant="custom"
                class="semibold-32 mb-16 mt-48 block"
                v-bind="{
                  href: menuSections?.sofas?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.sofas?.eventAction,
                    eventLabel: menuSections?.sofas?.eventLabel
                  },
                  'data-testid': menuSections?.sofas?.testId
                }"
              >
                {{ $t(menuSections?.sofas?.titleKey) }}
                <span
                  v-if="menuSections?.sofas?.badgeKey"
                  class="align-super text-[#C1FF00] semibold-12"
                >
                  &nbsp;{{ $t(menuSections?.sofas?.badgeKey) }}
                </span>
              </Baselink>
              <ul
                v-if="IS_SMOOTH_AVAILABLE"
                class="normal-16 space-y-8"
              >
                <li
                  v-for="item in menuSections?.sofas?.items"
                  v-bind:key="item.testId"
                >
                  <BaseLink
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                  </Baselink>
                  <span
                    v-if="item.badgeKey"
                    class="align-super text-[#C1FF00] semibold-12"
                  >
                    &nbsp;{{ $t(item.badgeKey) }}
                  </span>
                </li>
              </ul>
            </div>

            <div class="hidden lg:block " />

            <!-- Promo image-->
            <BaseLink
              class="hidden lg:block relative max-w-[360px] w-full place-self-start"
              variant="custom"
              v-bind="{
                href: `${$addLocaleToPath('plp')}?productLines=1,3,6,7,8,0,2,4,5`,
                trackData: { eventLabel: 'cta', eventPath: `${$addLocaleToPath('plp')}${$t('common.category.dressing_table_url_path')}` }
              }"
            >
              <BasePicture
                class="overflow-hidden rounded-8 w-full"
                v-bind="{
                  alt: 'promo banner',
                  type: 'M T SD LD XLD',
                  path: '/common/menu/megamenu/banner25062025',
                }"
              />
              <div
                class="absolute inset-0 p-16 xl:p-20 md:p-12 space-y-4 text-offwhite-700
                        flex flex-col justify-between"
              >
                <div>
                  <h1
                    class="bold-24 md:bold-16 lg2:bold-18 xl:bold-20 text-balance"
                    v-html="$t('menu.banner.sofas.headline')"
                  />
                </div>
                <div class="mx-auto justify-self-end ty-btn-outlined ty-btn-outlined--dark">
                  {{ $t('common.explore') }}
                </div>
              </div>
            </BaseLink>
          </nav>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { useMounted } from '@vueuse/core';
import type { MagaMenuTabs } from '~/stores/header';
import useMq from '~/composables/useMq';
import { SOTTY_CATEGORIES } from '~/consts/categories';

const { categories } = useCategories();
const isMounted = useMounted();
const {
  isMegaMenuOpened,
  toggleMegaMenu,
  toggleTab,
  activeMegaMenuTab
} = useHeaderModern();

const { isDesktopViewport } = useMq();
const { $addLocaleToPath } = useNuxtApp();
const { t } = useI18n();
const megaMenuScrollArea = ref<HTMLElement>();

watch(isMegaMenuOpened, () => {
  if (isMegaMenuOpened.value) {
    nextTick(() => {
      megaMenuScrollArea.value.scrollTop = 0;
    });
  }
});
watch(activeMegaMenuTab, () => {
  nextTick(() => {
    megaMenuScrollArea.value?.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  });
});

const isSamplePromoActive = useFeatureFlag('isSamplePromoActive');
const { IS_SMOOTH_AVAILABLE } = storeToRefs(useGlobal());

const menuSections: {[key: string]: {
  titleKey?: string;
  eventAction: string;
  eventLabel: string;
  testId: string;
  href?: string;
  badgeKey?: string ;
  items?: {
    classes?: string;
    testId: string;
    titleKey: string;
    mobileTitleKey?: string;
    href: string;
    tabName?: MagaMenuTabs;
    eventLabel: string;
    eventAction: string;
    badgeKey?: string ;
  }[];
  }} = {
    main: {
      testId: 'main',
      eventAction: 'main',
      eventLabel: 'main',
      items: [
        {
          classes: 'lg-max:hidden',
          href: `${$addLocaleToPath('plp')}`,
          testId: 'all-products',
          eventLabel: 'all-products',
          eventAction: 'main',
          titleKey: 'common.category.all',
          mobileTitleKey: 'menu.labels.shop',
          tabName: 'products'
        },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.influencers')}`,
          testId: 'creators',
          eventLabel: 'creators',
          eventAction: 'main',
          titleKey: 'menu.labels.influencers'
        },
        {
          classes: 'lg-max:hidden whitespace-nowrap',
          href: `${$addLocaleToPath('plp')}?additional=topSeller`,
          testId: 'bestsellers',
          eventLabel: 'bestsellers',
          eventAction: 'main',
          titleKey: 'common.megamenu.bestsellers'
        },
        // {
        //   classes: 'lg-max:hidden',
        //   href: `${$addLocaleToPath('plp')}`,
        //   testId: 'trending-now',
        //   eventLabel: 'trending-now',
        //   eventAction: 'main',
        //   titleKey: 'common.megamenu.trending_now'
        // },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.gallery')}`,
          testId: 'inspiration',
          eventLabel: 'inspiration',
          eventAction: 'main',
          titleKey: 'menu.labels.inspiration'
        },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.tylkopro')}`,
          testId: 'tylko-pro',
          eventLabel: 'tylko-pro',
          eventAction: 'main',
          titleKey: 'menu.labels.tylko_pro'
        }
      ]
    },
    pages: {
      testId: 'pages',
      eventAction: 'page',
      eventLabel: 'main',
      items: [
        {
          href: `${$addLocaleToPath('product-lines.index')}`,
          testId: 'product-lines',
          eventLabel: 'product-lines',
          eventAction: 'page',
          titleKey: 'menu.labels.product-lines'
        },
        {
          href: `${$addLocaleToPath('lp.samples')}`,
          testId: 'material-samples',
          eventLabel: 'material-samples',
          eventAction: 'page',
          titleKey: 'menu.labels.material-samples',
          badgeKey: isSamplePromoActive ? 'promo_label_sale' : ''
        },
        {
          href: `${$addLocaleToPath('lp.reviews')}`,
          testId: 'reviews',
          eventLabel: 'reviews',
          eventAction: 'page',
          titleKey: 'menu.labels.reviews'
        },
        {
          href: `${$addLocaleToPath('contact')}?topic=order_status`,
          testId: 'order-status',
          eventLabel: 'order-status',
          eventAction: 'page',
          titleKey: 'menu.labels.delivery_status'
        }
      ]
    },
    highStorage: {
      testId: 'high-storage',
      titleKey: 'common.megamenu.categories.high_furniture',
      eventAction: 'high-storage',
      eventLabel: 'high-storage',
      href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.high_furniture_url_path')}`,
      items: [
        {
          href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.high_furniture_url_path')}`,
          testId: 'all-high-storage',
          eventLabel: 'all-high-storage',
          eventAction: 'high-storage',
          titleKey: 'common.megamenu.categories.all_high_furniture'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wallstorage.urlPathKey)}`,
          testId: 'wallstorage',
          eventLabel: 'wallstorage',
          eventAction: 'high-storage',
          titleKey: categories.wallstorage.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.bookcase.urlPathKey)}`,
          testId: 'bookcase',
          eventLabel: 'bookcase',
          eventAction: 'high-storage',
          titleKey: categories.bookcase.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.vinylstorage.urlPathKey)}`,
          testId: 'bookcase',
          eventLabel: 'bookcase',
          eventAction: 'high-storage',
          titleKey: categories.vinylstorage.pluralNameKey
        }
      ]
    },
    lowStorage: {
      testId: 'low-storage',
      titleKey: 'common.megamenu.categories.low_furniture',
      eventAction: 'low-storage',
      eventLabel: 'low-storage',
      href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.low_furniture_url_path')}`,
      items: [
        {
          href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.low_furniture_url_path')}`,
          testId: 'all-low-storage',
          eventLabel: 'all-low-storage',
          eventAction: 'low-storage',
          titleKey: 'common.megamenu.categories.all_low_furniture'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.sideboard.urlPathKey)}`,
          testId: 'sideboards',
          eventLabel: 'sideboards',
          eventAction: 'low-storage',
          titleKey: categories.sideboard.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.tvstand.urlPathKey)}`,
          testId: 'tv-stand',
          eventLabel: 'tv-stand',
          eventAction: 'low-storage',
          titleKey: categories.tvstand.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.chest.urlPathKey)}`,
          testId: 'chest-of-drawers',
          eventLabel: 'chest-of-drawers',
          eventAction: 'low-storage',
          titleKey: categories.chest.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.dressing_table.urlPathKey)}`,
          testId: 'dressing-table',
          eventLabel: 'dressing_table',
          eventAction: 'low-storage',
          titleKey: categories.dressing_table.pluralNameKey,
          badgeKey: 'common.new'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.bedsidetable.urlPathKey)}`,
          testId: 'bedside-table',
          eventLabel: 'bedside-table',
          eventAction: 'low-storage',
          titleKey: categories.bedsidetable.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.shoerack.urlPathKey)}`,
          testId: 'shoe-rack',
          eventLabel: 'shoe-rack',
          eventAction: 'low-storage',
          titleKey: categories.shoerack.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.vinylstorage.urlPathKey)}`,
          testId: 'vinyl-storage',
          eventLabel: 'vinyl-storage',
          eventAction: 'low-storage',
          titleKey: categories.vinylstorage.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.desk.urlPathKey)}`,
          testId: 'desks',
          eventLabel: 'desks',
          eventAction: 'low-storage',
          titleKey: categories.desk.pluralNameKey
        }
      ]
    },
    wardrobes: {
      testId: 'wardrobes',
      titleKey: categories.wardrobe.pluralNameKey,
      eventAction: 'wardrobes',
      eventLabel: 'wardrobes',
      href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}`,
      items: [
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}`,
          testId: 'all-wardrobes',
          eventLabel: 'all-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.all_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=fullyClosed`,
          testId: 'closed-wardrobes',
          eventLabel: 'closed-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.closed_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=partiallyOpen`,
          testId: 'semi-open-wardrobes',
          eventLabel: 'semi-open-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.semi_open_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=fullyOpen`,
          testId: 'open-wardrobes',
          eventLabel: 'open-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.open_wardrobes'
        }
      ]
    },
    ...(IS_SMOOTH_AVAILABLE.value && {
      sofas: {
        testId: 'sofas',
        titleKey: 'common.megamenu.categories.smooth_furniture',
        eventAction: 'sotty',
        eventLabel: 'sotty',
        href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.sofas.urlPathKey)}/`,
        badgeKey: 'common.new',
        items: [
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.sofas.urlPathKey)}/`,
            testId: 'all-sofas',
            eventLabel: SOTTY_CATEGORIES.sofas.name,
            eventAction: 'sofas',
            titleKey: 'common.megamenu.categories.smooth_all_furniture'
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.two_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.two_seater.name,
            eventLabel: SOTTY_CATEGORIES.two_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.two_seater.nameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.three_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.three_seater.name,
            eventLabel: SOTTY_CATEGORIES.three_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.three_seater.nameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.four_plus_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.four_plus_seater.name,
            eventLabel: SOTTY_CATEGORIES.four_plus_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.four_plus_seater.nameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.chaise_longue.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.chaise_longue.name,
            eventLabel: SOTTY_CATEGORIES.chaise_longue.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.chaise_longue.nameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.corner.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.corner.name,
            eventLabel: SOTTY_CATEGORIES.corner.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.corner.nameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.armchair.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.armchair.name,
            eventLabel: SOTTY_CATEGORIES.armchair.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.armchair.nameKey
          }
          // {
          //   href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.footrests_and_modules.urlPathKey)}`,
          //   testId: SOTTY_CATEGORIES.footrests_and_modules.name,
          //   eventLabel: SOTTY_CATEGORIES.footrests_and_modules.name,
          //   eventAction: 'sofas',
          //   titleKey: SOTTY_CATEGORIES.footrests_and_modules.nameKey,
          //   badgeKey: 'common.new'
          // }
          // {
          //   href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.cover.urlPathKey)}`,
          //   testId: SOTTY_CATEGORIES.cover.name,
          //   eventLabel: SOTTY_CATEGORIES.cover.name,
          //   eventAction: 'sofas',
          //   titleKey: t(SOTTY_CATEGORIES.cover.nameKey)
          // }

        ]
      }
    })
  } as const;

const mobileProductsSections : Array<{
  href?: string;
  name?: string;
  key?: string;
  testId?: string;
  eventAction?: string;
  eventLabel?: string;
  titleKey?: string;
  items?: {
    testId: string;
    titleKey: string;
    href: string;
    tabName?: MagaMenuTabs;
    eventLabel: string;
    eventAction: string;
  }[]
}> = [
  {
    classes: 'lg-max:hidden',
    href: `${$addLocaleToPath('plp')}`,
    testId: 'all-products',
    eventLabel: 'all-products',
    eventAction: 'main',
    titleKey: 'All Products'
  },
  menuSections.main?.items?.[2],
  menuSections.main?.items?.[3],
  menuSections.highStorage,
  menuSections.lowStorage,
  menuSections.wardrobes,
  IS_SMOOTH_AVAILABLE.value && menuSections.sofas
].filter(Boolean);
</script>
