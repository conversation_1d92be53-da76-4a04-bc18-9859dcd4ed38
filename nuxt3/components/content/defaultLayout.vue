<template>
  <section
    ref="container"
    class="container-cstm-fluid mb-24"
  >
    <div
      class="lg:mt-48 grid grid-cols-12"
    >
      <div
        class="col-span-12 lg:col-span-3 order-1 lg:order-none before:block lg:before:hidden before:h-1 before:bg-grey-700 before:my-16 lg:mb-32"
      >
        <slot name="navigation" />
      </div>
      <div
        class="hidden lg:block col-span-1"
      />
      <div class="col-span-12 lg:col-span-7 xl:col-span-6 content-container">
        <slot name="content" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import scrollToElement from '~/helpers/scrollToElement';

const container = ref(null);

onUpdated(() => {
  const el = container.value;

  if (el && el.getBoundingClientRect().y < 0) {
    scrollToElement({
      element: el,
      offset: 20
    });
  }
});
</script>

<style lang="scss" scoped>
:deep(.content-container) {
  @apply text-offblack-600;

  h2 {
    @apply font-bold text-24 lg:text-46 mb-28 text-offblack-700;
  }

  h3 {
    @apply text-20 lg:text-24 mb-8 text-offblack-700;
  }

  p {
    @apply text-16 mb-28;
  }

  hr {
    @apply mb-24 text-grey-800;
  }

  a {
    @apply text-orange;
  }

  ul {
    @apply list-disc pl-16 mb-28;
  }

  ol {
    @apply list-decimal pl-16 mb-28;
  }
}
</style>
