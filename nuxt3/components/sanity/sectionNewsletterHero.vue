<template>
    <SectionNewsletterHero
      v-bind="{
        style: { backgroundColor: bgColor?.hex },
        ...props
      }"
    />
  </template>
  
  <script setup lang="ts">

  const props = withDefaults(defineProps<{
    bgColor?: {
        hex: string;
    };
    subheadline?: string;
    headline?: string;
    headlineThanks?: string;
    legalCopy?: string;
    imagePath?: string;
    description?: string;
    descriptionThanks?: string;
    ctaCopy?: string;
    source?: string;
    isReversed?: boolean;
    isDarkMode?: boolean;
    isReversedOnMobile?: boolean;
    showPhoneInput?: boolean;
    isPhoneRequired?: boolean;
    isEmailRequired?: boolean;
    isEmailOnTop?: boolean;
  }>(), {
  subheadline: '',
  headline: '',
  headlineThanks: '',
  legalCopy: '',
  imagePath: '',
  description: '',
  descriptionThanks: '',
  ctaCopy: '',
  source: 'content',
  isReversed: false,
  isDarkMode: false,
  isReversedOnMobile: false,
  showPhoneInput: false,
  isPhoneRequired: false,
  isEmailRequired: false,
  isEmailOnTop: false
});

  </script>