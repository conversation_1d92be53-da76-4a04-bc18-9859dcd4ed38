<template>
  <section>
    <BaseCarousel
      class="full-width-carousel !px-0"
      v-bind="{
        name: `${name}-fullscreenCarousel`,
        options: swiperOptions
      }"
    >
      <BaseCarouselSlide
        v-for="(item, index) in data"
        v-bind:key="index"
      >
        <FullScreenCard v-bind="{ ...item, variant }" />
      </BaseCarouselSlide>
    </BaseCarousel>
  </section>
</template>

<script setup lang="ts">
defineProps({
  data: {
    type: Array,
    required: true
  },
  variant: {
    type: String,
    default: 'category'
  },
  name: {
    type: String,
    default: ''
  }
});

const swiperOptions = {
  slidesPerView: 1,
  loop: true,
  effect: 'fade'
};
</script>
