<template>
  <main class="relative reset-modern-navigation-page-padding">
    <Head>
      <Title>{{ $t('pdp.configure.seo.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('pdp.configure.seo.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('pdp.configure.seo.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('pdp.configure.seo.description')"
      />
    </Head>

    <NuxtErrorBoundary v-on:error="$logException">
      <section
        id="pdp-section-configurator"
        ref="configurator"
        data-observe-view="configurator"
        class="relative configurator-main-wrapper z-3"
      >
        <PdpMobileHeader
          v-if="isMobileOrTabletViewport"
          class="block lg:hidden"
          v-on:save="isSaveForLaterModalOpen = true"
          v-on:share="isShareShelfModalOpen = true"
        />
        <PdpConfiguratorBack
          v-show="!loader"
          v-on:save="isSaveForLaterModalOpen = true"
        />
        <PdpConfigurator
          v-show="!loader"
          v-bind:title="seoTitle"
        />
        <PdpConfiguratorLoader
          v-show="loader"
          class="h-screen absolute inset-0"
          v-bind="{
            price,
            depth,
            width,
            height,
            preview,
            material,
            shelfType,
            minDelivery,
            maxDelivery,
            gridAllColors,
            title: seoTitle,
            configuratorType,
            configuratorPreview,
            salePrice: priceWithDiscount
          }"
        />
      </section>

      <LazyPdpSamplesBar
        v-model="isSampleBarOpen"
        :hydrate-when="isSampleBarOpen"
      />
      <LazyModalCarouselSofaDepth
        v-model="isSofaDepthModalOpen"
        :hydrate-when="isSofaDepthModalOpen"
      />
      <LazyModalCarouselSofaMaterials
        v-model="isSofaMaterialsModalOpen"
        :hydrate-when="isSofaMaterialsModalOpen"
      />
      <LazyModalSaveForLater
        v-if="configuratorData && isSaveForLaterModalOpen"
        v-model="isSaveForLaterModalOpen"
        v-bind="{
          configuratorData
        }"
      />
      <LazyModalShareShelf
        v-if="configuratorData && isShareShelfModalOpen"
        v-model="isShareShelfModalOpen"
        v-bind="{
          configuratorData
        }"
      />
    </NuxtErrorBoundary>
  </main>
</template>

<script lang="ts" setup>
import { INITIAL } from '~/api/pdp';
import { usePdpStore } from '~/stores/pdp';
import { SHORT_MODEL_TO_MODEL } from '~/utils/configuratorTypeToModel';
import { pdpAnalytics } from '~/composables/pdp/pdpAnalytics';

interface LocalStorageData {
  screenshot: string;
  geom: Object;
  endpoint: string;
  bgColor: string;
  isWardrobe: boolean;
  base_preset: number;
}

definePageMeta({
  layout: 'configure',
  tag: ['pdp']
});

const route = useRoute();
const pdpStore = usePdpStore();
const config = useRuntimeConfig();

const $gtm = useGtm();
const { locale } = useLocale();
const { isMobileOrTabletViewport } = useMq();

const { $logException } = useNuxtApp();

const {
  price,
  width,
  depth,
  height,
  preview,
  delivery,
  material,
  shelfType,
  seoTitles,
  gridAllColors,
  configuratorType,
  priceWithDiscount,
  configuratorPreview,
  priceInEuro,
  furnitureCategory,
  priceWithDiscountInEuro
} = storeToRefs(usePdpStore());

const loader = ref(true);
const isSampleBarOpen = ref(false);
const isSofaDepthModalOpen = ref(false);
const isShareShelfModalOpen = ref(false);
const isSaveForLaterModalOpen = ref(false);
const isSofaMaterialsModalOpen = ref(false);
const maxDelivery = ref(delivery.value && delivery.value.max);
const minDelivery = ref(delivery.value && delivery.value.min);
const seoTitle = ref(
  seoTitles.value &&
  Object.keys(seoTitles.value).length > 0 &&
  seoTitles.value[shelfType.value]?.[material.value]
    ? seoTitles.value[shelfType.value][material.value]
    : ''
);

const { id } = route.params;
const [productId, productModel] = id.split('?').join(',').split(',');
const properModel = SHORT_MODEL_TO_MODEL(productModel);
const { data, error }: { data: any, error: any } = await INITIAL(properModel, productId, locale.value, `${config.public.baseURL}${route.path}`);

const { configureItemGA4 } = pdpAnalytics({
  material: material.value,
  shelfType: shelfType.value,
  priceInEuro: priceInEuro.value,
  patternName: null,
  configuratorType: configuratorType.value,
  furnitureCategory: furnitureCategory.value,
  priceWithDiscountInEuro: priceWithDiscountInEuro.value,
  contentType: 'sotty'
});

if (error.value) {
  $logException(error.value);
} else {
  pdpStore.SET_PDP_DATA({
    ...data.value,
    model: properModel,
    category: 'sofa',
    furnitureType: GET_SHELF_TYPE(data.value.shelfType)
  });
}

const configuratorData = ref(null);
const localStorageData = shallowRef<LocalStorageData | null>();

const buildMobileExitSection = () => {
  const newData = window.localStorage.getItem('exitMobileSection');

  if (!newData) {
    return;
  }

  const newParsedData = JSON.parse(newData);

  localStorageData.value = newParsedData;

  configuratorData.value = {
    material_name: material.value,
    geom: localStorageData.value?.geom,
    configurator_type: configuratorType.value,
    endpoint: localStorageData.value?.endpoint,
    base_preset: localStorageData.value?.base_preset,
    screenshot: localStorageData.value?.screenshot
  };
};

const updateConfiguratorPayload = (updatedConfiguratorPayload: any) => {
  seoTitle.value = seoTitles.value?.[updatedConfiguratorPayload.shelfType]?.[updatedConfiguratorPayload.materialValue] || '';
};

onMounted(() => {
  $gtm?.push({ ecommerce: null });
  $gtm?.push(configureItemGA4(productId));

  if (window.PubSub) {
    window.PubSub.subscribe('configuratorInitialized', () => {
      loader.value = false;
    });

    window.PubSub.subscribe('ecommerceServiceSamplesDrawer', () => {
      isSampleBarOpen.value = true;
    });

    window.PubSub.subscribe('ecommerceServiceMaterialsModal', () => {
      isSofaMaterialsModalOpen.value = true;
    });

    window.PubSub.subscribe('ecommerceServiceSeatDepthModal', () => {
      isSofaDepthModalOpen.value = true;
    });

    window.PubSub.subscribe('rebuildMobileExitSection', () => {
      buildMobileExitSection();
    });

    window.PubSub.subscribe('configuratorParametersChange', (_: any, payload: any) => {
      updateConfiguratorPayload(payload);
    });
  }
});
</script>

<style lang="scss">
@import '~/assets/scss/ds/ty-configurator.scss';
</style>
