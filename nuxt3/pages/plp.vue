<template>
  <section class="min-h-screen bg-beige-100">
    <LazyPlpNavigation
      v-if="AB_TESTS_NAVIGATION_2025"
      id="plp-navigation-modern"
      data-section="section-plp-navigation"
      class="mt-16"
    />

    <LazyPlpSectionCategorySwiper
      v-if="!AB_TESTS_NAVIGATION_2025"
      v-bind="{
        activeCategory,
        imgDirPath: 'plp/category-selection',
        categoriesInPromotion,
        isPendingState: false
      }"
    />

    <div
      id="plp-category-description"
      class="grid-container grid grid-cols-12"
    >
      <template v-if="!AB_TESTS_NAVIGATION_2025">
        <hr class="hidden lg:block w-full text-neutral-500 pb-32 col-span-12">
        <h1
          class="mb-8 semibold-36 xl:semibold-46 text-neutral-900 col-span-12"
          data-testid="plp-category-title"
          v-html="sanityData?.categoryTitle ? sanityData.categoryTitle : $t(activeCategory?.pluralNameKey || activeCategory.nameKey)"
        />
        <!--        <p-->
        <!--          class="semibold-16 lg:semibold-24 text-neutral-900 col-span-12 md:col-span-9"-->
        <!--          data-testid="plp-category-description"-->
        <!--          v-html="sanityData?.categoryDescription ? sanityData.categoryDescription : $t(activeCategory.descriptionKey)"-->
        <!--        />-->
      </template>
      <aside class="col-span-12">
        <div
          v-if="promoBanner"
          class="mt-32 border-2 rounded-24 aspect-[3/1] md:aspect-[7/1] flex items-center justify-center"
          :style="{
            backgroundColor: bannerBackgroundColor,
            color: bannerTextColor,
            borderColor: bannerBorderColor
          }"
        >
          <p
            class="text-center bold-46 md:bold-54 xl:bold-96"
            v-html="$t('plp.banner.winterSale.title')"
          />
        </div>
        <PlpCategoryBanner
          v-else-if="!promoBanner && categoryBanner"
          class="mb-16 lg:mb-8"
          v-bind="{
            imageAlt: categoryBanner.heading,
            title: categoryBanner.heading,
            ctaCopy: categoryBanner.ctaCopy,
            ctaUrl: categoryBanner.ctaUrl,
            promoCode: categoryBanner.promoCode,
            isThemeLight: categoryBanner.isThemeLight,
            imagePath: 'plp/banners/wardrobe',
            'data-section': `category-banner-${activeCategory?.name}`
          }"
        />
      </aside>
    </div>

    <PlpFiltersPillBar
      v-bind:class=" {
        'pt-16': !AB_TESTS_NAVIGATION_2025,
        'md-max:hidden': AB_TESTS_NAVIGATION_2025
      }"
    />

    <LazyPlpFiltersDrawer :hydrate-when="isHydrated" />

    <div class="grid-container">
      <p class="normal-16 text-neutral-900 text-right col-span-12">
        {{ resultsCount }} {{ $t('common.products') }}
      </p>
    </div>

    <div
      id="plp"
      class="md-max:px-0 grid-container mt-12"
    >
      <template v-if="productsList?.length > 0">
        <LazyPlpGridBoard
          hydrate-on-idle
          data-section="grid-v2-board"
          v-bind:class="{ 'plp-loading': isFetching }"
        />
      </template>

      <section
        v-else-if="productsList?.length === 0"
        class="my-40 text-center grid-container"
      >
        <IconNoResults class="inline-block mb-16" />
        <h2
          class="mb-4 normal-20 xl:normal-28 text-neutral-900 xl:mb-8"
          v-html="$t('plp.no_results.title')"
        />
        <p
          class="normal-16 text-neutral-900 mb-32 max-w-[360px] mx-auto"
          v-html="$t('plp.no_results.body')"
        />
        <BaseLink
          variant="accent"
          class="mb-4 whitespace-nowrap"
          v-bind="{
            trackData: {},
            href: activeCategory && getLinkToGenericFurniture(activeCategory.name)
          }"
        >
          {{ $t('plp.no_results.button') }}
        </BaseLink>
      </section>
    </div>

    <span ref="endOfGridObserverEl" />

    <div
      v-if="productsList"
      class="flex flex-col items-center justify-center mt-32"
    >
      <template v-if="productsList.length < resultsCount">
        <BaseButton
          variant="filters-pill"
          class="mb-4 whitespace-nowrap"
          data-testid="plp-show-more-items-button"
          v-bind="{
            trackData: {}
          }"
          v-on="{
            click: showMoreItems,
          }"
        >
          {{ $t('plp.board.button.more') }}
        </BaseButton>
        <p
          class="mb-32 normal-12 text-grey-900"
          data-testid="plp-pagination-counter-bottom"
        >
          {{ $t('plp.board.current_view_1') }} {{ productsList?.length }} {{ $t('plp.board.current_view_2') }}
          {{ resultsCount }}
        </p>
      </template>
    </div>

    <LazySectionTextCenteredBackground
      v-if="activeCategory?.name === FurnitureCategory.WARDROBES"
      hydrate-on-visible
      data-section="customise-section"
      v-bind="{
        title: $t('plp.search-banner.wardrobes.headline'),
        imagePath: 'plp/banner',
        imageType: 'M T SD LD XLD',
        cta: $t('plp.search-banner.wardrobes.cta'),
        withIcon: false,
        isLightTheme: false,
        ctaUrl: activeCategory && getLinkToGenericFurniture(activeCategory.name)
      }"
    />
    <LazySectionTextCenteredBackground
      v-else
      hydrate-on-visible
      data-section="customise-section"
      v-bind="{
        title: $t('common.ui.customize1'),
        secondaryTitle: $t('common.ui.customize2'),
        imagePath: 'lp/type02sideboard/customise',
        cta: $t('lp.type02sideboard.customise.cta'),
        ctaUrl: activeCategory && getLinkToGenericFurniture(activeCategory.name)
      }"
    />

    <LazyPlpSectionSeo
      v-if="!isWebView && seoHtmlDescription"
      hydrate-on-visible
      v-bind="{
        body: seoHtmlDescription
      }"
    />

    <ClientOnly>
      <Transition name="fade">
        <div
          v-show="isScrollToTopButtonVisible"
          class="fixed z-2 cursor-pointer right-16 md:right-40 lg:right-64 xl:right-[112px] flex justify-center items-center
            w-40 h-40 rounded-full bg-white border border-solid border-grey-700 text-offblack-600"
          v-bind:class="isDixaVisible ? 'lg-max:hidden' : 'bottom-96 lg:bottom-24'"
          v-on:click="handleScrollToTopClick"
        >
          <IconCaretUp />
        </div>
      </Transition>
    </ClientOnly>
    <ClientOnly v-if="AB_TESTS_NAVIGATION_2025">
      <PlpFiltersNotificationPill />
    </ClientOnly>
  </section>
</template>

<script setup lang="ts">
import { FurnitureCategory } from '~/utils/filters';
import { useTrackingDOM } from '~/composables/useTracking';

import useShowMoreProducts from '~/composables/plp/useShowMoreProducts';
import useScrollToTopButton from '~/composables/plp/useScrollToTopButton';
import useGenericFurnitureLink from '~/composables/plp/useGenericFurnitureLink';

definePageMeta({ middleware: ['plp-middleware'], key: () => 'plp' });

const route = useRoute();
const store = usePlpStore();

const { locale } = useLocale();
const isDixaVisible = useState('isDixaEnabled', () => false);
const global = useGlobal();
const { extraData } = global;

const { IS_GLOBAL_FETCHED, AB_TESTS_NAVIGATION_2025 } = storeToRefs(global);

const { getLinkToGenericFurniture } = useGenericFurnitureLink();
const { endOfGridObserverEl, showMoreItems } = useShowMoreProducts();
const { isScrollToTopButtonVisible, handleScrollToTopClick, scrollToProductList } = useScrollToTopButton();
const { productsList, sanityData, activeCategory, resultsCount, categoriesInPromotion, isDrawerOpened, isFetching } = storeToRefs(store);

await store.fetchCatalogData();

const isHydrated = ref(false);
const pageIsLoading = ref(!IS_GLOBAL_FETCHED.value);

const isWebView = global.device.isWebView;

const promoBanner = computed(() => extraData?.plpBanner);
const bannerTextColor = computed(() => extraData?.textColor);
const bannerBorderColor = computed(() => extraData?.borderColor);
const bannerBackgroundColor = computed(() => extraData?.backgroundColor);
const seoHtmlDescription = computed(() => sanityData.value?.filterSeoData?.htmlValue || sanityData.value?.seoSectionBody || '');
const categoryBanner = computed(() => sanityData.value?.customUsps?.filter(item => item?.type?.type === 'categoryBanner')[0]?.type);

watch(() => route.fullPath, () => {
  store.fetchCatalogData();
  scrollToProductList();
});

watch(isDrawerOpened, () => {
  if (isDrawerOpened.value) {
    isHydrated.value = true;
  }
}, { once: true });

onMounted(() => {
  pageIsLoading.value = false;
});

useSeoMeta({
  title: () => sanityData.value?.filterSeoData?.metaTitle || sanityData.value?.seoTitle || '',
  ogTitle: () => sanityData.value?.filterSeoData?.metaTitle || sanityData.value?.seoTitle || '',
  description: () => sanityData.value?.filterSeoData?.metaDescription || sanityData.value?.seoDescription || '',
  ogDescription: () => sanityData.value?.filterSeoData?.metaDescription || sanityData.value?.seoDescription || '',
  ogLocale: () => locale.value,
  ogUrl: () => route.fullPath
});
</script>

<style lang="scss" >
@use '~/assets/scss/nuxt/plp.scss';
</style>
