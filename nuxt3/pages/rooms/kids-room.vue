<template>
  <main>
    <NuxtLazyHydrate when-idle>
      <SectionHeroImage
        data-section="hero"
        v-bind="{
          imagePath: 'lp/kids-room/hero',
          imageAlt: $t('lp.kidsroom.hero.title'),
          imageType: 'M T SD LD',
          headingCopy: $t('lp.kidsroom.hero.title'),
          subheadingCopy: $t('common.tylko_spaces'),
          ctaCopy: $t('common.explore_more'),
          ctaUrl: $addLocaleToPath('plp'),
          headerColorClass: 'text-offwhite-600',
          backgroundColor: '#d8d1cc'
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionMiddlePics
        class="bg-[#EDECE8]"
        v-bind="{
          title: $t('lp.kidsroom.harmony.title'),
          subtitle: $t('lp.kidsroom.harmony.body'),
          items: [
            {
              headline: $t('lp.kidsroom.harmony.desk.title'),
              description: $t('lp.kidsroom.harmony.desk.body'),
              imagePath: 'lp/kids-room/harmony/1',
              url: `${$addLocaleToPath('plp')}desk/`
            },
            {
              headline: $t('lp.kidsroom.harmony.chestofdrawers.title'),
              description: $t('lp.kidsroom.harmony.chestofdrawers.body'),
              imagePath: 'lp/kids-room/harmony/3',
              url: `${$addLocaleToPath('plp')}chest-of-drawers/`
            },
            {
              headline: $t('lp.kidsroom.harmony.wallstorage.title'),
              description: $t('lp.kidsroom.harmony.wallstorage.body'),
              imagePath: 'lp/kids-room/harmony/2',
              url: `${$addLocaleToPath('plp')}wallstorage/`
            }
          ],
          isLightTheme: true,
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionExperience
        data-section="experience"
        class="bg-[#847268] pt-full lg:pt-[53.33%]"
        v-bind="{
          videoID: {
            mobile: 'o9p67tuf4m',
            desktop: 'gnvpwx2pwf'
          },
          imageAlt: $t('common.experience_the_perfect_fit'),
          headingCopy: $t('common.experience_the_perfect_fit'),
          subheadingCopy: $t('common.tylko_spaces'),
          body: $t('common.animation.caption'),
          backgroundColor: '#d8d1cc'
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <SectionUspBar
          data-section="usps"
          is-assembly
          is-assembly-free
          v-bind="{
            reviews: {
              rating: reviewsAverageScore,
              count: reviewsCount
            }
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <LazySectionMinigrid
          data-observe-view="minigrid-browse-by-room"
          data-section="minigrid-browse-by-room"
          v-bind="{
            id: 'kids_category_sideboard',
            title: $t('common.browse_by_category'),
            itemListName: 'mini_grid',
            filters: [
              {
                value: 'kids_category_sideboard',
                label: $t('common.category.sideboard'),
              },
              {
                value: 'kids_category_wallstorage',
                label: $t('common.category.wallstorage'),
              },
              {
                value: 'kids_category_chest',
                label: $t('common.category.chest'),
              },
            ],
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <InstafeedCarousel
        data-section="insta-grid"
        class="bg-[#FFFFFF]"
        v-bind="{
          title: $t('hp.instafeed.title'),
          ids: ['t123', 't124', 't125', 't126', 't127', 't128', 't129', 't130', 't131', 't132', 't133', 't134', 't135', 't136', 't137', 't138', 't139'],
          'is-theme-light': false
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <SectionReviewsOld
          v-if="data && reviewsData"
          class="bg-[#FFF8E9]"
          v-bind="{
            reviews: reviewsData,
            overallReviewsCount: reviewsCount,
            generalScore: reviewsAverageScore,
            reviewsTitle: 'common.a_word_from_our_customers',
            cta: 'common.see_all_reviews'
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionTextImageColumns
        data-section="values-section"
        class="bg-[#A48F8E]"
        v-bind="{
          title: $t('lp.kidsroom.journal.headline'),
          subtitle: $t('common.tylko_journal'),
          imagePath: 'lp/kids-room/journal',
          description: $t('lp.kidsroom.journal.body'),
          ctaCopy: $t('common.read_full_story'),
          ctaUrl: $addLocaleToPath('5-reasons-to-get-a-tylko-shelf-in-your-kids-room'),
          isLightTheme: true,
          isReversed: false,
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionExplore
        data-section="explore"
        class="bg-[#F9F9F9]"
        v-bind="{
          heading: $t('common.discover_more_tylko_spaces'),
          ctaText: $t('common.explore'),
          type: 'spaces',
          sectionCategories: ['livingroom', 'bedroom', 'hallway', 'office'],
          largeItems: [0,2],
          isThemeLight: false
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionSeo
        v-bind="{
          body: $t('lp.kidsroom.seo')
        }"
      />
    </NuxtLazyHydrate>
  </main>
</template>

<script setup lang="ts">
import useSeo from '~/composables/useSeo';

import { reviews } from '~/api/spaces';

const { roomMeta, roomsJsonLd } = useSeo();
const { reviewsCount, reviewsAverageScore } = storeToRefs(useGlobal());

const { data } = await reviews('kidsroom');

interface Review {
  name?: string,
  country?: string,
  score?: number,
  title?: string,
  description?: string,
  properPhoto?: string
}

type Spaces = {
  reviews: Array<Review>,
  reviewsCount: Number,
  reviewsAverageScore: Number,
}

const reviewsData = computed(() => (data?.value as Spaces)?.reviews);

useSeoMeta(roomMeta('kidsroom'));

// @ts-expect-error
useJsonld(roomsJsonLd(
  reviewsData,
  reviewsCount.value,
  reviewsAverageScore.value
));
</script>
