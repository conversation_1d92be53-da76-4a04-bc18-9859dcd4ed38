import { useToast } from 'vue-toastification';
import { isNil, debounce } from 'lodash-es';
import * as countryCodes from 'country-codes-list';
import { storeToRefs } from 'pinia';
import { getNode } from '@formkit/core';
import { watchDebounced, watchThrottled } from '@vueuse/core';
import { hashSHA256 } from '~/composables/useSha';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';
import { regionsUtils } from '~/composables/checkout/regions';
import useCartStatus from '~/composables/useCartStatus';
import { useGlobal } from '~/stores/global';
import type { CheckoutState } from '~/stores/checkout';
import {
  GET_FORM_DATA,
  TRIGGER_CHECKOUT_ENTRY_EVENT,
  SET_FORM_DATA,
  TRIGGER_CHECKOUT_FULFILLED_EVENT,
  HANDLE_IS_FREE
} from '~/api/checkout';

interface CstmVue {
  criteo_account?: string;
  user?: {
    criteo_email?: string;
  };
}

declare global {
  interface Window {
    cstm_vue: CstmVue;
  }
}

export type FormDataType = {
  firstName: string,
  lastName: string,
  streetAddress1: string,
  streetAddress2: string,
  floorNumber: string,
  noElevator: string | null,
  postalCode: string,
  city: string,
  email: string,
  phone: string,
  invoicePhone: string,
  notes: string,
  invoiceFirstName: string,
  invoiceLastName: string,
  invoiceStreetAddress1: string,
  invoicePostalCode: string,
  invoiceCity: string,
  invoiceCountry: string,
  vat: string,
  companyName: string,
  newsletter: boolean,
  region?: string,
  sameAsDelivery: boolean,
  sms: boolean
}

export const handleFormValues = async () => {
  const formNode = ref(null);
  const phonePrefixCode = ref(null);
  const checkoutStore = useCheckoutStore();
  const { userInteractionEvent } = checkoutAnalytics();
  const { forceChangeRegion } = regionsUtils();
  const submitButtonIsLoading = ref(false);
  const toast = useToast();
  const { $logException, $i18n } = useNuxtApp();
  const gtm = useGtm();
  const route = useRoute();
  const vatNumberAndCompanyNameVisible = ref(false);
  const alternativePhoneNumberVisible = ref(false);
  const additionalAddressBoxVisible = ref(false);
  const B2B = ref('regular');
  let initialData = <FormDataType>{};
  const formLoaded = ref(false);
  const isFormMounted = ref(false);
  const formValues = ref<any>({});
  const { payment_only } = route.query;
  const { fetchUserStatus } = useCartStatus();
  const checkout2024 = useFeatureFlag('checkout2024');

  const preferencesTest = computed(() => ['UK', 'FR', 'ES', 'BE', 'AT', 'SE', 'NO', 'DK', 'FI', 'LU'].includes(global.regionCode) ? checkout2024 : false);
  const global = useGlobal();
  const { cartId: storeCartId } = storeToRefs(global);

  const orderId = ref(null);
  const checkout2025 = global.AB_TEST_VALUE('new_checkout2025')?.isActive || global.AB_TEST_VALUE('checkout2025_100')?.isActive;
  const {
    isNewFormMounted,
    shouldLoadPaymentsOnCheckout,
    paymentsMock,
    shouldAllowPayments,
    formData,
    shouldRenderAdyen,
    handleUpdateAdyen,
    disablePayments
  } = useAdyenDropin();
  const fixInitialValidation = ref(true);
  const cartId = route.params.cartId || storeCartId.value;
  const vatPlaceholder = ref(null);

  const initializePaymentMethods = (id) => {
    shouldLoadPaymentsOnCheckout.value = true;
    submitButtonIsLoading.value = false;
    orderId.value = id;
  };

  const triggerVatValidationSuccess = debounce(() => {
    gtm?.push(userInteractionEvent('Invoice details', 'VAT or EU VAT number', 'accepted'));
    toast.success($i18n.t('checkout.form.vat.valitadion_success'));
  }, 1000);

  onMounted(() => {
    setTimeout(async () => {
      isNewFormMounted.value = true;
      isFormMounted.value = true;
      formNode.value = getNode('myForm');
      checkoutStore.SET_FROM_IS_MOUNTED();

      const { error, data } = await SET_FORM_DATA(global, { country: formValues.value.region });

      await fetchUserStatus();

      if (error.value) {
        toast.error($i18n.t('common.error.connection'));
      } else {
        orderId.value = data.value.orderId;
        global.UPDATE_ORDER_ID(data.value.orderId);
        shouldLoadPaymentsOnCheckout.value = true;
      }

      const isFormValid = computed(() => formNode.value?.context.state.settled && formNode.value?.context.state.valid);
      shouldAllowPayments.value = payment_only === 'true' ? true : isFormValid.value;
      watch(() => isFormValid.value, debounce(async (value) => {
        shouldAllowPayments.value = value;
        checkoutStore.SET_CHECKOUT_DATA(formValues.value as CheckoutState);

        if (value) {
          paymentsMock.value = false;
          await liveSubmitForm();
          fixInitialValidation.value = false;
        } else if (fixInitialValidation.value) {
          fixInitialValidation.value = false;
          paymentsMock.value = false;
        } else {
          paymentsMock.value = true;
        }
      }, 800, { trailing: true, leading: false }));

      const isVatValidated = ref<boolean>();

      const onVatChanged = async (vat:string) => {
        if (isFormValid.value) { return; }
        const { data, error } = await SET_FORM_DATA(global, { vat, invoiceCountry: formValues.value.invoiceCountry });

        if (error.value) {
          toast.error($i18n.t('common.error.connection'));
          $logException(error.value);
        } else if (data.value?.vatValidated) {
          triggerVatValidationSuccess();
        }

        await fetchUserStatus();
      };

      watchDebounced(() => formValues.value.vat, onVatChanged, { debounce: 1000 });
      watchThrottled(isVatValidated, async () => {
        await fetchUserStatus();
      }, { throttle: 500 });
    }, 120);
    setTimeout(() => {
      watch(() => formValues.value.noElevator, (value) => {
        if (value) {
          gtm.push(userInteractionEvent('Delivery address', 'Is there an elevator', value));
        }
      });
      watch(() => formValues.value.sameAsDelivery, (value) => {
        gtm.push(userInteractionEvent('Invoice Details', 'Same as delivery address', value ? 'Yes' : 'No'));
        handleVat(value);
      });
      watch(() => B2B.value, (value) => {
        gtm.push(userInteractionEvent('Invoice Details', value === 'regular' ? 'Private user' : 'Company', 'click'));
        vatNumberAndCompanyNameVisible.value = value === 'B2B';
      });
      watch(() => formValues.value.newsletter, (value) => {
        gtm.push(userInteractionEvent('My preferences', 'Email newsletter', value ? 'yes' : 'no'));
      });

      if (preferencesTest.value) {
        watch(() => formValues.value.occasionalMessages, (value) => {
          gtm.push(userInteractionEvent('My preferences', 'Sms newsletter', value ? 'yes' : 'no'));
        });
        watch(() => formValues.value.termsOfService, (value) => {
          gtm.push(userInteractionEvent('My preferences', 'Terms of service', value ? 'yes' : 'no'));
        });
      }
    }, 1000);
  });

  const handleVat = (sameAsDelivery: boolean) => {
    if (!vatPlaceholder.value) { return; }

    if (sameAsDelivery) {
      formValues.value.vat = vatPlaceholder.value;
    } else {
      vatPlaceholder.value = formValues.value.vat;
      formValues.value.vat = '';
    }
  };

  watch(() => vatNumberAndCompanyNameVisible.value, async (value) => {
    if (!value && formValues.value.vat) {
      vatPlaceholder.value = formValues.value.vat;
      formValues.value.vat = null;
    }

    if (value && vatPlaceholder.value) {
      formValues.value.vat = vatPlaceholder.value;
      vatPlaceholder.value = null;
    }

    await liveSubmitForm();
    B2B.value = value ? 'B2B' : 'regular';
  });

  const { data, error, execute } = await GET_FORM_DATA(cartId);
  await execute();

  if (error.value) {
    toast.error($i18n.t('common.error.connection'));
    $logException(error.value);

    if (error.value.statusCode === 404) {
      navigateTo('/404');
    }
  } else {
    if (data.value.orderId && data.value.isProfileComplete) {
      initializePaymentMethods(data.value.orderId);
      paymentsMock.value = false;
    }

    checkoutStore.SET_CHECKOUT_DATA(data.value as CheckoutState);
    const { error: errorEntryEvent } = TRIGGER_CHECKOUT_ENTRY_EVENT(global);

    if (errorEntryEvent.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(errorEntryEvent.value);
    }

    if (!isNil(data.value?.country) && global.regionName !== data.value?.country) {
      await forceChangeRegion(data.value?.country);
      await fetchUserStatus();
    }

    shouldRenderAdyen.value = true;

    initialData = {
      firstName: data.value?.firstName,
      lastName: data.value?.lastName,
      streetAddress1: data.value?.streetAddress1,
      streetAddress2: data.value?.streetAddress2,
      floorNumber: data.value?.floorNumber,
      noElevator: isNil(data.value?.noElevator) ? null : (data.value?.noElevator ? 'no' : 'yes'),
      postalCode: data.value?.postalCode,
      city: data.value?.city,
      email: data.value?.email,
      phone: data.value?.phone,
      invoicePhone: data.value?.invoicePhone,
      notes: data.value?.notes,
      invoiceFirstName: data.value?.invoiceFirstName,
      invoiceLastName: data.value?.invoiceLastName,
      invoiceStreetAddress1: data.value?.invoiceStreetAddress1,
      invoicePostalCode: data.value?.invoicePostalCode,
      invoiceCity: data.value?.invoiceCity,
      invoiceCountry: data.value?.invoiceCountry ? data.value?.invoiceCountry : global.regionName,
      vat: data.value?.vat,
      companyName: data.value?.companyName,
      newsletter: data.value?.newsletter,
      sameAsDelivery: !data.value?.invoiceAddressUsed,
      sms: data.value?.sms
    };

    if (data.value?.vat || data.value?.companyName) {
      vatNumberAndCompanyNameVisible.value = true;
    }

    if (data.value?.invoicePhone) {
      alternativePhoneNumberVisible.value = true;
    }

    if (data.value?.streetAddress2) {
      additionalAddressBoxVisible.value = true;
    }

    if (data.value?.phonePrefix) {
      const prefix = data.value?.phonePrefix.replace(/^\+/, '');

      if (prefix === '44') {
        phonePrefixCode.value = 'UK';
      } else {
        phonePrefixCode.value = countryCodes.findOne('countryCallingCode', data.value?.phonePrefix.replace(/^\+/, '')).countryCode;
      }
    } else {
      phonePrefixCode.value = global.regionCode;
    }

    formValues.value = {
      ...initialData,
      region: global.regionName
    };

    if (import.meta.browser) {
      window.cstm_vue = {
        ...window.cstm_vue,
        criteo_account: data.value?.criteoAccount,
        user: {
          criteo_email: data.value?.criteoEmail
        }
      };
    }

    formLoaded.value = true;
  }

  const preparePayload = () => {
    const { region, sameAsDelivery, ...rest } = formValues.value;
    return {
      ...rest,
      streetAddress2: additionalAddressBoxVisible.value ? formValues.value.streetAddress2 : null,
      invoicePhone: alternativePhoneNumberVisible.value ? formValues.value.invoicePhone : null,
      invoiceFirstName: sameAsDelivery ? formValues.value.firstName : formValues.value.invoiceFirstName,
      invoiceLastName: sameAsDelivery ? formValues.value.lastName : formValues.value.invoiceLastName,
      invoiceStreetAddress1: sameAsDelivery ? formValues.value.streetAddress1 : formValues.value.invoiceStreetAddress1,
      invoicePostalCode: sameAsDelivery ? formValues.value.postalCode : formValues.value.invoicePostalCode,
      invoiceCity: sameAsDelivery ? formValues.value.city : formValues.value.invoiceCity,
      invoiceCountry: sameAsDelivery ? formValues.value.region : formValues.value.invoiceCountry,
      vat: (!sameAsDelivery && vatNumberAndCompanyNameVisible.value) ? formValues.value.vat : '',
      companyName: vatNumberAndCompanyNameVisible.value ? formValues.value.companyName : null,
      phonePrefix: `+${countryCodes.findOne('countryCode', (phonePrefixCode.value === 'UK' ? 'GB' : phonePrefixCode.value)).countryCallingCode}`,
      noElevator: noElevator(formValues.value.noElevator),
      invoiceAddressUsed: !sameAsDelivery,
      country: region
    };
  };

  const noElevator = (value: string) => {
    if (value === 'yes') {
      return false;
    }

    if (value === 'no') {
      return true;
    }

    return null;
  };

  const liveSubmitForm = async () => {
    const payload = preparePayload();
    const { data, error } = await SET_FORM_DATA(global, payload);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      return;
    }

    await fetchUserStatus();

    if (data.value?.vatValidated) {
      triggerVatValidationSuccess();
    }

    formData.value = payload;
    orderId.value = data.value.orderId;
    global.UPDATE_ORDER_ID(data.value.orderId);
    handleUpdateAdyen(formData.value);
    shouldLoadPaymentsOnCheckout.value = true;
  };

  const submitForm = async () => {
    submitButtonIsLoading.value = true;
    // @ts-ignore

    const payload = preparePayload();

    const { error, data } = await SET_FORM_DATA(global, payload);
    // console.log(123123123123, data.value.orderId HANDLE_IS_FREE);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      submitButtonIsLoading.value = false;
    }

    const { error: errorTriggerFulfilledEvent } = await TRIGGER_CHECKOUT_FULFILLED_EVENT(global);

    if (errorTriggerFulfilledEvent.value) {
      toast.error($i18n.t('common.error.connection'));
      submitButtonIsLoading.value = false;
    }

    if (payload.newsletter) {
      gtm.push({
        event: 'generate_lead',
        placement: 'checkout',
        userId: global.userId,
        leadsUserData: {
          ea: hashSHA256(formValues.value.email),
          address: {
            country: global.regionCode
          }
        }
      });
    }

    if (disablePayments.value) {
      const { error: isFreeError } = await HANDLE_IS_FREE(data.value.orderId);

      if (isFreeError.value) {
        toast.error($i18n.t('common.error.connection'));
        submitButtonIsLoading.value = false;
      } else {
        window.location.href = `${window.location.origin}/${$i18n.locale.value}/confirmation/${global.orderId}/?uuid=${global.userId}`;
      }

      return;
    }

    if (checkout2025) {
      initializePaymentMethods(data.value.orderId);
    } else {
      window.location.href = `${window.location.origin}/${$i18n.locale.value}/payment_methods/${data.value.orderId}/?uuid=${global.userId}`;
    }
  };

  watch(() => formValues.value.invoiceCountry, async (invoiceCountry) => {
    if (!formValues.value.vat || !formValues.value.vat.length) { return; }
    const { error } = await SET_FORM_DATA(global, { vat: (formValues.value.vat ? formValues.value.vat : ''), invoiceCountry });

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
    } else {
      await fetchUserStatus();
    }
  });

  return {
    formValues,
    submitForm,
    submitButtonIsLoading,
    phonePrefixCode,
    vatNumberAndCompanyNameVisible,
    B2B,
    alternativePhoneNumberVisible,
    additionalAddressBoxVisible,
    formLoaded,
    isFormMounted,
    orderId,
    formNode,
    liveSubmitForm
  };
};
