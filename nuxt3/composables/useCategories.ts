import { useState } from 'nuxt/app';
import { GRID_CATEGORIES, CATEGORIES } from '~/consts/categories';
import type { Categories, Category } from '~/consts/categories';

export default function () {
  const category = useState('tyCategory', () => shallowRef(CATEGORIES()));
  const categories = useState('tyCategories', () => shallowRef(GRID_CATEGORIES()));
  const { IS_SMOOTH_AVAILABLE } = storeToRefs(useGlobal());

  const pickCategoriesInOrder = <T extends keyof Categories>(orderList: Array<T>) => {
    return orderList.reduce((arr: Array<Category>, category) => {
      arr.push(toRaw(categories.value[category]));
      return arr;
    }, []);
  };

  return {
    categories: toRaw(categories.value),
    category: toRaw(category.value),
    pickCategoriesInOrder,
    getPLPCategoriesOrdered: () => IS_SMOOTH_AVAILABLE.value
      ? pickCategoriesInOrder(['all', 'sofas', 'sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'desk', 'dressing_table', 'tvstand', 'chest', 'shoerack', 'bedsidetable', 'vinylstorage'])
      : pickCategoriesInOrder(['all', 'sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'desk', 'dressing_table', 'tvstand', 'chest', 'shoerack', 'bedsidetable', 'vinylstorage'])
  };
}
