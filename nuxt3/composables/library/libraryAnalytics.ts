import { useAnalytics } from '~/composables/analytics/useAnalytics';
import { GET_SHELF_TYPE } from '~/utils/types';

export const libraryAnalytics = () => {
  const { userId, globalSessionId, FETCH_GLOBAL } = useGlobal();
  const { getFurnitureEnglishName: getItemName } = useAnalytics();

  const furniture = ref<any>([]);

  function splitArray (array: any, divider: any) {
    const chunkSize = Math.ceil(array.length / divider);
    const chunks = [];

    for (let i = 0; i < array.length; i += chunkSize) {
      const chunk = array.slice(i, i + chunkSize);
      chunks.push(chunk);
    }

    return chunks;
  }

  const impression = (item: any) => ({
    brand: item.brand,
    category: item.category,
    id: item.id,
    list: 'wishlist',
    name: item.id,
    position: 1,
    price: item.region_price_in_euro,
    variant: item.variant
  });

  const product = (item: any) => ({
    brand: item.brand,
    cart_item_status_density: item.density,
    cart_item_status_depth: item.depth,
    cart_item_status_doors: item.doors,
    cart_item_status_drawers: item.drawers,
    cart_item_status_height: item.height,
    cart_item_status_width: item.width,
    category: item.category,
    color_name: item.color_name,
    dimension15: item.dimension15,
    id: item.id,
    localProductPrice: item.region_price_with_discount,
    name: item.id,
    position: 1,
    price: item.region_price_in_euro,
    size_txt: item.size_txt,
    timeToDelivery: item.time_to_delivery,
    variant: item.variant
  });

  const findItem = (id: any) => furniture.value.find((item: any) => item.id === id);

  const getEcommerceProductRemoveEvent = (id: any) => {
    const item = findItem(id);

    return {
      event: 'ecommerce removeFromCart',
      eventAction: undefined,
      eventLabel: undefined,
      eventValue: undefined,
      ecommerce: {
        remove: {
          products: [product(item)]
        }
      }
    };
  };

  const a2cGa4Event = async (id: any) => {
    const item = findItem(id);
    const payloadPrice = parseFloat(item?.region_price_in_euro).toFixed(2);

    if (!userId) {
      await FETCH_GLOBAL();
    }

    return {
      event: 'add_to_cart',
      userId,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: null,
        product_type: 'furniture',
        coupon: null,
        items: [{
          item_id: item.id,
          item_name: getItemName(item.shelf_type <= 2 ? 'jetty' : item.shelf_type === 10 ? 'sotty' : 'watty', '', item.category, item.shelf_type),
          affiliation: 'Tylko',
          quantity: 1,
          promotion_id: null,
          coupon: null,
          discount: null,
          index: null,
          item_brand: 'furniture',
          item_category: item.category,
          item_category2: item.brand,
          item_category3: item.configurator_type,
          item_category4: null,
          item_category5: item.color_name,
          item_list_name: null,
          item_list_id: null,
          item_variant: GET_SHELF_TYPE(item.shelf_type, true),
          price: payloadPrice,
          price_discounted: parseFloat(item?.region_price_with_discount_in_euro).toFixed(2),
          promotion_name: null,
          assembly: item.shelf_type === 3 ? 'built-in' : 'additional',
          item_id_preset: item.dimension15,
          discount_rate: null
        }]
      }
    };
  };

  return {
    furniture,
    a2cGa4Event,
    getEcommerceProductRemoveEvent
  };
};
