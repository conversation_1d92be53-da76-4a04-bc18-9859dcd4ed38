// TAILWIND
@use 'tailwindcss/base';
@use 'tailwindcss/utilities';
@use 'tailwindcss/components';

// BASE STYLES
@use './base/global';
@use './base/typography';
@use './base/utils';
@use './base/transitions';
@use './base/webfonts';
@use './base/helpers';

// DESIGN SYSTEM
@use './ds/inputs';
@use './ds/ty-icon';
@use './ds/ty-link';
@use './ds/buttons';
@use './ds/ty-label';
@use './ds/ty-badge';
@use './ds/ty-button';
@use './ds/tooltips';
@use './ds/ty-toast';
@use './base/formkit';
@use './ds/formulate';
@use './ds/ty-avatar';
@use './ds/ty-tagline';
@use './ds/ty-divider';
@use './ds/ty-notification-inline';

// NUXT
@use './nuxt/layout';
@use './nuxt/loader';

// styles for BRAZE notifications drawer
@use './braze/braze';
