import { createClient } from '@sanity/client';

export default defineCachedEventHandler(async () => {
  const config = useRuntimeConfig();
  const sanity = createClient({
    projectId: config.public.sanity.projectId,
    dataset: config.public.sanity.dataset, // or 'development'
    useCdn: false
  });

  const result = await sanity.fetch(groq`*[_type == "Influencers"][0]`);

  return JSON.parse(result.content);
}, {
  base: 'redis',
  maxAge: 60 * 60 * 2
  // shouldBypassCache: event => true
});
