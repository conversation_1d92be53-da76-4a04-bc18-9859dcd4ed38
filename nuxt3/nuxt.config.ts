// https://nuxt.com/docs/api/configuration/nuxt-config

import head from './utils/head';
import { pages } from './consts/pages';
import { i18nLanguagesConfig } from './consts/regions';
const IS_DEV_BUILD = process.env.NODE_ENV === 'development';
export default defineNuxtConfig({
  devtools: { enabled: true },
  alias: {
    '@tylkocom': '/node_modules/@tylkocom'
  },
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/sanity',
    '@nuxtjs/device',
    '@nuxtjs/i18n',
    '@zadigetvoltaire/nuxt-gtm',
    '@formkit/nuxt',
    '@pinia/nuxt',
    'nuxt-lazy-hydrate',
    'nuxt-jsonld',
    'nuxt-svgo',
    'nuxt-time',
    '@sentry/nuxt/module',
    'nuxt-vitalizer'
  ],

  vite: {
    build: {
      modulePreload: false
    }
  },

  vitalizer: {
    disableStylesheets: 'entry',
    disablePrefetchLinks: true,
    disablePreloadLinks: true
  },
  formkit: {
    autoImport: true
  },

  app: {
    head,
    buildAssetsDir: '_nuxt3/'
  },

  devServer: {
    port: 3000
  },

  css: ['@/assets/scss/main.scss'],

  build: {
    transpile: ['vue-toastification']
  },

  i18n: {
    detectBrowserLanguage: false,
    strategy: 'prefix_and_default',
    defaultLocale: 'en',
    customRoutes: 'config',
    pages,
    locales: i18nLanguagesConfig
  },

  pinia: {
    storesDirs: ['./stores/**']
  },

  runtimeConfig: {
    public: {
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || 'AIzaSyA2rzur3tE9udJBFjOpRrJrFfwokMon3Os',
      cacheConfig: {
        routesEligibleForCache: 'homepage,comparison-page,about-tylko,tylko-pro,samples,gallery,lp-colab-andy,lp-partner-program,plp,pdp,shipping,terms',
        sharedCacheMaxAge: 7200,
        maxAge: 300,
        mustRevalidate: true
      },
      sanity: {
        dataset: process.env.SANITY_DATASET || 'staging',
        useCdn: !IS_DEV_BUILD
      },
      sentry: {
        dsn: process.env.SENTRY_DSN,
        environment: process.env.SENTRY_ENV
      },
      baseURL: 'http://127.0.0.1:80',
      redisURL: 'redis://127.0.0.1:6379/2',
      DIXA_TOKEN: {
        en: '',
        fr: '',
        de: ''
      },
      EXTERNAL_PATHS: {},
      datadogRUM: {
        applicationId: '',
        clientToken: '',
        site: 'datadoghq.com',
        service: 'nuxt-rum',
        env: 'production',
        version: '1.0.0',
        sessionSampleRate: 1,
        sessionReplaySampleRate: 0,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: 'mask-user-input'
      }
    }
  },

  features: {
    inlineStyles: false
  },

  sanity: {
    projectId: 'q7uj1um8'
  },

  svgo: {
    defaultImport: 'component',
    componentPrefix: 'icon',
    global: false,
    svgoConfig: {
      plugins: [
        {
          name: 'preset-default',
          params: {
            overrides: {
              removeViewBox: false
            }
          }
        },
        'prefixIds'
      ]
    }
  },

  gtm: {
    id: 'GTM-5KWDFT',
    devtools: IS_DEV_BUILD,
    ...(process.env.ENVIRONMENT === 'staging' && {
      variables: {
        gtm_auth: 'TbMYDMaZgGL_xBTB2FTvVg',
        gtm_preview: 'env-1196',
        gtm_cookies_win: 'x'
      }
    }),
    ...(process.env.ENVIRONMENT === 'dev' && {
      variables: {
        gtm_auth: '31drJfzpbkZTBXsUO1HBVg',
        gtm_preview: 'env-1195',
        gtm_cookies_win: 'x'
      }
    })
  },

  ...(!IS_DEV_BUILD && {
    webVitals: {
      provider: 'ga'
    },
    googleAnalytics: {
      id: process.env.GOOGLE_ANALYTICS_ID || 'disabled',
      disabled: !process.env.GOOGLE_ANALYTICS_ID
    }
  }),

  compatibilityDate: '2025-05-14',

  router: {
    options: {
      strict: false
    }
  },

  sentry: {
    sourceMapsUploadOptions: {
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: process.env.SENTRY_ORG,
      project: process.env.SENTRY_PROJECT
    }
  },

  sourcemap: {
    client: 'hidden'
  }
});
