export const tyText = createInput([
  {
    $el: 'input',
    bind: '$attrs',
    attrs: {
      id: '$id',
      placeholder: '$label',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value',
      type: '$inputType'
    }
  },
  {
    $cmp: 'IconFormulateClear',
    props: {
      type: 'button',
      class: '$classes.clear',
      onClick: '$handlers.clear'
    }
  },
  {
    if: '$slots.button',
    then: '$slots.button'
  }
],
{
  family: 'tyText',
  props: {
    inputType: {
      type: String,
      default: 'text'
    }
  },
  features: [(node) => {
    node.on('created', () => {
      node.context.handlers.clear = () => {
        node.input('');
      };
    });
  }]
},
{
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});

export const tyTextArea = createInput([
  {
    $el: 'textarea',
    bind: '$attrs',
    attrs: {
      id: '$id',
      rows: '$rows',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value'
    }
  }
], {
  props: {
    rows: {
      type: Number,
      default: 2
    }
  },
  family: 'tyTextArea'
},
{
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});

const showPasswordValidation = (node: any) => {
  node.on('created', () => {
    if (node.props.validationHints) {
      const updateHints = () => {
        node.props.parsedRules.forEach((rule, index) => {
          node.context.state.validationHints[index] = {
            name: node.props.validationMessages[rule.name],
            state: rule.state,
            isHint: rule.name !== 'hasNoSpaces'
          };
        });
      };

      node.context.state.validationHints = [];
      updateHints();
      node.on('message-updated', () => {
        if (node.props.validationHints) {
          updateHints();
        }
      });
    }
  });
};

export const tyPassword = createInput([
  {
    $el: 'input',
    bind: '$attrs',
    attrs: {
      id: '$id',
      placeholder: '$label',
      disabled: '$disabled',
      type: '$state.showPassword',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value'
    }
  },
  {
    $cmp: 'IconFormulateEyeClose',
    props: {
      type: 'button',
      class: '$classes.showPassword',
      onClick: '$handlers.togglePassword'
    }
  }
], {
  props: {
    validationHints: {
      boolean: true,
      default: false
    }
  },
  family: 'tyText',
  features: [showPasswordValidation, (node) => {
    node.on('created', () => {
      node.context.state.showPassword = 'password';

      node.context.handlers.togglePassword = () => {
        node.context.state.showPassword = node.context.state.showPassword === 'password' ? 'text' : 'password';
      };
    });
  }]
},
{
  help: {
    $el: 'ul',
    if: '$validationHints',
    attrs: {
      class: '$classes.help'
    },
    children: [
      {
        $el: 'li',
        attrs: {
          class: 'text-error-500'
        },
        for: ['item', 'key', '$state.validationHints'],
        if: '$item.isHint === false && $item.state == false',
        children: '$item.name'
      },
      {
        $el: 'li',
        attrs: {
          class: 'flex items-center gap-8'
        },
        for: ['item', 'key', '$state.validationHints'],
        if: '$item.isHint',
        children: [
          {
            if: '$item.state == true || $state.submitted == false',
            $cmp: 'IconFormulateValidTick',
            props: {
              class: {
                if: '$item.state == true',
                then: 'text-success-500',
                else: 'text-neutral-500'
              }
            }
          },
          {
            if: '$item.state == false && $state.submitted == true',
            $cmp: 'IconFormulateNotValid',
            props: {
              class: {
                if: '$item.state == true',
                then: 'text-success-500',
                else: 'text-error-500'
              }
            }
          },
          {
            $el: 'span',
            children: '$item.name'
          }
        ]
      }
    ]
  },
  messages: {
    if: '$validationHints == false'
  }
});

export const tyBox = createInput(
  [
    {
      $el: 'label',
      bind: '$attrs',
      attrs: {
        for: '$id',
        class: '$classes.label'
      },
      children: [
        {
          $el: 'input',
          bind: '$attrs',
          attrs: {
            disabled: '$disabled',
            checked: '$_value',
            onBlur: '$handlers.blur',
            value: '$_value',
            type: 'checkbox',
            id: '$id',
            onInput: '$handlers.toggleChecked',
            class: '$: $classes.input + " flex-none"'
          }
        },
        {
          $el: 'span',
          attrs: {
            class: 'w-full'
          },
          if: '$label || $slots.labelExtra',
          then: '$label',
          children: [
            {
              if: '$label',
              then: '$label'
            }, {
              if: '$slots.labelExtra',
              then: '$slots.labelExtra'
            }
          ]
        }
      ]
    }
  ], {
    family: 'tyBox',
    features: [(node) => {
      node.on('created', () => {
        if (node.context?.handlers) {
          node.context.handlers.toggleChecked = (e: Event) => {
            const el = e?.target;

            if (el instanceof HTMLInputElement) {
              if (el.checked) {
                node.input(true);
              } else {
                node.input(false);
              }
            }
          };
        }
      });
    }]
  }, {
    label: null
  });

export const tySelect = createInput([
  {
    $el: 'div',
    bind: '$attrs',
    if: '$_value',
    attrs: {
      class: 'absolute top-28 transform -translate-y-[6px] left-16 pointer-events-none'
    },
    children: [
      '$slots.icon'
    ]
  },
  {
    $el: 'select',
    bind: '$attrs',
    attrs: {
      id: '$id',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$: $classes.input + " " + $classes.selectIconExtra',
      value: '$_value'
    },
    children: [
      {
        $el: 'option',
        for: ['item', 'key', '$options'],
        attrs: {
          selected: '$item.value == $_value',
          value: '$item.value'
        },
        children: '$item.label'
      }
    ]
  },
  {
    $cmp: 'IconCaretDown',
    props: {
      type: 'button',
      class: '$classes.dropdown'
    }
  }
], {
  props: {
    options: {
      type: Array,
      default: []
    }
  },
  family: 'tySelect'
},
{
  // wrapper: { $el: 'div', attrs: { class: '' } },
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});
