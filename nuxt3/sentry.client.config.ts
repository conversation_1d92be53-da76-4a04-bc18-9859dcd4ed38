import * as Sentry from '@sentry/nuxt';

const { public: { sentry } } = useRuntimeConfig();

if (sentry.dsn) {
  Sentry.init({
    dsn: sentry.dsn,
    environment: sentry.environment,
    integrations: [
      Sentry.piniaIntegration(usePinia()),
      Sentry.vueIntegration({
        tracingOptions: {
          trackComponents: true
        }
      })
    ],
    normalizeDepth: 10,
    beforeSend (event) {
      const currentUrl = window?.location?.href;
      const allowedPaths = [
        'sentry-example-page',
        'checkout',
        'confirmation',
        'cart',
        'confirmation_pending'
      ];

      const shouldAllow = allowedPaths.some(allowedPath => currentUrl.includes(allowedPath));

      if (shouldAllow) {
        return event;
      }

      return null;
    },
    tracesSampleRate: 0.01,
    debug: false
  });
}
