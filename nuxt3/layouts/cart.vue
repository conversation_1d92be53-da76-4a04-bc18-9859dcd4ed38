<template>
  <main
    class="flex flex-col min-h-[100svh]"
    v-bind="{
      class: [
        { 'bg-beige-100': !isLoginPage },
        AB_TESTS_NAVIGATION_2025 ? '[--navigation-modern-padding:88px] md:[--navigation-modern-padding:96px]' : '[--navigation-modern-padding:0px]'
      ]
    }"
  >
    <Head>
      <Title>{{ $t('hp.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('hp.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('hp.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('hp.meta.description')"
      />
    </Head>
    <LazyTheHeaderModern
      v-if="AB_TESTS_NAVIGATION_2025"
      hydrate-on-idle
      variant="dark"
    />
    <TheHeader
      v-else
    />

    <div class="flex-1 flex flex-col [&>main]:flex-1">
      <NuxtPage class="pt-[--navigation-modern-padding]" />
    </div>

    <!-- FOOTER -->

    <LazyCheckoutFooterNew
      v-if="renderCheckoutFooter"
      hydrate-on-visible
      class="grid-container"
      data-section="checkout-footer"
    />
    <LazyTheFooter2025
      v-else
      hydrate-on-visible
    />

    <!-- GLOBAL -->
    <ClientOnly>
      <TheGlobal />
      <ScartLazyLoader />
    </ClientOnly>
    <nav id="feed">
      <div class="content-card-background" />
    </nav>
  </main>
</template>

<script lang="ts" setup>

const route = useRoute();
const getRouteBaseName = useRouteBaseName();
const routeBaseName = computed(() => getRouteBaseName(route));
const { AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());

const loginPageNames = ['register-or-login', 'register-or-login-login', 'register-or-login-register'];

const isLoginPage = computed(() => routeBaseName.value && loginPageNames.includes(routeBaseName.value));
const isCheckoutLoginPage = computed(() => isLoginPage.value && route.query?.cart);
const isCartPage = computed(() => routeBaseName.value === 'cart');

const renderCheckoutFooter = computed(() => isCartPage.value || isCheckoutLoginPage.value);

</script>
