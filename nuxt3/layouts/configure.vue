<template>
  <main>
    <NuxtPage />
    <ClientOnly>
      <TheGlobal />
    </ClientOnly>
    <ScartLazyLoader />
  </main>
</template>

<script setup lang="ts">
const i18nHead = useLocaleHead({ addSeoAttributes: true });
const filteredHead = i18nHead.value.link.filter(item => (item.hreflang && (!item.hreflang.includes('en-OT') && !item.hreflang.includes('en-UK') && !item.hreflang.includes('da-DA'))));

i18nHead.value.link = filteredHead;

useHead(i18nHead.value);
</script>
