<template>
  <main
    id="default-layout"
    v-bind="{
      style: `--ribbon-height:${ribbonHeight}px`,
      class: AB_TESTS_NAVIGATION_2025 ? '[--old-navbar-height:0px] [--navigation-modern-padding:88px] md:[--navigation-modern-padding:96px]'
        : '[--navigation-modern-padding:0px] [--old-navbar-height:56px] md:[--old-navbar-height:64px]'
    }"
  >
    <LazyTheHeaderModern
      v-if="AB_TESTS_NAVIGATION_2025"
      hydrate-on-idle
      v-bind:variant="themeVariant"
    />
    <LazyTheHeader
      v-else
      hydrate-on-idle
    />
    <NuxtPage class="modern-navigation-page-padding" />

    <slot name="content" />

    <LazySectionNewsletter
      v-if="isNewsletterVisible"
      hydrate-on-visible
    />

    <LazyTheFooter2025 hydrate-on-visible />

    <ClientOnly>
      <TheGlobal />
    </ClientOnly>

    <ScartLazyLoader />

    <nav id="feed">
      <div class="content-card-background" />
    </nav>
  </main>
</template>

<script setup lang="ts">
const { AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());
const i18nHead = useLocaleHead({ addSeoAttributes: true });
const { isNewsletterVisible } = useNewsletter();
const filteredHead = i18nHead.value.link.filter((item: { hreflang?: string }) => (item.hreflang && (!item.hreflang.includes('en-OT') && !item.hreflang.includes('en-UK') && !item.hreflang.includes('da-DA'))));

i18nHead.value.link = filteredHead;

useHead(i18nHead.value);

const route = useRoute();
const getRouteBaseName = useRouteBaseName();
const routeBaseName = computed(() => getRouteBaseName(route));
const { ribbonHeight } = storeToRefs(useHeaderStore());
const themeVariant = computed(() => {
  const routesWithLightTheme = ['homepage', 'sofa-teaser'];
  const routesWithTransparentTheme = ['homepage-new'];

  if (routesWithLightTheme.includes(routeBaseName.value)) {
    return 'light';
  } else if (routesWithTransparentTheme.includes(routeBaseName.value)) {
    return 'transparent';
  }

  return 'dark';
});

</script>
