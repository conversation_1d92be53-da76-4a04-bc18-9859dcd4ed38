import { useGlobal } from '~/stores/global';

export const regionsWithNavigationABTest = [
  // 'netherlands',
  // 'belgium',
  // 'spain',
  // 'denmark',
  // 'norway',
  // 'ireland',
  // 'france',
  // 'united_kingdom',
  // 'switzerland',
  // 'austria',
  // 'finland',
  // 'germany',
  // 'italy',
  // 'luxembourg',
  // 'sweden'
];

export default defineNuxtRouteMiddleware(async (to, from) => {
  if (import.meta.client) { return; }

  const {
    IS_USER_AUTHENTICATED: isGlobalFetched,
    regionName
  } = storeToRefs(useGlobal());

  if (regionName.value && regionsWithNavigationABTest.includes(regionName.value)) {
    if (!isGlobalFetched.value) {
      await useAuth();
    }
  }
});
