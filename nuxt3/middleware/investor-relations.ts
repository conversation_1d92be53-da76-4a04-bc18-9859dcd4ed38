import { profile } from '~/api/account';

export default defineNuxtRouteMiddleware(async function (to) {
  const { $addLocaleToURL, $logException } = useNuxtApp();
  const { data, error } = await profile();

  if (error.value) {
    $logException(error.value);
  }

  if (error.value || !data?.value?.is_investor) {
    const investorLoginURL = $addLocaleToURL('/investor-relations-login/');

    if (investorLoginURL && to.fullPath !== investorLoginURL) {
      abortNavigation();
      return navigateTo(investorLoginURL);
    }
  }
}
);
