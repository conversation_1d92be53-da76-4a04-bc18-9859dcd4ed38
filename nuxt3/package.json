{"private": true, "type": "module", "name": "nuxt-app", "scripts": {"dev": "nuxt dev", "build": "nuxt build", "preview": "nuxt preview", "generate": "nuxt generate", "postinstall": "nuxt prepare", "nitro": "node --import ./.output/server/sentry.server.config.mjs .output/server/index.mjs", "lint": "eslint --ext .js,.vue,.ts --ignore-path .gitignore ."}, "devDependencies": {"@nuxt/devtools": "latest", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "nuxt": "^3.17.5", "nuxt-purgecss": "^2.0.0", "sanitize-html": "^2.13.0"}, "dependencies": {"@adyen/adyen-web": "^5.67.0", "@datadog/browser-rum": "^6.6.4", "@floating-ui/vue": "^1.1.6", "@formkit/nuxt": "^1.6.9", "@formkit/themes": "^1.6.9", "@headlessui-float/nuxt": "^0.15.0", "@headlessui/tailwindcss": "^0.2.2", "@headlessui/vue": "^1.7.23", "@kgierke/formkit-inputs": "^0.3.5", "@nuxtjs/device": "^3.2.4", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/i18n": "^9.5.3", "@nuxtjs/sanity": "^1.13.3", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.0", "@primer-io/checkout-web": "^2.44.0", "@sanity/image-url": "^1.1.0", "@sentry/nuxt": "^9.5.0", "@tylkocom/configurator": "0.6.16", "@tylkocom/fpm": "2.0.39", "@tylkocom/space-describer": "1.4.23", "@tylkocom/space-renderer": "1.3.27", "@types/dompurify": "^3.2.0", "@vueuse/components": "^12.7.0", "@vueuse/core": "^12.7.0", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "country-codes-list": "^2.0.0", "date-fns": "^4.1.0", "dd-trace": "^5.40.0", "dompurify": "^3.2.4", "gsap": "^3.12.7", "js-sha256": "^0.11.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "memfs": "^4.17.0", "nuxt-jsonld": "^2.1.0", "nuxt-lazy-hydrate": "^1.0.0", "nuxt-svgo": "^4.0.14", "nuxt-time": "^0.1.3", "sass": "^1.85.1", "swiper": "^11.2.4", "textfit": "^2.4.0", "universal-cookie": "^7.2.2", "vue": "^3.5.13", "vue-country-flag-next": "^2.3.2", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5", "vue3-google-map": "^0.21.0", "nuxt-vitalizer": "^0.10.0"}}