import {defineConfig, definePlugin} from 'sanity'
import {structureTool} from 'sanity/structure'
import {schemaTypes} from './schemaTypes'
import {documentInternationalization, } from '@sanity/document-internationalization'
import {markdownSchema} from 'sanity-plugin-markdown'
import {colorInput} from '@sanity/color-input'
import {SCHEMA_LANG_FIELDS} from './helpers'
import {structure} from './structure'
import {crossDatasetDuplicator} from '@sanity/cross-dataset-duplicator'

const sharedConfig = definePlugin({
  name: 'shareConfig',
  plugins: [
    markdownSchema(),
    colorInput(),
    documentInternationalization({
      // Required configuration
      supportedLanguages: [
        ...SCHEMA_LANG_FIELDS.map(lang => ({id: lang.id, title: lang.id})),
      ],
      schemaTypes: ['page', 'pageTerms', 'pageShipping', 'pagePrivacyPolicy', 'pageFaq', 'faqArticle', 'productListingPageB', 'pageArticle', 'productFurniturePage'],
    }),
    structureTool({structure}),
    crossDatasetDuplicator({
      types: [    
        'page', 
        'pageFaq',
        'pressReleases',
        'reportFile',
        'reportsQuarters',
        'pageTerms',
        'pageShipping',
        'pagePrivacyPolicy',
        'pageArticle',
        'samples',
        'influencers',
        'productFurniturePage',
        'productListingPageB',
      ],
    }),
  ],
  schema: {
    types: schemaTypes
  }
})

export default defineConfig([
  {
    name: 'production',
    title: 'Production',
    projectId: 'q7uj1um8',
    dataset: 'production',
    // the base path is required whenever more than one workspace is defined and is used for route matching
    basePath: '/prod',
    plugins: [sharedConfig()],
  },
  {
    name: 'staging',
    title: 'Staging',
    projectId: 'q7uj1um8',
    dataset: 'staging',
    basePath: '/staging',
    plugins: [sharedConfig()],
  },
])
