interface FaqPage {
  name: string;
  title: string;
  type: string;
  preview: {
    select: {
      title: string;
    };
  };
  fields: Array<Field>;
  initialValue: {
    language: string;
  };
}

interface Field {
  name: string;
  type: string;
  title: string;
  description?: string;
  initialValue?: string;
  hidden?: boolean;
  validation?: (Rule: any) => any;
  to?: Array<{ type: string }>;
  of?: Array<{ name: string; type: string; to: Array<{ type: string }> }>;
}

const faqPage: FaqPage = {
  name: 'pageFaq',
  title: 'Faq Page',
  type: 'document',
  preview: {
    select: {
      title: 'category.title.en'
    }
  },
  fields: [
    {
      name: 'language',
      type: 'string',
      title: 'Language',
      initialValue: 'en',
      hidden: true,
    },
    {
      name: 'name',
      type: 'string',
      title: 'Page name',
      description: 'Unique page name (same for all languages)',
      validation: Rule => Rule.required()
    },
    {
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [
        { type: 'faqCategory' }
      ],
      validation: Rule => Rule.required()
    },
    {
      name: 'articles',
      type: 'array',
      title: 'Articles',
      description: 'Articles list',
      validation: Rule => Rule.required(),
      of: [
        {
          name: 'id',
          type: 'reference',
          to: [
            { type: 'faqArticle' }
          ]
        }
      ],
    },
  ],
  initialValue: {
    language: 'en'
  }
};

export default faqPage;
