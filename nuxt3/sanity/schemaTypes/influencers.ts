import { Rule } from '@sanity/types'
import { SanityDocument } from '@sanity/types'

export interface Influencer extends SanityDocument {
  _type: 'Influencers'
  language: string
  isEnabled: boolean
  content: string
}

export default {
  name: 'Influencers',
  title: 'Influencers',
  type: 'document',
  fields: [
    {
      name: 'language',
      type: 'string',
      title: 'Language',
      initialValue: 'en',
      hidden: true,
    },
    {
      name: 'name',
      type: 'string',
      title: 'Page name',
      hidden: true
    },
    {
      name: 'isEnabled',
      type: 'boolean',
      title: 'Is page enabled in navigation',
      initialValue: false
    },
    {
      name: 'content',
      type: 'text',
      title: 'JSON content',
      description: 'plain json with content',
      validation: (Rule: Rule) => Rule.required()
    }
  ],
  initialValue: {
    language: 'en'
  }
};
