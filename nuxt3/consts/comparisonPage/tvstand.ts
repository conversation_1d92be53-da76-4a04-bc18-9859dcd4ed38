import type { ComparisonCategoryData } from '~/consts/comparison';
import {
  IconAdjust,
  IconDiamond,
  IconDimensionsDepth,
  IconDimensionsHight,
  IconDimensionsWidth,
  IconDoorsFront,
  IconDrawer,
  IconHandel,
  IconMaterialsColorDrop,
  IconMaterialsForm, IconMaterialsPlywood,
  IconMaterialsWood,
  IconOpenCompartments,
  IconPuzzle,
  IconStandFeetPlinth,
  IconTruck
} from '#components';
import { COLORS } from '~/consts/colors';
import { PRODUCT_COLOR_STYLES } from '~/consts/productLines';
import { lowFurnitureGeometries } from '~/consts/comparisonPage/geometries';

const colors = COLORS();

export const TVSTAND: ComparisonCategoryData = {
  defaultDetailsColor: [colors['01p']['01p-white'], colors['23']['23-offwhite']],
  galleryDetailMatrix: [
    // photo 1
    [
      {
        '01p': '1_cplus',
        '01v': '1_cplus',
        '02': '1_cplus'
      },
      {
        23: '1'
      }
    ],
    // photo 2
    [
      {
        '01p': '5_cplus',
        '01v': '5_cplus',
        '02': '8_cplus'
      },
      {
        23: '5'
      }
    ],
    // photo 3
    [
      {
        '01p': '7_cplus',
        '01v': '7_cplus',
        '02': '4_cplus'
      },
      {
        23: '7'
      }
    ]
  ],
  colorStyles: [PRODUCT_COLOR_STYLES.SOLID, PRODUCT_COLOR_STYLES.PLYWOOD, PRODUCT_COLOR_STYLES.WOODEN],
  hero: {
    title: 'comparison_page.tvstand.hero.title',
    imagePath: 'comparison-page/hero_new/tv-stand'
  },
  minigrid: {
    boardId: 'tvstand_pdp'
  },
  compareDetails: {
    sections: [{
      icon: IconDrawer,
      title: 'comparison_page.tvstand.compare.section1.title',
      detailsImageId: [
        {
          '01p': '8_cplus',
          '01v': '8_cplus',
          '02': '4_cplus'
        },
        {
          23: '8'
        }
      ],
      columns: [
        ['comparison_page.tvstand.compare.section1.classic1',
          'comparison_page.tvstand.compare.section1.classic2',
          'comparison_page.tvstand.compare.section1.classic3'
        ],
        ['comparison_page.sideboard.compare.section1.modern1',
          'comparison_page.sideboard.compare.section1.tone2',
          'comparison_page.sideboard.compare.section1.tone3',
          'comparison_page.sideboard.compare.section1.tone4'
        ]
      ]
    }, {
      icon: IconDoorsFront,
      title: 'comparison_page.tvstand.compare.section2.title',
      detailsImageId: [
        {
          '01p': '7_cplus',
          '01v': '7_crow',
          '02': '4_cplus'
        },
        {
          23: '7'
        }
      ],
      columns: [
        [
          'comparison_page.tvstand.compare.section2.classic'
        ],
        [
          'comparison_page.sideboard.compare.section2.tone'
        ]
      ]
    }, {
      icon: IconOpenCompartments,
      title: 'common_cable_openings',
      customImgPath: 'comparison-page/details/open_compartments/tvstand',
      detailsImageId: [
        {
          '01p': '6_cplus',
          '01v': '6_cplus',
          '02': '4_cplus'
        },
        {
          23: '6'
        }
      ],
      columns: [
        [
          'comparison_page.tvstand.compare.section3.classic1',
          'comparison_page.tvstand.compare.section3.classic2'
        ],
        [
          'comparison_page.tvstand.compare.section3.tone1',
          'comparison_page.tvstand.compare.section3.tone2'
        ]
      ]
    }, {
      icon: IconHandel,
      title: 'comparison_page.tvstand.compare.section4.title',
      customImgPath: 'comparison-page/details/handles',
      detailsImageId: [
        {
          '01p': '7_cplus',
          '01v': '7_crow',
          '02': '4_cplus'
        }
      ],
      columns: [
        [
          'comparison_page.tvstand.compare.section4.classic'
        ],
        [
          'comparison_page.sideboard.compare.section4.tone'
        ]
      ]
    }, {
      icon: IconStandFeetPlinth,
      title: 'comparison_page.tvstand.compare.section5.title',
      customImgPath: 'comparison-page/details/legs',
      detailsImageId: [
        {
          '01p': '5_cplus',
          '01v': '5_cplus',
          '02': '8_cplus'
        }
      ],
      columns: [
        [
          'comparison_page.sideboard.compare.section5.original1',
          'comparison_page.sideboard.compare.section5.original2'
        ],
        [
          'comparison_page.sideboard.compare.section5.tone1',
          'comparison_page.sideboard.compare.section5.tone2'
        ]
      ]
    }]
  },
  dimensions: {
    sections: [
      {
        icon: IconDimensionsWidth,
        title: 'common.width',
        columns: [
          'comparison_page.tvstand.dimensions.section1.classic',
          'comparison_page.sideboard.dimensions.section1.tone'
        ]
      }, {
        icon: IconDimensionsHight,
        title: 'common.height',
        columns: [
          'comparison_page.tvstand.dimensions.section2.classic',
          'comparison_page.sideboard.dimensions.section2.tone'
        ]
      }, {
        icon: IconDimensionsDepth,
        title: 'common.depth',
        columns: [
          'comparison_page.tvstand.dimensions.section3.classic',
          'comparison_page.sideboard.dimensions.section3.tone'
        ]
      }
    ]
  },
  usp: {
    isDarkMode: true,
    backgroundColor: '#28304A',
    header: {
      heading: [
        'new_comparison_page.sideboard.which_one.header1',
        'new_comparison_page.sideboard.which_one.header2'
      ]
    },
    sections: [{
      icon: IconAdjust,
      title: 'comparison_page.tvstand.which_one.section1.title',
      columns: [
        [
          'comparison_page.tvstand.which_one.section1.classic1',
          'comparison_page.tvstand.which_one.section1.classic2',
          'comparison_page.tvstand.which_one.section1.classic3'
        ], [
          'comparison_page.sideboard.which_one.section1.tone1',
          'comparison_page.sideboard.which_one.section1.tone2',
          'comparison_page.sideboard.which_one.section1.tone3'
        ]
      ]
    }, {
      icon: IconDiamond,
      title: 'comparison_page.tvstand.which_one.section2.title',
      columns: [
        [
          'comparison_page.tvstand.which_one.section2.rtb1',
          'comparison_page.tvstand.which_one.section2.rtb2',
          'comparison_page.tvstand.which_one.section2.rtb3',
          'comparison_page.tvstand.which_one.section2.rtb4'
        ]
      ]
    }]
  },
  styles: {
    sections: [
      {
        icon: IconMaterialsColorDrop,
        title: 'comparison_page.bookcase.adapt_style.section1.title',
        subTitle: 'comparison_page.bookcase.adapt_style.section1.subtitle',
        customComponentParams: 'solid',
        columns: [
          '',
          'comparison_page.sideboard.adapt_style.section1.tone'
        ]
      }, {
        icon: IconMaterialsPlywood,
        title: 'comparison_page.bookcase.adapt_style.section2.title',
        subTitle: 'comparison_page.bookcase.adapt_style.section2.subtitle',
        customComponentParams: 'plywood',
        columns: [

        ]
      },
      {
        icon: IconMaterialsWood,
        title: 'comparison_page.bookcase.adapt_style.section3.title',
        subTitle: 'comparison_page.bookcase.adapt_style.section3.subtitle',
        customComponentParams: 'wooden',
        columns: [
          'comparison_page.sideboard.adapt_style.section2.classic'
        ]
      }, {
        icon: IconMaterialsForm,
        title: 'comparison_page.bookcase.adapt_style.section4.title',
        subTitle: 'comparison_page.bookcase.adapt_style.section4.subtitle',
        hasDnaIcons: lowFurnitureGeometries,
        columns: [
          'comparison_page.sideboard.adapt_style.section3.modern',
          'comparison_page.sideboard.adapt_style.section3.tone'
        ]
      }
    ]
  },
  assembly: {
    sections: [{
      icon: IconPuzzle,
      title: 'comparison_page.tvstand.assembly.section1.title',
      columns: [
        [
          'comparison_page.tvstand.assembly.section1.classic1',
          'comparison_page.tvstand.assembly.section1.classic2',
          'comparison_page.tvstand.assembly.section1.classic3',
          'comparison_page.tvstand.assembly.section1.classic4',
          'comparison_page.tvstand.assembly.section1.classic5'
        ], [
          'comparison_page.sideboard.assembly.section1.modern1',
          'comparison_page.sideboard.assembly.section1.modern2',
          'comparison_page.sideboard.assembly.section1.modern3',
          'comparison_page.sideboard.assembly.section1.modern4',
          'comparison_page.sideboard.assembly.section1.tone1'
        ]
      ]
    }, {
      icon: IconTruck,
      title: 'comparison_page.tvstand.assembly.section2.title',
      subTitle: 'comparison_page.tvstand.assembly.section2.subtitle',
      columns: [
        [
          'comparison_page.tvstand.assembly.section2.classic'
        ],
        [
          'comparison_page.tvstand.assembly.section2.modern'
        ]
      ]
    }]
  }
};
