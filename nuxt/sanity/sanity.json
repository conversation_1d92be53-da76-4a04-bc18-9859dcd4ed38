{"root": true, "project": {"name": "<PERSON><PERSON><PERSON>"}, "__experimental_spaces": [{"name": "production", "title": "Production", "default": true, "api": {"projectId": "q7uj1um8", "dataset": "production"}}, {"name": "staging", "title": "Staging", "api": {"projectId": "q7uj1um8", "dataset": "staging"}}], "api": {"projectId": "q7uj1um8", "dataset": "staging"}, "plugins": ["@sanity/base", "@sanity/default-layout", "@sanity/default-login", "@sanity/desk-tool", "barcode-input", "@sanity/document-internationalization", "color-picker", "@sanity/color-input", "@sanity/cross-dataset-duplicator", "markdown"], "env": {"development": {"plugins": ["@sanity/vision"]}}, "parts": [{"name": "part:@sanity/base/schema", "path": "./schemas/schema"}, {"name": "part:@sanity/desk-tool/structure", "path": "./deskStructure.js"}, {"implements": "part:@sanity/base/document-actions/resolver", "path": "./src/document-actions"}], "server": {"hostname": "localhost", "port": 3338}}