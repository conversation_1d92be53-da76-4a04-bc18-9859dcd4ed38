export default {
  name: 'pageFaq',
  title: 'Faq Page',
  type: 'document',
  hidden: true,
  i18n: true,
  initialValue: {
    __i18n_lang: 'en'
  },
  preview: {
    select: {
      title: 'category.title.en'
    }
  },
  fields: [

    {
      name: 'name',
      type: 'string',
      title: 'Page name',
      description: 'Unique page name (same for all languages)',
      validation: Rule => Rule.required()
    },
    {
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [
        { type: 'faqCategory' }
      ],
      validation: Rule => Rule.required()
    },
    {
      name: 'articles',
      type: 'array',
      title: 'Articles',
      description: 'Articles list',
      validation: Rule => Rule.required(),
      of: [
        {
          name: 'id',
          type: 'reference',
          to: [
            { type: 'faqArticle' }
          ]
        }
      ],
    },
  ]
};
