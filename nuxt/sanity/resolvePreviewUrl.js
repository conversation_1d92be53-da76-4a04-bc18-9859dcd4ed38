export default function resolvePreviewUrl(doc) {
  // return `https://my-site.com/posts/${document.slug.current}`;
  // const baseUrl = 'http://127.0.0.1';
  const baseUrl = 'https://vesemir.ecom.oklyt.pl';

  let route = '';
  let lang = '';

  // addNewLanguage
  switch (doc.__i18n_lang) {
    case 'de':
      lang = 'de/';
      break;
    case 'fr':
      lang = 'fr/';
      break;
    case 'es':
      lang = 'es/';
      break;
    case 'nl':
      lang = 'nl/';
      break;
    case 'pl':
      lang = 'pl/';
      break;
    case 'it':
      lang = 'it/';
      break;
    case 'sv':
      lang = 'sv/';
      break;
    case 'da':
      lang = 'da/';
      break;
    case 'no':
      lang = 'no/';
      break;
    default:
      lang = '';
      break;
  }
  switch (doc.name) {
    case 'Get App':
      route = 'get-app';
      break;
    case 'Rooms':
      route = 'rooms';
      break;
    default:
      route = '';
      break;
  }

  return `${baseUrl}/${lang}${route}?preview=true`;
}
