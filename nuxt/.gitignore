# dependencies
node_modules

# logs
logs
*.logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Nuxt build
.nuxt

# Nuxt generate
dist

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE / Editor
.idea

# Service worker
sw.*

# macOS
.DS_Store

# Vim swap files
*.swp

# Phrase
.phrase.yml
