import { computed, ref, useStore } from '@nuxtjs/composition-api';
import { GET_SHELF_TYPE } from '~/utils/types';
import { useAnalytics } from '~/composables/analitics/useAnalytics';

function splitArray(array: any, divider: any) {
  const chunkSize = Math.ceil(array.length / divider);
  const chunks = [];

  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    chunks.push(chunk);
  }

  return chunks;
}

export default function() {
  const { getters, dispatch } = useStore();
  const furniture = ref<any>([]);
  const userId = computed(() => getters['global/USER_ID']);
  const sessionId = computed(() => getters['global/SESSION_ID']);
  const { getFurnitureEnglishName: getItemName } = useAnalytics();

  const impression = (item: any) => ({
    brand: item.brand,
    category: item.category,
    id: item.id,
    list: 'wishlist',
    name: item.id,
    position: 1,
    price: item.region_price_in_euro,
    variant: item.variant
  });

  const product = (item: any) => ({
    brand: item.brand,
    cart_item_status_density: item.density,
    cart_item_status_depth: item.depth,
    cart_item_status_doors: item.doors,
    cart_item_status_drawers: item.drawers,
    cart_item_status_height: item.height,
    cart_item_status_width: item.width,
    category: item.category,
    color_name: item.color_name,
    dimension15: item.dimension15,
    id: item.id,
    localProductPrice: item.region_price_with_discount,
    name: item.id,
    position: 1,
    price: item.region_price_in_euro,
    size_txt: item.size_txt,
    timeToDelivery: item.time_to_delivery,
    variant: item.variant
  });

  const getEcommerceProductRemoveEvent = (id: any) => {
    const item = furniture.value.find((i: any) => i.id === id);
    return {
      event: 'ecommerce removeFromCart',
      eventAction: undefined,
      eventLabel: undefined,
      eventValue: undefined,
      ecommerce: {
        remove: {
          products: [product(item)]
        }
      }
    };
  };

  const a2cGa4Event = async (id: any) => {
    const item = furniture.value.find((i: any) => i.id === id);
    // const { materialName } = getColor(item.shelf_type, item.material);
    // TODO: BE material
    const payloadPrice = parseFloat(item?.region_price_in_euro).toFixed(2);

    if (!userId.value) {
      await dispatch('global/UPDATE_GLOBAL_AFTER_ACCOUNT_CREATION', null, { root: true });
    }

    return {
      event: 'add_to_cart',
      userId: userId.value,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: null, // TODO: BE
        product_type: 'furniture',
        coupon: null, // TODO: BE
        items: [{
          item_id: item.id,
          item_name: getItemName(
            item.shelf_type <= 2 ? 'jetty' : 'watty',
            '', // TODO: BE pattern_name
            item.category,
            item.shelf_type
          ),
          affiliation: 'Tylko',
          quantity: 1,
          promotion_id: null, // TODO: BE,
          coupon: null, // TODO: BE
          discount: null, // TODO: BE
          index: null, // TODO: BE
          item_brand: 'furniture',
          item_category: item.category,
          item_category2: item.brand, // TODO: BE
          item_category3: item.configurator_type,
          item_category4: null, // TODO: BE
          item_category5: item.color_name, // TODO: BE
          item_list_name: null, // TODO: BE
          item_list_id: null, // TODO: BE
          item_variant: GET_SHELF_TYPE(item.shelf_type, true),
          price: payloadPrice,
          price_discounted: parseFloat(item?.region_price_with_discount_in_euro).toFixed(2),
          promotion_name: null, // TODO: BE
          assembly: item.shelf_type === 3 ? 'built-in' : 'additional', // TODO: BE
          item_id_preset: item.dimension15,
          discount_rate: null // TODO BE
        }]
      }
    };
  };

  return {
    getEcommerceProductRemoveEvent,
    a2cGa4Event,
    furniture
  };
}
