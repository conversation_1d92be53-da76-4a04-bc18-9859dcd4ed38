import { computed, useStore } from '@nuxtjs/composition-api';

interface review {
  name?: string,
  country?: string,
  score?: number,
  title?: string,
  description?: string,
  properPhoto?: string
}

export default function() {
  const store = useStore();

  const aggregateRating = computed(() => {
    const reviewsCount = store.getters['global/REVIEWS_COUNT'];
    const reviewsAverageScore = store.getters['global/REVIEWS_AVERAGE_SCORE'];

    return {
      '@type': 'AggregateRating',
      bestRating: '5',
      ratingCount: reviewsCount,
      ratingValue: reviewsAverageScore,
      worstRating: '1'
    };
  });

  const reviews = (data: Array<review>) => data.map(review => ({
    '@type': 'Review',
    name: review.title,
    reviewBody: review.description,
    reviewRating: {
      '@type': 'Rating',
      ratingValue: review.score,
      bestRating: '5',
      worstRating: '1'
    },
    author: { '@type': 'Person', name: review.name }
  }));

  return {
    aggregateRating,
    reviews
  };
}
