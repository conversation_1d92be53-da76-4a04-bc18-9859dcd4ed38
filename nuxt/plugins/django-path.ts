import { Context } from '@nuxt/types';
import { Inject } from '@nuxt/types/app';

export default (ctx: Context, inject: Inject) => {
  inject('localeDjangoPath', (pageKey: string, params?: Record<string, string>) => {
    const lang = ctx.i18n.locale === 'en' ? '' : `/${ctx.i18n.locale}`;
    const translation = ctx.i18n.t(`url.${pageKey}`);
    let url = `${lang}${translation}`;

    if (params) {
      const queryString = Object.entries(params).reduce((previousValue, [key, value], index) => {
        const prefix = index ? '&' : '';
        return `${previousValue}${prefix}${key}=${value}`;
      }, '?');

      url += queryString;
    }

    return url;
  });
  inject('localePrefix', (path: string) => {
    const lang = ctx.i18n.locale === 'en' ? '' : `/${ctx.i18n.locale}`;
    return `${lang}/${path}`;
  });
};

declare module 'vue/types/vue' {
  interface Vue {
    $localeDjangoPath: (key: string, params?: Record<string, string>) => string;
    $localePrefix: (path: string) => string;
  }
}

declare module '@nuxt/types' {
  interface NuxtAppOptions {
    $localeDjangoPath: (key: string) => string;
    $localePrefix: (path: string) => string;
  }

  interface Context {
    $localeDjangoPath: (key: string) => string;
    $localePrefix: (path: string) => string;
  }
}
