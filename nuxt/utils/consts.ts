export const analyticsShelfTypes = [
  'type01',
  'type02',
  'type01v',
  'type03'
];

export const globalUrl: string = '/api/v1/ecommerce/global/';

export const cart: Object = {
  ctaUrl: 'plp'
};

export const lastPlpBreakLinks: Object = {
  all: {
    id: '3647707',
    type: '2'
  },
  sideboard: {
    id: '3647707',
    type: '1'
  },
  bookcase: {
    id: '905336',
    type: '1'
  },
  wallstorage: {
    id: '3762451',
    type: '1'
  },
  wardrobe: {
    id: '92286',
    type: '1'
  },
  desk: {
    id: '3716776',
    type: '1'
  },
  tvstand: {
    id: '3650136',
    type: '1'
  },
  chest: {
    id: '3649867',
    type: '1'
  },
  shoerack: {
    id: '3760578',
    type: '1'
  },
  bedsidetable: {
    id: '3650075',
    type: '1'
  },
  vinylstorage: {
    id: '3763287',
    type: '1'
  }
};
export const excludedDixaPages: RegExp = /tylko-for-business|tylko-fur-unternehmen|tylko-para-empresas|tylko-business/gm;
