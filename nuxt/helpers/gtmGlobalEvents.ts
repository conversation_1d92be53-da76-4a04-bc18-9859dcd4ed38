import { Context } from '@nuxt/types';
import { Store } from 'vuex';
import type { Route } from 'vue-router';
import Cookies from 'universal-cookie';
import { getActiveCategory } from '~/helpers/plpCategories';

const contentGroupingDict: { [key: string]: string } = {
  '.*/(etageres|regale|shelves|products|produits|produkte|furniture-c|mobel-c|meubles-c|muebles-c|meubels-c)(/|\\?|$).*': 'Category Page',
  '.*/journal.*': 'Blog Page',
  'assembly-service': 'Assembly Service Page',
  '.*/(payment_method|payment_methods)/.*': 'Payment Method Page',
  '.*/(checkout|betaal)/.*': 'Checkout Page',
  '.*/(terms|terminos|voorwaarden)/.*': 'Terms Of Service Page',
  '.*/(shipping|envio|verzenden)/.*': 'Shipping Page',
  '.*/(review-list|lista-resenas|bekijk-lijst).*': 'Reviews Page',
  '.*/(product-lines|produktlinien|gammes-de-produits|productportfolio|lineas-de-productos)/.*': 'Comparison (Product Lines)',
  '.*/(contact|contacto)/.*': 'Contact Page',
  '.*/(library|wishlist)/.*': 'Wishlist Page',
  '.*/(sale|reduction|venta|uitverkoop).*': 'Promo LP',
  '.*/(categorie|kategorie|category|categoria|kledingkast)/.*': 'Category Landing Page',
  '.*/(gallery|galeria)/.*': 'Gallery',
  '.*/(echantillon-de-matiere|material-samples|muster|muestras-de-material|materiaal-staaltje)/.*': 'Material Samples',
  '.*/(rooms|pieces|raeume|habitaciones|kamers)/.*': 'Spaces Page',
  '.*(tylko-for-business|tylko-business|tylko-fur-unternehmen|tylko-para-empresas|tylko-voor-bedrijven).*': 'B2B Page',
  '.*/(etagere|regal|shelf|wardrobe|furniture|mobel|meuble|mueble|meubel)/.*': 'Product Page',
  '/(our-mission|de/unsere-mission|fr/mission|nuestra-mision)/.*': 'Our Mission Page',
  '/(|(de|fr|es|nl)/)faq/': 'FAQ Page',
  '/(|(de|fr|es|nl)/)account/': 'Account Page',
  '.*/review-form/.*': 'Review Form',
  '.*/cart/.*': 'Cart Page',
  '.*/visit-our-showroom/.*': 'Showroom Page',
  '.*/delivery-time-frames/.*': 'Delivery Time Frames',
  '.*/error-404/(.*/|)payment_methods/.*': 'Error on payment method - return with cart drop',
  '.*get-app.*': 'Get App Page',
  '.*register.*': 'Register or Login Page',
  '/error.*': 'Error Page',
  '.*blackfriday.*': 'Black Friday',
  '.*/subscription-confirmation/.*': 'Subscription',
  '.*confirmation.*': 'Purchase',
  '/(|(de|fr|es|nl|pl|en|it)/)': 'Home Page'
};

const getContentGrouping = (url: string): string | null => {
  for (const regex in contentGroupingDict) {
    if (new RegExp(regex).test(url)) {
      return contentGroupingDict[regex];
    }
  }

  return null;
};

export default function(context: Context) {
  const { store, $gtm, i18n, route }: { $gtm?: any, store: Store<any>, i18n: any, route: Route } = context;

  const cookies = new Cookies();
  const globalSessionIdCookie = cookies.get('global_session_id');

  return {
    pageView: (
      prevUrl: string | null,
      pageUrl: string,
      initialPage: boolean = false
    ) => {
      if (process.client) {
        if (initialPage) {
          prevUrl = document.referrer;
        }

        const pageType = getContentGrouping(pageUrl);

        const langCode = i18n.loadedLanguages[0];
        const width = window.innerWidth;
        const height = window.innerHeight;
        let properCategory = null;

        if (pageType === 'Category Page') {
          properCategory = getActiveCategory(i18n, route).categoryIndex || 'All products';
        } else if (pageType === 'Product Page') {
          properCategory = store.getters['global/PDP_PRODUCT_CATEGORY'];
        }

        $gtm.push({
          event: 'page_view',
          globalSessionId: store.getters['global/GLOBAL_SESSION_ID'] || globalSessionIdCookie,
          language_version: langCode,
          region_name: store.getters['global/REGION_NAME'],
          ab_test: store.getters['global/AB_IDS'],
          page_url: pageUrl,
          product_category: properCategory,
          screen_resolution: `${width}x${height}`,
          canonical_url: document.querySelector("link[rel='canonical']")?.getAttribute('href'),
          page_type: pageType,
          virtual_referrer: prevUrl
        });
      }
    }
  };
}
