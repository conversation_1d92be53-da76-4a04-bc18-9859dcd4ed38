<template>
  <div>
    <ContentHeroHeader
      class="lg:block"
      v-bind:class="{
        'hidden': $route.params.article || $route.params.tags
      }"
      v-bind="{
        headingCopy: $t('faq.header_title'),
        imagePath: 'faq/hero',
        imageAlt: $t('faq.header_title'),
        imageType: 'M T SD LD'
      }"
    >
      <ul
        v-if="tags"
        class="mt-8 md:mt-24"
      >
        <li
          v-for="(tag, index) in tags"
          v-bind:key="index"
          class="inline-block after:mx-8 md:after:mx-16 after:-mb-2 after:w-1 after:h-16 after:bg-offblack-600 after:inline-block last:after:hidden"
        >
          <NuxtLink
            v-bind="{
              class: [
                'inline-block mt-16 xl:mb-0 normal-16 hover:text-orange transition-color basic-transition',
                $route.params.tags === tag.slug ? 'text-orange' : 'text-offblack-600'
              ],
              to: `${FAQPages.getTagsRoute(tag.slug)}`,
              'data-testid': `faq-${tag.slug}-tag`
            }"
          >
            {{ tag.name }}
          </NuxtLink>
        </li>
      </ul>
    </ContentHeroHeader>

    <div
      id="faq-nuxt-container"
      class="container-cstm-fluid lg:mb-64"
    >
      <NuxtChild />
    </div>

    <div
      id="faq-contact-form-container"
      class="container-cstm-fluid bg-offwhite-600 py-64 lg:pb-128 grid grid-cols-12"
    >
      <div class="col-span-12 lg:col-start-2 lg:col-end-12 xl:col-start-3 xl:col-end-11 xl2:col-start-4 xl2:col-end-10">
        <h1
          class="bold-46 text-offblack-800 "
          v-html="$t('contact_form.title')"
        />
        <p
          class="normal-20 text-offblack-600 mt-16 mb-32"
          v-html="$t('contact_form.contact_us')"
        />
        <Contact />
        <ContactFooter />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { deepMerge } from '@/utils/helpers';
import { FAQPages } from '@/utils/pages';
import { alternateFaqLang } from '@/utils/regions';

export default defineComponent({
  setup() {
    return {
      FAQPages
    };
  },
  async asyncData({ $sanity, i18n }) {
    const query = groq`*[_type == "faqTag"]`;
    const lang = alternateFaqLang(i18n.locale);
    const tags = await $sanity.fetch(query).then(response => response.map(item => ({
      id: item._id,
      name: item.name[lang],
      slug: item.slug[lang]
    })));

    return {
      tags
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('faq.meta.title'),
      meta: [
        {
          hid: 'og:title',
          name: 'og:title',
          content: this.$t('faq.meta.title')
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('faq.meta.description')
        },
        {
          hid: 'og:description',
          name: 'og:description',
          content: this.$t('faq.meta.description')
        }
      ]
    });
  }
});
</script>
