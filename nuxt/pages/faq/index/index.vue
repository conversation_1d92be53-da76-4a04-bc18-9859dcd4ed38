<template>
  <div class="pt-16 mb-32 lg:mb-0 lg:pt-64">
    <div class="lg:grid lg:grid-cols-3 lg:gap-16 xl:mx-64 xl2:mx-128">
      <TheFAQCategory
        v-for="(category, index) in categories"
        v-bind:key="index"
        class="border-b border-solid last:border-b-0 border-grey-700 lg:border-b-0"
        v-bind="{
          slug: category.slug,
          questions: category.questions,
          title: category.title,
          iconComponent: category.icon
        }"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { deepMerge } from '@/utils/helpers';
import { alternateFaqLang } from '~/utils/regions';

export default defineComponent({
  async asyncData({ i18n, $sanity }) {
    const lang = alternateFaqLang(i18n.locale);
    const categoriesQuery = groq`*[_type == "faqCategory"]`;
    const articlesQuery = groq`*[_type == "faqArticle" && __i18n_lang == "${lang}"]`;

    const articles = await $sanity.fetch(articlesQuery);
    const categories = await $sanity.fetch(categoriesQuery).then(response => response.map(item => ({
      id: item._id,
      title: item.title[lang],
      slug: item.slug[lang],
      icon: item.icon,
      order: item.order,
      questions: articles.filter(article => article.category._ref === item._id).map((article) => {
        return {
          title: article.title,
          slug: article.slug
        };
      })
    })));

    categories.sort((a, b) => a.order - b.order);

    return {
      articles,
      categories
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('faq.meta.title'),
      meta: [
        {
          hid: 'og:title',
          name: 'og:title',
          content: this.$t('faq.meta.title')
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('faq.meta.description')
        },
        {
          hid: 'og:description',
          name: 'og:description',
          content: this.$t('faq.meta.description')
        }
      ]
    });
  }
});
</script>
