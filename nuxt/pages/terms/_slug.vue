<template>
  <div>
    <ContentMobileHeader v-bind:title="title" />
    <article
      class="mt-32 lg:mt-0"
      v-html="md.render(content)"
    />
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { alternateFaqLang } from '~/utils/regions';

const md = require('markdown-it')();

export default defineComponent({
  setup() {
    return {
      md
    };
  },
  async asyncData({ $sanity, params, i18n, redirect, $localeDjangoPath }) {
    const lang = alternateFaqLang(i18n.locale);
    const query = groq`*[_type == "pageTerms" && pageUrl == "${params.slug}" && __i18n_lang == "${lang}"][0]`;
    const data = await $sanity.fetch(query);

    if (!data) {
      const initialPage = groq`*[_type == "pageTerms" && pageID == 1 && __i18n_lang == "${lang}"][0]`;
      const initialPageData = await $sanity.fetch(initialPage);

      return redirect(`${$localeDjangoPath('terms')}${initialPageData.pageUrl}`);
    }

    return {
      title: data.pageName,
      content: data.pageContent
    };
  }
});
</script>
