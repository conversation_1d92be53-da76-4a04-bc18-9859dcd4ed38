<template>
  <main class="tylko-for-business">
    <LazyHydrate when-idle>
      <SectionHeroCarousel
        data-section="hero-carousel"
        class="bg-[#998a7f]"
        v-bind="{
          images: [{ path: 'lp/tylko-for-business/hero_2/1' }, { path: 'lp/tylko-for-business/hero_2/2' }, { path: 'lp/tylko-for-business/hero_2/3' }, { path: 'lp/tylko-for-business/hero_2/4' }],
          imageAlt: $t('lp.tylkoforbusiness.hero.headline'),
          imageType: 'M T SD LD XLD',
          imagePath: '',
          headingCopy: $t('lp.tylkoforbusiness.hero.headline'),
          subheadingCopy: $t('lp.tylkoforbusiness.hero.subheadline'),
          ctaCopy: $t('common.learn_more'),
          ctaCallback: () => scrollToElement({ element: '#designerQuestion' }),
          headerColorClass: 'text-white'
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionUspsCustomBar
        class="bg-white"
        data-section="perks-bar"
        v-bind="{
          items: uspsData,
        }"
      />
    </LazyHydrate>

    <LazyHydrate when-visible>
      <SectionTextInRow
        id="designerQuestion"
        data-section="designer-question"
        class="bg-[#403B37]"
        v-bind="{
          title: $t('lp.tylkoforbusiness.intro.headline'),
          paragraph: $t('lp.tylkoforbusiness.intro.body'),
          ctaUrl: $localeDjangoPath('lp.tylkoforbusinessform'),
          ctaCopy: $t('lp.tylkoforbusiness.intro.cta'),
          secondaryCta: $t('lp.tylkoforbusiness.intro.secondary.button'),
          secondaryCtaCallback: () => scrollToElement({ element: '#uspsVideo' }),
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionUspsCustomBar
        v-if="isDesktop"
        data-section="usps-large"
        is-big-variant
        v-bind="{
          items: uspsDataLarge,
          isAssembly: isAssemblyAvailable
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionFullVideoWithButton
        id="uspsVideo"
        v-bind="{
          videoId: isDesktop ? videoToShow.desktopId : videoToShow.mobileId,
          paddingTop: isDesktop ? '56.25%' : '142.5%',
          click: () => scrollToElement({ element: '#benefits' }),
          ctaCopy: $t('lp.tylkoforbusiness.usps.cta')
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionIconsInRow
        id="benefits"
        data-section="benefits"
        class="bg-[#F3EBDE]"
        v-bind="{
          title: $t('lp.tylkoforbusiness.benefits.headline'),
          items: benefitsData,
          ctaCopy: $t('lp.tylkoforbusiness.benefits.cta'),
          ctaUrl: $localeDjangoPath('lp.tylkoforbusinessform'),
          isCustomColor: true
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionTestimonials
        data-section="testimonials"
        class="bg-[#F9F9F9]"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <ErrorBoundary>
        <SectionUspsCustomBar
          v-if="!isDesktop"
          data-section="usps-large"
          is-big-variant
          v-bind="{
            items: uspsDataLarge,
            isAssembly: isAssemblyAvailable
          }"
        />
      </ErrorBoundary>
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionShowroom
        data-section="featured-projects"
        class="bg-[#403B37]"
        v-bind="{
          heading: $t('lp.tylkoforbusiness.projects.headline'),
          boardsData: projectsData,
          isLightTheme: true,
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionTextCenteredBackground
        data-section="image-bank"
        class="bg-[#fcfdf6] image-bank"
        v-bind="{
          title: $t('lp.tylkoforbusiness.assets.headline'),
          subTitle: $t('lp.tylkoforbusiness.assets.subheadline'),
          imagePath: 'lp/tylko-for-business/image-bank',
          cta: $t('lp.tylkoforbusiness.assets.cta'),
          ctaUrl: `${$localeDjangoPath('press')}#download`,
          withIcon: false,
          isLightTheme: false
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionTextImageColumns
        data-section="journal-section-autor"
        class="bg-[#D3D3CA]"
        v-bind="{
          title: $t('lp.tylkoforbusiness.journal.autor.headline'),
          subtitle: $t('common.tylko_journal'),
          imagePath: 'lp/tylko-for-business/journal/autor-rooms',
          description: $t('lp.tylkoforbusiness.journal.autor.body'),
          ctaCopy: $t('common.read_full_story'),
          ctaUrl: $localeDjangoPath('autor-rooms-warsaw-new-hospitality'),
          isLightTheme: false,
          isReversed: false,
          isReversedOnMobile: true,
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionTextImageColumns
        data-section="journal-section-bistro"
        class="bg-[#DDC894]"
        v-bind="{
          title: $t('lp.tylkoforbusiness.journal.bistro.headline'),
          subtitle: $t('common.tylko_journal'),
          imagePath: 'lp/tylko-for-business/journal/bistro',
          description: $t('lp.tylkoforbusiness.journal.bistro.body'),
          ctaCopy: $t('common.read_full_story'),
          ctaUrl: $localeDjangoPath('marianne-philipp-chefs-and-owners-of-bistro-stadtsalettl-in-berlin'),
          isLightTheme: false,
          isReversed: true,
          isReversedOnMobile: true,
        }"
      />
    </LazyHydrate>
    <LazyHydrate when-visible>
      <SectionExplore
        data-section="explore"
        class="bg-[#282E39]"
        v-bind="{
          heading: $t('lp.tylkoforbusiness.explore.headline'),
          ctaText: $t('common.explore'),
          type: 'sideboard',
          sectionCategories: ['bookcase', 'shoerack', 'sideboard', 'tvstand', 'wallstorage', 'chest', 'vinyl storage'],
          isThemeLight: true
        }"
      />
    </LazyHydrate>
  </main>
</template>

<script>
import { computed, defineComponent, useContext, useStore } from '@nuxtjs/composition-api';
import { scrollToElement } from '@/helpers';
import { deepMerge } from '~/utils/helpers';
import { TYLKO_FOR_BUSINESS } from '~/utils/languages';

export default defineComponent({
  layout: 'defaultNoPromoNoNewsletter',
  setup() {
    const { i18n } = useContext();
    const store = useStore();
    const isDesktop = computed(() => store.getters['global/IS_DESKTOP']);

    const uspsData = [
      {
        iconComponent: 'IconDiscountBold',
        title: 'lp.tylkoforbusiness.benefitsshort.title1'
      },
      {
        iconComponent: 'IconCashBold',
        title: 'lp.tylkoforbusiness.benefits.title5'
      },
      {
        iconComponent: 'IconSamplesBold',
        title: 'lp.tylkoforbusiness.benefitsshort.title2'
      },
      {
        iconComponent: 'Icon3dBold',
        title: 'lp.tylkoforbusiness.benefitsshort.title5'
      },
      {
        iconComponent: 'IconSupportBold',
        title: 'lp.tylkoforbusiness.benefitsshort.title4'
      }
    ];

    const uspsDataLarge = [
      {
        iconComponent: 'IconClock',
        title: 'lp.tylkoforbusiness.usp.title1',
        subtitle: 'lp.tylkoforbusiness.usp.subtitle1',
        hint: 'lp.tylkoforbusiness.usp.hint1'
      },
      {
        iconComponent: 'IconDelivery',
        title: 'lp.tylkoforbusiness.usp.title2',
        subtitle: 'lp.tylkoforbusiness.usp.subtitle2',
        hint: 'lp.tylkoforbusiness.usp.hint2'
      },
      {
        iconComponent: 'IconAssembly',
        title: 'lp.tylkoforbusiness.usp.title3',
        subtitle: 'lp.tylkoforbusiness.usp.subtitle3',
        hint: 'lp.tylkoforbusiness.usp.hint3',
        modal: 'lp.tylkoforbusiness.usp.modal3',
        isAssembly: true
      },
      {
        iconComponent: 'IconReturn',
        title: 'lp.tylkoforbusiness.usp.title4',
        subtitle: 'lp.tylkoforbusiness.usp.subtitle4',
        hint: 'lp.tylkoforbusiness.usp.hint4'
      }
    ];

    const benefitsData = [
      {
        iconComponent: 'IconDiscountBold',
        text: 'lp.tylkoforbusiness.benefits.title1'
      },
      {
        iconComponent: 'IconCashBold',
        text: 'lp.tylkoforbusiness.benefits.title5'
      },
      {
        iconComponent: 'IconSamplesBold',
        text: 'lp.tylkoforbusiness.benefits.title2'
      },
      {
        iconComponent: 'Icon3dBold',
        text: 'lp.tylkoforbusiness.benefits.title6'
      },
      {
        iconComponent: 'IconSupportBold',
        text: 'lp.tylkoforbusiness.benefits.title4'
      }
    ];

    const selectedProjects = [
      { name: 'contentful', photosAmount: 6 },
      { name: 'yay', photosAmount: 5 },
      { name: 'adyen', photosAmount: 4 },
      { name: 'balagan', photosAmount: 6 },
      { name: 'tylko', photosAmount: 8 },
      { name: 'azur', photosAmount: 5 },
      { name: 'koi_studio', photosAmount: 5 },
      { name: 'stadtsalettl', photosAmount: 4 }
    ];

    const projectsData = selectedProjects.map(({ name, photosAmount }) => {
      const images = [];

      for (let i = 1; i <= photosAmount; i++) {
        images.push(
          {
            imagePath: `lp/tylko-for-business/projects/${name}/${i}`,
            imageAlt: `lp.tylkoforbusiness.projects.name.${name}`
          }
        );
      }

      return {
        label: i18n.t(`lp.tylkoforbusiness.projects.name.${name}`),
        text: i18n.t(`lp.tylkoforbusiness.projects.body.${name}`),
        images
      };
    });

    const languageDetected = i18n.localeProperties.code;
    const videosAssembly = TYLKO_FOR_BUSINESS.assembly;
    const videosNoAssembly = TYLKO_FOR_BUSINESS.noAssembly;

    const isAssemblyAvailable = computed(() => store.getters['global/ASSEMBLY_AVAILABLE']);
    const videoToShow = computed(() => (isAssemblyAvailable.value ? videosAssembly[languageDetected] : videosNoAssembly[languageDetected]));

    return {
      benefitsData,
      scrollToElement,
      projectsData,
      isAssemblyAvailable,
      isDesktop,
      uspsData,
      uspsDataLarge,
      videoToShow
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('lp.tylkoforbusiness.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('lp.tylkoforbusiness.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('lp.tylkoforbusiness.meta.title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('lp.tylkoforbusiness.meta.og_description')
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.$t('lp.tylkoforbusiness.meta.image')
        }
      ],
      __dangerouslyDisableSanitizers: ['script'],
      script: [
        {
          src: 'https://leadbooster-chat.pipedrive.com/assets/loader.js',
          async: true
        },
        {
          innerHTML: 'window.pipedriveLeadboosterConfig = {base: \'leadbooster-chat.pipedrive.com\',companyId: 11615988,playbookUuid: \'d820aad5-d8cf-4c88-baaa-7e5dabf33b1c\',version: 2};(function () {var w = window;if (w.LeadBooster) {console.warn(\'LeadBooster already exists\');} else {w.LeadBooster = {q: [],on: function (n, h) {this.q.push({ t: \'o\', n: n, h: h });},trigger: function (n) {this.q.push({ t: \'t\', n: n });},};}})();'
        }
      ]
    });
  }
});
</script>

<style lang="scss">
.tylko-for-business {
  .hero-slider .heading {
    @apply font-bold md:text-[46px] lg:text-[46px] xl:text-[72px];
  }

  .image-bank .content {
    @apply top-auto bottom-32 sm:bottom-64 translate-y-[0] md:top-1/2 md:-translate-y-1/2;
  }
}

</style>
