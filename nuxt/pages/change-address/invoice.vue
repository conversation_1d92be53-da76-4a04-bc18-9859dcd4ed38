<template>
  <main class="container-cstm-fluid pb-64 lg:pb-96">
    <div class="grid grid-cols-12">
      <div class="col-span-12 mt-16 md:mt-32 lg:mt-16 xl:mt-32">
        <BaseLink
          variant="link"
          v-bind="{
            arrow: 'left',
            href: $localeDjangoPath('account'),
            trackData: { eventLabel: 'change-password-back' }
          }"
          class="bold-12 text-offblack-600 hover:text-offblack-600"
        >
          {{ $t('common.back') }}
        </BaseLink>
      </div>
      <div class="col-span-12 md:col-span-8 md:col-start-3 lg:col-span-5 xl:col-span-4 xl:col-start-2 mt-16">
        <h1
          class="bold-32 text-offblack-800 md:text-center lg:text-left"
          v-html="$t('account.change_address')"
        />
      </div>
      <div
        class="col-span-12 md:col-span-8 md:col-start-3 lg:col-span-6 lg:col-start-7 xl:col-span-5 xl:col-start-7 mt-32 md:mt-16"
      >
        <FormulateForm
          v-slot="{ isLoading }"
          v-model="formValues"
          v-on:submit="submitForm"
          v-on:failed-validation="scrollToError"
        >
          <div class="grid grid-cols-12 gap-16">
            <div class="col-span-12 lg:col-span-6">
              <FormulateInput
                v-model="data.invoice_first_name"
                type="text"
                v-bind="{
                  name: 'invoice_first_name',
                  label: $t('account.first_name'),
                  placeholder: $t('account.enter_name')
                }"
                validation="required"
                v-bind:validation-name="$t('account.first_name')"
                validation-behavior="live"
                autocomplete="on"
              />
            </div>
            <div class="col-span-12 lg:col-span-6">
              <FormulateInput
                v-model="data.invoice_last_name"
                type="text"
                v-bind="{
                  name: 'invoice_last_name',
                  label: $t('account.surname'),
                  placeholder: $t('account.enter_surname')
                }"
                validation="required"
                v-bind:validation-name="$t('account.surname')"
                validation-behavior="live"
                autocomplete="on"
              />
            </div>
          </div>

          <FormulateInput
            v-model="data.invoice_street_address_1"
            class="mt-32"
            type="text"
            v-bind="{
              name: 'invoice_street_address_1',
              label: $t('account.address_line'),
              placeholder: $t('account.enter_address')
            }"
            validation="required"
            v-bind:validation-name="$t('account.address_line')"
            validation-behavior="live"
            autocomplete="on"
          />

          <FormulateInput
            v-model="data.invoice_street_address_2"
            class="mt-32"
            type="text"
            v-bind="{
              name: 'invoice_street_address_2',
              label: $t('account.apt_number'),
              placeholder: $t('account.apartament_number')
            }"
            autocomplete="on"
          />

          <div class="grid grid-cols-12 gap-16 mt-32">
            <div class="col-span-12 lg:col-span-6">
              <FormulateInput
                v-model="data.invoice_postal_code"
                type="text"
                v-bind="{
                  name: 'invoice_postal_code',
                  label: $t('account.postcode'),
                  placeholder: $t('account.enter_postcode')
                }"
                validation="required"
                v-bind:validation-name="$t('account.postcode')"
                validation-behavior="live"
                autocomplete="on"
              />
            </div>
            <div class="col-span-12 lg:col-span-6">
              <FormulateInput
                v-model="data.invoice_city"
                type="text"
                v-bind="{
                  name: 'invoice_city',
                  label: $t('account.city'),
                  placeholder: $t('account.enter_city')
                }"
                validation="required"
                v-bind:validation-name="$t('account.city')"
                validation-behavior="live"
                autocomplete="on"
              />
            </div>
          </div>

          <FormulateInput
            v-model="data.invoice_country"
            class="mt-32"
            type="select"
            v-bind="{
              name: 'invoice_country',
              label: $t('account.country'),
              placeholder: $t('account.select_country.placeholder'),
              options: countryOptions
            }"
          />

          <FormulateInput
            v-model="data.invoice_phone"
            class="mt-32"
            type="text"
            v-bind="{
              name: 'invoice_phone',
              label: $t('account.phone_number'),
              placeholder: $t('account.enter_phone_number')
            }"
            validation="required"
            v-bind:validation-name="$t('account.phone_number')"
            validation-behavior="live"
            autocomplete="on"
          />

          <FormulateInput
            v-model="data.invoice_company_name"
            class="mt-32"
            type="text"
            v-bind="{
              name: 'invoice_company_name',
              label: $t('account.company_name'),
              placeholder: $t('account.enter_company_name')
            }"
            autocomplete="on"
          />

          <FormulateInput
            v-model="data.invoice_vat"
            class="mt-32"
            type="text"
            v-bind="{
              name: 'invoice_vat',
              label: $t('account.vat_or_eu_vat'),
              placeholder: $t('account.enter_vat_or_eu_vat')
            }"
            autocomplete="on"
          />

          <FormulateInput
            class="mt-32 md:text-center lg:text-left"
            v-bind:input-class="`btn-cta relative w-full md:w-auto ${isLoading ? '!text-orange !bg-orange' : 'text-white'}`"
            type="submit"
            v-bind:disabled="isLoading"
          >
            {{ $t('common.save') }}
            <UiDotsLoader
              v-if="isLoading"
              class="mt-2"
              bounce-class="bg-white"
            />
          </FormulateInput>
        </FormulateForm>
      </div>
    </div>
  </main>
</template>

<script>
import { defineComponent, useContext, ref, useFetch, useStore, useRoute, useRouter } from '@nuxtjs/composition-api';
import { deepMerge } from '@/utils/helpers';
import { ACCOUNT, INITIAL } from '@/utils/api';
import useRegions from '@/composables/useRegions';
import useForms from '@/composables/useForms';

export default defineComponent({
  name: 'ChangeAddressInvoice',
  setup() {
    const { sortedTranslatedRegionsWithOtherRegion } = useRegions();
    const { $notify, i18n, $axios, $logException } = useContext();
    const { scrollToError } = useForms();
    const store = useStore();
    const route = useRoute();
    const router = useRouter();
    const formValues = ref({});
    const data = ref(null);

    const countryOptions = sortedTranslatedRegionsWithOtherRegion.reduce((o, region) => ({
      ...o,
      [region.name]: region.translatedName
    }), {});

    useFetch(async() => {
      try {
        data.value = await ACCOUNT.PROFILE($axios);
      } catch (e) {
        $logException(e);
      }
    });

    const submitForm = async(data) => {
      try {
        data.value = await ACCOUNT.PROFILE_UPDATE($axios, data);
        const globalData = await INITIAL.GLOBAL($axios, route.value.path);
        store.dispatch('global/UPDATE_GLOBAL', globalData);
        router.push('/account');
      } catch (e) {
        await $notify({ text: i18n.t('common.error.connection'), type: 'error' });
      }
    };

    return {
      data,
      formValues,
      submitForm,
      countryOptions,
      scrollToError
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('common.category.all_meta_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('hp.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('common.category.all_meta_title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('hp.meta.description')
        },
        {
          hid: 'robots',
          name: 'robots',
          content: 'noindex'
        }
      ]
    });
  }
});
</script>
