<template>
  <main>
    <BlockContent
      v-if="pageData && pageData['pageBuilder']"
      v-bind="{
        blocks: pageData['pageBuilder'],
        serializers: serializers
      }"
    />
    <h1
      v-else
      class="container-cstm-fluid text-24 py-64"
    >
      Please select page sections in Sanity
    </h1>
  </main>
</template>

<script>
import { defineComponent, useRoute } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { isEmpty } from 'lodash-es';
import useSeo from '@/composables/useSeo';
import { deepMerge } from '~/utils/helpers';
import { serializers } from '~/utils/sanitySerializers';

export default defineComponent({
  setup() {
    const route = useRoute();
    const { aggregateRating } = useSeo();

    return {
      route,
      aggregateRating,
      serializers
    };
  },
  async asyncData({ route, $sanity, store, $logException, redirect }) {
    let pageData;

    try {
      const query = groq`*[_type=="pageCategory" && pageUrl == "${route.params.category}"][0]{...,"langUrls": *[_type=='pageCategory' && name == ^.name]{ pageUrl, __i18n_lang }}`;
      pageData = await $sanity.fetch(query);

      if (_.isEmpty(pageData)) {
        return redirect('/404');
      }

      // addNewLanguage
      store.dispatch('i18n/setRouteParams', {
        en: { category: pageData.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl },
        de: { category: pageData.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl },
        fr: { category: pageData.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl },
        nl: { category: pageData.langUrls.find(el => el.__i18n_lang === 'nl')?.pageUrl },
        es: { category: pageData.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl },
        pl: { category: pageData.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl },
        it: { category: pageData.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl },
        sv: { category: pageData.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl },
        da: { category: pageData.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl },
        no: { category: pageData.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl },
      });
    } catch (error) {
      $logException(error);

      return null;
    }

    return {
      pageData
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: false });
    return deepMerge(i18nHead, {
      title: this.pageData?.seoTitle,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.pageData?.seoDescription
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.pageData?.seoTitle
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.pageData?.seoDescription
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.pageData && this.$urlFor(this.pageData.seoImage).url()
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `${this.$t('url.base')}/category/`
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'en'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'en-GB'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl}/`,
          hreflang: 'fr'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl}/`,
          hreflang: 'fr-FR'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl}/`,
          hreflang: 'de'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl}/`,
          hreflang: 'de-DE'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl}/`,
          hreflang: 'es'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl}/`,
          hreflang: 'es-ES'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl}/`,
          hreflang: 'pl'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl}/`,
          hreflang: 'pl-PL'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl}/`,
          hreflang: 'it'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl}/`,
          hreflang: 'it-IT'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl}/`,
          hreflang: 'sv'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl}/`,
          hreflang: 'sv-SE'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl}/`,
          hreflang: 'da'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl}/`,
          hreflang: 'da-DK'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl}/`,
          hreflang: 'no'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl}/`,
          hreflang: 'no-NO'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData?.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'x-default'
        }
      ]
    });
  },
  jsonld() {
    return {
      '@context': 'https://schema.org',
      '@graph': [
        {
          '@type': 'BreadcrumbList',
          itemListElement: [
            {
              '@type': 'ListItem',
              item: `${this.$t('url.base')}/category/`,
              name: this.$t('common.category'),
              position: 1
            }
          ]
        },
        {
          '@type': 'Product',
          name: this.pageData?.seoTitle,
          description: this.pageData?.seoDescription,
          brand: {
            '@type': 'Brand',
            name: 'Tylko'
          },
          aggregateRating: this.aggregateRating
        }
      ]
    };
  }
});
</script>
