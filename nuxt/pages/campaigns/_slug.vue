<template>
  <main>
    <BlockContent
      v-if="pageData && pageData['pageBuilder']"
      v-bind="{
        blocks: pageData['pageBuilder'],
        serializers: serializers
      }"
    />
    <h1
      v-else
      class="container-cstm-fluid text-24 py-64"
    >
      Please select page sections in Sanity
    </h1>
  </main>
</template>

<script>
import { defineComponent, useRoute } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { isEmpty } from 'lodash-es';
import useSeo from '@/composables/useSeo';
import { deepMerge } from '~/utils/helpers';
import { serializers } from '~/utils/sanitySerializers';

export default defineComponent({
  setup() {
    const route = useRoute();
    const { aggregateRating } = useSeo();

    return {
      route,
      aggregateRating,
      serializers
    };
  },
  async asyncData({ route, $sanity, store, $logException, redirect }) {
    let pageData;

    try {
      const query = groq`*[_type=="pageCampaign" && pageUrl == "${route.params.campaign}"][0]{...,"langUrls": *[_type=='pageCampaign' && name == ^.name]{ pageUrl, __i18n_lang }}`;
      pageData = await $sanity.fetch(query);

      if (isEmpty(pageData)) {
        redirect('/404');
      }

      // addNewLanguage
      store.dispatch('i18n/setRouteParams', {
        en: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl },
        de: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl },
        fr: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl },
        nl: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'nl')?.pageUrl },
        es: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl },
        pl: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl },
        it: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl },
        sv: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl },
        da: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl },
        no: { campaign: pageData.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl }
      });
    } catch (error) {
      $logException;

      return null;
    }

    return {
      pageData
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: false });
    return deepMerge(i18nHead, {
      title: this.pageData.seoTitle,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.pageData.seoDescription
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.pageData.seoTitle
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.pageData.seoDescription
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.$urlFor(this.pageData.seoImage).url()
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: this.$t('url.base') + this.$t('url.lp.campaigns')
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'en'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'en-GB'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl}/`,
          hreflang: 'fr'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'fr')?.pageUrl}/`,
          hreflang: 'fr-FR'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl}/`,
          hreflang: 'de'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'de')?.pageUrl}/`,
          hreflang: 'de-DE'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl}/`,
          hreflang: 'es'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'es')?.pageUrl}/`,
          hreflang: 'es-ES'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl}/`,
          hreflang: 'pl'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'pl')?.pageUrl}/`,
          hreflang: 'pl-PL'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl}/`,
          hreflang: 'it'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'it')?.pageUrl}/`,
          hreflang: 'it-IT'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl}/`,
          hreflang: 'sv'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'sv')?.pageUrl}/`,
          hreflang: 'sv-SE'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl}/`,
          hreflang: 'da'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'da')?.pageUrl}/`,
          hreflang: 'da-DK'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl}/`,
          hreflang: 'no'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'no')?.pageUrl}/`,
          hreflang: 'no-NO'
        },
        {
          rel: 'alternate',
          href: `https://tylko.com${this.pageData.langUrls.find(el => el.__i18n_lang === 'en')?.pageUrl}/`,
          hreflang: 'x-default'
        }
      ]
    });
  },
  jsonld() {
    return {
      '@context': 'https://schema.org',
      '@graph': [
        {
          '@type': 'BreadcrumbList',
          itemListElement: [
            {
              '@type': 'ListItem',
              item: this.$t('url.base') + this.$t('url.lp.campaigns'),
              name: this.$t('common.campaigns'),
              position: 1
            }
          ]
        },
        {
          '@type': 'Product',
          name: this.pageData.seoTitle,
          description: this.pageData.seoDescription,
          brand: {
            '@type': 'Brand',
            name: 'Tylko'
          },
          aggregateRating: this.aggregateRating
        }
      ]
    };
  }
});
</script>
