<template>
  <main>
    <ContentHeroHeader
      class="hidden lg:block"
      v-bind="{
        headingCopy: $t('tylko-for-business-terms.header.title'),
        imagePath: 'terms/hero',
        imageAlt: $t('tylko-for-business-terms.header.title'),
        link: $localeDjangoPath('tylko-for-business-terms')
      }"
    />
    <ContentDefaultLayout>
      <template #navigation>
        <ContentSideNavigation
          v-bind="{
            pages,
            slug: $route.params.slug
          }"
        />
      </template>
      <template #content>
        <NuxtChild />
      </template>
    </ContentDefaultLayout>
  </main>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import { deepMerge } from '@/utils/helpers';
import { alternateFaqLang } from '~/utils/regions';

export default defineComponent({
  async asyncData({ $content, error, i18n }) {
    const lang = alternateFaqLang(i18n.locale);
    const pages = await $content('tylko-for-business-terms', lang)
      .only(['title', 'slug', 'orderId'])
      .sortBy('orderId', 'asc')
      .fetch()
      .catch((_err) => {
        error({ statusCode: 404, message: 'Page not found' });
      });

    return {
      pages
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('tylko-for-business-terms.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('tylko-for-business-terms.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('tylko-for-business-terms.meta.title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('tylko-for-business-terms.meta.description')
        }
      ]
    });
  }
});
</script>
