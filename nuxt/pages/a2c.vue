<template>
  <div>
    <h1 class="w-full bold-24 text-center mt-64">
      a2c
    </h1>
    <div class="min-h-[300px] flex items-center justify-center">
      <button
        class="btn-cta mr-32"
        v-on:click="trigger('jetty', 't01')"
      >
        shelf t01
      </button>
      <button
        class="btn-cta mr-32"
        v-on:click="trigger('jetty', 't02')"
      >
        shelf t02
      </button>
      <button
        class="btn-cta mr-32"
        v-on:click="trigger('watty', 't03')"
      >
        wardrobe t03
      </button>
      <button
        class="btn-cta"
        v-on:click="trigger('watty', 't13')"
      >
        wardrobe t13
      </button>
    </div>

    <h1 class="w-full bold-24 text-center mt-64">
      s4l
    </h1>
    <div class="min-h-[300px] flex items-center justify-center">
      <button
        class="btn-cta mr-32"
        v-on:click="triggerS4L('jetty', 't01')"
      >
        shelf t01
      </button>
      <button
        class="btn-cta mr-32"
        v-on:click="triggerS4L('jetty', 't02')"
      >
        shelf t02
      </button>
      <button
        class="btn-cta mr-32"
        v-on:click="triggerS4L('watty', 't03')"
      >
        wardrobe t03
      </button>
      <button
        class="btn-cta"
        v-on:click="triggerS4L('watty', 't13')"
      >
        wardrobe t13
      </button>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed, useContext, useStore } from '@nuxtjs/composition-api';
import useHeader from '~/composables/useHeader';
import useCartStatus from '~/composables/useCartStatus';
import t01 from '~/assets/furniture_mocks/t01.json';
import t02 from '~/assets/furniture_mocks/t02.json';
import t03 from '~/assets/furniture_mocks/t03.json';
import t13 from '~/assets/furniture_mocks/t13.json';
import { deepMerge } from '~/utils/helpers';

export default defineComponent({
  name: 'A2c',

  setup() {
    const { $axios } = useContext();

    const furniture = { t01, t02, t03, t13 };
    const url = model => `/api/v1/gallery/${model}/add_to_cart/`;
    const urlS4l = model => `/api/v1/gallery/${model}/add_to_wishlist/?mm=true`;
    const store = useStore();
    const isT03Available = computed(() => store.getters['global/T03_AVAILABLE']);
    const { setHeaderDefaultState } = useHeader();
    const { changeCartStatus } = useCartStatus(isT03Available);

    const trigger = async(model, type) => {
      const payload = furniture[type];
      const { cart_id: cartId } = await $axios.$post(url(model), payload);
      await store.dispatch('global/UPDATE_CART_ID', cartId);
      changeCartStatus();
      setHeaderDefaultState();
      await store.dispatch('global/UPDATE_CART_ITEMS_COUNT_BY_ONE');
    };

    const triggerS4L = async(model, type) => {
      const payload = furniture[type];
      await $axios.$post(urlS4l(model), payload);
      setHeaderDefaultState();
      await store.dispatch('global/UPDATE_LIBRARY_ITEMS_COUNT_BY_ONE');
    };

    return {
      trigger,
      triggerS4L
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });
    return deepMerge(i18nHead, {
      title: this.$t('common.category.all_meta_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('hp.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('common.category.all_meta_title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('hp.meta.description')
        },
        {
          hid: 'robots',
          name: 'robots',
          content: 'noindex'
        }
      ]
    });
  }
});
</script>
