<template>
  <div class="-mx-16 md:mx-0">
    <BasePicture
      img-classes="w-full"
      picture-classes="w-full"
      type="M T SD LD"
      v-bind="{
        path: path,
        alt: alt
      }"
    />
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    path: {
      type: String,
      default: ''
    },
    alt: {
      type: String,
      default: ''
    }
  }
});
</script>
