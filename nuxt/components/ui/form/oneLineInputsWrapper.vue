<template>
  <div
    class="flex w-full"
    v-bind:class="{ 'flex-col': !isDesktopViewport }"
  >
    <slot
      name="leftInput"
      inputClass="input-oneline-left"
    />
    <slot
      name="rightInput"
      inputClass="input-oneline-right"
    />
  </div>
</template>
<script>
import { defineComponent } from '@nuxtjs/composition-api';
import useMq from '~/composables/useMq';

export default defineComponent({
  setup() {
    const { isDesktopViewport } = useMq('md');
    return { isDesktopViewport };
  }
});
</script>
