<template>
  <div class="bg-offwhite-800 rounded-6 p-16 md:py-96 md-max:min-h-[calc(100vh-188px)] flex flex-col justify-center">
    <div class="h-full flex items-center justify-center flex-col text-center flex-1">
      <h1
        v-if="!isNil(cartItems) && cartItems.length === 0"
        class="inline-block bold-24 text-offblack-800 mb-48"
        v-html="$t('cart.your_basket_is_empty')"
      />
      <ShelfImg class="mx-auto" />
      <p
        class="normal-16 text-grey-900 mt-32 px-40"
        v-html="$t('cart.empty_basket_caption')"
      />
      <BaseLink
        v-if="!isNil(cartItems) && cartItems.length === 0"
        variant="accent"
        class="block md-max:w-full mt-48 text-center"
        v-bind="{
          trackData: {},
          href: gridPath,
        }"
      >
        {{ $t('cart.start_with_presets') }}
      </BaseLink>
    </div>
  </div>
</template>

<script>
import { defineComponent, useContext, useStore, computed } from '@nuxtjs/composition-api';
import { isNil } from 'lodash-es';
import ShelfImg from '~/assets/img/shelf.svg?inline';
import { cart } from '~/utils/consts';

export default defineComponent({
  components: {
    ShelfImg
  },
  props: {
    cartItems: {
      type: [Array, null]
    }
  },
  setup() {
    const { getters } = useStore();
    const isWebView = computed(() => getters['global/IS_WEBVIEW']);
    const { $localeDjangoPath } = useContext();
    return {
      gridPath: isWebView.value ? 'tylkoapp://grid' : $localeDjangoPath(cart.ctaUrl),
      isNil
    };
  }
});
</script>
