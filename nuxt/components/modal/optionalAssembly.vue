<template>
  <client-only>
    <BaseModal v-bind="{ name: 'optionalAssembly','content-class': 'max-w-[560px]' }">
      <div class="text-left py-64 px-16 lg:px-64">
        <h3 class="normal-24 xl:normal-28 text-offblack-800 mb-8" v-html="$t('usps.modal_optional.title')" />
        <p class="normal-14 text-offblack-600 mb-32" v-html="$t('usps.modal_optional.subtitle')" />
        <p class="normal-16 text-offblack-600 mb-12" v-html="$t('usps.modal_optional.body')" />
        <ul class="mb-32">
          <li
            v-for="(item, index) in [$t('usps.modal_optional.list1'), $t('usps.modal_optional.list2'), $t('usps.modal_optional.list3')]"
            v-bind:key="index"
            class="grid grid-cols-[minmax(min-content,max-content),1fr] gap-8 items-center mb-8"
          >
            <IconCheck class="text-[#3CC85A] h-[14px] w-[14px]" />
            <p class="normal-16 text-offblack-600 w-full" v-html="item" />
          </li>
        </ul>
        <p class="normal-14 text-offblack-600 mb-12" v-html="$t('usps.modal_optional.availability')" />
        <p class="normal-14 text-offblack-600">
          {{ $t('usps.modal_optional.footer') }}
          <BaseLink
            variant="link-color"
            arrow="right"
            class="mt-8 normal-14 inline-flex items-center"
            v-bind="{
              trackData: { eventLabel: 'open-optional-assembly-modal' },
              href: $localeDjangoPath('faq-links.assembly'),
            }"
          >
            {{ $t('usps.modal_optional.faq') }}
          </BaseLink>
        </p>
      </div>
    </BaseModal>
  </client-only>
</template>
