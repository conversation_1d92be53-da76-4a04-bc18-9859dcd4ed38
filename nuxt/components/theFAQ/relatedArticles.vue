<template>
  <div
    v-if="articles && articles.length"
    class="lg:hidden before:h-1 before:bg-grey-700 before:my-16 before:block after:h-[1px] after:block after:bg-grey-700 after:mt-16 last:after:hidden"
  >
    <h3
      class="bold-20 text-offbblack-700"
      v-html="$t('faq.other_topics')"
    />
    <ul class="text-offblack-600">
      <li
        v-for="(item, index) in articles"
        v-bind:key="index"
      >
        <NuxtLink
          v-bind:to="linkVariant === 'article' ? FAQPages.getArticleRoute($route.params.category, item.slug) : FAQPages.getTagRoute($route.params.tags, item.slug)"
          class="flex justify-between items-center mb-24 first:mt-16"
        >
          <h4
            class="normal-16"
            v-html="item.title"
          />
          <IconArrow class="rotate-180 w-8 h-[14px] ml-4 lg:hidden" />
        </NuxtLink>
      </li>
    </ul>
  </div>
</template>

<script>

import { defineComponent } from '@nuxtjs/composition-api';
import { FAQPages } from '@/utils/pages';

export default defineComponent({
  props: {
    linkVariant: {
      type: String,
      required: true,
      validation: variant => ['article', 'tags'].includes(variant)
    },
    articles: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    return {
      FAQPages
    };
  }
});
</script>
