<template>
  <div class="flex items-center">
    <div
      class="rating"
      v-bind:style="`--value: ${rating}; --max: 5; --size: ${size}px`"
    />
    <span
      class="normal-16 lg:normal-20 ml-4 text-offblack-600"
      v-bind:class="hideLabel && 'hidden'"
      v-html="rating"
    />
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    rating: {
      type: Number,
      required: true,
      validator: rating => rating >= 0 && rating <= 5
    },
    size: {
      type: Number,
      default: 24
    },
    hideLabel: {
      type: Boolean,
      default: false
    }
  }
});
</script>

<style scoped lang="scss">
.rating {
  --v1: transparent, #000 0.5deg, 108deg, #0000 109deg;
  --v2: transparent, #000 0.5deg, 36deg, #0000 37deg;

  background: linear-gradient(#ffc107 0 0) 0 / calc(var(--value, 0) * var(--size)) 100% no-repeat #ccc;
  height: calc(var(--size) * 0.9);
  mask:
    conic-gradient(from 54deg  at calc(var(--size) * 0.68) calc(var(--size) * 0.57), var(--v1)),
    conic-gradient(from 90deg  at calc(var(--size) * 0.02) calc(var(--size) * 0.35), var(--v2)),
    conic-gradient(from 126deg at calc(var(--size) * 0.5)  calc(var(--size) * 0.7), var(--v1)),
    conic-gradient(from 162deg at calc(var(--size) * 0.5)  0, var(--v2));
  mask-composite: xor, destination-over;
  mask-composite: exclude, add;
  mask-size: var(--size) var(--size);
  width: calc(var(--max, 5) * var(--size));
}
</style>
