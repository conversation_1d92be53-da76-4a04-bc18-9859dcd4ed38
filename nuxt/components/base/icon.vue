<script>
const icons = {
  close: () => import('~/assets/icons/ds2023/close.svg?inline'),
  check: () => import('~/assets/icons/ds2023/check.svg?inline'),
  info: () => import('~/assets/icons/ds2023/info.svg?inline'),
  'calendar-time': () => import('~/assets/icons/ds2023/calendar-time.svg?inline'),
  'caret-left': () => import('~/assets/icons/ds2023/caret-left.svg?inline'),
  'caret-right': () => import('~/assets/icons/ds2023/caret-right.svg?inline'),
  'dna-pattern': () => import('~/assets/icons/ds2023/dna-pattern.svg?inline'),
  leaf: () => import('~/assets/icons/ds2023/leaf.svg?inline'),
  material: () => import('~/assets/icons/ds2023/material.svg?inline'),
  plus: () => import('~/assets/icons/ds2023/plus.svg?inline'),
  premium: () => import('~/assets/icons/ds2023/premium.svg?inline'),
  puzzle: () => import('~/assets/icons/ds2023/puzzle.svg?inline'),
  scratch: () => import('~/assets/icons/ds2023/scratch.svg?inline'),
  van: () => import('~/assets/icons/ds2023/van.svg?inline'),
  installmentsIcon: () => import('~/assets/icons/pdp/installments.svg?inline'),
  taxesIcon: () => import('~/assets/icons/pdp/taxes.svg?inline'),
  feesIcon: () => import('~/assets/icons/pdp/fees.svg?inline'),
  alert: () => import('~/assets/icons/ds2023/alert.svg?inline'),
  copy: () => import('~/assets/icons/copy.svg?inline'),
  stars: () => import('~/assets/icons/stars.svg?inline'),
  star: () => import('~/assets/icons/ds2023/star.svg?inline'),
  heart: () => import('~/assets/icons/ds2023/heart.svg?inline'),
  cart: () => import('~/assets/icons/ds2023/trolley.svg?inline')
};

export default {
  functional: true,
  props: {
    name: {
      type: String,
      required: true
    }
  },

  render(createElement, context) {
    return createElement(icons[context.props.name], context.data);
  }
};
</script>
