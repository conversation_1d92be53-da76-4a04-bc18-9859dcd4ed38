<template>
  <aside class="ty-avatar flex items-center">
    <BasePicture
      img-classes="rounded-full mr-16 w-[44px] h-[44px]"
      v-bind="{
        path: imagePath,
        type: 'A',
        alt: title
      }"
    />
    <div>
      <p
        class="ty-avatar__title text-neutral-900 opacity-90"
        v-html="title"
      />
      <p
        class="text-neutral-750 normal-12"
        v-html="subtitle"
      />
    </div>
  </aside>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      required: true
    },
    imagePath: {
      type: String,
      required: true
    }
  }
});
</script>
