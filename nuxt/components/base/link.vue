<template>
  <a
    ref="el"
    v-bind:class="{
      'custom': variant === 'custom',
      'ty-btn-accent': variant === 'accent',
      'ty-btn-outlined': variant === 'outlined',
      'ty-btn-outlined ty-btn-outlined--dark': variant === 'outlined-dark',
      'ty-btn-filled': variant === 'filled',
      'ty-btn-filled ty-btn-filled--dark': variant === 'filled-dark',
      'link': variant === 'link',
      'link link--color': variant === 'link-color',
      'btn-link': variant === 'link-secondary',
      'btn-link btn-link--small': variant === 'link-secondary-small',
      [`link--arrow-${arrow}`]: (variant === 'link' || variant === 'link-color') && arrow,
      [`btn-link--arrow-${arrow}`]: variant === 'link-secondary' && arrow,
      'ty-link-s': variant === 'underlined',
      'ty-link-disabled': disabled
    }"
    v-bind="{ ...$attrs }"
    v-on="{
      ...$listeners,
      click: (event) => {
        preventDefault && event.preventDefault();
        trackInteraction(trackData);
        $listeners.click && $listeners.click();
      }
    }"
  >
    <slot name="icon" />
    <template v-if="variant === 'underlined'">
      <span>
        <slot name="default" />
      </span>
    </template>
    <template v-else>
      <slot name="default" />
    </template>
  </a>
</template>

<script>
import { defineComponent, ref } from '@nuxtjs/composition-api';
import { isNil } from 'lodash-es';
import { useTrackInteraction } from '~/composables/useTracking';

export default defineComponent({
  props: {
    variant: {
      type: String,
      required: true,
      validation: variant => ['custom', 'accent', 'filled', 'filled-dark', 'outlined', 'outlined-dark', 'link', 'link-color', 'link-secondary', 'link-secondary-small', 'underlined'].includes(variant)
    },
    arrow: {
      type: String,
      default: '',
      validation: (arrow) => {
        if (!arrow) {
          return true;
        }

        return ['up', 'right', 'down'].includes(arrow);
      }
    },
    trackData: {
      type: Object,
      required: true,
      validator: data => (!isNil(data.eventLabel) || !Object.keys(data).length)
    },
    preventDefault: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const el = ref();
    const trackInteraction = useTrackInteraction(el);
    return {
      el,
      trackInteraction
    };
  }
});
</script>
