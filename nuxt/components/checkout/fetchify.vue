<template>
  <FormulateInput
    v-show="isC2ALoaded"
    ref="searchInput"
    class="select-arrow"
    type="text"
    name="search-gbp"
    v-bind="{
      placeholder: $t('checkout.c2a.default_placeholder'),
      label: $t('checkout.c2a.label'),
    }"
  />
</template>

<script>
import { defineComponent, onMounted, ref, useContext, onBeforeUnmount } from '@nuxtjs/composition-api';
import { throttle } from 'lodash-es';

const accessToken = '66392-e31d4-97331-0270d';

export default defineComponent({
  setup(_, { emit }) {
    const { i18n } = useContext();
    const searchInput = ref(null);
    const isC2ALoaded = ref(false);
    let onResizeThrottled;
    let setInputWidthToCssVariable;

    const initClickToAddress = () => {
      // eslint-disable-next-line new-cap
      window.clickToAddress && new window.clickToAddress({
        accessToken,
        dom: {
          search: 'search-gbp'
        },
        countrySelector: false,
        getIpLocation: false,
        domMode: 'name',
        texts: {
          default_placeholder: i18n.t('checkout.c2a.default_placeholder'),
          country_placeholder: i18n.t('checkout.c2a.country_placeholder'),
          country_button: i18n.t('checkout.c2a.country_button'),
          generic_error: i18n.t('checkout.c2a.generic_error'),
          no_results: i18n.t('checkout.c2a.no_results'),
          more: i18n.t('checkout.c2a.more')
        },
        onSearchFocus: () => {
          setInputWidthToCssVariable();
        },
        onResultSelected: (...args) => {
          clickToAddressSelected(...args);
        }
      });
    };

    const clickToAddressSelected = (c2a, elements, address) => {
      const payload = {
        postalCode: address.postal_code,
        city: address.locality
      };

      if (address.company_name) {
        payload.streetAddress1 = `${address.company_name}, ${address.line_1}`;
      } else {
        payload.streetAddress1 = address.line_1;
      }

      if (address.line_2) {
        payload.streetAddress2 = address.line_2;
      } else {
        payload.streetAddress2 = null;
      }

      emit('clickToAddressSelected', payload);
    };

    onMounted(() => {
      const c2aScript = document.createElement('script');

      setInputWidthToCssVariable = () => {
        document.documentElement.style.setProperty(
          '--c2a-input-width',
          `${searchInput.value.$el.offsetWidth}px`
        );
      };

      c2aScript.setAttribute(
        'src',
        'https://cc-cdn.com/generic/scripts/v1/cc_c2a.min.js'
      );
      document.body.appendChild(c2aScript);

      c2aScript.onload = () => {
        isC2ALoaded.value = true;
        initClickToAddress();
      };

      onResizeThrottled = throttle(() => {
        setInputWidthToCssVariable();
      }, 200);

      if (process.browser) {
        window.addEventListener('resize', onResizeThrottled);
      }
    });
    onBeforeUnmount(() => {
      window.removeEventListener('resize', onResizeThrottled);
    });
    return { isC2ALoaded, searchInput };
  }
});
</script>

<style lang="scss">

#cc_c2a {
    /* stylelint-disable */
    @apply border-grey-900 #{!important};

    margin-left: -4px;
    margin-top: -2px;
    width: var(--c2a-input-width) !important;

    .cc-filter,
    &.c2a_light .cc-history .cc-forward {
        background-image: url('~@/assets/icons/chevron_right.svg') !important;
        background-size: auto;

        &:hover {
            background-image: url('~@/assets/icons/chevron_right.svg') !important;
        }
    }

    &.c2a_light .cc-history .cc-back {
        background-image: url('~@/assets/icons/chevron_left.svg') !important;
        background-size: auto;

        &:hover {
            background-image: url('~@/assets/icons/chevron_left.svg') !important;
        }
    }

    &.c2a_accent_default .mainbar .cc-history > div:hover,
    &.c2a_accent_default .c2a_footer .progressBar {
        @apply bg-grey-600 #{!important};
    }

    ul.c2a_results {
        scrollbar-color: #cad0d0 transparent;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            @apply bg-grey-800;

            border-radius: 2px;
        }

        li {
            font-size: 16px;
            span:first-child {
                @apply text-black;
            }

            span.light {
                @apply text-grey-800;

                font-style: normal;
                margin-left: 5px;
            }

            &:hover {
                @apply bg-grey-600 #{!important};

                span.light {
                    @apply text-grey-800 #{!important};
                }
            }

            & > div {
                float: none;
                overflow: visible;
                text-overflow: unset;
                white-space: unset;
            }
        }

    }
}

</style>
