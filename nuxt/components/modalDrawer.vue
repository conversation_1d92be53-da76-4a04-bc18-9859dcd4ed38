<template>
  <BaseOverlay
    v-bind="{
      value,
      overlayClasses: 'bg-black/[0.4] z-4 overflow-y-auto overflow-x-hidden'
    }"
  >
    <div
      class="flex min-h-full flex-col justify-end"
      v-on:click.self="isDrawerOpened = false"
    >
      <Transition
        name="modal-drawer__transition-slide"
        appear
      >
        <aside
          v-if="isDrawerOpened"
          class="
            bg-white w-full pt-16 pb-48 px-16 md-max:mt-48
              md-max:rounded-t-24 md-max:min-h-[160px]
              md:w-[600px] md:absolute md:right-0 md:top-0 md:p-48 md:min-h-full
             "
        >
          <BaseButton
            class="block z-2 ml-auto sticky top-16 right-16 md:fixed md:top-24 md:right-24"
            v-bind="{
              variant: 'close',
              trackData: {}
            }"
            v-on:click="isDrawerOpened = false"
          >
            <BaseIcon name="close" />
          </BaseButton>

          <div class="md-max:mt-8">
            <h2
              class="semibold-24 md:semibold-28 xl:semibold-32 md:mr-32"
              v-html="heading"
            />

            <div v-bind:class="containerClasses">
              <slot />
            </div>
          </div>
        </aside>
      </Transition>
    </div>
  </BaseOverlay>
</template>

<script>
import { defineComponent, watch, ref, useContext } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    heading: {
      type: String,
      required: true
    },
    headingClasses: {
      type: String,
      default: 'bold-32'
    },
    containerClasses: {
      type: String,
      default: 'mt-32 first:mt-0'
    }
  },
  setup(props, { emit }) {
    const isDrawerOpened = ref(false);
    const { $dixa } = useContext();

    watch(() => props.value, () => {
      if (props.value) {
        isDrawerOpened.value = true;
      }
    });

    watch(isDrawerOpened, () => {
      if (isDrawerOpened.value) {
        $dixa.setWidgetVisibility(false);
      } else {
        emit('update:value', false);
        $dixa.setWidgetVisibility(true);
      }
    }, { flush: 'post' });

    return {
      isDrawerOpened
    };
  }
});
</script>

<style lang="scss">
.modal-drawer {
  &__transition-slide {
    &-enter,
    &-leave-to {
      @apply md-max:translate-y-full md:translate-x-full;
    }

    &-enter-active {
      @apply transition-transform ease-[cubic-bezier(0,0,0.2,1)] duration-[400ms];
    }

    &-leave-active {
      @apply transition-transform ease-[cubic-bezier(0.4,0,1,1)] duration-200;
    }
  }
}
</style>
