<template>
  <span
    class="input-base__error-message"
    v-bind="{ class: errorStyles }"
  >
    {{ error }}
  </span>
</template>

<script>
import { defineComponent, computed } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    errors: {
      type: Array,
      required: true
    },
    externalError: {
      type: String,
      default: ''
    },
    errorStyles: {
      type: Array,
      default: () => ([])
    }
  },
  setup(props) {
    const error = computed(() => {
      if (props.externalError.length) { return props.externalError; }
      return props.errors[0];
    });
    return {
      error
    };
  }
});
</script>
