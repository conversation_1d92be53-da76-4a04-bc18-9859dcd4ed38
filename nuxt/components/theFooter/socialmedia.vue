<template>
  <div class="border-b border-solid border-grey-900 pb-16 lg:pb-0 lg:border-b-0">
    <p
      class="hidden lg:block normal-16 text-grey-800 mb-32"
      v-html="$t('footer.follow_us')"
    />
    <ul class="flex justify-between px-16 lg:px-0">
      <li>
        <BaseLink
          variant="custom"
          class="block"
          v-bind="{ trackData: {}, href: $t('url.facebook') }"
          target="_blank"
          data-testid="footer-facebook"
        >
          <img
            loading="lazy"
            width="24"
            height="24"
            src="~/assets/icons/social/facebook.svg"
            class="transition-opacity short-transition opacity-100 hover:opacity-80"
          >
        </BaseLink>
      </li>
      <li>
        <BaseLink
          variant="custom"
          class="block"
          v-bind="{ trackData: {}, href: $t('url.instagram') }"
          target="_blank"
          data-testid="footer-instagram"
        >
          <img
            loading="lazy"
            width="24"
            height="24"
            src="~/assets/icons/social/instagram.svg"
            class="transition-opacity short-transition opacity-100 hover:opacity-80"
          >
        </BaseLink>
      </li>
      <li>
        <BaseLink
          variant="custom"
          class="block"
          v-bind="{ trackData: {}, href: $t('url.pinterest') }"
          target="_blank"
          data-testid="footer-pinterest"
        >
          <img
            loading="lazy"
            width="24"
            height="24"
            src="~/assets/icons/social/pinterest.svg"
            class="transition-opacity short-transition opacity-100 hover:opacity-80"
          >
        </BaseLink>
      </li>
      <li>
        <BaseLink
          variant="custom"
          class="block"
          v-bind="{ trackData: {}, href: $t('url.twitter') }"
          target="_blank"
          data-testid="footer-twitter"
        >
          <img
            loading="lazy"
            width="24"
            height="20"
            src="~/assets/icons/social/twitter.svg"
            class="transition-opacity short-transition opacity-100 hover:opacity-80"
          >
        </BaseLink>
      </li>
    </ul>
  </div>
</template>
