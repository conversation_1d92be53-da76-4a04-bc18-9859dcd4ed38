<template>
  <div class="text-center">
    <p
      class="mb-16 normal-20 text-offwhite-800"
      v-html="$t('footer.mobile_header3')"
    />

    <div class="flex justify-center mb-16">
      <BaseLink
        variant="outlined-dark"
        v-bind="{
          href: `https://api.whatsapp.com/send?phone=${$t('whatsapp.modal.whatsapp_number')}`,
          trackData: {}
        }"
        class="w-1/2 px-32"
        data-testid="footer-phone"
      >
        WhatsApp
      </BaseLink>
    </div>

    <p
      class="normal-14 text-offwhite-800"
      v-html="$t('const.other.contact_hours1')"
    />
    <p
      class="normal-14 text-offwhite-800"
      v-html="$t('const.other.contact_hours2')"
    />
    <BaseLink
      class="inline-block mt-16 normal-14 text-offwhite-800"
      variant="custom"
      v-bind="{
        href: $localeDjangoPath('contact'),
        trackData: { eventLabel: 'contact-footer-link' },
        'data-testid': 'footer-contact'
      }"
    >
      {{ $t('footer.contact_form') }}
    </BaseLink>
  </div>
</template>
