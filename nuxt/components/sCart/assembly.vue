<template>
  <div v-if="isAssemblyServiceAvailable" class="border-b border-grey-900 border-solid">
    <transition name="slide-fade" mode="out-in">
      <div v-if="!cart.cartUsedAssembly" key="add" class="flex py-16">
        <BaseButton
          variant="custom"
          class="flex items-center group"
          data-testid="scart-assembly-add-button"
          v-bind="{
            trackData: {},
            class: [
              { 'pointer-events-none': !isAssemblyServiceAvailable },
              isAssemblyServiceAvailable ? 'text-black' : 'text-grey-800'
            ]
          }"
          v-on="{ click: showAddAssemblyModal }"
        >
          <IconPlus
            class="mr-8 transform group-hover:rotate-45 group-focus:rotate-45 transition-rotate basic-transition"
          />
          <span v-if="isT13FeatureFlagActive" class="normal-16 " v-html="$t('scart.assembly_service.button.add')" />
          <span v-else class="normal-16 " v-html="$t('scart.add_shelf_assembly_service')" />
        </BaseButton>

        <VPopover class="ml-8 flex justify-center items-center">
          <IconTooltip class="text-black" />

          <div slot="popover" class="max-w-[300px]">
            <p v-html="$t('scart.shelf_assembly_service_tooltip')" />
            <p v-if="cart.hasT03" class="mt-16" v-html="$t('scart.wardrobe_assembly_service_tooltip')" />
          </div>
        </VPopover>
      </div>

      <div v-else key="remove" class="flex flex-col py-16">
        <div class="flex items-center">
          <BaseButton
            variant="custom"
            class="box-content mr-8"
            data-testid="scart-assembly-remove-button"
            v-bind="{ trackData: {} }"
            v-on="{ click: removeAssemblyService }"
          >
            <IconPlus class="text-black transform rotate-45" />
          </BaseButton>

          <span v-if="isT13FeatureFlagActive" class="normal-16 text-black" v-html="$t('scart.assembly_service.button.remove')" />
          <span v-else class="normal-16 text-black" v-html="$t('scart.shelf_assembly_service')" />

          <VPopover class="ml-8 flex justify-center items-center">
            <IconTooltip class="text-black" />

            <div slot="popover" class="max-w-[300px]">
              <p v-html="$t('scart.shelf_assembly_service_tooltip')" />
              <p v-if="cart.hasT03" class="mt-16" v-html="$t('scart.wardrobe_assembly_service_tooltip')" />
            </div>
          </VPopover>

          <span 
            data-testid="scart-assembly-price" 
            class="block bold-16 text-black ml-auto"
          >
            {{ format(cart.orderPricing.assembly_price) }}
          </span>
        </div>

        <div
          v-if="isT13FeatureFlagActive"
          class="mt-2 self-end normal-10 text-offblack-600"
          v-html="itemForAssemblyCopy"
        />
      </div>
    </transition>
  </div>
</template>

<script>
import { computed, defineComponent, useContext, useStore } from '@nuxtjs/composition-api';
import useModal from '~/composables/useModal';
import usePriceFormat from '~/composables/usePriceFormat';

export default defineComponent({
  setup() {
    const store = useStore();
    const modal = useModal();
    const { format } = usePriceFormat();

    const cart = computed(() => store.getters['cart/ALL']);
    const isAssemblyServiceAvailable = computed(() => store.getters['cart/ALL'].hasAssemblyPossible && !store.getters['cart/ALL'].cartUsedFastTrack);

    const onSuccess = () => {
      store.dispatch('cart/ASSEMBLY_ADD');
      modal.hide('addAssembly');
    };

    const isT13FeatureFlagActive = computed(() => store.getters['global/FEATURE_FLAG_VALUE']('t13'));
    const itemForAssemblyCount = computed(() => store.getters['cart/ITEM_FOR_ASSEMBLY_COUNT']);

    const showAddAssemblyModal = () => {
      modal.show('addAssembly', {
        onSuccess,
        assemblyItemsCount: cart.value.shelfItemsCount,
        ...(isT13FeatureFlagActive.value && {
          assemblyItemsCountText: itemForAssemblyCopy.value
        }),
        assemblyPrice: cart.value.orderPricing.assembly_price,
        hasT03: cart.value.hasT03
      });
    };

    const removeAssemblyService = async() => {
      await store.dispatch('cart/ASSEMBLY_REMOVE');
    };

    const { i18n } = useContext();

    const itemForAssemblyCopy = computed(() => {
      const itemsCopy = itemForAssemblyCount.value === 1
        ? i18n.t('scart.assembly_service.item_count.part2.singular')
        : i18n.t('scart.assembly_service.item_count.part2.plural');

      return `${i18n.t('scart.assembly_service.item_count.part1')} ${itemForAssemblyCount.value} ${itemsCopy}`;
    });

    return {
      cart,
      isAssemblyServiceAvailable,
      showAddAssemblyModal,
      removeAssemblyService,
      format,
      itemForAssemblyCount,
      isT13FeatureFlagActive,
      itemForAssemblyCopy
    };
  }
});
</script>
