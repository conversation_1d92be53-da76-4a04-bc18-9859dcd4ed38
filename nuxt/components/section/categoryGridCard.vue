<template>
  <BaseLink
    variant="custom"
    v-bind="{
      href: `${$localeDjangoPath('plp')}${$t(item.urlPathKey)}`,
      trackData: { eventLabel: item.name }
    }"
    class="block mb-32 group md:mb-40 lg:mb-48 xl:mb-64"
  >
    <div class="relative overflow-hidden rounded-12">
      <BasePicture
        picture-classes="w-full h-full"
        type="M T SD LD"
        v-bind="{
          'img-classes': `w-full h-full transform basic-transition group-hover:scale-[1.03] ${item.labelKey ? 'rounded-tl-[40px]' : ''}`,
          path: `homepage/categoryGrid/${item.name === 'wardrobe' && isWardrobes23FeatureFlagActive ? `wardrobes23-${item.name}` : item.name }`,
          alt: $t(item.pluralNameKey)
        }"
      />
      <span
        v-if="item.labelKey"
        class="absolute px-16 py-8 bg-white -left-1 -top-1 bold-12 md:bold-14 text-orange rounded-br-12"
        v-html="$t(item.labelKey)"
      />
    </div>
    <h2
      class="mt-12 text-center md:mt-16 lg:mt-24 xl:mt-32 transition-color basic-transition text-offblack-700 lg:bold-24 md:bold-20 bold-16 group-hover:text-orange-900"
      v-html="$t(item.pluralNameKey)"
    />
  </BaseLink>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import useFeatureFlag from '~/composables/useFeatureFlag';

export default defineComponent({
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup() {
    const isWardrobes23FeatureFlagActive = useFeatureFlag('wardrobes23');

    return {
      isWardrobes23FeatureFlagActive
    };
  }
});
</script>
