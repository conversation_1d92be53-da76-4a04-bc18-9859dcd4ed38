<template>
  <section class="py-64 md:py-128">
    <div class="container-cstm flex justify-center mx-auto">
      <h2
        class="bold-28 lg:bold-46 mb-40 md:mb-64 text-center"
        v-bind:class="isThemeLight ? 'text-offwhite-900' : 'text-offblack-800'"
        v-html="heading"
      />
    </div>
    <BaseCarousel
      v-bind="{
        options: swiperOptions,
        name: `${name}-exploreCarousel`,
        navNextElClasses: items.length <= 3 ? '!hidden' : '',
        navPrevElClasses: items.length <= 3 ? '!hidden' : '',
      }"
      is-navigation
      class="justify-center"
    >
      <BaseCarouselSlide
        v-for="(item, index) in items"
        v-bind:key="index"
        class="block max-w-[250px] rounded-[12px] overflow-hidden"
        v-bind:class="{ 'lg:max-w-[491px]': isLargeItem(index) }"
      >
        <BaseLink
          variant="custom"
          v-bind="{ href: type === 'spaces' ? $localeDjangoPath(item.urlPathKey) : `${$localeDjangoPath('plp')}${$t(item.urlPathKey)}`, trackData: { eventLabel: item.name } }"
          class="group block h-[250px]"
        >
          <BasePicture
            img-classes="block h-full w-full object-cover object-center"
            picture-classes="h-full"
            type="M D"
            v-bind="{
              path: `common/explore-collection/${item.name}/${isLargeItem(index) ? 'wide' : 'square'}${item.name === 'hallway' && veneer2024FeatureFlagActive ? '/new' : ''}`,
              alt: $t(item.pluralNameKey || item.nameKey),
              isRetinaUploaded: false
            }"
          />
          <div class="flex items-start absolute left-16 top-16">
            <h3
              class="bold-20 md:bold-24 text-white"
              v-html="$t(item.pluralNameKey || item.nameKey)"
            />
            <BaseBadge
              v-if="item.labelKey"
              class="ml-4 text-offblack-600"
            >
              {{ $t(item.labelKey) }}
            </BaseBadge>
          </div>
          <span
            class="btn-cta absolute right-16 lg:right-24 bottom-24 group-hover:bg-orange-900"
            v-html="ctaText"
          />
        </BaseLink>
      </BaseCarouselSlide>
    </BaseCarousel>
  </section>
</template>

<script>
import { defineComponent, useStore, computed } from '@nuxtjs/composition-api';
import { GET_CATEGORY_IN_ORDER, GET_SPACE_IN_ORDER } from '~/utils/types';

export default defineComponent({
  props: {
    isThemeLight: {
      type: Boolean,
      default: true
    },
    heading: {
      type: String,
      required: true
    },
    ctaText: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    sectionCategories: {
      type: Array,
      required: true
    },
    largeItems: {
      type: Array,
      default: () => [0, 3, 5, 6]
    },
    name: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const items = props.type === 'spaces' ? GET_SPACE_IN_ORDER(props.sectionCategories) : GET_CATEGORY_IN_ORDER(props.sectionCategories);
    const isLargeItem = index => props.largeItems.includes(index);
    const { getters } = useStore();

    const veneer2024FeatureFlagActive = computed(() => getters['global/FEATURE_FLAG_VALUE']('veneer_2024'));

    const swiperOptions = {
      slidesPerView: 'auto',
      freeMode: true,
      spaceBetween: 16,
      breakpoints: {
        0: {
          slidesOffsetBefore: 16,
          slidesOffsetAfter: 16
        },
        768: {
          slidesOffsetBefore: 32,
          slidesOffsetAfter: 32
        },
        1024: {
          slidesOffsetBefore: 48,
          slidesOffsetAfter: 48
        },
        1440: {
          slidesOffsetBefore: 80,
          slidesOffsetAfter: 80
        },
        1920: {
          slidesOffsetBefore: 128,
          slidesOffsetAfter: 128
        }
      }
    };

    return {
      items,
      swiperOptions,
      veneer2024FeatureFlagActive,
      isLargeItem
    };
  }
});
</script>
