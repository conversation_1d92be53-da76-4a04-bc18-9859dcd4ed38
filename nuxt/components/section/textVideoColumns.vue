<template>
  <section>
    <SectionUiSplit v-bind="{ isReversed }">
      <template #media>
        <ClientOnly>
          <div class="h-full">
            <BaseVideo
              v-bind="{
                ...currentVideo,
                id: 'text-video-columns',
                alt: 'Design Video',
              }"
              class="overflow-hidden"
            />
          </div>
        </ClientOnly>
      </template>
      <template #content>
        <p
          v-if="subtitle"
          class="normal-16-2 mb-16 uppercase"
          v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-800'"
          v-html="subtitle"
        />
        <h2
          v-if="title"
          class="normal-24 lg:normal-28 pb-16 lg:pb-24 lg:max-w-[470px]"
          v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-800'"
          v-html="title"
        />
        <p
          v-if="description"
          class="normal-16 lg:normal-20 lg:max-w-[450px]"
          v-bind:class="isLightTheme ? 'text-offwhite-700' : 'text-offblack-600'"
          v-html="description"
        />
        <div class="flex flex-wrap justify-center md:justify-start">
          <BaseLink
            v-if="ctaUrl && ctaCopy"
            variant="accent"
            v-bind="{
              href: ctaUrl,
              trackData: {
                eventLabel: 'cta',
                eventPath: ctaUrl
              }
            }"
            class="whitespace-nowrap mt-24 mx-8 sm:mx-12 md:ml-0 md:mr-24"
          >
            {{ ctaCopy }}
          </BaseLink>
          <BaseLink
            v-if="ctaAdditionalCopy && ctaAdditionalUrl"
            v-bind="{
              href: ctaAdditionalUrl,
              trackData: {
                eventLabel: 'additional-cta',
                eventPath: ctaAdditionalUrl,
              },
              variant: ctaAdditionalVariant,
            }"
            class="whitespace-nowrap mt-24 mx-8 sm:mx-12 md:ml-0"
            v-bind:class="ctaAdditionalClass"
          >
            {{ ctaAdditionalCopy }}
          </BaseLink>
        </div>
      </template>
    </SectionUiSplit>
  </section>
</template>

<script>
import { computed, defineComponent } from '@nuxtjs/composition-api';
import useMq from '~/composables/useMq';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    ctaCopy: {
      type: String,
      default: ''
    },
    ctaUrl: {
      type: String,
      default: ''
    },
    ctaAdditionalCopy: {
      type: String,
      default: ''
    },
    ctaAdditionalUrl: {
      type: String,
      default: ''
    },
    ctaAdditionalClass: {
      type: String,
      default: ''
    },
    ctaAdditionalVariant: {
      type: String,
      default: 'outlined-dark'
    },
    isLightTheme: {
      type: Boolean,
      default: true
    },
    isReversed: {
      type: Boolean,
      deafult: true
    },
    videoData: {
      type: Object,
      required: true,
      validator: videos => Object.keys(videos).every(breakpoint => ['M', 'T', 'SD', 'LD'].includes(breakpoint))
    }
  },
  setup(props) {
    const { isSm, isMd, isLg, isAboveXlViewport, isRetina } = useMq();
    const hasRetina = computed(() => Object.values(props.videoData).every(video => !!video.retina));
    const brekapoints = computed(() => ({
      M: isSm.value,
      T: isMd.value,
      SD: isLg.value,
      LD: isAboveXlViewport.value
    }));

    const currentBreakpoint = computed(() => Object
      .entries(brekapoints.value)
      .find(([, isActive]) => isActive)[0]);

    const currentVideo = computed(() => {
      const video = props.videoData[currentBreakpoint.value];
      const retinaScreen = hasRetina && isRetina.value;

      return {
        videoId: retinaScreen ? video.id : video.retina.id,
        paddingTop: retinaScreen ? video.paddingTop : video.retina.paddingTop
      };
    });

    return {
      currentVideo
    };
  }
});
</script>
