<template>
  <section
    class="py-64 lg:py-96"
    data-section="category-grid"
    v-bind="{ style: { backgroundColor: bgColor.hex } }"
  >
    <div class="container-cstm-fluid grid justify-items-center">
      <h1
        class="bold-32 lg:bold-46 xl:bold-54 mb-32 md:mb-40 lg:mb-48 xl:mb-64 text-offblack-700 w-fit"
        v-html="title"
      />
      <ul class="w-full grid grid-cols-2 md:grid-cols-3 -mx-8">
        <li
          v-for="(item, index) in items"
          v-bind:key="index"
          class="px-8 lg:px-40"
        >
          <SectionCategoryGridCard v-bind="{ item }" />
        </li>
      </ul>
      <BaseLink
        variant="accent"
        v-bind="{ href: $localeDjangoPath('plp'), trackData: { eventLabel: 'cta' } }"
      >
        {{ $t('hp.category-grid.explore_button') }}
      </BaseLink>
    </div>
  </section>
</template>

<script>
import { defineComponent, computed, useStore } from '@nuxtjs/composition-api';
import { CATEGORIES } from '~/utils/types';

export default defineComponent({
  props: {
    bgColor: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: undefined
    }
  },
  setup() {
    const categoriesObj = CATEGORIES();
    const sectionCategories = ['sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'desk', 'tvstand'];
    const filteredCategories = sectionCategories.map(item => categoriesObj[item]);

    return {
      items: filteredCategories
    };
  }
});
</script>
