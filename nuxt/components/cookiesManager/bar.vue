<template>
  <ClientOnly>
    <Fragment>
      <div
        v-if="!tyCookieExist"
        class="fixed top-0 left-0 z-5 w-full h-full bg-[rgba(72,68,68,0.4)]"
      >
        <div class="absolute left-0 bottom-0 w-full bg-white">
          <div class="container-cstm-fluid">
            <div class="grid grid-cols-12">
              <div class="col-span-12 md:col-span-8 lg:col-start-1 lg:col-span-8 py-16 md:py-32 lg:py-40 text-black">
                <h2
                  class="bold-20 md:bold-28 mb-8 md:mb-12"
                  v-html="$t('cookiesmanager.bar.headline')"
                />
                <p
                  class="normal-12 md:normal-14 lg:normal-16"
                  v-html="$t('cookiesmanager.bar.body1')"
                />
                <p
                  class="normal-12 md:normal-14 lg:normal-16"
                  v-html="$t('cookiesmanager.bar.body2')"
                />
                <p
                  class="normal-12 md:normal-14 lg:normal-16"
                  v-html="$t('cookiesmanager.bar.body3')"
                />
              </div>
              <div
                class="col-span-12 md:col-span-4 lg:col-start-10 lg:col-span-3 py-16 md:py-32 lg:py-40 flex flex-col
             items-center md:items-end lg:items-start top-md border-t border-solid border-grey-700"
              >
                <BaseButton
                  data-testid="cookies-agree"
                  variant="accent"
                  class="md:mt-48 min-w-[195px]"
                  v-bind="{ trackData: {} }"
                  v-on:click="handleAcceptAllCookies"
                >
                  {{ $t('cookiesmanager.bar.button_ok') }}
                </BaseButton>
                <BaseButton
                  data-testid="cookies-open-modal"
                  variant="outlined"
                  class="mt-16 min-w-[195px]"
                  v-bind="{ trackData: {} }"
                  v-on:click="openCookiesSettingModal"
                >
                  {{ $t('cookiesmanager.bar.button_settings') }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  </ClientOnly>
</template>

<script>
import { defineComponent, onMounted } from '@nuxtjs/composition-api';
import useModal from '~/composables/useModal';
import useCookies from '~/composables/useCookies';
import { useTrackingDOM } from '~/composables/useTracking';

export default defineComponent({
  props: {
    closeEvent: {
      type: Object,
      default: () => ({})
    }
  },
  setup() {
    const modal = useModal();
    const { tyCookieExist, saveCookies } = useCookies();
    const { trackCookiebar } = useTrackingDOM();

    const openCookiesSettingModal = () => {
      modal.show('cookiesSettings');
      trackCookiebar('consent', 'open_settings');
    };

    const handleAcceptAllCookies = () => {
      saveCookies(true);
      modal.hide('cookiesSettings');
      trackCookiebar('consent', 'accept_all');
    };

    onMounted(() => {
      if (!tyCookieExist.value) {
        trackCookiebar('loaded', undefined, true);
      }
    });

    return {
      handleAcceptAllCookies,
      openCookiesSettingModal,
      tyCookieExist
    };
  }
});
</script>
