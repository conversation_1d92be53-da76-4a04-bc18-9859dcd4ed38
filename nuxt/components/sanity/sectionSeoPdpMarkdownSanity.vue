<template>
  <section class="seo-section bg-offwhite-600 py-64 xl:py-96">
    <div
      class="grid grid-cols-12 mx-auto"
      v-bind:class="title ? 'container-cstm-fluid' : 'container-cstm !border-box'"
    >
      <h3
        v-if="title"
        class="col-span-12 md:col-span-3 md:col-start-2 lg:col-span-4 lg:col-start-2 text-offblack-600 normal-24 md:normal-28 pb-12 md:pb-0"
        v-html="title"
      />
      <div
        class="col-span-12 text-offblack-700 normal-16 xl:normal-20"
        v-bind:class="title ? 'md:col-span-7 lg:col-span-6 underline' : 'md:col-span-10 md:col-start-2'"
      >
        <BlockContent
          v-if="body"
          v-bind="{
            blocks: body,
            serializers: {}
          }"
        />
      </div>
    </div>
  </section>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    body: {
      type: Array,
      required: true
    }
  }
});
</script>
