<template>
  <section
    v-bind="{
      class: [
        { 'relative w-full border-box min-h-[475px] max-h-[calc(100vh-132px)]' : type === 'static' },
        { 'relative w-full border-box pt-[140%] md:pt-[51%]' : type === 'video' },
      ],
      style: {
        backgroundColor: backgroundColorHex
      }
    }"
  >
    <BasePicture
      v-if="type === 'static'"
      disable-lazy
      img-classes="block min-h-[475px] h-full max-w-full w-full max-h-[calc(100vh-132px)] object-cover object-bottom"
      v-bind="{
        path: imageOptions.path,
        alt: imageOptions.alt,
        type: imageOptions.type
      }"
    />
    <template v-else-if="type === 'video'">
      <client-only>
        <BaseVideo
          v-bind="{
            paddingTop: isSm ? videoOptions.padding && videoOptions.padding.mobile : videoOptions.padding && videoOptions.padding.desktop,
            id: 'hero-video',
            videoId: isSm ? videoOptions.videoId.mobile : videoOptions.videoId.desktop,
            alt: videoOptions.alt,
            embedOptions: videoOptions.embedOptions
          }"
          class="block h-full max-w-full w-full object-cover object-bottom !absolute top-0 left-0"
        />
      </client-only>
    </template>
    <div
      v-if="headingCopy"
      class="container-cstm-fluid !box-border absolute top-0 left-0 h-full w-full md:h-auto md:top-1/4 transform md:translate-y-[-20%]"
    >
      <div class="grid grid-cols-12">
        <div class="col-span-12 md:col-span-8 lg:col-span-6">
          <h2
            v-if="subheadingCopy"
            v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-700'"
            class="normal-16 sm:normal-20 md:normal-24 mb-8 mt-24 md:mt-0 xl:mb-16"
            v-html="subheadingCopy"
          />
          <h1
            v-bind:class="[
              isLightTheme ? 'text-offwhite-600' : 'text-offblack-700',
              subheadingCopy ? 'text-left' : 'text-center',
            ]"
            class="bold-32 md:bold-54 lg:bold-74 xl:bold-72 pb-0 pt-24 md:pt-0 md:text-left"
            v-html="headingCopy"
          />
          <div class="text-center md:text-left md:flex justify-center md:justify-start mt-16 xl:mt-32 absolute bottom-16 left-1/2 transform translate-x-[-50%] w-[calc(100%-32px)] md:w-full md:translate-x-[0] md:static">
            <BaseButton
              v-if="ctaCopy && ctaAnchor"
              variant="accent"
              v-bind="{ trackData: {
                eventLabel: 'cta'
              } }"
              v-on="{ click: () => scrollTo(ctaAnchor) }"
            >
              {{ ctaCopy }}
            </BaseButton>
            <BaseLink
              v-else-if="ctaCopy && ctaUrl"
              variant="accent"
              v-bind="{
                href: ctaUrl,
                trackData: {
                  eventLabel: 'cta',
                  eventPath: ctaUrl
                }
              }"
            >
              {{ ctaCopy }}
            </BaseLink>
            <!-- second button -->
            <BaseButton
              v-if="ctaCopy2 && ctaAnchor2"
              variant="accent"
              v-bind="{ trackData: {
                eventLabel: 'cta'
              } }"
              class="mt-12 md:mt-0 md:ml-32"
              v-on="{ click: () => scrollTo(ctaAnchor2) }"
            >
              {{ ctaCopy2 }}
            </BaseButton>
            <BaseLink
              v-else-if="ctaCopy2 && ctaUrl2"
              variant="accent"
              v-bind="{
                href: ctaUrl2,
                trackData: {
                  eventLabel: 'cta',
                  eventPath: ctaUrl2
                }
              }"
              class="ml-16 md:ml-32"
            >
              {{ ctaCopy2 }}
            </BaseLink>
          </div>
        </div>
      </div>
    </div>
    </div>
  </section>
</template>

<script>
import { defineComponent, computed } from '@nuxtjs/composition-api';
import useMq from '~/composables/useMq';
import { scrollToElement } from '@/helpers';

export default defineComponent({
  props: {
    isLightTheme: {
      type: Boolean,
      default: false
    },
    headingCopy: {
      type: String,
      default: ''
    },
    subheadingCopy: {
      type: String,
      default: ''
    },
    ctaCopy: {
      type: String,
      default: ''
    },
    ctaUrl: {
      type: String,
      default: ''
    },
    ctaAnchor: {
      type: String,
      default: ''
    },
    ctaCopy2: {
      type: String,
      default: ''
    },
    ctaUrl2: {
      type: String,
      default: ''
    },
    ctaAnchor2: {
      type: String,
      default: ''
    },
    // Average image color
    bgColor: {
      type: [String, Object],
      default: '#fff'
    },
    type: {
      type: String,
      default: '#fff',
      validator: value => ['static', 'video'].includes(value)
    },
    imageOptions: {
      type: Object,
      default: () => ({})
    },
    videoOptions: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const backgroundColorHex = computed(() => (props.bgColor.hasOwnProperty('hex') ? props.bgColor.hex : props.bgColor));
    const { isSm } = useMq();

    const scrollTo = el => {
      scrollToElement({
        element: `#${el}`
      });
    };

    return {
      scrollTo,
      backgroundColorHex,
      isSm
    };
  }
});
</script>
