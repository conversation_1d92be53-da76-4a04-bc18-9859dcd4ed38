<template>
  <LazyHydrate when-visible>
    <section class="bg-offwhite-700 py-64 lg:py-96">
      <div class="container-cstm-fluid relative text-center">
        <h2
          class="absolute top-0 left-0 w-full text-offwhite-600 bold-28 pt-16 md:bold-32 lg:bold-46 xl:bold-54 md:pt-40 lg:pt-48"
          v-html="title"
        />
        <BasePicture
          picture-classes="flex rounded-12 overflow-hidden"
          img-classes="w-full"
          type="M T SD LD"
          v-bind="{
            path: imagePath,
            alt: $t('hp.imageusps.title')
          }"
        />
        <BaseCarousel
          v-if="isMobileOrTabletViewport"
          class="-mt-32"
          v-bind="{
            options: swiperOptions,
            name: `${name}-uspsCarousel`,
            isPaginationDesktop: true,
            isNavigation: false
          }"
        >
          <BaseCarouselSlide
            v-for="(item, index) in options"
            v-bind:key="index"
          >
            <div class="px-24 flex flex-col items-center">
              <span v-bind:class="'text-grey-900 w-[56px] h-[56px] p-12 lg:w-[80px] lg:h-[80px] lg:p-24 rounded-full bg-white mb-12 lg:mb-16'">
                <img
                  v-if="!item.isComponent"
                  v-bind:src="$urlFor(item.icon).url()"
                  class="w-full"
                >
                <component
                  v-bind:is="item.icon"
                  v-else
                  class="w-full h-full"
                />
              </span>
              <p
                class="normal-24 text-offblack-800"
                v-html="item.appendRegion ? `${item.text} ${ currentRegion !== '_other' ? '<b>' + currentTranslatedRegion + '</b>' : $t('usps.large.subtitle2')}` : item.text"
              />
            </div>
          </BaseCarouselSlide>
        </BaseCarousel>
        <ul v-else class="flex -mt-40 justify-between">
          <li
            v-for="(item, index) in options"
            v-bind:key="index"
            class="w-1/3 px-24 flex flex-col items-center"
          >
            <span v-bind:class="'text-grey-900 w-[56px] h-[56px] p-12 lg:w-[80px] lg:h-[80px] lg:p-24 rounded-full bg-white mb-12 lg:mb-16'">
              <img
                v-if="!item.isComponent"
                v-bind:src="$urlFor(item.icon).url()"
                class="w-full"
              >
              <component
                v-bind:is="item.icon"
                v-else
                class="w-full h-full"
              />
            </span>
            <p
              class="normal-24 text-offblack-800"
              v-html="item.appendRegion ? `${item.text} ${ currentRegion !== '_other' ? '<b>' + currentTranslatedRegion + '</b>' : $t('usps.large.subtitle2')}` : item.text"
            />
          </li>
        </ul>
        <BaseLink
          v-if="ctaCopy"
          variant="accent"
          v-bind="{ href: $localeDjangoPath('plp'), trackData: { eventLabel: 'cta' } }"
          class="whitespace-nowrap mt-32"
          v-html="ctaCopy"
        />
      </div>
    </section>
  </LazyHydrate>
</template>

<script>
import { computed, defineComponent, useStore } from '@nuxtjs/composition-api';
import useMq from '~/composables/useMq';
import useRegions from '@/composables/useRegions';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    ctaCopy: {
      type: String,
      default: ''
    },
    imagePath: {
      type: String,
      default: 'homepage/comfortable'
    },
    options: {
      type: Array,
      required: true,
      validator: items => items.every(item => item.icon && item.text)
    },
    name: {
      type: String,
      default: ''
    }
  },
  setup() {
    const swiperOptions = {
      slidesPerView: 1,
      freeMode: false,
      breakpoints: 0
    };

    const { getters } = useStore();
    const currentRegion = computed(() => getters['global/REGION_NAME']);
    const { translatedRegion } = useRegions();
    const currentTranslatedRegion = translatedRegion(currentRegion.value);

    const { isMobileOrTabletViewport } = useMq('lg');

    return {
      isMobileOrTabletViewport,
      swiperOptions,
      currentTranslatedRegion,
      currentRegion
    };
  }
});
</script>

<style lang="scss" scoped>
::v-deep {
  .swiper-pagination {
    @apply mt-20;
  }
}
</style>
