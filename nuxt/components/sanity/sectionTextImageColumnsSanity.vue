<template>
  <LazyHydrate when-visible>
    <section v-bind:style="`background-color: ${bgColor && bgColor.hex};`">
      <SectionUiSplit v-bind="{ isReversed, isReversedOnMobile }">
        <template #media>
          <div class="h-full">
            <BasePicture
              img-classes="w-full h-full object-cover"
              type="M T SD LD"
              v-bind="{
                alt: title || subtitle,
                path: imagePath
              }"
            />
          </div>
        </template>
        <template #content>
          <p
            v-if="subtitle"
            class="normal-16-2 mb-16 uppercase"
            v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-800'"
            v-html="subtitle"
          />
          <h2
            v-if="title"
            v-bind:class="[isLightTheme ? 'text-offwhite-800' : 'text-offblack-800', titleClass]"
            v-html="title"
          />
          <h2
            v-if="secondaryTitle"
            class="normal-16-2 pb-24 lg:pb-32 uppercase lg:max-w-[470px]"
            v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-600'"
            v-html="secondaryTitle"
          />
          <p
            v-if="description"
            v-bind:class="[isLightTheme ? 'text-offwhite-700' : 'text-offblack-600', descriptionClass]"
            v-html="description"
          />

          <ul>
            <li
              v-for="(option, index) in options"
              v-bind:key="index"
              class="w-full flex pb-16 mb-24 md:mb-16 lg:pb-24 lg:mb-32 border-b border-grey-900/50
            last:mb-8 lg:last:mb-16 last:border-none last:pb-0"
            >
              <span class="mr-4 bold-16 text-orange">{{ String(index + 1).padStart(2, '0') }}</span>
              <div class="flex flex-col">
                <span
                  class="bold-24 md:bold-20 lg:bold-24 mb-4"
                  v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-600'"
                >
                  {{ option.header }}
                </span>
                <span
                  class="normal-16"
                  v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-700'"
                >
                  {{ option.body }}
                </span>
              </div>
            </li>
          </ul>

          <div class="flex flex-wrap justify-center md:justify-start">
            <BaseLink
              v-if="ctaUrl && ctaCopy"
              v-bind="{
                href: ctaUrl,
                variant: ctaVariant,
                trackData: {
                  eventLabel: 'cta',
                  eventPath: ctaUrl
                }
              }"
              class="whitespace-nowrap mt-24 mx-8 sm:mx-12 md:ml-0 md:mr-24"
            >
              {{ ctaCopy }}
            </BaseLink>
            <BaseLink
              v-if="ctaAdditionalCopy && ctaAdditionalUrl && t03Available"
              v-bind="{
                href: ctaAdditionalUrl,
                trackData: {
                  eventLabel: 'additional-cta',
                  eventPath: ctaAdditionalUrl,
                },
                variant: ctaAdditionalVariant,
              }"
              class="whitespace-nowrap mt-24 mx-8 sm:mx-12 md:ml-0"
              v-bind:class="ctaAdditionalClass"
            >
              {{ ctaAdditionalCopy }}
            </BaseLink>

            <BaseButton
              v-if="hasJoinTheWaitlistPopup && ctaAdditionalCopy && !t03Available"
              variant="accent"
              class="whitespace-nowrap mt-24 mx-8 sm:mx-12 md:ml-0"
              v-bind="{ trackData: {} }"
              v-on="{ click: showJoinTheWaitlistModal }"
            >
              {{ ctaAdditionalCopy }}
            </BaseButton>
          </div>
        </template>
      </SectionUiSplit>
      <ModalJoinTheWaitlist v-if="hasJoinTheWaitlistPopup && !t03Available" />
    </section>
  </LazyHydrate>
</template>

<script>
import { computed, defineComponent, useStore } from '@nuxtjs/composition-api';
import useModal from '~/composables/useModal';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    bgColor: {
      type: Object,
      default: () => ({})
    },
    secondaryTitle: {
      type: String,
      default: ''
    },
    imagePath: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => ([]),
      validator: options => options.every(option => 'header' in option && 'body' in option)
    },
    ctaCopy: {
      type: String,
      default: ''
    },
    ctaUrl: {
      type: String,
      default: ''
    },
    ctaVariant: {
      type: String,
      default: 'accent'
    },
    ctaAdditionalCopy: {
      type: String,
      default: ''
    },
    ctaAdditionalUrl: {
      type: String,
      default: ''
    },
    ctaAdditionalClass: {
      type: String,
      default: ''
    },
    ctaAdditionalVariant: {
      type: String,
      default: 'outlined-dark'
    },
    isLightTheme: {
      type: Boolean,
      default: true
    },
    isReversed: {
      type: Boolean,
      default: true
    },
    isReversedOnMobile: {
      type: Boolean,
      default: false
    },
    hasJoinTheWaitlistPopup: {
      type: Boolean,
      default: false
    },
    titleClass: {
      type: String,
      default: 'lg:max-w-[470px] bold-28 md:bold-32 lg:bold-54 pb-16'
    },
    descriptionClass: {
      type: String,
      default: 'lg:max-w-[450px] normal-16 lg:normal-20'
    }
  },
  setup() {
    const store = useStore();
    const modal = useModal();
    const t03Available = computed(() => store.getters['global/T03_AVAILABLE']);

    const showJoinTheWaitlistModal = () => {
      modal.show('joinTheWaitlist');
    };

    return { showJoinTheWaitlistModal, t03Available };
  }
});
</script>
