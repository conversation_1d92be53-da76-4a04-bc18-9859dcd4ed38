<template>
  <div
    class="relative w-full lg:w-auto text-center lg-max:overflow-hidden transition-all basic-transition lg:h-32"
    v-bind:class="isExpanded ? 'h-[43px]' : 'h-0'"
  >
    <div
      class="inline-flex border py-2 px-8 bold-18 proportional-nums mt-8 lg:mt-0"
      v-bind:style="{ color: textColor, 'border-color': textColor }"
    >
      <div v-if="days">
        {{ days.toString().padStart(2, '0') }}<span class="bold-14">D<span class="px-4">:</span></span>
      </div>
      <div>{{ hours.toString().padStart(2, '0') }}<span class="bold-14">H<span class="px-4">:</span></span></div>
      <div>{{ minutes.toString().padStart(2, '0') }}<span class="bold-14">M</span></div>
      <div v-if="!days">
        <span class="px-4">:</span>{{ seconds.toString().padStart(2, '0') }}<span class="bold-14">S</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    endDate: {
      type: String,
      required: true
    },
    textColor: {
      type: String,
      default: '#F3F677'
    },
    isDark: {
      type: Boolean,
      required: false
    },
    isExpanded: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const days = ref<number>(0);
    const hours = ref<number>(0);
    const minutes = ref<number>(0);
    const seconds = ref<number>(0);
    let startTimer: ReturnType<typeof setInterval>;

    const countdown = () => {
      const now: Date = new Date();
      const distance: number = Date.parse(props.endDate) - now.getTime();

      if (distance < 0) {
        clearInterval(startTimer);
        return;
      }

      days.value = Math.floor(distance / (1000 * 60 * 60 * 24));
      hours.value = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      minutes.value = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      seconds.value = Math.floor((distance % (1000 * 60)) / 1000);
    };

    countdown();

    onMounted(() => {
      startTimer = setInterval(countdown, 1000);
    });
    onUnmounted(() => (startTimer && clearInterval(startTimer)));

    return {
      days,
      hours,
      minutes,
      seconds
    };
  }
});
</script>
