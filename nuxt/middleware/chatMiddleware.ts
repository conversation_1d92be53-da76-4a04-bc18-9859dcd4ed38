import { defineNuxtMiddleware } from '@nuxtjs/composition-api';
import { excludedDixaPages } from '../utils/consts';

export default defineNuxtMiddleware((context) => {
  // to get dixa work after going from tylko-for-business pages it needs to refresh the page

  if (process.client) {
    const isRefreshPage = excludedDixaPages.test(context?.from?.path);

    if (isRefreshPage) {
      window.location.replace(context.route?.fullPath);
    }
  }
});
