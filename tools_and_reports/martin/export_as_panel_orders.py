from openpyxl.workbook import Workbook
from logistic.submodels.assembly_service import AssemblyService

workbook = Workbook()

sheet = workbook.create_sheet(title='AS panel export')

data = []

for i, assembly in enumerate(
    AssemblyService.objects.filter(
        service_invoiced_by_carrier=False, shipping_invoiced_by_carrier=False, status__in=[5,7]
    )
):
    data.append(
        [
            assembly.logistic_order.order.pk,
            assembly.service_price,
            assembly.shipping_price,
            assembly.logistic_order.total_brutto_weight_string(),
        ]
    )
    data[i].insert(
        1,
        " ".join(
            str(id)
            for id in list(
                product.get_product_id()
                for product in assembly.logistic_order.order.product_set.all()
            )
        ),
    )

headers = ['Order', 'Items', 'Service price', 'Shipping price', 'Weight']

sheet.append(headers)

for row_data in data:
    sheet.append(list(row_data))

workbook.save('AS_panel_export.xlsx')
workbook.close()
