from datetime import datetime
from django.utils import timezone

from tqdm import tqdm

from invoice.choices import InvoiceStatus
from invoice.models import (
    Invoice,
)
from django.core.mail import EmailMessage

ISSUED_AT_CORRECTION = datetime(2020, 12, 31, 23, 59)
ISSUED_AT_NEW = datetime(2021, 1, 1, 0, 1)

SUBJECT = '''Your Corrected Tylko Invoice'''


def get_invoices():
    return Invoice.objects.filter(
        order__country='united_kingdom',
        status=InvoiceStatus.CORRECTING,
        issued_at=ISSUED_AT_CORRECTION,
    )


def send(corrections):
    for correction in tqdm(corrections):
        correction.create_pdf()
        correction.refresh_from_db()
        body = f'''Hello {correction.order.first_name},

As you are most probably aware, the UK stepped out of the European Union on 01.01.2021. As Tylko is a Polish company operating in the EU, we must abide by newly-established regulations regarding trade and exchange between the UK and European Union. This means we are required to correct all intra-community acquisition of goods invoices issued before 01.01.2021 to designate goods export.

We will therefore zero out the original invoice for your order, and a new corrected invoice will be issued for the same amount.
If you have any questions at all, don't hesitate to ask!

Thanks Kindly,
The Tylko Team'''
        enabled = Invoice.objects.filter(
            order__country='united_kingdom',
            status=InvoiceStatus.ENABLED,
            issued_at=ISSUED_AT_NEW,
            order__id=correction.order_id,
        ).last()
        if not enabled:
            print(correction.pk, correction.order.pk)
            continue
        email = EmailMessage(
            SUBJECT,
            body,
            from_email='Tylko <<EMAIL>>',
            to=[
                correction.order.email,
            ],
        )
        email.attach(
            correction.pdf.name.split('/')[1],
            correction.pdf.read(),
            mimetype='application/pdf',
        )
        email.attach(
            enabled.pdf.name.split('/')[1],
            enabled.pdf.read(),
            mimetype='application/pdf',
        )
        email.send()
        enabled.sent_invoice_at = timezone.now()
        enabled.save()
        correction.sent_invoice_at = timezone.now()
        correction.save()

print(get_invoices().count())
send(get_invoices())
