from datetime import datetime, timedelta, timezone
from decimal import ROUND_HALF_UP, Decimal
from subprocess import Popen

from past.utils import old_div

from custom.utils.exports import dump_list_as_csv
from invoice.choices import InvoiceStatus
from invoice.models import (Invoice, InvoiceCorrectionChange, InvoiceItem,
                            SymfoniaFConfiguration)


def export_to_fka(invoices):
    headers = (
        'data_wystawienia',
        'data_sprzedazy',
        'data_platnosci',
        'skrot',
        'typ_dokumentu',
        'numer_ewidencyjny',
        'numer_faktury',
        'tresc_dokumentu',
        'netto_calego_dokumentu',
        'brutto_calego_dokumentu',
        'vat_calego_dokumentu',
        'waluta',
        'kurs',
        'konto_1',
        'kwota_1',
        'konto_2',
        'kwota_2',
        'konto_3',
        'kwota_3',
        'konto_4',
        'kwota_4',
        'konto_5',
        'kwota_5',
        'netto_27',
        'vat_27',
        'netto_26',
        'vat_26',
        'netto_25',
        'vat_25',
        'netto_24',
        'vat_24',
        'netto_23',
        'vat_23',
        'netto_22',
        'vat_22',
        'netto_21',
        'vat_21',
        'netto_20',
        'vat_20',
        'netto_19',
        'vat_19',
        'netto_16',
        'vat_16',
        'netto_3',
        'vat_3',
        'netto_0',
        'vat_0',
        'dok_powiazany',
        'data_powiazana',
        'rodzaj_transakcji',
        'okres sprawozdawczy',
        'okres rejestru',
        'NIP',
        'Ulica',
        'kod_pocztowy',
        'miasto',
        'kraj',
        'nettopln',
        'vatpln',
    )

    doc = []
    invoices = invoices.order_by('id')
    for x, i in enumerate(invoices):
        print(x, invoices.count())
        export = i.generate_date_for_symfonia_export()
        to_dict = i.to_dict()
        nazwa = export[0]['kodOdKH']
        document_type = export[0]['typ_dk']
        try:
            attach = Invoice.objects.get(
                order=i.order.pk, status=InvoiceStatus.ENABLED
            )
        except:
            attach = i.corrected_invoice
        if document_type.lower() in ['wdt', 'dex']:
            dk = document_type
        else:
            dk = document_type.split('_')
            TWOPLACES = Decimal(10) ** -2
            if Decimal(to_dict['vat_rate']).quantize(TWOPLACES) in [
                Decimal(0.23).quantize(TWOPLACES),
                Decimal(0),
            ]:
                dk = 'F_{}'.format(dk[0])
            else:
                dk = 'F2{}'.format(dk[0])
        dk_fks = False
        net_pln = to_dict['net_value_in_pln']
        total_pln = to_dict['total_value_in_pln']
        if i.status == InvoiceStatus.CORRECTING:
            att, att_date = attach.pretty_id, attach.issued_at.strftime('%d.%m.%Y')
            diff = i.to_diff_dict()
            netto, brutto, vat = (
                diff['net_value'],
                diff['total_value'],
                diff['total_value'] - diff['net_value'],
            )
            total_pln = (brutto * to_dict['exchange_rate']).quantize(
                Decimal('.01'), rounding=ROUND_HALF_UP
            )
            vat_in_pln = (
                (
                    old_div(
                        brutto * to_dict['exchange_rate'],
                        Decimal(to_dict['vat_rate'] + 1),
                    )
                )
                * Decimal(to_dict['vat_rate'])
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
            net_pln = total_pln - vat_in_pln
            reason = i.corrected_notes
            dk_fks = True
            name = 'FKS'
            if dk in ['DEX', 'WDT'] or '2' in dk:
                name = dk
        else:
            netto, brutto, vat = (
                to_dict['net_value'],
                to_dict['total_value'],
                to_dict['vat_value'],
            )
            reason = ''
            att, att_date = '', ''
        register_date = i.issued_at.strftime('%d.%m.%Y')
        fk = SymfoniaFConfiguration.objects.filter(document_type=dk).last()

        if not reason:
            opis = 'Order no.{}'.format(i.order.pk)
        else:
            opis = reason
        unformated_row = {
            'data_wystawienia': i.issued_at.strftime('%d.%m.%Y'),
            'data_sprzedazy': i.sell_at.strftime('%d.%m.%Y'),
            'data_platnosci': i.sell_at.strftime('%d.%m.%Y'),
            'skrot': nazwa,
            'typ_dokumentu': fk.document_type if not dk_fks else name,
            'numer_faktury': i.pretty_id,
            'tresc_dokumentu': opis,
            'netto_calego_dokumentu': netto,
            'brutto_calego_dokumentu': brutto,
            'vat_calego_dokumentu': vat,
            'waluta': export[0]['waluta'],
            'kurs': to_dict['exchange_rate'],
            'konto_1': '203-3-1-',
            'kwota_1': brutto,
            'konto_2': fk.account,
            'kwota_2': netto,
            'konto_3': fk.vat_account,
            'kwota_3': vat,
            'dok_powiazany': att,
            'data_powiazana': att_date,
            'rodzaj_transakcji': 'sprzedaż',
            'okres sprawozdawczy': i.issued_at.strftime('%d.%m.%Y'),
            'okres rejestru': register_date,
            'NIP': (
                export[1]['nip']
                if 'nip' in export[1]
                else ''
                if not export[1]['osfiz']
                else ''
            ),
            'Ulica': export[1]['ulica'],
            'kod_pocztowy': export[1]['kodpocz'],
            'miasto': export[1]['miejscowosc'],
            'kraj': export[0]['khKrajKod'],
            'nettopln': net_pln,
            'vatpln': total_pln - net_pln,
        }
        vat_rate = to_dict['vat_rate']
        netto_k, vat_k = 'netto_{0},vat_{0}'.format(int(vat_rate * 100)).split(',')
        unformated_row[netto_k] = netto
        unformated_row[vat_k] = vat
        row = [
            (lambda a: unformated_row[a] if a in unformated_row else '')(x)
            for x in headers
        ]
        doc.append(row)
    dump_list_as_csv(
        doc,
        output=open('/tmp/symfonia_export.csv_tmp', 'w'),
        mail=None,
        headers=headers,
        delimiter=';',
    )
    f_name = '/tmp/symfonia_export.csv'
    call = Popen('iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(f_name), shell=True)
    call.wait()

    with open(f_name, 'rb') as fp:
        data = fp.read()
    print('done')


ISSUED_AT_CORRECTION = datetime(2020, 12, 31, 23, 59)

invs = Invoice.objects.filter(
    issued_at=ISSUED_AT_CORRECTION,
).order_by('pk')

export_to_fka(invs)
