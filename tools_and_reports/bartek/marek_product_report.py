from huey import crontab
from huey.contrib.djhuey import periodic_task
from custom.utils.exports import dump_list_as_csv
from orders.models import Order
from producers.models import Product, ProductStatusHistory
from producers.choices import ProductStatus, ProductPriority


@periodic_task(crontab(month='*', day='3,4,5', day_of_week='*', hour='8', minute='0'))
def generate_production_report():
    products = Product.objects.all().select_related('order').order_by('id')

    result = []
    headers = [
        'order id',
        'product id',
        'manufactor',
        'to be shipped date',
        'sent to customer',
        'delivered date',
        'product status',
        'product priority',
        'order status'
    ]

    for p in products:
        h = ProductStatusHistory.objects.filter(status=ProductStatus.TO_BE_SHIPPED, product=p).last()
        result.append(
            [
                p.order.id,
                p.id,
                p.manufactor.name if p.manufactor else '?',
                h.changed_at if h else 'brak',
                (
                    p.order.logistic_info.last().sent_to_customer
                    if p.order.logistic_info.last()
                    else 'brak'
                ),
                (
                    p.order.logistic_info.last().delivered_date
                    if p.order.logistic_info.last()
                    else 'brak'
                ),
                ProductStatus(p.status),
                ProductPriority(p.priority),
                dict(Order.STATUS_CHOICES)[p.order.status]
            ]
        )
    dump_list_as_csv(result,
                     output='production_report.csv',
                     mail=[
                         '<EMAIL>',
                         '<EMAIL>',
                     ],
                     delimiter=';',
                     headers=headers,
                     mail_subject='Marek Report',
                     )
