# Komunikacja z Gala

## Autoryzacja

Autoryzacja dla wszystkich endpointów odbywa się poprzez token. Token należy dołączyć do zapytania w nagłówku `Authorization` w formacie `Token <token>`.

## Panel producenta

### Informacje o batchach - produkcja

Dane można uzyskać pod adresem:

```
<adres_cstm>/pages/api/producer_batches_rest/?page_number=1&page_size=100
```

Przykładowa odpowiedź:

```json
{
  "page_size": 100,
  "page_number": 1,
  "count": 1,
  "batches": [
    {
      "batch_id": 42977,
      "batch_type": "STANDARD",
      "is_sidebohr": false,
      "material_description": "Black laminate Egger",
      "created_at": "2024/12/17 00:13",
      "batch_status": "In production",
      "priorities": {
        "Fast Track": 2
      },
      "actions_needed": "no action needed",
      "cnc_files_status": 0,
      "production_files_status": 0,
      "packaging_files_status": 0,
      "delayed_items": [
        337537,
        337538
      ],
      "items": [
        337537,
        337538
      ],
      "completed_products": {
        "all": 2,
        "completed": 0
      },
      "element_order_ids": [
        [1]
      ],
      "area": 2.54,
      "banding_length": 6.46,
      "days_of_production": "-"
    }
  ]
}
```

#### Kluczowe pola dla sof:

- **`batch_id`** - identyfikator batcha.
- **`material_description`** - opis materiału (dla sof będzie to kolor).
- **`batch_status`** - status batcha.
- **`actions_needed`** - informacja o koniecznych działaniach zespołu Tylko. Jeśli wartość jest inna niż `no action needed`, należy wstrzymać produkcję.
- **`items`** - lista produktów w batchu.
- **`delayed_items`** - lista opóźnionych produktów.
- **`completed_products`** - informacje o produktach gotowych do odbioru.
- **`element_order_ids`** - lista plików z kosztami i zużyciem materiałów (szczegóły poniżej).

### Pobieranie plików batch:

- Pliki produkcyjne: `<adres_cstm>/pages/api/production_files/?batch_ids=<batch_id>`
- Połączenia CNC: `<adres_cstm>/pages/api/cnc_connections/?batch_ids=<batch_id>`
- Pliki opakowaniowe: `<adres_cstm>/pages/api/packaging_files/?batch_ids=<batch_id>`

### Informacje o batchach - wysyłka

Dane dostępne pod adresem:

```
<adres_logistyki>/api/v1/transport_release?page_number=1&page_size=50
```

Przykładowa odpowiedź:

```json
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 4,
      "created_at": "2024-12-20 14:34",
      "transport_number": "74760/2022/AA",
      "transport_type": "Dt",
      "get_related_items": "T1_325322, T1_325323, T1_325324, T1_325325",
      "get_package_number": 4,
      "get_courier_details": " - None",
      "released_at": null,
      "load_date": "2024-12-20",
      "logistic_label_url": "https://s3.oklyt.pl/wilk-logistic/media/private/logistic/dedicated_transport_label/2024/12/label_350644_lLYYT4l.pdf",
      "additional_document_url": "https://s3.oklyt.pl/wilk-logistic/media/private/logistic/cmr_document/2024/12/cmr_doc_6_Xy04697.pdf",
      "load_hours": "10:00",
      "are_changes_confirmed": true,
      "notes": "",
      "producer_notes": "",
      "attachments_exists": false,
      "were_files_downloaded": false,
      "is_estimated_delivery_date": false,
      "products_in_production": []
    }
  ]
}
```

Aby potwierdzić wysyłkę, należy wysłać PATCH na adres:

```
<adres_logistyki>/api/v1/transport_release/<id>/set_released_at_date
```

### Informacje o plikach ze zużyciem i cenami materiałów

Dane dostępne pod adresem:

```
<adres_cstm>/api/v1/elements_orders?page_number=1&page_size=50
```

Przykładowa odpowiedź:

```json
{
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 2,
            "generated_at": "2025-01-09T13:06:02.687421",
            "batches": [
                42981
            ],
            "deleted": false,
            "new_report": null
        },
        {
            "id": 1,
            "generated_at": "2025-01-09T13:06:02.687421",
            "batches": [
              42981
            ],
            "deleted": true,
            "new_report": 2
        }
    ]
}
```

Jeśli pole `deleted` jest ustawione na `true`, dany plik został zastąpiony przez plik wskazany w polu `new_report`.

Zawartość pliku w formacie JSON można pobrać pod adresem:

```
<adres_cstm>/api/v1/elements_orders_json/<id>/
```

Przykładowa zawartość pliku:

```json
{
    "totals": {
        "Koszt - ogółem": "1786.29",
        "Koszt - moduły": 0,
        "Koszt - pokrowce": 0
    },
    "details": [
        {
            "index": "item_count",
            "MANUFACTURER_CODE": null,
            "Unit": "-",
            "B_42981": "2,0",
            "TOTAL": "2,0",
            "TOTAL BRUTTO": null,
            "PRICE": null,
            "TYLKO NETTO": null,
            "TYLKO BRUTTO": null,
            "MANUFACTURER NETTO": null,
            "MANUFACTURER BRUTTO": null
        },
        {
            "index": "fitting_connector_00_extended-horizontal-dowel-8x60",
            "MANUFACTURER_CODE": null,
            "Unit": "szt",
            "B_42981": null,
            "TOTAL": null,
            "TOTAL BRUTTO": null,
            "PRICE": null,
            "TYLKO NETTO": null,
            "TYLKO BRUTTO": null,
            "MANUFACTURER NETTO": null,
            "MANUFACTURER BRUTTO": null
        },
        {
            "index": "fitting_connector_00_modular-halfblocking",
            "MANUFACTURER_CODE": null,
            "Unit": "szt",
            "B_42981": "72,0",
            "TOTAL": "72,0",
            "TOTAL BRUTTO": "73",
            "PRICE": "0,49",
            "TYLKO NETTO": null,
            "TYLKO BRUTTO": null,
            "MANUFACTURER NETTO": "35,28",
            "MANUFACTURER BRUTTO": "0,49"
        }
    ]
}
```

#### Kluczowe pola:

- **`INDEX`** - identyfikator elementu (nazwa elementu lub `item_count`, `elements_amount`  ).
- **`MANUFACTURER_CODE`** - nazwa elementu u producenta.
- **`Unit`** - jednostka pomiaru zużycia (np. sztuki, metry).
- **`PRICE`** - cena elementu.
- **`B_42981`** - podsumowanie zużycia dla danego batcha (dla większej liczby batchy pojawi się więcej kluczy).
- **`TOTAL`** - suma zużycia dla całego zamówienia.
- **`TOTAL BRUTTO`** - suma zużycia z uwzględnieniem strat materiałowych.
- **`TYLKO NETTO`** - koszt `TOTAL` \* `PRICE` dla materiałów zarządzanych przez Tylko.
- **`TYLKO BRUTTO`** - koszt `TOTAL BRUTTO` - `TOTAL` \* `PRICE` dla materiałów zarządzanych przez Tylko.
- **`MANUFACTURER NETTO`** - koszt `TOTAL` \* `PRICE` dla materiałów zarządzanych przez producenta.
- **`MANUFACTURER BRUTTO`** - koszt `TOTAL BRUTTO` - `TOTAL` \* `PRICE` dla materiałów zarządzanych przez producenta.


### Meble gotowe do wysyłki

Po spakowaniu danej paczki i przygotowaniu do wysyłki, należy zgłosić gotowość danej paczki do odbioru. Gdy wszystkie paczki dla danego mebla zostaną zgłoszone, mebel zostanie oznaczony jako gotowy do wysyłki.
Gotowe paczki można zgłaszać wysyłając POST na adres:

```
<adres_cstm>/api/v1/producers/release_package
```

Zapytanie powinno zawierać dane w formacie:

```json
{
  "package": "1",
  "product": 113,
  "released_at": "2025-01-10T08:21:50.427Z"
}
```

# Edited 13.03.2025:
### Produkt - potrzebne materiały i dane o zamówieniu
Dane potrzebne do produkcji danego produktu można uzyskać pod adresem:
```
<adres_cstm>/api/v1/producers/product_info/<product_id>
```
`product_id` można znaleźć w polu `items` w odpowiedzi z endpointu `producer_batches_rest`.

Przykładowa odpowiedź:
```json
{"id": 352299,
 "order_info": {"id": *********,
  "country": "poland",
  "country_area": null,
  "first_name": "TEST ORDER FIRST NAME",
  "last_name": "TEST ORDER LAST NAME",
  "postal_code": null,
  "street_address_1": "TEST ORDER ADDRESS",
  "street_address_2": null,
  "company_name": null,
  "floor_number": null,
  "order_notes": "",
  "hard_parking": false,
  "above_3rd_floor": false,
  "no_elevator": null,
  "city": "TEST CITY",
  "phone": null,
  "phone_prefix": null,
  "email": "EMAIL@TEST"},
 "material_info": [{"name": "S01_AR_D07_W02",
   "color": "OLIVE_GREEN",
   "pack_id": 6,
   "material_usage": [{"fitting_S1_metal_connector-armrest": 1},
    {"semiproduct_S1_blank_armrest-875x250": 1},
    {"cover_S1_rewool2-olive-green_armrest-875x250": 1}]},
  {"name": "S01_AR_D07_W02",
   "color": "OLIVE_GREEN",
   "pack_id": 1,
   "material_usage": [{"fitting_S1_metal_connector-armrest": 1},
    {"semiproduct_S1_blank_armrest-875x250": 1},
    {"cover_S1_rewool2-olive-green_armrest-875x250": 1}]},
  {"name": "S01_CL_D13_W07",
   "color": "OLIVE_GREEN",
   "pack_id": 2,
   "material_usage": [{"semiproduct_S1_blank_cushion-875": 1},
    {"semiproduct_S1_blank_backrest-875": 1},
    {"cover_S1_rewool2-olive-green_cushion-875": 1},
    {"fitting_S1_metal_connector-seater-corner": 2},
    {"cover_S1_rewool2-olive-green_backrest-875": 1},
    {"semiproduct_S1_blank_chaise-lounge-1625x875": 1},
    {"cover_S1_rewool2-olive-green_chaise-lounge-1625x875": 1}]},
  {"name": "S01_FR_D06_W06",
   "color": "OLIVE_GREEN",
   "pack_id": 3,
   "material_usage": [{"fitting_S1_metal_connector-footrest": 1},
    {"semiproduct_S1_blank_footrest-750x750": 1},
    {"cover_S1_rewool2-light-gray_footrest-750x750": 1}]},
  {"name": "S01_ST_D08_W06",
   "color": "OLIVE_GREEN",
   "pack_id": 4,
    "material_usage": [{"semiproduct_S1_blank_cushion-750": 1},
    {"semiproduct_S1_blank_backrest-750": 1},
    {"semiproduct_S1_blank_seater-1000x750": 1},
    {"cover_S1_rewool2-olive-green_cushion-750": 1},
    {"fitting_S1_metal_connector-seater-corner": 2},
    {"cover_S1_rewool2-olive-green_backrest-750": 1},
    {"cover_S1_rewool2-olive-green_seater-1000x750": 1}]},
  {"name": "S01_ST_D08_W06",
   "color": "OLIVE_GREEN",
   "pack_id": 5,
   "material_usage": [{"semiproduct_S1_blank_cushion-750": 1},
    {"semiproduct_S1_blank_backrest-750": 1},
    {"semiproduct_S1_blank_seater-1000x750": 1},
    {"cover_S1_rewool2-olive-green_cushion-750": 1},
    {"fitting_S1_metal_connector-seater-corner": 2},
    {"cover_S1_rewool2-olive-green_backrest-750": 1},
    {"cover_S1_rewool2-olive-green_seater-1000x750": 1}]}],
 "batch": 44569,
 "front_view": "https://zajac.op4.oklyt.pl/media/private/producers/product/front_view/2025/03/PZS43K/S1_352299_front.pdf",
 "labels_packaging": "https://zajac.op4.oklyt.pl/media/private/producers/products/labels_packaging/2025/03/GIACJJ/B352299_labels-packaging.zip"}
```



#### Kluczowe pola:

- **`id`** - id produktu
- **`order_info`** - informacje o zamówieniu
- **`batch`** - batch
- **`front_view`** - plik z rzutem z góry na sofę
- **`labels_packaging`** - plik z etykietami na paczki
- **`material_info`** - zawiera:
    - **`name`** - nasza wewnętrzna nazwa modułu, która pojawia się również na front view. Może jeszcze ulec zmianie.
    - **`material_usage`** - materiały potrzebne do produkcji modułu.
