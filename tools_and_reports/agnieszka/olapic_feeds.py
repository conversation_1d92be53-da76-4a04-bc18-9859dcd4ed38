import csv
import xml.etree.cElementTree as ET
from xml.dom import minidom

from gallery.models import (
    Jetty,
    Watty,
)
from product_feeds.utils import get_id_from_string


def _prepare_data_from_csv(csv_file):
    """Prepares data for feed.
    Returns:
        a list of dictionaries representing products
    """
    data = []
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            color = row['COLOUR']
            product_url = row['ID LINK'].partition('?sharing=true')[0]
            furniture_type = row['TYPE']
            line = row['LINE']
            description = row['DESCRIPTION'].replace('\n', ' ')
            name = f'{furniture_type} {line}' if line else furniture_type
            id_from_url = get_id_from_string(product_url)
            furniture_model = Jetty if 'wardrobe' not in product_url else Watty
            product_unique_id = f'w{id_from_url}' if furniture_model == Watty \
                else str(id_from_url)
            image_url = ''
            try:
                furniture = furniture_model.objects.get(pk=id_from_url)
                try:
                    image_url = furniture.preview.url
                except ValueError:
                    print(f'Furniture {product_unique_id} does not have an image!')
            except furniture_model.DoesNotExist:
                print(
                    f'The furniture of type {furniture_model} with id {id_from_url} '
                    f'does not exist.')

            product = {
                'name': name,
                'product_unique_id': product_unique_id,
                'product_url': product_url,
                'image_url': image_url,
                'description': description,
                'color': color
            }
            data.append(product)
    return data


def produce_olapic_feeds(csv_file):
    """Creates a feed for olapic in xml format.
    Returns:
        a string in xml format
    """
    products_data = _prepare_data_from_csv(csv_file)
    root = ET.Element('Feed')
    products_elem = ET.SubElement(root, 'Products')
    for product in products_data:
        product_elem = ET.SubElement(products_elem, 'Product')
        name = ET.SubElement(product_elem, 'Name')
        name.text = product['name']

        product_unique_id = ET.SubElement(product_elem, 'ProductUniqueID')
        product_unique_id.text = product['product_unique_id']

        product_url = ET.SubElement(product_elem, 'ProductUrl')
        product_url.text = product['product_url']

        image_url = ET.SubElement(product_elem, 'ImageUrl')
        image_url.text = product['image_url']

        description = ET.SubElement(product_elem, 'Description')
        description.text = product['description']

        color = ET.SubElement(product_elem, 'Color')
        color.text = product['color']
    dom = minidom.parseString(ET.tostring(root))
    xml_string = dom.toprettyxml()
    part1, part2 = xml_string.split('?>')
    xml_string = part1 + 'encoding="UTF-8"?>' + part2
    with open('olapic_feeds.xml', 'w') as f:
        f.write(xml_string)
    return xml_string


csvfile = '/home/<USER>/plik.csv'
feeds = produce_olapic_feeds(csvfile)
