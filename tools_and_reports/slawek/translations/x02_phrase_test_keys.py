import json, requests, csv
from pprint import pprint

# phrase:
api_key = ''
locale_pl = '9cc9ab5e88a85ad6a4d409cc07a0af46'
project_id = '2078a6589146aabd57c4feec8d5178e7'  #django

# target locale:

target_locale = "cb7aa1764f11efa4e5d2c0a74c6a7fbd"
target_branch = 'SPANISH'

#ids_for_nuxt = json.load(open('data/0001_keys_nuxt.json', 'r'))
ids_for_django = json.load(open('data/0001_keys_django.json', 'r'))

#nuxt_translations = json.load(open('wynik_z_pl_1000_koniec.json', 'r'))
django_translations = csv.reader(open('translations/all_django.csv', 'r'), delimiter=';')

number_of_already_done = 0
number_of_not_found = 0
number_of_found = 0

number_of_404 = 0
number_of_200 = 0

number_of_translated = 0
number_of_nottranslated = 0

# lets find id for this entry
for entry in django_translations:
    if entry[0] == 'cplus_ash':
        print('ha, jest')
    else:
        continue

    found_key = next((x for x in ids_for_django if entry[0] == x['name']), None)
    if found_key is None:
        print(f'nie znaleziono', {entry[0]})
        number_of_already_done += 1
        continue
    else:
        number_of_found += 1

    print(found_key)
    key_update_parameters = {"branch": target_branch,
                             "locale_id": target_locale,
                             "key_id": found_key['id'],
                             "content": entry[2]}

    # request = requests.get(f'https://api.phrase.com/v2/projects/{project_id}/keys/{found_key["id"]}/translations',
    #                         headers={'Content-Type': 'application/json',
    #                                  'Authorization': f'token {api_key}',
    #                                  'User-Agent': 'Example test app for tylko account'
    #                                  })
    # response = request.json()
    # if len(response) > 2:
    #     number_of_translated += 1
    # else:
    #     number_of_nottranslated += 1
    # if request.status_code == 404:
    #     number_of_404 += 1
    # elif request.status_code == 200:
    #     number_of_200 += 1
    # else:
    #     print(request.status_code)
    #     print(request.content)

print(number_of_already_done)
print(number_of_not_found)
print(number_of_found)
print(number_of_404)
print(number_of_200)

print(number_of_translated)
print(number_of_nottranslated)


# request = requests.patch(f'https://api.phrase.com/v2/projects/{project_id}/translations/863c2a8f500886f98f86920e60951035',
#                          data=json.dumps(key_update_parameters),
#                         headers={'Content-Type': 'application/json',
#                                  'Authorization': f'token {api_key}',
#                                  })
#
# print(request)
# print(request.content)
#
#     request = requests.post(f'https://api.phrase.com/v2/projects/{project_id}/translations', data=json.dumps(key_update_parameters),
#                             headers={'Content-Type': 'application/json',
#                                      'Authorization': f'token {api_key}',
#                                      'User-Agent': 'Example test app for tylko account'
#                                      })
