import json, requests, csv
import time
import pandas as pd
from rich import print
from rich.console import Console
from rich.table import Table

# phrase:
api_key = ''
# project_id = '2078a6589146aabd57c4feec8d5178e7'  # django
project_id = '5ea8fb45eb7b06e4cc830d6d6254f11c'  # nuxt

# target locale:

TARGET_LOCALE = 'es'

if TARGET_LOCALE == 'es':
    target_locale = "da8c1374fdb5206dbe31357cd8884596"
    target_language = 'es'

elif TARGET_LOCALE == 'nl':
    target_locale = "88732dd9b9dc05dfbda9588868dcbb00"
    target_language = 'nl'

target_branch = 'SPANISH'

messed_keys = True


def get_ids_for_keys(project):
    if project == 'nuxt':
        return json.load(open('data/0001_keys_nuxt.json', 'r'))
    elif project == 'django':
        return json.load(open('data/0001_keys_django.json', 'r'))
    else:
        return {}


def get_translations_as_list(project, language):
    if project == 'nuxt':
        if language == 'es':
            return list(csv.reader(open('translations/keys_nuxt_2_es.csv', 'r'), delimiter=';'))
        elif language == 'nl':
            return list(csv.reader(open('translations/nuxt_nl_stripped.csv', 'r'), delimiter=','))
    elif project == 'django':
        if language == 'es':
            return list(csv.reader(open('translations/all_django_es.csv', 'r'), delimiter=';'))
        elif language == 'nl':
            return list(csv.reader(open('translations/all_django_and_nuxt_nl.csv', 'r'), delimiter=';'))
    return []


def get_base_translations_from_phrase(project):
    english_plus_keys = []
    if project == "nuxt":
        file_path = 'translations/en.csv'
    elif project == 'django':
        file_path = 'translations/en_django.csv'
    with open(file_path) as f:
        english_plus_keys = [{k: v for k, v in row.items()}
                             for row in csv.DictReader(f, skipinitialspace=True)]
    return english_plus_keys


table_rows = []

missing_list = {
    'nuxt_es': [],
    'nuxt_nl': [],
    'django_es': [],
    'django_nl': []
}

console = Console()

for project in ['nuxt', 'django']:
    for language in ['es', 'nl']:
        ids_for_translations = get_ids_for_keys(project)
        translation_list = get_translations_as_list(project, language)
        english_plus_keys = get_base_translations_from_phrase(project)

        line_response = [project, language, str(len(ids_for_translations))]

        keys_in_files = 0
        keys_nonenglish = 0

        missing_list_entry = missing_list[f'{project}_{language}']

        if len(translation_list) == 0:
            print(f'oj, babol dla {project} , {language}')
            continue

        # print([x for x in translation_list if len(x) < 2])

        for key in ids_for_translations:

            found_fixed_key = next((x for x in english_plus_keys if key['name'] == x['key_name']), None)
            if found_fixed_key is None:
                print(f'brak {key}')
                continue

            found_line = next((x for x in translation_list if len(x) > 1 and found_fixed_key['en'] == x[1]), None)
            if found_line is not None:
                keys_in_files += 1
                if found_line[1] != found_line[2] and len(found_line[2]) > 0:
                    keys_nonenglish += 1
            else:
                missing_list_entry.append([found_fixed_key['key_name'], found_fixed_key['en'],])

        line_response.append(f'{keys_in_files} - {keys_in_files/len(ids_for_translations)*100:.2f}%')
        line_response.append(f'{keys_nonenglish} - {keys_nonenglish/keys_in_files*100:.2f}%')
        line_response.append(f'{keys_nonenglish/len(ids_for_translations)*100:.2f}%')
        table_rows.append(line_response)
        df = pd.DataFrame(missing_list_entry)
        df.columns = ['key_name', 'en']
        df.to_excel(f'output/missing_{project}_{language}.xlsx', index=None, header=True)
        console.print(f'Missing from translations: {project}_{language} - {len(missing_list_entry)}')

table = Table(show_header=True, header_style="bold magenta")
table.add_column("Project", style="dim", width=12)
table.add_column("Language")
table.add_column("Keys in project")
table.add_column("Keys in files")
table.add_column("Nonenglish keys")
table.add_column("Total Translated")

for row in table_rows:
    table.add_row(*row)

console.print(table)


