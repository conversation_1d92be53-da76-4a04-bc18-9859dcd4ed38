import phrase_api, json
import click
from phrase_api.rest import ApiException
from settings import get_configuration, get_project_settings

translation_keys = []

@click.command()
@click.option('--branch', required=True, help='Name of branch')
@click.option('--project', required=True, help='nuxt/django')
def phrase_get_keys(branch, project):
    project_conf = get_project_settings(project)
    with phrase_api.ApiClient(get_configuration()) as api_client:
        # Create an instance of the API class
        api_instance = phrase_api.KeysApi(api_client)
        project_id = project_conf['project_id']  # str | Project ID (required)
        page = 1  # int | Page number
        per_page = 100  # int | allows you to specify a page size up to 100 items, 25 by default
        sort = 'updated_at'  # str | Sort by field. Can be one of: name, created_at, updated_at.
        order = 'desc'  # str | Order direction. Can be one of: asc, desc.
        keys_search_parameters = phrase_api.KeysSearchParameters(branch=branch, sort=sort, order=order)

        try:
            # List keys
            is_not_empty = True
            while is_not_empty:
                api_response = api_instance.keys_search(project_id, keys_search_parameters, page=page, per_page=per_page)
                for entry in api_response:
                    translation_keys.append(
                        {
                            'id': entry.id,
                            'name': entry.name
                        }
                    )
                if len(api_response) == 0:
                    is_not_empty = False
                page += 1
            json.dump(translation_keys, open(f'data/0001_keys_{project}.json', 'w'))

        except ApiException as e:
            print("Exception when calling KeysApi->keys_list: %s\n" % e)


if __name__ == '__main__':
    phrase_get_keys()
