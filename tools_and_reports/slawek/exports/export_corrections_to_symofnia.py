# -*- coding: utf-8 -*-
from __future__ import print_function
from datetime import datetime
import csv
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from datetime import datetime
from django.core.management.base import BaseCommand

from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from orders.models import Order
from subprocess import Popen
from custom.utils.exports import dump_list_as_csv
from tqdm import tqdm
import os


INVOICE_DATA = [[11515617, '203-3-1-13671', '844-3-1-4'], [10472174, '203-3-1-13684', '844-3-3'], [11439383, '203-3-1-13685', '844-3-3'], [11574841, '203-3-1-13674', '844-3-1-7'], [11383641, '203-3-1-13694', '844-3-1-12'], [11108712, '203-3-1-13687', '844-3-1-16'], [11401586, '203-3-1-13606', '844-3-1-12'], [11354334, '203-3-1-13607', '844-3-1-3'], [11434597, '203-3-1-13613', '844-3-1-4'], [11433557, '203-3-1-13616', '844-3-1-8'], [11326205, '203-3-1-13617', '844-3-1-8'], [11390335, '203-3-1-13611', '844-3-1-7'], [11409647, '203-3-1-13608', '844-3-1-3'], [11431600, '203-3-1-13612', '844-3-1-7'], [11446135, '203-3-1-13604', '844-3-3'], [11445351, '203-3-1-13614', '844-3-1-4'], [11443695, '203-3-1-13605', '844-3-1-12'], [11353598, '203-3-1-13609', '844-3-1-3'], [11387795, '203-3-1-13618', '844-3-1-8'], [11442792, '203-3-1-13615', '844-3-1-4'], [11416983, '203-3-1-13610', '844-3-1-3'], [11057565, '203-3-1-13619', '844-3-3'], [11128740, '203-3-1-13650', '844-3-1-4'], [11490095, '203-3-1-13628', '844-3-1-3'], [11486795, '203-3-1-13625', '844-3-1-5'], [11490588, '203-3-1-13629', '844-3-1-3'], [5281483, '203-3-1-13630', '844-3-1-3'], [11490193, '203-3-1-13631', '844-3-1-3'], [11479062, '203-3-1-13632', '844-3-1-3'], [6919449, '203-3-1-13633', '844-3-1-3'], [11407629, '203-3-1-10424', '844-3-1-4'], [11490973, '203-3-1-13643', '844-3-1-7'], [11495388, '203-3-1-13634', '844-3-1-3'], [11493650, '203-3-1-4688', '844-3-1-7'], [11496085, '203-3-1-13644', '844-3-1-7'], [11495348, '203-3-1-13624', '844-3-4'], [11261018, '203-3-1-13620', '844-3-3'], [11496056, '203-3-1-13651', '844-3-1-4'], [11496146, '203-3-1-13635', '844-3-1-3'], [11499482, '201-3-1-13657', '844-3-1-2'], [11499847, '203-3-1-13621', '844-3-3'], [11501240, '203-3-1-13655', '844-3-1-6'], [11501020, '203-3-1-13645', '844-3-1-7'], [11501296, '203-3-1-13626', '844-3-1-12'], [11501979, '203-3-1-13622', '844-3-3'], [11502910, '203-3-1-13652', '844-3-1-4'], [11501727, '203-3-1-13656', '844-3-1-8'], [9429201, '203-3-1-13646', '844-3-1-7'], [11438179, '203-3-1-13636', '844-3-1-3'], [11265237, '203-3-1-13642', '844-3-1-10'], [10856868, '203-3-1-13647', '844-3-1-7'], [11504878, '203-3-1-13623', '844-3-3'], [11504937, '203-3-1-13637', '844-3-1-3'], [11495955, '203-3-1-13638', '844-3-1-3'], [10989029, '203-3-1-13653', '844-3-1-4'], [10891157, '203-3-1-13648', '844-3-1-7'], [11502848, '203-3-1-13641', '844-3-1-20'], [11508483, '203-3-1-13639', '844-3-1-3'], [11181610, '203-3-1-13627', '844-3-1-3'], [11510557, '203-3-1-13640', '844-3-1-3'], [11508815, '203-3-1-13649', '844-3-1-7'], [11505605, '203-3-1-13654', '844-3-1-4'], [11503981, '203-3-1-13658', '844-3-3'], [11456421, '203-3-1-13667', '844-3-1-3'], [11366554, '203-3-1-13659', '844-3-4'], [11538571, '203-3-1-13664', '844-3-1-7'], [11538830, '203-3-1-13663', '844-3-1-7'], [11532909, '203-3-1-13660', '844-3-4'], [11401048, '203-3-1-13668', '844-3-1-12'], [6781370, '203-3-1-10444', '844-3-4'], [11336616, '203-3-1-10472', '844-3-1-8'], [11292901, '203-3-1-13661', '844-3-4'], [11548572, '203-3-1-13661', '844-3-4'], [10966310, '203-3-1-12393', '844-3-1-3'], [10996443, '203-3-1-13662', '844-3-1-7'], [11510187, '203-3-1-13665', '844-3-1-10'], [11017088, '203-3-1-13666', '844-3-1-3'], [10983701, '203-3-1-11904', '844-3-1-3'], [11374788, '203-3-1-13561', '844-3-1-7'], [11074242, '203-3-1-8396', '844-3-1-3'], [10972327, '203-3-1-8891', '844-3-1-4'], [11387644, '203-3-1-11534', '844-3-3'], [11028298, '203-3-1-13562', '844-3-1-7'], [11386940, '203-3-1-13556', '844-3-1-3'], [8324099, '203-3-1-13563', '844-3-1-7'], [11333384, '203-3-1-13567', '844-3-1-21'], [6948229, '203-3-1-13553', '844-3-1-5'], [7171036, '203-3-1-10788', '844-3-1-12'], [9519390, '203-3-1-11391', '844-3-1-5'], [11196170, '203-3-1-13550', '844-3-3'], [2422838, '203-3-1-13555', '844-3-1-12'], [11385904, '203-3-1-13565', '844-3-1-4'], [8074738, '203-3-1-13551', '844-3-3'], [10996889, '203-3-1-13558', '844-3-1-3'], [11389742, '203-3-1-13552', '844-3-4'], [11390428, '203-3-1-13566', '844-3-1-4'], [10901494, '203-3-1-13559', '844-3-1-3'], [11390964, '203-3-1-13569', '844-3-1-8'], [11391114, '203-3-1-13564', '844-3-1-7'], [10284798, '203-3-1-13570', '844-3-1-8'], [11391321, '203-3-1-13560', '844-3-1-3'], [11350648, '201-3-1-13571', '844-3-1-2'], [11390567, '203-3-1-12386', '844-3-1-3'], [11353901, '203-3-1-13554', '844-3-1-12'], [11334498, '203-3-1-13557', '844-3-1-3'], [10607263, '203-3-1-13528', '844-3-1-3'], [10858384, '203-3-1-13529', '844-3-1-3'], [11279407, '203-3-1-13530', '844-3-1-3'], [11374702, '203-3-1-13531', '844-3-1-3'], [9489041, '203-3-1-13540', '844-3-1-7'], [11377345, '203-3-1-13541', '844-3-1-7'], [11363822, '203-3-1-13532', '844-3-1-3'], [11271760, '203-3-1-13542', '844-3-1-7'], [10958576, '203-3-1-13547', '844-3-1-8'], [11308269, '203-3-1-13545', '844-3-1-4'], [11380691, '203-3-1-13524', '844-3-4'], [11381640, '203-3-1-13525', '844-3-4'], [11382170, '203-3-1-13520', '844-3-3'], [10094174, '203-3-1-13521', '844-3-3'], [11382313, '203-3-1-13543', '844-3-1-7'], [11241506, '203-3-1-13527', '844-3-1-3'], [11345199, '203-3-1-13548', '844-3-1-8'], [11031191, '203-3-1-13533', '844-3-1-3'], [10327539, '203-3-1-13534', '844-3-1-3'], [11382782, '203-3-1-13535', '844-3-1-3'], [11382993, '203-3-1-13534', '844-3-1-3'], [11382862, '203-3-1-13522', '844-3-3'], [11369569, '203-3-1-13523', '844-3-3'], [11190740, '203-3-1-13536', '844-3-1-3'], [11318204, '203-3-1-13526', '844-3-4'], [11089141, '203-3-1-13537', '844-3-1-3'], [11376703, '203-3-1-13538', '844-3-1-3'], [10318504, '203-3-1-13546', '844-3-1-15'], [11368422, '203-3-1-13539', '844-3-1-3'], [10974683, '203-3-1-13544', '844-3-1-7'], [11376703, '203-3-1-13538', '844-3-1-3'], [11225608, '203-3-1-13549', '844-3-3'], [11248034, '203-3-1-13388', '844-3-1-3'], [11325500, '203-3-1-13397', '844-3-1-20'], [11316941, '203-3-1-13389', '844-3-1-3'], [11327595, '203-3-1-13399', '844-3-1-4'], [11327072, '203-3-1-13385', '844-3-3'], [11327620, '203-3-1-13400', '844-3-1-4'], [9820241, '203-3-1-13386', '844-3-3'], [11328491, '203-3-1-13390', '844-3-1-3'], [153804, '203-3-1-13391', '844-3-1-3'], [11329058, '203-3-1-10421', '844-3-3'], [10991190, '203-3-1-13392', '844-3-1-3'], [11122980, '203-3-1-13393', '844-3-1-3'], [10946214, '203-3-1-13401', '844-3-1-4'], [11329032, '203-3-1-13384', '844-3-4'], [11330757, '203-3-1-13398', '844-3-1-7'], [11330715, '203-3-1-13402', '844-3-1-4'], [11330154, '203-3-1-13394', '844-3-1-3'], [11329265, '203-3-1-13387', '844-3-1-5'], [11332542, '203-3-1-13403', '844-3-1-4'], [9873375, '203-3-1-13395', '844-3-1-3'], [11333563, '203-3-1-13404', '844-3-1-4'], [11325851, '203-3-1-13405', '844-3-1-4'], [10984714, '203-3-1-13396', '844-3-1-3'], [8091062, '203-3-1-11354', '844-3-1-4'], [6627738, '203-3-1-13441', '844-3-1-8'], [11194130, '203-3-1-13445', '844-3-1-18'], [10884180, '203-3-1-13409', '844-3-3'], [7534, '203-3-1-1055', '844-3-1-3'], [11343128, '203-3-1-13437', '844-3-1-4'], [11344371, '203-3-1-13418', '844-3-1-3'], [11319365, '203-3-1-13568', '844-3-1-8'], [11345688, '203-3-1-12711', '844-3-1-3'], [11345936, '203-3-1-13442', '844-3-1-8'], [11346056, '203-3-1-8055', '844-3-1-3'], [11345412, '203-3-1-13419', '844-3-1-3'], [11347011, '203-3-1-13419', '844-3-1-3'], [11347363, '203-3-1-13420', '844-3-1-3'], [11346097, '203-3-1-13410', '844-3-3'], [11348120, '203-3-1-13438', '844-3-1-4'], [10943630, '203-3-1-13411', '844-3-3'], [11337115, '203-3-1-13432', '844-3-1-7'], [11342004, '203-3-1-13433', '844-3-1-7'], [11342454, '203-3-1-13421', '844-3-1-3'], [11349191, '203-3-1-13434', '844-3-1-7'], [11325280, '203-3-1-13422', '844-3-1-3'], [7551131, '203-3-1-13423', '844-3-1-3'], [11349698, '203-3-1-13424', '844-3-1-3'], [11349878, '203-3-1-13417', '844-3-1-12'], [11347246, '203-3-1-13412', '844-3-3'], [11341552, '203-3-1-13435', '844-3-1-7'], [11282811, '203-3-1-13406', '844-3-4'], [11350410, '203-3-1-13407', '844-3-4'], [10861333, '203-3-1-13430', '844-3-1-20'], [11350090, '203-3-1-13425', '844-3-1-3'], [11158210, '203-3-1-13439', '844-3-1-4'], [10904538, '203-3-1-13413', '844-3-3'], [11006949, '203-3-1-13414', '844-3-3'], [11352714, '203-3-1-13426', '844-3-1-3'], [11202694, '203-3-1-13443', '844-3-1-8'], [11311287, '203-3-1-13408', '844-3-4'], [11354446, '203-3-1-13427', '844-3-1-3'], [11008269, '203-3-1-13428', '844-3-1-3'], [11354208, '203-3-1-13444', '844-3-1-8'], [11336132, '203-3-1-13429', '844-3-1-3'], [11353018, '203-3-1-9861', '844-3-1-7'], [11354416, '203-3-1-13415', '844-3-1-5'], [11354838, '203-3-1-13416', '844-3-1-5'], [11302060, '203-3-1-13436', '844-3-1-7'], [11354016, '203-3-1-13440', '844-3-1-4'], [11163237, '203-3-1-13431', '844-3-1-10'], [11327514, '203-3-1-13455', '844-3-1-3'], [11302284, '203-3-1-13450', '844-3-3'], [11345275, '203-3-1-13468', '844-3-1-4'], [463653, '203-3-1-13447', '844-3-4'], [11359285, '203-3-1-13456', '844-3-1-3'], [10295977, '203-3-1-13473', '844-3-1-21'], [11312826, '203-3-1-13469', '844-3-1-4'], [11355198, '203-3-1-13457', '844-3-1-3'], [11296710, '203-3-1-13458', '844-3-1-3'], [9645597, '203-3-1-13470', '844-3-1-4'], [11272555, '203-3-1-13471', '844-3-1-4'], [11362127, '203-3-1-13459', '844-3-1-3'], [10918141, '203-3-1-13460', '844-3-1-3'], [11328902, '203-3-1-13451', '844-3-3'], [10859321, '203-3-1-13472', '844-3-1-4'], [11249751, '203-3-1-13466', '844-3-1-20'], [11364794, '203-3-1-12042', '844-3-4'], [11364684, '203-3-1-13454', '844-3-1-3'], [10730461, '203-3-1-13461', '844-3-1-3'], [11365637, '203-3-1-13448', '844-3-4'], [11193596, '203-3-1-13462', '844-3-1-3'], [11367852, '203-3-1-13452', '844-3-3'], [11330023, '203-3-1-13463', '844-3-1-3'], [11081778, '203-3-1-12221', '844-3-1-8'], [11367611, '203-3-1-13449', '844-3-4'], [11221808, '203-3-1-13453', '844-3-1-5'], [10914959, '203-3-1-13464', '844-3-1-3'], [11353773, '203-3-1-13465', '844-3-1-3'], [11236572, '203-3-1-13467', '844-3-1-7'], [11006949, '203-3-1-13414', '844-3-3'], [10669955, '203-3-1-13446', '844-3-4'], [11227031, '203-3-1-13370', '844-3-3'], [11306499, '203-3-1-13375', '844-3-1-3'], [11034576, '203-3-1-13371', '844-3-3'], [11274977, '203-3-1-13376', '844-3-1-3'], [11312405, '203-3-1-13377', '844-3-1-3'], [11196020, '203-3-1-13374', '844-3-1-5'], [11313636, '203-3-1-13382', '844-3-4'], [11314104, '203-3-1-13372', '844-3-3'], [8311196, '203-3-1-11479', '844-3-1-7'], [11314653, '203-3-1-13378', '844-3-1-3'], [11279877, '203-3-1-13373', '844-3-3'], [11317857, '203-3-1-13379', '844-3-1-3'], [11297528, '203-3-1-13381', '844-3-1-4'], [11113126, '203-3-1-13380', '844-3-1-3'], [11319835, '203-3-1-13383', '844-3-4'], [11319195, '203-3-1-8034', '844-3-1-3'], [11100495, '203-3-1-13290', '844-3-1-3'], [11116907, '203-3-1-13295', '844-3-1-8'], [11147958, '203-3-1-13287', '844-3-3'], [11091085, '203-3-1-13296', '844-3-1-8'], [11154208, '203-3-1-13288', '844-3-4'], [11156628, '203-3-1-13291', '844-3-1-3'], [10962211, '203-3-1-13292', '844-3-1-3'], [11158749, '203-3-1-13294', '844-3-1-7'], [11160721, '203-3-1-13293', '844-3-1-3'], [11160153, '203-3-1-13289', '844-3-4'], [11169853, '203-3-1-13302', '844-3-4'], [11172150, '203-3-1-13314', '844-3-1-4'], [10959232, '203-3-1-13304', '844-3-1-3'], [11172593, '203-3-1-13297', '844-3-3'], [11152216, '203-3-1-13305', '844-3-1-3'], [11174800, '203-3-1-13315', '844-3-1-4'], [11175721, '203-3-1-13306', '844-3-1-3'], [10739717, '203-3-1-13298', '844-3-3'], [10765871, '203-3-1-13299', '844-3-3'], [10935424, '203-3-1-13324', '844-3-1-8'], [11177722, '203-3-1-13324', '844-3-1-8'], [10817192, '203-3-1-13307', '844-3-1-3'], [11178845, '203-3-1-13316', '844-3-1-4'], [11178680, '203-3-1-13300', '844-3-3'], [11180252, '203-3-1-13317', '844-3-1-4'], [11176146, '203-3-1-13325', '844-3-1-8'], [10960087, '203-3-1-13308', '844-3-1-3'], [11182251, '203-3-1-13313', '844-3-1-7'], [11184274, '203-3-1-13326', '844-3-1-8'], [10698032, '203-3-1-13309', '844-3-1-3'], [11187145, '203-3-1-13318', '844-3-1-4'], [11183410, '203-3-1-10702', '844-3-1-8'], [11003354, '203-3-1-13301', '844-3-3'], [11177554, '203-3-1-13319', '844-3-1-4'], [10589213, '203-3-1-13320', '844-3-1-4'], [10101935, '203-3-1-13303', '844-3-4'], [11015850, '203-3-1-13323', '844-3-1-15'], [10513241, '203-3-1-13310', '844-3-1-3'], [11192340, '203-3-1-13321', '844-3-1-4'], [11194168, '203-3-1-13311', '844-3-1-3'], [11193919, '203-3-1-13312', '844-3-1-3'], [11195909, '203-3-1-13327', '844-3-1-8'], [11172097, '203-3-1-11007', '844-3-1-3'], [10970607, '203-3-1-13322', '844-3-1-4'], [11040358, '203-3-1-13336', '844-3-1-3'], [11082934, '203-3-1-13335', '844-3-1-3'], [11126577, '203-3-1-13337', '844-3-1-3'], [11120758, '203-3-1-13343', '844-3-1-7'], [10809173, '203-3-1-13338', '844-3-1-3'], [11207533, '203-3-1-13342', '844-3-1-7'], [11060591, '203-3-1-13328', '844-3-4'], [11053355, '203-3-1-13344', '844-3-1-7'], [11164030, '203-3-1-13348', '844-3-1-18'], [10742913, '203-3-1-13329', '844-3-4'], [6755836, '203-3-1-9671', '844-3-1-3'], [11178935, '203-3-1-13334', '844-3-1-12'], [4831696, '203-3-1-13332', '844-3-1-5'], [11225854, '203-3-1-13340', '844-3-1-3'], [11177918, '203-3-1-13331', '844-3-3'], [11181693, '203-3-1-13341', '844-3-1-3'], [11228009, '203-3-1-13347', '844-3-1-8'], [10963994, '203-3-1-13346', '844-3-1-4'], [11233290, '203-3-1-13345', '844-3-1-7'], [11192422, '203-3-1-13333', '844-3-1-5'], [10552495, '203-3-1-13339', '844-3-1-3'], [10976862, '203-3-1-13282', '844-3-1-3'], [11112377, '203-3-1-13283', '844-3-1-3'], [11032601, '203-3-1-13284', '844-3-1-3'], [10872851, '203-3-1-13286', '844-3-1-7'], [11073681, '203-3-1-2400', '844-3-1-5'], [11125349, '203-3-1-13285', '844-3-1-3'], [10953074, '203-3-1-13330', '844-3-3'], [11062970, '203-3-1-13275', '844-3-1-4'], [11068875, '203-3-1-13276', '844-3-1-4'], [8812678, '203-3-1-13272', '844-3-1-3'], [8845992, '203-3-1-13269', '844-3-3'], [10880381, '203-3-1-13267', '844-3-4'], [11073805, '203-3-1-11371', '844-3-1-7'], [10871654, '203-3-1-13270', '844-3-3'], [10801054, '203-3-1-13280', '844-3-1-8'], [11078212, '203-3-1-11238', '844-3-4'], [9648164, '203-3-1-12221', '844-3-1-8'], [11051824, '203-3-1-13268', '844-3-4'], [10908441, '203-3-1-13277', '844-3-1-4'], [11087132, '203-3-1-13278', '844-3-1-4'], [11089091, '203-3-1-13281', '844-3-1-8'], [10200508, '203-3-1-7102', '844-3-1-3'], [10886960, '203-3-1-13279', '844-3-1-4'], [11090867, '203-3-1-10874', '844-3-1-7'], [10854275, '203-3-1-13274', '844-3-1-3'], [9762810, '203-3-1-13121', '844-3-3'], [10089269, '203-3-1-13115', '844-3-1-4'], [10558634, '203-3-1-13114', '844-3-1-21'], [10811080, '203-3-1-13119', '844-3-4'], [9249606, '203-3-1-12783', '844-3-1-3'], [10904178, '203-3-1-13118', '844-3-3'], [10844321, '203-3-1-13117', '844-3-1-3']]
result = {}
for entry in INVOICE_DATA:
    if entry[0] in INVOICE_DATA:
        print('duplikat?')
        exit(-1)

    result[entry[0]] = entry

invs = ['']
invoices = Invoice.objects.filter(status=InvoiceStatus.CORRECTING, pk__gte=23203, pk__lte=23518, issued_at__year=2018).order_by('id')

len(invoices)

doc = []
doc2 = []
for i in tqdm(invoices):
    to_dict = i.to_dict()
    company = to_dict['invoice_address']['company_name'] and to_dict['invoice_address']['vat']
    private = u'{0} {1}'.format(to_dict['invoice_address']['first_name'],
                                to_dict['invoice_address']['last_name'])
    nazwa = u'{0} {1}'.format(to_dict['invoice_address']['company_name'],
                              private) if company else private
    diff = i.to_diff_dict()
    try:
        attach = Invoice.objects.get(order=i.order.pk, status=InvoiceStatus.ENABLED)
    except:
        print(':/')
        attach = i.corrected_invoice
    if i.order.id not in result:
        print('?')
        continue
    account = result[i.order.id][1]

    attach_to_dict = attach.to_dict()

    currency_code = attach.order.get_region().currency.code
    # lets do na minus
    row = [i.issued_at.strftime('%d.%m.%Y'), i.sell_at.strftime('%d.%m.%Y'), i.sell_at.strftime('%d.%m.%Y'),
           nazwa, 'FKS', '', i.pretty_id,
           u'{} {}'.format(i.order.pk, u'Wrong exchange date/ Zła data kursu'),
           to_dict['net_value'], to_dict['total_value'], - to_dict['net_value'] + to_dict['total_value'],
           currency_code, to_dict['exchange_rate'],
           account, to_dict['total_value'],
           result[i.order.id][2], to_dict['net_value'],
           '221-1', - to_dict['net_value'] + to_dict['total_value'], '', '', '', '',
           to_dict['net_value'] if - to_dict['net_value'] + to_dict['total_value'] > 0 else '', - to_dict['net_value'] + to_dict['total_value'] if - to_dict['net_value'] + to_dict['total_value'] > 0 else '', '', '', '', '', '', '', to_dict['net_value'] if - to_dict['net_value'] + to_dict['total_value'] == 0 else '', '0' if - to_dict['net_value'] + to_dict['total_value'] == 0 else '',  # inne kwoty nie 23
           attach.pretty_id, attach.issued_at.strftime('%d.%m.%Y'), 'sprzedaż',
           ]
    doc.append(row)

    # lets do na plus
    row = [i.issued_at.strftime('%d.%m.%Y'), i.sell_at.strftime('%d.%m.%Y'), i.sell_at.strftime('%d.%m.%Y'),
           nazwa, 'FKS', '', i.pretty_id,
           u'{} {}'.format(i.order.pk, u'Wrong exchange date/ Zła data kursu'),
           -to_dict['net_value'], -to_dict['total_value'], -(- to_dict['net_value'] + to_dict['total_value']),
           currency_code, attach_to_dict['exchange_rate'],
           account, -to_dict['total_value'],
           result[i.order.id][2], -to_dict['net_value'],
           '221-1', -(- to_dict['net_value'] + to_dict['total_value']), '', '', '', '',
           -to_dict['net_value'] if - to_dict['net_value'] + to_dict['total_value'] > 0 else '', - (- to_dict['net_value'] + to_dict['total_value']) if - to_dict['net_value'] + to_dict['total_value'] > 0 else '', '', '', '', '', '', '', -to_dict['net_value'] if - to_dict['net_value'] + to_dict['total_value'] == 0 else '', '0' if - to_dict['net_value'] + to_dict['total_value'] == 0 else '',  # inne kwoty nie 23
           attach.pretty_id, attach.issued_at.strftime('%d.%m.%Y'), 'sprzedaż',
           ]
    doc2.append(row)

def sort_function(x):
    tmp = x[6].replace('FKS','').split('/')[:3]
    return '{}{}{}'.format(tmp[2],tmp[1],tmp[0])

sorted(doc, key=sort_function)

print((len(doc)))
headers = ['data_wystawienia', 'data_sprzedazy', 'data_platnosci', 'skrot', 'typ_dokumentu',
           'numer_ewidencyjny', 'numer_faktury', 'tresc_dokumentu', 'netto_calego_dokumentu',
           'brutto_calego_dokumentu', 'vat_calego_dokumentu', 'waluta', 'kurs', 'konto_1', 'kwota_1', 'konto_2',
           'kwota_2', 'konto_3', 'kwota_3', 'konto_4', 'kwota_4', 'konto_5', 'kwota_5', 'netto_23', 'vat_23',
           'netto_7', 'vat_7', 'netto_5', 'vat_5', 'netto_3', 'vat_3', 'netto_0', 'vat_0', 'dok_powiazany',
           'data_powiazana', 'rodzaj_transakcji']
dump_list_as_csv(doc, output=open('/tmp/symfonia_export.csv_tmp', 'w'), mail=None, headers=headers, delimiter=';')
call = Popen('iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(
    '/tmp/symfonia_export.csv'), shell=True)
call.wait()

dump_list_as_csv(doc2, output=open('/tmp/symfonia_export_2.csv_tmp', 'w'), mail=None, headers=headers, delimiter=';')
call = Popen('iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(
    '/tmp/symfonia_export_2.csv'), shell=True)
call.wait()
