from __future__ import print_function

from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from orders.models import Order
import datetime
from tqdm import tqdm

invoices_to_add = [['9649376', '2018-09-07', ],
                   ['10881760', '2018-10-23', ],
                   ['11127895', '2018-10-23', ],
                   ['11241374', '2018-10-23', ],
                   ['10860506', '2018-10-23', ],
                   ['11244590', '2018-10-23', ],
                   ['11247520', '2018-10-23', ],
                   ['11250636', '2018-10-23', ],
                   ['11255499', '2018-10-23', ],
                   ['11185089', '2018-10-23', ],
                   ['11195734', '2018-10-23', ],
                   ['11193547', '2018-10-23', ],
                   ['10974873', '2018-10-23', ],
                   ['10942015', '2018-10-23', ],
                   ['11120337', '2018-10-24', ],
                   ['11236122', '2018-10-24', ],
                   ['11140452', '2018-10-24', ],
                   ['10535227', '2018-10-24', ],
                   ['10965996', '2018-10-24', ],
                   ['6084881', '2018-10-24', ],
                   ['11186327', '2018-10-24', ],
                   ['11216677', '2018-10-24', ],
                   ['11249215', '2018-10-24', ],
                   ['11258650', '2018-10-24', ],
                   ['9429720', '2018-10-31', ],
                   ['9539326', '2018-10-31', ],
                   ['10835941', '2018-10-31', ],
                   ['723321', '2018-10-31', ],
                   ['11359389', '2018-10-31', ],
                   ['9457563', '2018-10-31', ],
                   ['2489089', '2018-10-31', ],
                   ['11358678', '2018-10-31', ],
                   ['9588429', '2018-10-31', ],
                   ['11315928', '2018-10-31', ],
                   ['11346117', '2018-10-31', ],
                   ['11342714', '2018-11-01', ],
                   ['11389979', '2018-11-01', ],
                   ['11394163', '2018-11-01', ],
                   ['11099678', '2018-11-01', ],
                   ['11397053', '2018-11-01', ],
                   ['11086678', '2018-11-01', ],
                   ['11398258', '2018-11-01', ],
                   ['11400214', '2018-11-01', ],
                   ['11324493', '2018-11-01', ],
                   ['11346639', '2018-11-01', ],
                   ['11401791', '2018-11-01', ],
                   ['11350387', '2018-11-01', ],
                   ['11402065', '2018-11-01', ],
                   ['10792897', '2018-11-01', ],
                   ['11402143', '2018-11-01', ],
                   ['11397007', '2018-11-01', ],
                   ['10865068', '2018-11-07', ],
                   ['11495113', '2018-11-07', ],
                   ['11599596', '2018-11-07', ],
                   ['11632117', '2018-11-07', ],
                   ['11389437', '2018-11-07', ],
                   ['11642698', '2018-11-07', ],
                   ['11375461', '2018-11-07', ],
                   ['11651174', '2018-11-07', ],
                   ['11651877', '2018-11-07', ],
                   ['11504260', '2018-11-07', ],
                   ['11653889', '2018-11-07', ],
                   ['11656116', '2018-11-07', ],
                   ['10939250', '2018-11-07', ],
                   ['11369687', '2018-11-07', ],
                   ['11657141', '2018-11-07', ],
                   ['9504297', '2018-11-08', ],
                   ['11319644', '2018-11-08', ],
                   ['11558238', '2018-11-08', ],
                   ['11633223', '2018-11-08', ],
                   ['9669079', '2018-11-01', ],
                   ['11399816', '2018-11-01', ],
                   ['9613507', '2018-09-03', ],
                   ['8240346', '2018-08-28', ], # 2018-08-17, 2018-08-28
                   ['9587539', '2018-09-02', ],
                   ['9930024', '2018-09-13', ],
                   ['9538694', '2018-09-15', ],
                   ['10217902', '2018-09-23', ],
                   ['10264184', '2018-09-29', ],
                   #['12401037', '2018-06-11', ], #brak w systemie
                   ['10560534', '2018-10-01', ],
                   ['9802532', '2018-09-09', ],  # 2018-10-04 -> 2018-09-09
                   ['11563842', '2018-11-07', ],
                   #['11221975', '2018-11-06', ], # nie ma takiego zamóœienia
                   ['11722212', '2018-11-14', ]  # tylko na produkcji
                   ]

hours_to_add = 0
minutes_to_add = 0

day_to_use = 15


#lets get all enabled invoice after this date , ,
invoice_to_change_numbers = Invoice.objects.filter(status=InvoiceStatus.ENABLED, issued_at__gte='2018-11-%s' % day_to_use).order_by('issued_at').values_list('id', flat=True)

print(list(invoice_to_change_numbers))

#lets change from enabled to disabled
# before adding new one, lets disable old ones, to have proper numbers
Invoice.objects.filter(id__in=invoice_to_change_numbers).update(status=InvoiceStatus.DISABLED)


for index, entry in enumerate(tqdm(invoices_to_add)):
    #first check
    order = Order.objects.get(pk=entry[0])
    if not order.paid_at:
        raise Exception("no paid at date")
    if not (order.paid_at.year == int(entry[1].split('-')[0]) and order.paid_at.month == int(entry[1].split('-')[1]) and order.paid_at.day == int(entry[1].split('-')[2])):
        print(order.paid_at.year == int(entry[1].split('-')[0]), order.paid_at.month == int(entry[1].split('-')[1]), order.paid_at, entry)
        raise Exception("wrong paid at date")

    # lets get all
    invoice = Invoice.objects.create(order=order, sell_at=order.paid_at, issued_at=datetime.datetime(year=2018, month=11, day=day_to_use, hour=1+hours_to_add, minute=0 + minutes_to_add),
                                                         currency_symbol=order.get_region().currency.symbol)
    minutes_to_add += 1
    if minutes_to_add > 58:
        hours_to_add += 1
        minutes_to_add = 0


for inv in tqdm(invoice_to_change_numbers):
    invoice_entry = Invoice.objects.get(pk=inv)
    invoice_entry.status = InvoiceStatus.ENABLED
    invoice_entry.pretty_id = invoice_entry._generate_pretty_id()
    invoice_entry.save()
