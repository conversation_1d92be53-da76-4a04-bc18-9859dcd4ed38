from django.core.paginator import Paginator
from tqdm import tqdm

from customer_service.models import CSOrder
from orders.enums import OrderStatus
from orders.models import Order


def bulk_create_cs_orders(orders):
    for page in tqdm(Paginator(orders, 50000)):
        print(page.number)
        cs_orders = []
        for order in page.object_list:
            if not any([getattr(order, field_name) for field_name in CSOrder.ORDER_REQUIRED_FIELD_NAMES]):
                continue

            cs_order = CSOrder(
                id=order.id,
                status=order.status,
                first_name=order.first_name or '',
                invoice_first_name=order.invoice_first_name or '',
                last_name=order.last_name or '',
                invoice_last_name=order.invoice_last_name or '',
                company_name=order.company_name or '',
                email=order.email or '',
                invoice_email=order.invoice_email or '',
                phone=order.phone or '',
                city=order.city or '',
                invoice_city=order.invoice_city or '',
                street_address_1=order.street_address_1 or '',
                invoice_street_address_1=order.invoice_street_address_1 or '',
                street_address_2=order.street_address_2 or '',
                invoice_street_address_2=order.invoice_street_address_2 or '',
                owner_username=order.owner.username,
                product_ids=list(order.product_set.values_list('pk', flat=True))
            )
            cs_orders.append(cs_order)
        CSOrder.objects.bulk_create(cs_orders, ignore_conflicts=True)


orders = Order.objects.exclude(status=OrderStatus.CART).order_by('-id')
bulk_create_cs_orders(orders)

carts = Order.objects.filter(status=OrderStatus.CART).order_by('-id')
bulk_create_cs_orders(carts)
