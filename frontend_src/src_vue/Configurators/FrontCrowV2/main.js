import Vue from 'vue';
import Vuex from 'vuex';
import VueRouter from 'vue-router';
import VueI18n from 'vue-i18n';
import Fragment from 'vue-fragment';

import localizedUrlPlugin from '@configurator-common/plugins/localizedUrlPlugin';
import { initializeSentryLogger } from '@configurator-common/lib/sentry';
import { TyApiPlugin, TyModalPlugin, TyScrollTracker } from '@tylko_ui/plugins/';
import { CustomFormatter } from '@vue-locale/core';
import getCrowLocales from '@vue-locale/locale-loaders/getCrowLocales';
import { injectConfiguratorIntoWindow, isConfiguratorTestModeAvailable } from '@configurator-common/globals';
import { getBookcaseConfigurator } from './core/bookcaseTestCfg';
import store from './store/index';
import App from './App';
import '../../../src/scss/crow/crow-nuxt.scss';


Vue.use(localizedUrlPlugin);
Vue.use(Vuex);
Vue.use(VueRouter);
Vue.use(TyApiPlugin, { store });
Vue.use(TyScrollTracker, { store });
Vue.use(TyModalPlugin, {
  store,
  options: {
    container: {
      id: 'vueModalWrapper',
    },
  },
});
Vue.use(VueI18n);
Vue.use(Fragment.Plugin);

initializeSentryLogger(Vue);

const mountPointID = '#vue-row-configurator';
const mountPointElement = document.querySelector(mountPointID);
const { language } = window.cstm_i18n;
const messages = getCrowLocales();

const i18n = new VueI18n({
  locale: language === 'no' ? 'nb' : language,
  messages,
  formatter: new CustomFormatter(language === 'no' ? 'nb' : language),
});

new Vue({ // eslint-disable-line
  el: mountPointID,
  store,
  i18n,
  components: { App },
  propsData: { ...mountPointElement.dataset },
  props: {
    productPrice: {
      required: true,
      type: String,
    },
    promoData: {
      required: true,
      type: String,
    },
    seasonSale: {
      required: false,
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      isMobile: false,
      lang: language,
    };
  },
  created() {
    const defaultProductPrice = this.productPrice;
    const { promoData } = this;

    if (this.seasonSale !== undefined) {
      this.$store.dispatch('commonPrices/SET_SEASON_SALE', JSON.parse(this.seasonSale));
    }

    this.$store.dispatch('commonPrices/SET_DEFAULT_PRODUCT_PRICE', parseFloat(defaultProductPrice));
    this.$store.dispatch('commonPrices/SET_PROMO_DATA', JSON.parse(promoData));
    this.isMobile = window.matchMedia('(max-width: 1023px)').matches;
    this.$store.dispatch('ui/UPDATE_IS_MOBILE', window.matchMedia('(max-width: 1023px)').matches);

    window.addEventListener('resize', e => {
      this.isMobile = window.matchMedia('(max-width: 1023px)').matches;
      this.$store.dispatch('ui/UPDATE_IS_MOBILE', window.matchMedia('(max-width: 1023px)').matches);
    });

    if (isConfiguratorTestModeAvailable()) {
      const configuratorUpdateCall = (key, value) => this.$store.dispatch('configuration/UPDATE_CONFIGURATOR', {
        key, value, release: true, holdActiveRow: false,
      });
      const bookcaseConfigurator = getBookcaseConfigurator(configuratorUpdateCall);
      injectConfiguratorIntoWindow(bookcaseConfigurator);
    }
  },
  destroy() {
    window.removeEventListener('resize');
  },
  template: '<App/>',
});
