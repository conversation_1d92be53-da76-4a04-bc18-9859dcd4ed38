export default {
  GET_FURNITURE_STYLE(state) {
    return state.furnitureStyle;
  },
  GET_WIDTH(state) {
    return state.width;
  },
  GET_HEIGHT(state) {
    return state.height;
  },
  GET_DEPTH(state) {
    return state.depth;
  },
  GET_DENSITY(state) {
    return state.density;
  },
  GET_BACK_PANELS(state) {
    return state.backPanels;
  },
  GET_MATERIAL(state) {
    return state.material;
  },
  GET_PREVIOUS_MATERIAL(state) {
    return state.previousMaterial;
  },
  GET_PREVIOUS_OTHER_MATERIAL(state) {
    return state.previousOtherMaterial;
  },
  GET_TOP_STORAGE(state) {
    return state.topStorage;
  },
  GET_TOP_STORAGE_HEIGHT(state) {
    return state.topStorageHeight;
  },
  GET_BOTTOM_STORAGE(state) {
    return state.bottomStorage;
  },
  GET_BOTTOM_STORAGE_HEIGHT(state) {
    return state.bottomStorageHeight;
  },
  GET_ROW_AMOUNT(state) {
    return Object.keys(state.rows).length;
  },
  GET_ROW_HEIGHT(state, _, rootState) {
    return rootState.activeRow !== null ? state.rows[rootState.activeRow].rowHeight : null;
  },
  GET_ROW_DOORS(state, _, rootState) {
    return rootState.activeRow !== null ? state.rows[rootState.activeRow].rowDoors : null;
  },
  GET_ROW_DRAWERS(state, _, rootState) {
    return rootState.activeRow !== null ? state.rows[rootState.activeRow].rowDrawers : null;
  },
  GET_ROW_HEIGHTS_ARRAY(state) {
    return Object.entries(state.rows)
      .sort(([id1], [id2]) => id1 - id2)
      .map(([, row]) => row.rowHeight);
  },
  GET_IS_BOTTOM_STORAGE_SELECTED(state, getters, rootState) {
    return getters.GET_BOTTOM_STORAGE && rootState.activeRow === -1;
  },
  GET_IS_TOP_STORAGE_SELECTED(state, getters, rootState) {
    return getters.GET_TOP_STORAGE && rootState.activeRow === getters.GET_ROW_AMOUNT;
  },
  GET_IS_STORAGE_SELECTED(state, getters) {
    return getters.GET_IS_BOTTOM_STORAGE_SELECTED
            || getters.GET_IS_TOP_STORAGE_SELECTED;
  },
};
