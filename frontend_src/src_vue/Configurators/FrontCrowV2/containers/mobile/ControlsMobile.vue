<template>
  <div
    v-if="params"
    class="controls"
  >
    <tylkoCard class="configurator-mobile-card">
      <tylkoTabs
        ref="tabs"
        name="tylko-tabs"
        :activeTab="tab"
        class="mobile-tabs pb-16"
      >
        <button
          v-for="(t, i) in mobileTabs"
          :key="i"
          class="c-tab-button"
          :class="{ active: tab === i }"
          @click="
            () => {
              tab = i;
            }
          "
        >
          {{ t.label }}
        </button>
      </tylkoTabs>
      <div class="px-16 pb-16">
        <tylkoContainers
          v-if="mobileTabs.length"
          :selected="mobileTabs[tab].code"
          class="configuration-wrapper"
        >
          <tylkoContainer
            v-if="widthParam"
            class="flex-wrapper"
            name="width"
            wrapperClass="full-box-width mx-32"
          >
            <div class="slider-mock-wrapper w-full pl-40 pr-32">
              <TylkoSliderControls
                v-if="isWidthRangeContinuous"
                v-model="width"
                isMobile
                :rangeMin="widthRange.min"
                :rangeMax="widthRange.max"
              />
              <TylkoSlider
                v-else
                v-model="width"
                :step="widthRange.step"
                :rangeMin="widthRange.min"
                :rangeMax="widthRange.max"
              />
            </div>
          </tylkoContainer>
          <tylkoContainer
            v-if="heightParam"
            class="flex-wrapper"
            name="height"
            wrapperClass="full-box-width mx-32"
          >
            <HeightSlider />
          </tylkoContainer>
          <tylkoContainer
            v-if="depthParam"
            class="flex-wrapper"
            :wrapperClass="
              isVinylStorage ? 'middle-xs flex-column text-center' : ''
            "
            name="depth"
          >
            <template v-if="isVinylStorage">
              <p class="text-offblack-600 normal-14">
                {{ $t("common_configurator_depth_vinyls_tooltip_paragraph_1") }}
              </p>
              <a
                :href="
                  `${$options.originName}${getPlpCategoryLink}/sideboard/`
                "
                target="_blank"
                class="btn-link--small text-orange btn-link--arrow-right mt-8"
              >
                {{ $t("common_configurator_depth_vinyls_tooltip_buton_1") }}
              </a>
            </template>
            <template v-else>
              <div class="flex-wrapper flex-column">
                <toggleGroup
                  v-model="depth"
                  :options="depthOptions"
                  noUppercase
                  :trigger="mobileTabs[tab].code === 'depth'"
                  size="t-md"
                />
                <tylkoCollapse
                  v-if="hasFullRangeOfDepthOptions"
                  :trigger="mobileTabs[tab].code === 'depth'"
                  class="configurator-collapse mt-8"
                >
                  <div
                    class="collapse-trigger text-center normal-14 text-offblack-800"
                  >
                    {{ $t("crow_depth_tooltip_header") }}
                    <span class="arrow">
                      <img
                        svg-inline
                        src="@tylko_ui/icons-cplus/chevron_down.svg"
                      >
                    </span>
                  </div>
                  <div class="collapse-content pt-4">
                    <p class="normal-14 text-center text-offblack-600">
                      {{ $t("crow_depth_tooltip_body") }}
                    </p>
                  </div>
                </tylkoCollapse>
              </div>
            </template>
          </tylkoContainer>
          <tylkoContainer
            v-if="furnitureStyleParam"
            class="flex-wrapper"
            name="style"
          >
            <ToggleFurnitureStyle v-if="isFurnitureStyleTab" />
          </tylkoContainer>
          <tylkoContainer
            v-if="densityParam"
            class="flex-wrapper"
            name="density"
            wrapperClass="full-box-width mx-32 flex-col"
          >
            <div class="slider-mock-wrapper w-full pl-40 pr-32">
              <TylkoSliderControls
                v-model="density"
                :rangeMin="densityRange.min"
                :rangeMax="densityRange.max"
                unit="%"
                isMobile
                :customTooltipRange="3"
              />
            </div>
            <tylkoCollapse
              :trigger="mobileTabs[tab].code === 'density'"
              class="configurator-collapse mt-8"
            >
              <div
                class="collapse-trigger text-center normal-14 text-offblack-800"
              >
                {{ $t("common_density_tooltip_link_button") }}
                <span class="arrow">
                  <img
                    svg-inline
                    src="@tylko_ui/icons-cplus/chevron_down.svg"
                  >
                </span>
              </div>
              <div class="collapse-content pt-4">
                <div
                  class="text-offblack-600 normal-14"
                  v-html="$t('crow_density_info', { button: densityButton })"
                />
              </div>
            </tylkoCollapse>
          </tylkoContainer>
          <tylkoContainer
            v-if="materialParam"
            class="flex-wrapper"
            name="material"
            v-bind="{
              wrapperClass: 'flex-wrapper flex-column'
            }"
          >
            <ControlsMaterialsToggleMobile v-if="(shelfType === 0 || shelfType === 2) && isMaterialTab && !isVinylStorage" />
            <ControlsMaterialsScrollMobile v-if="shelfType === 1 && isMaterialTab || isVinylStorage && isMaterialTab" />
            <OrderSamplesButton />
          </tylkoContainer>
          <tylkoContainer
            v-if="bottomStorageParam"
            class="flex-wrapper"
            name="bottomStorage"
          >
            <StorageToggle
              type="BOTTOM"
              :shouldTrigger="mobileTabs[tab].code === 'bottomStorage'"
            />
          </tylkoContainer>
          <tylkoContainer
            v-if="topStorageParam"
            class="flex-wrapper"
            name="topStorage"
          >
            <StorageToggle
              type="TOP"
              :shouldTrigger="mobileTabs[tab].code === 'topStorage'"
            />
          </tylkoContainer>
          <tylkoContainer
            class="flex-wrapper"
            name="rows"
          >
            <ToggleGroup
              v-if="activeRow !== null && !isAnyStorageSelected"
              v-model="activeRowHeight"
              noUppercase
              :options="rowHeightOptions"
              :trigger="mobileTabs[tab].code === 'rows'"
            />
          </tylkoContainer>
          <tylkoContainer
            class="flex-wrapper"
            name="doors"
          >
            <template v-if="areDoorsAvailable">
              <ToggleGroup
                v-show="doors !== null && rowDoorsOptions"
                v-model="doors"
                noUppercase
                :options="rowDoorsOptions"
                :trigger="mobileTabs[tab].code === 'doors'"
              />
            </template>
            <template v-else>
              <p class="normal-14 text-center text-offblack-600">
                {{ $t("crow_adjust_rows_option1") }}
              </p>
            </template>
          </tylkoContainer>
          <tylkoContainer
            class="flex-wrapper"
            name="drawers"
          >
            <template v-if="areDrawersAvailable">
              <ToggleGroup
                v-if="drawers !== null"
                v-model="drawers"
                noUppercase
                :options="rowDrawersOptions"
                :trigger="mobileTabs[tab].code === 'drawers'"
              />
            </template>
            <template v-else>
              <p class="normal-14 text-center text-offblack-600">
                {{
                  $t(
                    drawersUnavailabilityReason === "depth"
                      ? "crow_adjust_rows_option3"
                      : "crow_adjust_rows_option2"
                  )
                }}
              </p>
            </template>
          </tylkoContainer>
          <tylkoContainer
            v-if="backPanelsParam"
            class="flex-wrapper"
            :wrapperClass="
              isVinylStorage ? 'middle-xs flex-column text-center' : ''
            "
            name="backPanels"
          >
            <template v-if="isVinylStorage">
              <p class="text-offblack-600 normal-14">
                {{ $t("common_configurator_backpanels_vinyl_tooltip") }}
              </p>
              <a
                :href="
                  `${$options.originName}${getPlpCategoryLink}/sideboard/`
                "
                target="_blank"
                class="btn-link--small text-orange btn-link--arrow-right mt-8"
              >
                {{ $t("common_configurator_depth_vinyls_tooltip_buton_1") }}
              </a>
            </template>
            <template v-else>
              <toggleGroup
                v-if="areBackPanelsOptionsEnabled"
                v-model="backPanels"
                noUppercase
                :options="backPanelsOptions"
                :trigger="mobileTabs[tab].code === 'backPanels'"
              />
              <p
                v-else
                class="normal-14 text-offblack-600 mt-8 text-center"
              >
                {{ $t("common_configurator_backpanels_disclaimer") }}
              </p>
            </template>
          </tylkoContainer>
        </tylkoContainers>
        <OpenNewConfiguratorButton
          v-if="showOpenNewConfigurator"
          class="mt-16 text-center"
          :isMobile="true"
          :targetConfigurationScheme="openNewConfiguratorButtonTarget"
        />
        <div class="cplus-divider my-16" />
        <template
          v-if="region !== '_other'"
        >
          <div
            class="flex flex-col"
            :class="{'flex-col-reverse': isUserComesFromFacebook}"
          >
            <button
              class="cta-button save-for-later"
              :class="{
                'orange-button mb-0': isUserComesFromFacebook,
                'white-button mb-8': !isUserComesFromFacebook
              }"
              data-testid="s4l-button"
              @click="saveForLater"
            >
              <img
                :class="{
                  'heart-icon mr-8': true,
                  'heart-icon-orange': isUserComesFromFacebook
                }"
                svg-inline
                src="@tylko_ui/icons-save-for-later/icon-heart.svg"
                alt="Heart icon"
              >
              {{ $t("crow_save_for_later") }}
            </button>
            <tylkoAddToCart
              :addToCart="addToCart"
              :cartLoader="cartLoader"
              class="orange-button"
              :class="{
                'white-button mb-8': isUserComesFromFacebook
              }"
            >
              {{ $t("crow_add_to_cart") }}
            </tylkoaddtocart>
          </div>
        </template>
        <template v-else>
          <button
            class="cta-button save-for-later white-button mb-8"
            @click="saveForLater"
          >
            {{ $t("crow_save_for_later") }}
          </button>
          <p
            class="normal-12 text-offblack-600 mb-8 text-center"
            v-html="$t('crow_save_other_region_header_mobile')"
          />
        </template>
        <ConfiguratorDeliveryDetails />
        <p class="text-center mt-32 furniture-title-mobile">
          {{ furnitureTitle }}
        </p>
      </div>
    </tylkoCard>
  </div>
</template>
<script>
import {
  ToggleGroup,
  TylkoTabs,
  TylkoCard,
  TylkoContainers,
  TylkoContainer,
  TylkoAddToCart,
  TylkoCollapse,
  TylkoSliderControls,
  TylkoSlider,
} from '@componentsConfigurator';
import OpenNewConfiguratorButton from '@configurator-common/components/ui/OpenNewConfiguratorButton';
import ToggleFurnitureStyle from '@src_vue/Configurators/FrontCrowV2/components/ToggleFurnitureStyle';
import OrderSamplesButton from '@configurator-common/components/ui/OrderSamplesButton';
import ConfiguratorDeliveryDetails from '@configurator-common/components/ui/ConfiguratorDeliveryDetails';
import ControlsMaterialsToggleMobile from './ControlsMaterialsToggleMobile';
import ControlsMaterialsScrollMobile from './ControlsMaterialsScrollMobile';
import HeightSlider from '../HeightSlider';
import { sharedConfiguration } from '../../mixins/sharedConfiguration';
import {
  updateConfigurator,
  addToCart,
  saveForLater,
} from '../../mixins/storeComponentCommunication';
import StorageToggle from './StorageToggle';
import plpLinks from '../../helpers/plpLinks';

export default {
  components: {
    ConfiguratorDeliveryDetails,
    ControlsMaterialsScrollMobile,
    ControlsMaterialsToggleMobile,
    ToggleGroup,
    TylkoTabs,
    TylkoCard,
    TylkoContainers,
    TylkoContainer,
    TylkoSliderControls,
    TylkoSlider,
    TylkoAddToCart,
    TylkoCollapse,
    HeightSlider,
    StorageToggle,
    OpenNewConfiguratorButton,
    ToggleFurnitureStyle,
    OrderSamplesButton,
  },
  mixins: [sharedConfiguration, updateConfigurator, addToCart, saveForLater],
  originName: window.location.origin,
  data() {
    return {
      activeRowPlaceholder: 0,
      furnitureTitle: '',
    };
  },
  computed: {
    mobileTabs() {
      return this.$store.getters['ui/GET_MOBILE_TABS'];
    },
    cartLoader() {
      return this.$store.getters['ui/GET_CART_LOADER'];
    },

    region() {
      return window.cstm_i18n.region_name;
    },
    tab: {
      get() {
        return this.$store.getters['ui/GET_ACTIVE_TAB'];
      },
      set(newValue) {
        this.$store.dispatch('ui/UPDATE_ACTIVE_TAB', newValue);
      },
    },
    activeRowHeight: {
      get() {
        return this.$store.getters['configuration/GET_ROW_HEIGHT'];
      },
      set(value) {
        this.updateRowHeight(this.activeRow, value);
      },
    },
    activeRow: {
      get() {
        return this.$store.getters.GET_ACTIVE_ROW;
      },
      set(index) {
        this.$store.dispatch('SELECT_ACTIVE_ELEMENT_IN_RENDERER', index);
        this.$store.dispatch('UPDATE_ACTIVE_ROW', index);
      },
    },
    doors: {
      get() {
        return this.$store.getters['configuration/GET_ROW_DOORS'];
      },
      set(value) {
        this.updateRowDoors(this.activeRow, value);
      },
    },
    drawers: {
      get() {
        return this.$store.getters['configuration/GET_ROW_DRAWERS'];
      },
      set(value) {
        this.updateRowDrawers(this.activeRow, value);
      },
    },
    activeRowConfiguratorParams() {
      return this.activeRow !== null && !this.isAnyStorageSelected
        ? this.$store.getters['ui/GET_ACTIVE_ROW_CONFIGURATOR_PARAMS']
        : { rowDoors: { enabled: false }, rowDrawers: { enabled: false } };
    },
    rowHeightOptions() {
      return this.activeRowConfiguratorParams.rowHeight.options;
    },
    rowDoorsOptions() {
      return this.activeRowConfiguratorParams.rowDoors.options;
    },
    rowDrawersOptions() {
      return this.activeRowConfiguratorParams.rowDrawers.options;
    },
    areDoorsAvailable() {
      return this.activeRowConfiguratorParams.rowDoors.enabled;
    },
    areDrawersAvailable() {
      return this.activeRowConfiguratorParams.rowDrawers.enabled;
    },
    drawersUnavailabilityReason() {
      return this.activeRowConfiguratorParams.rowDrawers.disablementReason;
    },
    placeholderTrigger() {
      return this.getMappedTabsIndexes(['rows', 'doors', 'drawers']);
    },
    activeTabCode() {
      return this.mobileTabs[this.tab].code;
    },
    isAnyStorageSelected() {
      return this.$store.getters['configuration/GET_IS_STORAGE_SELECTED'];
    },
    isTopStorageTab() {
      return this.activeTabCode === 'topStorage';
    },
    isBottomStorageTab() {
      return this.activeTabCode === 'bottomStorage';
    },
    isFurnitureStyleTab() {
      return this.activeTabCode === 'style';
    },
    isMaterialTab() {
      return this.activeTabCode === 'material';
    },
    densityButton() {
      return `<a class="bold text-orange" target="_blank" href='${this.$t('crow_density_tips_url')}'>${this.$t('crow_density_info_link')}</a>`;
    },
    getPlpCategoryLink() {
      return this.getLocalizedUrl(plpLinks);
    },
    isUserComesFromFacebook() {
      return this.$store.getters['commonAbTests/AB_TEST_VALUE']('is_user_comes_from_facebook').isActive;
    },
  },
  watch: {
    params: 'updateTabs',
  },
  created() {
    this.updateTabs();

    const activeRowWatcher = this.$store.watch(
      (state, getters) => getters.GET_ACTIVE_ROW,
      newValue => {
        if (newValue !== null && !this.isAnyStorageSelected) {
          this.activeRowPlaceholder = newValue;
        }
      },
    );
    const activeTabWatcher = this.$store.watch(
      (state, getters) => getters['ui/GET_ACTIVE_TAB'],
      newValue => {
        if (this.placeholderTrigger.includes(newValue)) {
          if (this.activeRow === null || this.isAnyStorageSelected) {
            const lastRowIndex = this.$store.getters['configuration/GET_ROW_AMOUNT'] - 1;
            this.activeRowPlaceholder = Math.min(
              this.activeRowPlaceholder,
              lastRowIndex,
            );
            this.activeRow = this.activeRowPlaceholder;
          }
          this.$store.dispatch('ui/UPDATE_ROW_TOGGLE_VISIBLE', true);
        } else {
          this.$store.dispatch('ui/UPDATE_ROW_TOGGLE_VISIBLE', false);
          if (this.isTopStorageTab && this.topStorage) {
            this.activeRow = this.$store.getters[
              'configuration/GET_ROW_AMOUNT'
            ];
          } else if (this.isBottomStorageTab && this.bottomStorage) {
            this.activeRow = -1;
          } else {
            this.activeRow = null;
          }
        }
        this.$refs.tabs.triggerScroll(newValue, true);
      },
    );

    const topStorageWatcher = this.$store.watch(
      (state, getters) => getters['configuration/GET_TOP_STORAGE']
        && getters['configuration/GET_TOP_STORAGE_HEIGHT'],
      newValue => {
        this.activeRow = newValue && this.isTopStorageTab
          ? this.$store.getters['configuration/GET_ROW_AMOUNT']
          : null;
      },
    );

    const bottomStorageWatcher = this.$store.watch(
      (state, getters) => getters['configuration/GET_BOTTOM_STORAGE']
        && getters['configuration/GET_BOTTOM_STORAGE_HEIGHT'],
      newValue => {
        this.activeRow = newValue && this.isBottomStorageTab ? -1 : null;
      },
    );

    this.$once('hook:beforeDestroy', () => {
      activeTabWatcher();
      activeRowWatcher();
      topStorageWatcher();
      bottomStorageWatcher();
    });
  },
  mounted() {
    PubSub.subscribe('passPaymentInformationToModal', () => {
      PubSub.publish('ecommerceServiceOpenPaymentInformationModal', {
        model: 'jetty',
        geom: this.$store.getters['FPM/GET_GEOMETRY'],
        price: this.$store.getters['commonPrices/GET_PRICE'],
        priceRaw: this.$store.getters['commonPrices/GET_RAW_PRICE'],
      });
    });

    window.PubSub.subscribe('titleHasChanged', (name, title) => {
      this.furnitureTitle = title;
    });
  },
  methods: {
    colorLabel() {
      const classicShelfTypes = [0, 2];
      return classicShelfTypes.includes(this.shelfType) ? this.$t('common_section_label_color_and_finish') : this.$t('common_section_label_color');
    },
    getMappedTabsIndexes(tabs) {
      return this.mobileTabs.reduce((prev, current, index) => {
        if (tabs.includes(current.code)) {
          return [...prev, index];
        } else {
          return prev;
        }
      }, []);
    },
    updateMaterial(material) {
      this.updateConfigurator('material', material, true);
    },
    densityLabel(value) {
      return `${Math.round(value)}%`;
    },
    updateTabs() {
      const tabs = this.params
        ? [
          {
            label: this.$t('crow_width'),
            code: 'width',
            disabled: !this.widthParam,
          },
          {
            label: this.$t('crow_height'),
            code: 'height',
            disabled: !this.heightParam,
          },
          {
            label: this.$t('crow_depth'),
            code: 'depth',
            disabled: !this.depthParam,
          },
          {
            label: this.$t('crow_style'),
            code: 'style',
            disabled: !this.furnitureStyleParam,
          },
          {
            label: this.$t('crow_density'),
            code: 'density',
            disabled: !this.densityParam,
          },
          {
            label: this.colorLabel(),
            code: 'material',
            disabled: !this.materialParam,
          },
          {
            label: this.$t(
              'cRow_wall_storage_plus_mobile_tab_bottom_storage',
            ),
            code: 'bottomStorage',
            disabled: !(
              this.bottomStorageParam && this.isBottomStorageAvailable
            ),
          },
          {
            label: this.$t('cRow_wall_storage_plus_mobile_tab_upper_storage'),
            code: 'topStorage',
            disabled: !(this.topStorageParam && this.isTopStorageAvailable),
          },
          { label: this.$t('common_configurator_rows'), code: 'rows' },
          { label: this.$t('crow_features_header_2'), code: 'doors' },
          { label: this.$t('crow_features_header_1'), code: 'drawers' },
          {
            label: this.$t('common_configurator_backpanels'),
            code: 'backPanels',
            disabled: !this.backPanelsParam,
          },
        ]
        : [];

      this.$store.dispatch(
        'ui/UPDATE_MOBILE_TABS',
        tabs.filter(tab => !tab.disabled),
      );
    },
  },
};
</script>
<style lang="scss" scoped>

.heart-icon {
  &-orange path {
    fill: #FF3C00 !important;
    stroke: #fff;
  }

  &-white path {
    fill:  #fff !important;
    stroke:  #FF3C00;
  }

}

.furniture-title-mobile {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
}
</style>
