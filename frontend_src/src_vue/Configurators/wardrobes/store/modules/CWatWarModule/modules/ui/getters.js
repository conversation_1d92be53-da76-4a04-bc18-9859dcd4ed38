export default {
    GET_CONFIGURATOR_PARAMS(state) {
        return state.configuratorParams;
    },
    GET_ACTIVE_COMPONENT_PARAMS(state, _, rootState) {
        const activeChannelId = rootState.FPM.components[rootState.activeComponent].channel_id;
        return state.configuratorParams.channels[activeChannelId];
    },
    GET_ACTIVE_TAB(state) {
        return state.activeTab;
    },
    GET_IS_MOBILE(state) {
        return state.isMobile;
    },
    GET_IS_CANVAS_FOCUSED(state) {
        return state.isCanvasFocused;
    },
    GET_COORDINATES_FOR_BUTTONS_DESKTOP(state) {
        return state.adjustButtonsCoordinatesDesktop;
    },
    GET_COORDINATES_FOR_BUTTONS_MOBILE(state) {
        return state.adjustButtonsCoordinatesMobile;
    },
    GET_CART_LOADER(state) {
        return state.cartLoader;
    },
    GET_SAVE_FOR_LATER_LOADER(state) {
        return state.saveForLaterLoader;
    },
    GET_TABS(state) {
        return state.tabs;
    },
    GET_ACTIVE_COMPONENT_TABS(state) {
        return state.activeComponentTabs;
    },
    GET_ACTIVE_COMPONENT_ACTIVE_TAB(state) {
        return state.activeComponentActiveTab;
    },
    GET_HEIGHT_SLIDER_IS_ACTIVE(state) {
        return state.heightSliderIsActive;
    },
    GET_THUMBNAIL_STATE(state) {
        return state.thumbnails;
    },
};
