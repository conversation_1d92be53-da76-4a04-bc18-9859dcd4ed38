import { disableScroll, enableScroll } from '@configurator-common/helpers/simpleScrollLock';
import { setDefaultProductPriceHelper } from '@configurator-common/helpers/commonPricesHelpers';
import {
  FPM, offscreenRenderer, pricing, renderer,
  initWattyPricing,
} from '../../../modules/CWatWar/core';

export default {
  async INITIALIZE_APP(
    { dispatch, state, commit },
    { configurator, canvasWidth, canvasHeight },
  ) {
    try {
      await dispatch('commonFurniture/FETCH_ITEM', {
        model: 'watty',
        id: window.cstm.itemId,
        customDnaId: window.cstm.customDnaId,
        cv: (new URL(document.location)).searchParams.get('cv'),
      });
      dispatch('SETUP_STATE');
      dispatch('commonConfiguration/SET_PRODUCT_ID');
      dispatch('commonPrices/INITIALIZE_PRICE_FORMATTER');
      dispatch('commonAbTests/SET_AB_TESTS');
      const { data } = state.commonFurniture.item.override_dna_json
        ? { data: state.commonFurniture.item.override_dna_json }
        : await dispatch('api/REQUEST', {
          method: 'GET',
          url: `/api/v1/furniture_dna/?shelf_type=${state.shelfType}&collection_type=${state.commonFurniture.item.configurator_params.additional_parameters.collection_type}&dna_version=4`,
        });
      commit('updateAvaliableDnas', data.available_dnas);
      dispatch('FPM/INIT_FPM', data.json);
      await dispatch('renderer/INIT_RENDERER', { configurator, canvasWidth, canvasHeight });
      await dispatch('INIT_PRICING');
      dispatch('ECOMMERCE_SERVICE_LISTENERS');
      dispatch('FPM/UPDATE_PRODUCT_DETAILS', null, { root: true });

      // Set default Product Price from schema
      const { defaultProductPrice } = state.commonPrices;
      if (defaultProductPrice) {
        commit('commonPrices/setPrice', setDefaultProductPriceHelper(state.commonPrices.price, state.commonPrices.defaultProductPrice));
      }
      PubSub.publish('atupaleLayer_configurator', { method: 'passInitialData', payload: state });
    } catch (e) {
      throw new Error(`Initialize CWatWarModule app error: ${e}`);
    }
  },
  SETUP_STATE({ commit, state: { commonFurniture: { item } } }) {
    commit('setInitialStateValues', item);
  },
  APP_INITIALIZED({ commit, dispatch }) {
    commit('appInitialized');
    PubSub.publish('configuratorInitialized');
    setTimeout(() => dispatch('ui/UPDATE_COORDINATES_FOR_BUTTONS'), 0);
  },
  RENDERER_LOADED({ commit }, payload) {
    commit('rendererLoaded', payload);
  },
  ACTIVE_COMPONENT({ commit, dispatch, rootState }, payload) {
    let paramName;
    if (!rootState.activeComponent) {
      paramName = 'activeComponent';
    } else {
      paramName = 'switchComponent';
    }
    dispatch('FPM/DISPATCH_TRACKING_DATA', { paramName, payload: rootState.FPM.components[payload].bi_info.component_id });
    commit('activeComponent', payload);
    dispatch('FPM/UPDATE_THUMBNAILS_GEOMETRY', payload);
    dispatch('renderer/RENDERER_SET_COMPONENT_VIEW', payload);
    dispatch('ui/RERENDER_THUMBNAILS');
    // if (!rootState.renderer.isDoorsOpen && rootState.dimensions.show) {
    //   dispatch('renderer/UPDATE_DOORS_OPEN', true);
    // }
    // if (!rootState.dimensions.show) dispatch('renderer/UPDATE_INTERACTION_STATE');
    if (rootState.ui.isMobile) {
      disableScroll();
    }
  },
  ACTIVE_COMPONENT_BY_INDEX({ dispatch, state }, payload) {
    dispatch('ACTIVE_COMPONENT', Object.keys(state.FPM.components)[payload]);
  },
  DEACTIVATE_COMPONENT({ commit, dispatch, state }) {
    commit('activeComponent', null);
    dispatch('FPM/DISPATCH_TRACKING_DATA', { paramName: 'deactiveComponent', payload: null });
    if (renderer) {
      renderer.setInactiveState();
      // if (state.ui.isMobile) {
      //   renderer.clearHoverBoxes();
      // }
    }
    if (state.ui.isMobile) {
      enableScroll();
    }
  },
  async INIT_PRICING({ dispatch }) {
    const geom = FPM.getGeometry();
    await initWattyPricing({ ...geom, shelfType: 3 });
    dispatch('UPDATE_PRICE', geom);
  },
  UPDATE_PRICE({ state, commit }, geom) {
    if (!pricing) return;
    const price = pricing.getPrice({
      ...geom,
      shelf_category: 'wardrobe',
      material: state.renderer.material, // FIXME Temporary hack.
    }, 'watty');
    commit('commonPrices/setPrice', price);
  },
  async ADD_TO_CART({ state, dispatch, rootState }) {
    const geom = FPM.getGeometry();
    const { dna_name, commonFurniture: { item } } = state;
    const { capacity } = rootState.commonPrices.price;
    geom.material = state.renderer.material;

    dispatch('ui/UPDATE_CART_LOADER', true);
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');
    const magic_preview = offscreenRenderer.getScreenshotForCart().split(',')[1];

    const payload = {
      geom,
      magic_preview,
      capacity,
      dna_name,
      configuratorType: 'watty',
      base_preset: item.id,
    };

    PubSub.publish('ecommerceServiceAddToCart', payload);
  },
  async SAVE_FOR_LATER({ dispatch, state }) {
    const { dna_name, commonFurniture: { item } } = state;
    try {
      dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');
      const screenshotBase64 = offscreenRenderer.getScreenshotForSave().split(',')[1];
      const isLoggedIn = Site.CartController.isLoggedIn();
      const geom = FPM.getGeometry();
      geom.material = state.renderer.material;

      PubSub.publish(isLoggedIn ? 'addToWishlistLoggedIn' : 'toggleSaveForLaterModal', {
        geom,
        screenshot: screenshotBase64,
        endpoint: 'watty',
        dna_name,
        base_preset: item.id,
        configurator_type: item.configurator_type,
      });
    } catch (error) {
      PubSub.publish('Notify-onError', cstm_i18n.shelf_not_saved);
      throw new Error(`Save For Later error: ${error}`);
    }
  },
  // TEMPORARY ACTION FOR PERFORMANCE TESTING
  async GET_METRICS({ dispatch, state }) {
    const preset = renderer.getAnalyzedSession();
    const downloadLinkData = `data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(preset))}`;
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', downloadLinkData);
    downloadAnchorNode.setAttribute('download', 'renderer-metrics.json');
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  },
  async SAVE_AS_PRESET({ dispatch, state }) {
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');
    const screenshotBase64 = offscreenRenderer.getScreenshotForSave().split(',')[1];
    const geom = FPM.getGeometry();
    geom.material = state.renderer.material;

    try {
      const { data } = await dispatch('api/REQUEST', {
        method: 'POST',
        url: '/api/v1/gallery/watty/save_as_preset/',
        data: {
          geom,
          magic_preview: screenshotBase64.split(',')[1],
        },
      }, { root: true });
      dispatch('CLEAR_GEOM_LOCAL_STORAGE');
    } catch (error) {
      if (Site && Site.Notify) {
        Site.Notify.onError(cstm_i18n.shelf_not_saved);
      }
      throw new Error(`Save As Preset error: ${error}`);
    }
  },
  UPDATE_HOVER_COMPONENT({ commit, rootState }, payload) {
    if (!rootState.activeComponent) {
      renderer.updateComponentHoverBox(payload);
    } else {
      renderer.updateHoverBoxWithActiveComponent(rootState.activeComponent, payload);
    }
    commit('updateHoverComponent', payload);
  },
  UPDATE_INVITE_LINK_STATUS({ commit }, { isInvite, isActive }) {
    commit('updateInviteLink', {
      isInvite,
      isActive,
    });
  },
  WAITING_LIST({ state, dispatch }) {
    const geom = FPM.getGeometry();
    const { dna_name } = state;
    geom.material = state.renderer.material;
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');
    const magic_preview = offscreenRenderer.getScreenshotForCart().split(',')[1];
    PubSub.publish('toggleNotifyModal', {
      type: 'waiting list',
      geom,
      magic_preview,
      dna_name,
    });
    PubSub.publish('atupaleLayer_notify', { method: 'waitingListModal' });
  },
  NOTIFY_ME({ state, dispatch, rootState }) {
    const geom = FPM.getGeometry();
    const { dna_name } = state;
    const { capacity } = rootState.commonPrices.price;

    geom.material = state.renderer.material;
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');

    const magic_preview = offscreenRenderer.getScreenshotForCart().split(',')[1];
    PubSub.publish('toggleNotifyModal', {
      type: 'notify me',
      configurationData: {
        geom,
        magic_preview,
        dna_name,
        capacity,
      },
    });
    PubSub.publish('atupaleLayer_notify', { method: 'notifyModal' });
  },
  INVITE_LINK_EXPIRED_POPUP() {
    setTimeout(() => {
      PubSub.publish('toggleNotifyModal', {
        type: 'expiration',
        geom: '',
      });
      PubSub.publish('atupaleLayer_notify', { method: 'inviteInvalid' });
    }, 500);
  },
  SHARE_SHELF({ dispatch, state: { configuration, renderer: _renderer, commonFurniture: { item } } }) {
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');
    const screenshotBase64 = offscreenRenderer.getScreenshotForSave();
    const geom = FPM.getGeometry();
    geom.digital_product_version = configuration.digital_product_version;
    geom.material = _renderer.material;
    PubSub.publish('shareShelf', {
      geom,
      screenshot: screenshotBase64.split(',')[1],
      endpoint: 'watty',
      base_preset: item.id,
      configurator_type: item.configurator_type,
    });
  },
  UPDATE_DYNAMIC_DELIVERY({ dispatch, rootState }) {
    const { category } = rootState.commonFurniture.item;
    dispatch('commonUI/UPDATE_DYNAMIC_DELIVERY', {
      shelfType: rootState.shelfType,
      material: rootState.renderer.material,
      category,
    }, { root: true });
  },
  ECOMMERCE_SERVICE_LISTENERS({ dispatch }) {
    dispatch('EXIT_POPUP_WINDOW_LEAVE_LISTENER');
    PubSub.subscribe('configuratorAddedToCart', () => {
      dispatch('ui/UPDATE_CART_LOADER', false);
      dispatch('commonGeomLocalStorage/CLEAR_GEOM_LOCAL_STORAGE', null, { root: true });
    });
  },
  HANDLE_ANY_CONFIGURATION_UPDATE({ rootState }) {
    if (rootState.appInitialized) {
      PubSub.publish('configuratorUpdated');
    }
  },
  EXIT_POPUP_WINDOW_LEAVE_LISTENER({
    dispatch, commit, state: {
      renderer: _renderer, commonFurniture: { item },
    },
  }) {
    PubSub.subscribe('exitpopupOnWindowLeave', () => {
      dispatch('renderer/UPDATE_OFFSCREEN_RENDERER');

      const screenshotBase64 = offscreenRenderer.getScreenshotForSave();

      const geom = FPM.getGeometry();
      geom.material = _renderer.material;

      PubSub.publish('exitpopupOnActivated');
      PubSub.publish('openExitPopup', {
        geom,
        screenshot: screenshotBase64.split(',')[1],
        endpoint: 'watty',
        base_preset: item.id,
        configurator_type: item.configurator_type,
      });
      commit('exitPopupSliderIsSet');
    });
  },
};
