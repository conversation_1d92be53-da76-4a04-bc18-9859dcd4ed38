<template>
  <div class="z-30 mobile-dim-wrapper bg-grey-600">
    <button
      class="c-button-round control-btn close-btn"
      @click="closeModal"
    >
      <img
        svg-inline
        src="@tylko_ui/icons-cplus/ic_plus.svg"
        style="transform: rotate(45deg)"
        alt="close"
      >
    </button>
    <Pinch
      :base64DimImage="base64DimImage"
      :originalWidth="pinchWrapperWidth * 4"
      :originalHeight="pinchWrapperHeight * 4"
      :wrapperWidth="pinchWrapperWidth"
      :wrapperHeight="pinchWrapperHeight"
      :zoom="0.4"
    />
  </div>
</template>

<script>
import Pinch from '@configurator-common/components/ui/Pinch';

export default {
  components: {
    Pinch,
  },
  data() {
    return {
      pinchWrapperWidth: window.innerWidth,
      pinchWrapperHeight: window.innerHeight,
    };
  },
  computed: {
    base64DimImage() {
      return this.$store.getters['dimensions/GET_BASE_64_IMAGE'];
    },
    isShowDimensions: {
      get() {
        return this.$store.getters['dimensions/GET_SHOW_DIMENSIONS_MOBILE'];
      },
      set(newVal) {
        this.$store.dispatch('dimensions/UPDATE_SHOW_DIMENSIONS_MOBILE', newVal);
      },
    },
  },
  methods: {
    closeModal() {
      this.isShowDimensions = false;
    },
  },

};
</script>
