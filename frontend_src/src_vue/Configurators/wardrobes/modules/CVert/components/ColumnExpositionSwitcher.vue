
<template>
    <div class="dna-switcher flex flex-wrap">
        <button
            v-for="(option, index) in columnExpositionOptions"
            :key="index"
            class="dna-switch-button column-exposition-btn c-button-thumbnail bold-12 text-offblack-600 px-6 pb-8 mr-8 mb-8"
            :class="{ active: option.value === activeColumnExposition }"
            @click="() => switchColumnExposition(option.value)"
        >
            <img
                v-if="option.value === 'none1'"
                src="@tylko_ui/icons-cvert/doors_none_1.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-if="option.value === 'none2'"
                src="@tylko_ui/icons-cvert/doors_none_2.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'partial'"
                src="@tylko_ui/icons-cvert/doors_partial_2.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'partialLeft'"
                src="@tylko_ui/icons-cvert/doors_partial_1_left.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'partialRight'"
                src="@tylko_ui/icons-cvert/doors_partial_1_right.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'full'"
                src="@tylko_ui/icons-cvert/doors_full_2.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'fullRight'"
                src="@tylko_ui/icons-cvert/doors_full_1_right.svg"
                svg-inline
                class="mt-4"
            >
            <img
                v-else-if="option.value === 'fullLeft'"
                src="@tylko_ui/icons-cvert/doors_full_1_left.svg"
                svg-inline
                class="mt-4"
            >
            <p class="my-4 text-offblack-600">
                {{ columnExpositionDictionary[option.value] }}
            </p>
        </button>
    </div>
</template>
<style lang="scss" scoped>
.column-exposition-btn {
  &.dna-switch-button {
     width: 75px;
     height: 50px;
     padding-top: 22px;
   }
}
</style>

<script>
    import { updateConfigurator } from '../mixins/storeComponentCommunication';

    export default {
        mixins: [updateConfigurator],
        data() {
            return {
                columnExpositionDictionary: {
                    none1: this.$t('cvert_doors_option_1'),
                    none2: this.$t('cvert_doors_option_1'),
                    partial: this.$t('cvert_doors_option_2'),
                    partialLeft: this.$t('cvert_doors_option_2'),
                    partialRight: this.$t('cvert_doors_option_2'),
                    full: this.$t('cvert_doors_option_3'),
                    fullLeft: this.$t('cvert_doors_option_3'),
                    fullRight: this.$t('cvert_doors_option_3'),
                },
            };
        },
        computed: {
            activeColumnExposition() {
                return this.$store.getters['configuration/GET_COLUMN_EXPOSITION'];
            },
            configurationParams() {
                return this.$store.getters['ui/GET_CONFIGURATOR_PARAMS'];
            },
            columnExpositionOptionsParam() {
                return this.$store.getters['ui/GET_ACTIVE_COMPONENT_PARAMS'].columnExposition;
            },
            columnExpositionOptions() {
                return this.columnExpositionOptionsParam.options;
            },
        },
        methods: {
            switchColumnExposition(newColumnExposition) {
                this.updateLocalConfiguration('column_exposition', newColumnExposition);
            },
        },
    };
</script>
