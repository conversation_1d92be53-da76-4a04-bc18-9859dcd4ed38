<template>
  <button
    class="cta-button save-for-later"
    data-testid="s4l-button"
    :class="extraClasses"
    @click="saveForLater"
  >
    <img
      class="heart-icon mr-8"
      svg-inline
      src="@tylko_ui/icons-save-for-later/icon-heart.svg"
      alt="Heart icon"
    >
    {{ $t(i18n) }}
  </button>
</template>

<script>
import {
  saveForLater,
} from '../mixins/storeComponentCommunication';

export default {
  name: 'SaveForLaterButton',
  mixins: [saveForLater],
  props: {
    extraClasses: {
      type: Array,
      required: false,
      default: () => [],
    },
    i18n: {
      type: String,
      default: 'cvert_save_for_later',
    },
  },
};
</script>
