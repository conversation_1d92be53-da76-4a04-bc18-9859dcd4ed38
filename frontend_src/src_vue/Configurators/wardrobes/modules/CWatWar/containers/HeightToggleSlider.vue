<template>
    <div :class="heightSliderIsActive ? 'height-slider-wrapper' : ''">
        <button
            v-if="heightSliderParam && heightSliderParam.options.length === 1"
            class="c-button text-grey-900 bg-grey-600 active"
            disabled
            style="cursor: auto"
        >
            {{ heightSliderParam.options[0].label }}
        </button>
        <div
            v-else
            :class="heightSliderIsActive ? 'height-slider' : ''"
        >
            <div
                v-show="!heightSliderIsActive"
                class="flex-wrapper"
                style="justify-content: flex-start"
            >
                <toggleGroup
                    v-if="customHeightParams"
                    v-model="height"
                    noUppercase
                    :trigger="!heightSliderIsActive"
                    :options="customHeightParams.options"
                />
            </div>
            <div
                v-show="heightSliderIsActive"
                class="flex-wrapper"
                :class="{'height-toggle-width': heightSliderIsActive}"
            >
                <button
                    class="height-slider__button c-button border-grey-800 text-offblack-600"
                    @click="closeHeightSlider"
                >
                    {{ heightSliderIsActive ? $t('cwatwar_extra_height_2') : $t('cwatwar_extra_height_1') }}
                </button>
                <div class="height-slider__slider">
                    <div class="slider-mock-wrapper px-32">
                        <TylkoSliderControls
                            v-if="heightSliderIsActive && heightSliderParam"
                            v-model="heightSlider"
                            :rangeMin="heightSliderParam.ranges[0].min"
                            :rangeMax="heightSliderParam.ranges[0].max"
                            :isMobile="isMobile"
                            @onSliderRelease="onSliderRelease"
                            @onSliderChange="onSliderChange"
                        />
                    </div>
                </div>
            </div>
        </div>
        <p
            v-show="heightSliderIsActive"
            class="text-center md:text-left normal-14 md:normal-12 text-offblack-600 mt-8"
            :style="!isMobile ? 'margin-bottom: -4px;' : ''"
            v-html="copy"
        />
    </div>
</template>

<script>
    import {
        ToggleGroup,
        TylkoSliderControls,
    } from '@componentsConfigurator';
    import { updateConfigurator } from '../mixins/storeComponentCommunication';

    export default {
        components: {
            ToggleGroup,
            TylkoSliderControls,
        },
        mixins: [updateConfigurator],
        props: {
            value: {
                type: [String, Number, Boolean],
            },
        },
        data() {
            return {
                sliderKey: 0,
            };
        },
        computed: {
            isMobile() {
                return this.$store.getters['ui/GET_IS_MOBILE'];
            },
            heightSliderIsActive: {
                get() {
                    return this.$store.getters['ui/GET_HEIGHT_SLIDER_IS_ACTIVE'];
                },
                set(value) {
                    this.$store.dispatch('ui/UPDATE_HEIGHT_SLIDER_IS_ACTIVE', value);
                },
            },
            height: {
                get() {
                    return this.$store.getters['configuration/GET_HEIGHT'];
                },
                set(value) {
                    if (value === 'toggleSlider') {
                        this.heightSliderIsActive = true;
                        this.sliderKey = 1;
                        this.updateConfigurator('height', this.heightSliderParam.ranges[0].min, true);
                        return;
                    }
                    this.updateConfigurator('height', value, true);
                },
            },
            heightSlider: {
                get() {
                    return this.height;
                },
                set({ value, release }) {
                    this.isCanvasFocused = false;
                    this.updateConfigurator('height', value, release);
                },
            },
            customHeightParams: {
                get() {
                    if (this.heightSliderParam) {
                        return {
                            ...this.heightSliderParam,
                            options: [
                                {
                                    ...this.heightSliderParam.options[0],
                                },
                                {
                                    ...this.heightSliderParam.options[1],
                                },
                                {
                                    label: `${this.$t('cwatwar_height')}+`,
                                    value: 'toggleSlider',
                                },
                            ],
                        };
                    }
                    return null;
                },
            },
            configurationParams() {
                return this.$store.getters['ui/GET_CONFIGURATOR_PARAMS'];
            },
            heightSliderParam() {
                return this.configurationParams.height;
            },
            isCanvasFocused: {
                get() {
                    return this.$store.getters['ui/GET_IS_CANVAS_FOCUSED'];
                },
                set(newVal) {
                    this.$store.dispatch('ui/UPDATE_IS_CANVAS_FOCUSED', newVal);
                },
            },
            copy() {
                return `${this.$t('cwatwar_extra_height_warning_1')} <span class="text-bold">${(this.heightSlider + 10)} cm</span> ${this.$t('cwatwar_extra_height_warning_2')} `;
            },
        },
        mounted() {
            if (this.height && this.height > this.heightSliderParam.options[1].value) {
                this.heightSliderIsActive = true;
            }
        },
        beforeUpdate() {
            if (this.height && this.height > this.heightSliderParam.options[1].value) {
                this.heightSliderIsActive = true;
            }
        },
        methods: {
            onSliderRelease() {
                this.isCanvasFocused = true;
                this.$store.dispatch('renderer/UPDATE_RENDERER', true);
                if (this.height === 238) {
                    this.closeHeightSlider();
                }
            },
            onSliderChange(val) {
                this.$store.dispatch('interactions/SET_IS_SLIDER_MOVING', val);
            },
            closeHeightSlider() {
                this.heightSliderIsActive = false;
                this.height = this.heightSliderParam.options[1].value;
            },
        },
    };
</script>
