<template>
  <div class="layout-ac">
    <div class="flex-wrapper">
      <Thumbnails
        ref="thumbnails"
      >
        <template v-slot="{ thumb, activeComponent }">
          <Miniature
            :id="thumb.id"
            ref="miniature"
            :geom="thumb.format"
            :active="thumb.series_id === activeComponent.series_id"
            :createMiniatureSize="createSize(thumb.format)"
          />
        </template>
      </Thumbnails>
    </div>
    <HangingOptionHint :wrapperClasses="'px-16 mb-12'" />
    <div class="px-16 layout-ac__wrapper">
      <TylkoPicture
        v-bind="{ imageSrc }"
        :imgAlt="componentTitle"
        imgClasses="img-responsive layout-ac__image"
      />
      <ActiveComponentCell
        v-bind="{ iconSrc }"
        :iconAlt="componentTitle"
        :title="componentTitle"
        :description="componentDescription"
      />
    </div>
  </div>
  </div>
</template>

<script>
import { TylkoPicture } from '@componentsConfigurator';
import Thumbnails from '@configurator-common/components/ui/Thumbnails';
import Miniature from '@configurator-common/components/ui/Miniature';
import HangingOptionHint from '@src_vue/Configurators/wardrobes/modules/CWatWar/components/HangingOptionHint';
import { componentsCopy } from '../../../mixins/utils';
import ActiveComponentCell from '../../../../../shared/components/ActiveComponentCell';

export default {
  components: {
    Thumbnails,
    TylkoPicture,
    ActiveComponentCell,
    Miniature,
    HangingOptionHint,
  },
  mixins: [componentsCopy],
  computed: {
    activeThumb() {
      return this.$store.getters['FPM/GET_ACTIVE_THUMBNAIL'];
    },
    activeThumbName() {
      return this.$store.getters['FPM/GET_THUMBNAIL_TITLE'][this.activeThumb];
    },
    imageSrc() {
      return `/r_static/images-cwatwar/components/${this.activeThumbName}`;
    },
    iconSrc() {
      return `/r_static/icons-cwatwar/thumbs-modal/${this.activeThumbName}.png`;
    },
    componentTitle() {
      return this.$t(this.$options.componentsTitles[this.activeThumbName]);
    },
    componentDescription() {
      return this.$t(this.$options.componentsDescription[this.activeThumbName]);
    },
  },
  methods: {
    createSize(data) {
      const doubleDoor = data.doors_exterior.length > 1 || data.doors_raiser.length > 1;
      const heightPlus = data.components[0].height > 2100;
      const width = doubleDoor ? 58 : 48;
      const height = heightPlus ? 115 : 109;
      return { width, height };
    },
  },
};
</script>
