<template>
  <fragment>
    <div class="canvas-header pl-16 pt-12">
      <h1 class="normal-12 text-offblack-800 pb-0 one-line">
        {{ $options.furnitureTitle }}
      </h1>
    </div>
    <div class="canvas-price pr-16  pt-16">
      <p class="light-16 text-offblack-800">
        {{ price }}
      </p>
    </div>
  </fragment>
</template>

<script>
export default {
  furnitureTitle: document.getElementById('pdp-cplus').getAttribute('data-furniture-title'),
  data() {
    return {
      pdpCategory: window.cstm.pdp_category,
    };
  },
  computed: {
    price() {
      return this.$store.getters['commonPrices/GET_PRICE'];
    },
  },
};
</script>
