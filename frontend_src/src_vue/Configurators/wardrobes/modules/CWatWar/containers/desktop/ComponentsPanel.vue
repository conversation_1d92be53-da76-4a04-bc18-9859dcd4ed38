<template>
  <div class="components-panel pt-16">
    <ConfiguratorColumnSwitcher
      :translationKeys="columnSwitcherTranslationKeys"
    />
    <div class="thumbs thumbs-wardrobe-padding">
      <TylkoChevrons
        transitionName="fade-slow"
        :leftButtonVisible="disabledChevron !== 'left'"
        :rightButtonVisible="disabledChevron !== 'right'"
        @leftButtonHandler="() => leftButtonHandler()"
        @rightButtonHandler="() => rightButtonHandler()"
      />
      <Thumbnails
        ref="thumbnails"
        emitScroll
        @scrollHandler="(distance) => scrollHandler(distance)"
      >
        <template v-slot="{ thumb, activeComponent }">
          <Miniature
            :id="thumb.id"
            ref="miniature"
            :geom="thumb.format"
            :active="thumb.series_id === activeComponent.series_id"
            :createMiniatureSize="createSize(thumb.format)"
          />
        </template>
      </Thumbnails>
    </div>
    <div class="px-16">
      <transition name="fade-slow">
        <div
          v-if="controlsVisible && activeComponentIndex !== null"
          class="row middle-xs mt-12"
        >
          <HangingOptionHint :wrapperClasses="'col-xs-12 mb-12'" />
          <div :class="isOneLocalXButton ? 'col-xs-3' : 'col-xs-3 pr-4'">
            <p class="normal-14 text-offblack-800 mt-4">
              {{ $t("cwatwar_mobile_col_width") }}: <span class="bold-14">{{ Math.round((componentWidth / 10)) }}cm</span>
            </p>
            <p class="normal-10 text-offblack-600">
              {{ $t("cwatwar_col_width_war_width") }}: <span class="bold-10">{{ width }}cm</span>
            </p>
          </div>
          <div
            class="text-left flex items-center"
            :class="'col-xs-9'"
          >
            <div
              class="local-x-container"
              :class="{'mr-8': isOneLocalXButton}"
            >
              <LocalX
                :localX="localX"
                :setLocalXActive="setLocalXActive"
                :activeComponentIndex="activeComponentIndex"
              />
            </div>
            <div
              v-if="isOneLocalXButton"
              class="z-10"
            >
              <TylkoConfiguratorTooltip>
                <p class="text-offblack-600 normal-14 text-left">
                  {{ $t("cwatwar_col_width_unavailable_1") }}<span class="bold-14">{{ `${width}cm` }}</span>{{ $t("cwatwar_col_width_unavailable_2") }}
                </p>
              </TylkoConfiguratorTooltip>
            </div>
          </div>
        </div>
      </transition>
      <transition name="fade-slow">
        <div
          v-if="doorDirection && doorDirection.visible && controlsVisible"
          class="flex-wrapper mt-16"
          style="justify-content: space-between"
        >
          <div class="row full-box-width">
            <div class="col-xs-3">
              <p class="normal-14 text-offblack-800">
                {{ $t("cwatwar_doors") }}
              </p>
            </div>
            <div class="col-xs-9">
              <DoorDirectionToggle :trigger="controlsVisible" />
            </div>
          </div>
        </div>
      </transition>
    </div>
    <div class="cplus-divider my-16" />
    <div class="px-16">
      <div class="row middle-xs mt-16">
        <div class="col-xs-3">
          <p
            class="normal-14 text-offblack-800"
            :class="{ disabled: !drawerStyleSwitchEnabled}"
          >
            {{ $t("cwatwar_drawers_feature") }}
          </p>
        </div>
        <div class="col-xs-9 drawer-switcher-wrapper">
          <div class="flex-wrapper start-xs">
            <DrawerStyleSwitcher
              :disabled="!drawerStyleSwitchEnabled"
            />
            <TylkoConfiguratorTooltip>
              <template v-if="drawerStyleSwitchEnabled">
                <h2
                  class="bold-14 text-offblack-600"
                  v-html="$t('cwatwar_drawers_external_tooltip')"
                />
                <p
                  class="normal-14 text-offblack-600 mb-8"
                  v-html="$t('cwatwar_drawers_external_body')"
                />
                <h2
                  class="bold-14 text-offblack-600"
                  v-html="$t('cwatwar_drawers_internal_tooltip')"
                />
                <p
                  class="normal-14 text-offblack-600"
                  v-html="$t('cwatwar_drawers_internal_body')"
                />
              </template>
              <template v-else>
                <p
                  class="normal-14 text-offblack-600"
                  v-html="$t('cwatwar_segment_drawers')"
                />
              </template>
            </TylkoConfiguratorTooltip>
          </div>
          <p class="normal-12 text-offblack-600 mt-8">
            {{ $t('cwatwar_drawers_info') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  TylkoChevrons,
  TylkoConfiguratorTooltip,
} from '@componentsConfigurator';
import ResizeObserver from '@configurator-common/plugins/resizeObserver';
import LocalX from '@configurator-common/components/watty/LocalX';
import ConfiguratorColumnSwitcher from '@configurator-common/components/ui/ConfiguratorColumnSwitcher';
import Thumbnails from '@configurator-common/components/ui/Thumbnails';
import Miniature from '@configurator-common/components/ui/Miniature';
import HangingOptionHint from '@src_vue/Configurators/wardrobes/modules/CWatWar/components/HangingOptionHint';
import { updateConfigurator } from '../../mixins/storeComponentCommunication';
import DoorDirectionToggle from '../../components/DoorDirectionToggle';
import DrawerStyleSwitcher from '../DrawerStyleSwitcher';

export default {
  components: {
    Thumbnails,
    LocalX,
    TylkoChevrons,
    TylkoConfiguratorTooltip,
    DoorDirectionToggle,
    ConfiguratorColumnSwitcher,
    Miniature,
    HangingOptionHint,
    DrawerStyleSwitcher,
  },
  mixins: [updateConfigurator],
  data() {
    return {
      controlsVisible: false,
      disabledChevron: null,
      columnSwitcherTranslationKeys: {
        title: 'cwatwar_columns_pagination_1',
        second: 'cwatwar_columns_pagination_2',
      },
    };
  },
  computed: {
    activeComponentIndex() {
      return this.$store.getters.GET_ACTIVE_COMPONENT_INDEX;
    },
    componentWidth() {
      return this.$store.getters['FPM/GET_COMPONENT_WIDTH'];
    },
    width: {
      get() {
        return this.$store.getters['configuration/GET_WIDTH'];
      },
    },
    doorDirection: {
      get() {
        return this.$store.getters['ui/GET_ACTIVE_COMPONENT_PARAMS']
          .doorDirection;
      },
    },
    // FIXME Use Configuration API in a proper way.
    localX() {
      return {
        active: this.$store.getters['configuration/GET_LOCAL_X'],
        available: this.$store.getters[
          'ui/GET_ACTIVE_COMPONENT_PARAMS'
        ].localX.options.map(({ value }) => value),
      };
    },
    localXArr() {
      return this.localX.available;
    },
    isOneLocalXButton() {
      return !this.localXArr.length;
    },
    configurationParams() {
      return this.$store.getters['ui/GET_CONFIGURATOR_PARAMS'];
    },
    drawerStyleOptionsParam() {
      return this.configurationParams.drawerStyle;
    },
    drawerStyleSwitchEnabled() {
      return this.drawerStyleOptionsParam ? this.drawerStyleOptionsParam.enabled : false;
    },
  },
  created() {
    this.activeColumnWatcher = this.$store.watch(
      (state, getters) => getters.GET_ACTIVE_COMPONENT_INDEX,
      (newValue, oldValue) => {
        if (oldValue !== newValue) {
          this.controlsVisible = false;
          setTimeout(() => {
            this.controlsVisible = true;
          }, 200);
        }
      },
    );
  },
  mounted() {
    this.controlsVisible = true;
    this.resizeObserver = new ResizeObserver(entires => {
      this.$emit('uprateHeight', entires[0].target.clientHeight);
    });
    this.resizeObserver.observe(this.$el);
  },
  beforeDestroy() {
    this.activeColumnWatcher();
    this.resizeObserver.unobserve(this.$el);
  },
  methods: {
    leftButtonHandler() {
      this.$refs.thumbnails.$refs.thumbnailTabs.scrollToElement(-3);
    },
    rightButtonHandler() {
      this.$refs.thumbnails.$refs.thumbnailTabs.scrollToElement(3);
    },
    scrollHandler({ target: { scrollLeft, offsetWidth, scrollWidth } }) {
      const offset = 50;
      if (scrollLeft <= offset) {
        this.disabledChevron = 'left';
      } else if (scrollLeft + offsetWidth >= scrollWidth - offset) {
        this.disabledChevron = 'right';
      } else {
        this.disabledChevron = null;
      }
    },
    setLocalXActive(value) {
      this.updateLocalConfiguration('local_x', value);
      this.$store.dispatch('ui/UPDATE_LOCAL_X');
    },
    createSize(data) {
      const doubleDoor = data.doors_exterior.length > 1 || data.doors_raiser.length > 1;
      const heightPlus = data.components[0].height > 2100;
      const width = doubleDoor ? 58 : 48;
      const height = heightPlus ? 115 : 109;
      return { width, height };
    },
  },
};
</script>
