class OwnConsole {
    constructor({ mode }) {
        this.mode = mode;
    }

    log(...theArgs) {
        if (this.mode === 'production') return;
        console.log('DEBUG -->', ...theArgs);
        return [...theArgs];
    }

    warn(...theArgs) {
        if (this.mode === 'production') return;
        console.warn(...theArgs);
        return [...theArgs];
    }
}
const ownConsole = new OwnConsole({ mode: process.env.NODE_ENV });
window.ownConsole = ownConsole;

export default ownConsole;
