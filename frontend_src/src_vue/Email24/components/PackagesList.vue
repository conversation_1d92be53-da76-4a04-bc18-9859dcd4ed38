<template>
    <ul class="packages-list mb-16">
        <h6 class="normal-14 text-offblack-600 mb-8">
            {{ $t('mail24_summary_body_1', {dimensions: title}) }}
        </h6>
        <li
            v-for="(item, index) in packages"
            class="normal-14 text-offblack-600 mb-8"
        >
            <span class="package-list__index bold-14">{{ index + 1 }}.</span>
            <span class="mx-8">
                {{
                    $t('mail24_summary_body_2', {
                        size: item.dimensions + ' mm',
                        weight: item.weight + ' kg',
                    })
                }}
            </span>
        </li>
    </ul>
</template>
<script>
    export default ({
        props: {
            title: {
                type: String,
                default: '',
            },
            packages: {
                type: Array,
                default: () => [],
            },
        },
    });
</script>
<style lang="scss" scoped>
    .packages-list__index {
        width: 28px;
    }
</style>
