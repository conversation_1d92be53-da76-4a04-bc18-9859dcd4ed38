<template>
    <div class="email-24-start">
        <p class="normal-16 text-offblack-600 mb-16 lg:mb-32">
            {{ $t('mail24_survey_body') }}
        </p>
        <div class="email-24-start__address mobile-visible normal-14 text-offblack-600 p-16 mb-32">
            <AddressSection
                :title="$t('mail24_survey_body_mobile')"
            />
        </div>
        <div class="text-center lg:text-left">
            <button
                class="btn-cta"
                @click="increaseStep(step = 1)"
            >
                {{ $t('mail24_survey_CTA') }}
            </button>
        </div>
    </div>
</template>
<script>
    import AddressSection from '../../../components/AddressSection';

    export default {
        components: {
            AddressSection,
        },
        props: {
            increaseStep: {
                type: Function,
                default: () => {},
            },
        },
    };
</script>
