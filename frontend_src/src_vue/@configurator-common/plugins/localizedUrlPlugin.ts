declare global {
    interface Window {
        cstm_i18n: {
            language: string,
        }
    }
}

interface LocalizedUrls {
    enUrl: string,
    frUrl: string,
    deUrl: string,
    nlUrl: string,
    esUrl: string,
    plUrl: string,
    svUrl: string
}

export default {
    install: Vue => {
        Vue.prototype.getLocalizedUrl = (urls: LocalizedUrls): string => {
            const langPrefix = (window.cstm_i18n.language === 'en' ? '' : `/${window.cstm_i18n.language}`);
            const url = urls[`${window.cstm_i18n.language}Url`];

            return `${langPrefix}/${url}`;
        }
    },
};
