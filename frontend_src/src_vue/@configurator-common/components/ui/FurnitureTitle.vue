<template>
  <header
    v-if="!isMobile"
    class="furniture-title--container"
    :style="{'margin-top' : isMaterialPink ? '90px': ''}"
  >
    <div class="lg:grid-container">
      <h1 class="furniture-title">
        {{ furnitureTitle }}
      </h1>
      <br>
      <span
        v-if="!isMobile && isMaterialGreenMoss"
        class="font-semibold capitalize text-12 text-white rounded-6 px-8 py-4 bg-[#5A834B] capitalize"
      >
        {{ $t('common_new') }}
      </span>
    </div>
  </header>
</template>
<script>
export default {
  props: {
    furnitureTitle: {
      type: String,
      default: '',
    },
  },
  computed: {
    isMobile() {
      return this.$store.getters['ui/GET_IS_MOBILE'];
    },
    shelfType() {
      return this.$store.getters.GET_SHELF_TYPE;
    },
    material() {
      return this.$store.getters['configuration/GET_MATERIAL'];
    },
    isMaterialPink() {
      return this.material === 15;
    },
    isMaterialGreenMoss() {
      return this.material === 11 && this.shelfType === 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.furniture-title--container {
  position: absolute;
  top: 20px;
  width: 100%;
  z-index: 100;
  pointer-events: none;
}

.furniture-title {
  color: #1D1E1F;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  height: 25px;
  width: 340px;
  overflow: hidden;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 0;
}
</style>
