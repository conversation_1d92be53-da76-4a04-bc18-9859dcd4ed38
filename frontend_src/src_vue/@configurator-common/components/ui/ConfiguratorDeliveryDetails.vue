<template>
  <aside class="configurator-delivery-details">
    <div class="flex mt-8 items-center justify-center">
      <p class="normal-12 mr-16 configurator-delivery-text">
        {{ $t('common_made_in_eu') }}
      </p>
      <DynamicDeliverTimeLabel />
    </div>
    <p class="mt-8 text-center">
      {{ $t('common_delivery_details') }}
    </p>
  </aside>
</template>

<script>
import DynamicDeliverTimeLabel from '@configurator-common/components/ui/DynamicDeliverTimeLabel';

export default {
  components: {
    DynamicDeliverTimeLabel,
  },
};
</script>

<style scoped>
.configurator-delivery-details p {
  font-size:  12px;
  line-height: 18px;
  color: #4B4D4E!important;
}
.configurator-delivery-text {
  position: relative;
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: #6F7173;
    width: 2px;
    height: 2px;
    right: -8px;
  }
}
</style>
