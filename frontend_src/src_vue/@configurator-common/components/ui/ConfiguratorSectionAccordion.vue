<template>
  <div
    ref="accordion"
    class="configurator-section-accordion mb-32"
  >
    <div
      class="accordion-header mb-16 bold-16 text-offblack-900"
      @click="toggleAccordion"
    >
      <slot name="header" />
      <img
        src="@tylko_ui/icons-cplus/chevron_down-new.svg"
        svg-inline
        class="accordion-chevron"
      >
    </div>
    <div
      ref="accordioncontent"
      class="accordion-content"
    >
      <div class="accordion-content-wrapper">
        <slot name="component" />
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'ConfiguratorSectionAccordion',
  props: {
    icon: {
      type: String,
      required: true,
    },
    opened: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    if (this.$props.opened) {
      this.toggleAccordion();
    }
  },
  methods: {
    toggleAccordion() {
      const { accordion } = this.$refs;
      const showClass = 'accordion-show';

      if (accordion.classList.contains(showClass)) {
        accordion.classList.remove(showClass);
      } else {
        accordion.classList.add(showClass);
      }
    },
  },
};
</script>

<style lang="scss">
  .configurator-section-accordion {
    .accordion-header {
      display: flex;
      flex-direction: row;
      gap: 8px;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;

      > .accordion-chevron {
        width: 12px;
        height: 12px;
        position: relative;
        top: -1px;
        transition: transform .4s ease;
      }
    }

    .accordion-content {
      transition: max-height 0.3s ease;
      visibility: hidden;
      z-index: 40;
      max-height: 0;
    }

    &.accordion-show {

      .accordion-content {
        visibility: visible;
        max-height: 240px;
      }

      .accordion-chevron {
        transform: rotate(180deg);
      }
    }
  }
</style>
