import axios from 'axios';

export default class DynamicDelivery {
	private static instance: DynamicDelivery; // eslint-disable-line
	private deliveryTimeCopy: string;
	private deliveryTimeWithBrokenCopy: string;
	private deliveryTimes: TimeObject;
  private availableDeliveryTimes: { [key: string]: TimeObject };

	constructor() {
		this.deliveryTimes = { min: 0, max: 0 };
    this.availableDeliveryTimes = {};
	}

	getFilledString(text: string, deliveryTime: TimeObject) {
		return text.replace('{0}', deliveryTime.min.toString()).replace('{1}', deliveryTime.max.toString());
	}

	async getUpdatedDeliveryTimeObject(shelfType, material, category) {
    await this.fetchProductionTime(shelfType, material, category);
    return this.getDeliveryTimeObject();
	}

  async getDeliveryTimes(shelfType, material, category) {
    if (this.deliveryTimes.min === 0 && this.deliveryTimes.max === 0) {
      await this.fetchProductionTime(shelfType, material, category);
    };

    return this.deliveryTimes;
  }

	getDeliveryTimeObject() {
		if (this.deliveryTimes.min === 0 && this.deliveryTimes.max === 0) return {};

		const deliveryTimeCopy = this.getFilledString(this.deliveryTimeCopy.replace('<br/>', ''), this.deliveryTimes);
		const deliveryTimeWithBrokenCopy = this.getFilledString(this.deliveryTimeWithBrokenCopy, this.deliveryTimes);

		return {
			deliveryTimeCopy,
			deliveryTimeWithBrokenCopy,
		};
	}

	async fetchProductionTime(shelfType, material, category) {
    const categoryLowerCase = category.toLowerCase();
    const deliveryTimeId = this.getDeliveryTimeId(shelfType, material, category);

    if (this.availableDeliveryTimes[deliveryTimeId]) {
			this.deliveryTimes = this.availableDeliveryTimes[deliveryTimeId];
		} else {
      try {
        const { data } = await axios({
          method: 'GET',
          url: `/api/v1/production-time/?shelf_type=${shelfType}&materials=${material}&furniture_category=${categoryLowerCase}`,
        });
        this.deliveryTimes = data;
        this.availableDeliveryTimes[deliveryTimeId] = data;
        this.deliveryTimeCopy = data.translations.text;
        this.deliveryTimeWithBrokenCopy = data.translations.text_with_broken_copy;

      } catch (error) {
        console.error('Failed to fetch production time:', error);
      }
    }
	}
  
  getDeliveryTimeId(shelf_type: number, material: number, category: string) {
		return `${category}${shelf_type}${material}`;
	}

	public static getInstance(): DynamicDelivery {
		if (!DynamicDelivery.instance) {
			DynamicDelivery.instance = new DynamicDelivery();
		}

		return DynamicDelivery.instance;
	}
}

interface DeliveryTimeObject {
	deliveryTimeCopy: string;
	deliveryTimeWithBrokenCopy: string;
}

type TimeObject = {
	min: number;
	max: number;
};
