/* eslint-disable no-param-reassign */

export default {
  updateDiscounts(state, payload) {
    state.discounts = { ...state.discounts, ...payload };
  },
  setPrice(state, payload) {
    state.price = payload;
  },
  setCurrency(state, payload) {
    state.currency = payload;
  },
  setLocale(state, payload) {
    state.locale = payload;
  },
  setPromo(state, payload) {
    state.promo = payload;
  },
  setOmnibusPrice(state, payload) {
    state.omnibusPrice = payload;
  },
  setDefaultProductPrice(state, payload) {
    state.defaultProductPrice = payload;
  },
  setIsFetchingPromo(state, payload) {
    state.isFetchingPromo = payload;
  },
  setPromoData(state, payload) {
    state.promoData = payload;
  },
  setSeasonSale(state, payload) {
    state.seasonSale = payload;
  },
};
