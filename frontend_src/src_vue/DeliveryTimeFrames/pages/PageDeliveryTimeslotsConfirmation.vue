<template>
  <section>
    <h1 class="normal-24 md:normal-28 text-offblack-800 pb-8 text-center md:text-left">
      {{ isAccepted
        ? $t('delivery_timeslots_confirmation_headline')
        : $t('delivery_timeslots_confirmation_title_1')
      }}
    </h1>
    <p class="mw-unset normal-16 text-offblack-600 mb-16 md:mb-24 text-center md:text-left">
      {{ isAccepted
        ? $t('delivery_timeslots_confirmation_body')
        : $t('delivery_timeslots_confirmation_subtitle', { user: $username }) + ' '
          + (isEditable
            ? $t('delivery_timeslots_confirmation_body_1', { date: $lastDateForChange })
            : $t('delivery_timeslots_confirmation_body_3'))
      }}
    </p>
    <section class="row mt-32">
      <div class="col-xs-12 col-md-6">
        <div class="summary-grid__card border-grey-800 p-16 my-16">
          <p class="normal-20 text-offblack-800">
            {{ isAccepted
              ? $t('delivery_timeslots_confirmation_title')
              : $t('delivery_timeslots_confirmation_title_3')
            }}
            <button
              v-if="isEditable"
              class="summary-grid__button"
              @click="editTimeslots"
            >
              <img
                svg-inline
                src="@tylko_ui/icons-dtf/ic_edit.svg"
              >
            </button>
          </p>
          <ul class="dates mt-8">
            <li
              v-for="(item, key) in (isAccepted ? [$acceptedTimeslot] : timeslots)"
              :key="key"
              class="normal-14 text-offblack-600 dates__item flex py-8"
            >
              <div class="dates__item-date">
                <img
                  svg-inline
                  src="@tylko_ui/icons-dtf/ic_calendar.svg"
                >
                {{ item.date }}
              </div>
              <div class="dates__item-hours">
                <img
                  svg-inline
                  src="@tylko_ui/icons-dtf/ic_clock.svg"
                >
                {{ formatDate(item) }}
              </div>
            </li>
          </ul>
        </div>
        <div class="summary-grid__card border-grey-800 p-16 my-16">
          <p class="normal-20 text-offblack-800">
            {{ $t('delivery_timeslots_confirmation_title_5') }}
            <button
              v-if="isEditable"
              class="summary-grid__button"
              @click="editTimeslots"
            >
              <img
                svg-inline
                src="@tylko_ui/icons-dtf/ic_edit.svg"
              >
            </button>
          </p>
          <p class="normal-14 text-grey-900 mt-8">
            {{ notes || $t('delivery_timeslots_body_5') }}
          </p>
        </div>
        <div class="summary-grid__card border-grey-800 p-16 my-16">
          <p class="normal-20 text-offblack-900 mb-16">
            {{ $t('delivery_timeslots_confirmation_body_6') }}
          </p>
          <ul>
            <template v-for="(order) in $orderDetails">
              <li
                v-for="({
                  url,
                  product_number,
                  description_dimensions,
                }) in order"
                :key="product_number"
                class="summary-grid__instruction flex between-xs"
              >
                <p class="'normal-14 text-offblack-600 mb-16">
                  <!--TODO: Add translations from backend in second iteration-->
                  {{ $t('delivery_timeslots_shelf') }}
                  {{ description_dimensions }}
                </p>
                <a
                  :href="url"
                  rel="noreferrer noopener"
                >
                  <img
                    svg-inline
                    src="@tylko_ui/icons-dtf/ic_instruction.svg"
                  >
                </a>
              </li>
            </template>
          </ul>
          <p class="normal-14 text-grey-900 my-8">
            {{ $t('delivery_timeslots_confirmation_body_7') }}
          </p>
        </div>
      </div>
      <div class="col-xs-12 col-md-6">
        <div class="summary-grid__card border-grey-800 p-16 my-16">
          <p class="normal-24 text-offblack-800">
            {{ $t('delivery_timeslots_confirmation_title_4') }}
          </p>
          <p
            v-for="(order, name) in $orderDetails"
            :key="name"
            class="normal-14 text-offblack-600 mt-16"
          >
            {{ $t('delivery_timeslots_confirmation_body_4') }} {{ name }}
            <ul class="order-details mt-12">
              <template
                v-for="{
                  product_number,
                  description_dimensions,
                  packages,
                } in order"
              >
                <!--TODO: Add translations from backend in second iteration-->
                <!--{{ $t('delivery_timeslots_confirmation_body_5') }}-->
                <!--{{ shelf_type }}-->
                <!--{{ description_material }}-->
                <!--{{ category }},-->
                <!--{{ color_name }},-->
                <p class="normal-16 mt-8 text-offblack-600">
                  {{ $t('delivery_timeslots_shelf') }}
                  {{ description_dimensions }}:
                </p>
                <li
                  v-for="(item, key) in packages"
                  :key="`${product_number}-${key}`"
                  class="normal-14 text-offblack-600 order-details__item py-8 flex"
                >
                  <div class="bold-14 order-details__item-id">
                    {{ key + 1 }}.
                  </div>
                  <div>
                    {{ item.dimensions }}
                  </div>
                  <div class="ml-16">
                    {{ item.weight }} kg
                  </div>
                </li>
              </template>
            </ul>
          </p>
        </div>
      </div>
    </section>
    <TyAccordeon
      class="mt-64"
      :data="faqData(minimalTimeslots)"
    >
      <template slot="title">
        <p class="normal-16-2 text-offblack-800">
          {{ $t('delivery_timeslots_faq_subheader') }}
        </p>
      </template>
    </TyAccordeon>
  </section>
</template>
<script>
import TyAccordeon from '@tylko_ui/components/TyAccordeon';
import { mapGetters } from 'vuex';

export default {
  components: {
    TyAccordeon,
  },
  data() {
    return {
      faqData: () => [
        {
          question: this.$t('delivery_timeslots_faq_question_2'),
          answer: this.$t('delivery_timeslots_faq_answer_2'),
        },
        {
          question: this.$t('delivery_timeslots_faq_question_3'),
          answer: this.$t('delivery_timeslots_faq_answer_3'),
        },
        {
          question: this.$t('delivery_timeslots_faq_question_4'),
          answer: this.$t('delivery_timeslots_faq_answer_4'),
        },
        {
          question: this.$t('delivery_timeslots_faq_question_5'),
          answer: this.$t('delivery_timeslots_faq_answer_5'),
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      timeslots: 'deliveryTimeslots/timeslots',
      notes: 'deliveryTimeslots/notes',
      isEditable: 'deliveryTimeslots/isEditable',
      isAccepted: 'deliveryTimeslots/isAccepted',
      minimalTimeslots: 'deliveryTimeslots/minimalTimeslots',
    }),
  },
  methods: {
    formatDate({
      startHour, endHour, optionalStartHour, optionalEndHour, allDay,
    }) {
      const isOptional = optionalStartHour && optionalEndHour;
      if (allDay) return '6:00 - 23:00';
      return `${startHour} - ${endHour}${isOptional
        ? `, ${optionalStartHour} - ${optionalEndHour}`
        : ''
      }`;
    },
    editTimeslots() {
      this.$router.push({ name: 'delivery-time-frames', params: { dtfId: this.$dtfId } });
    },
  },
};
</script>
