/* eslint-disable camelcase, no-param-reassign */

// TODO: Should have global wrapper for cookies handling
const getCookie = name => {
    const re = new RegExp(`${name}=([^;]+)`);
    const value = re.exec(document.cookie);
    return (value !== null) ? unescape(value[1]) : null;
}

const initialState = () => ({
    user: null,
})

const actions = {}

const mutations = {
    SET_USER(state, user) {
        state.user = user
    },
}

const getters = {
    user: state => {
        const marketing_grant = getCookie('marketing_grant')

        return Object.assign({}, state.user, { marketing_grant })
    },
}

export default {
    state: initialState(),
    actions,
    mutations,
    getters,
    namespaced: true,
}
