<template>
    <button
        class="cta-button add-to-cart-button bg-orange"
        data-testid="a2c-button"
        :class="{loading: cartLoader}"
        @click="addToCart"
    >
        <img
            class="cart-icon"
            svg-inline
            src="@tylko_ui/icons-cplus/ic_cart_outline.svg"
            alt="Cart icon"
        >
        <span
            class="button-wrapper ml-8"
        >
            <slot />
        </span>
        <span
            class="loader-wrapper"
        >
            <tylkoSpinner class="loader-scale" />
        </span>
    </button>
</template>

<script>
    import TylkoSpinner from '@componentsConfigurator/TylkoSpinner';

    export default {
        components: { TylkoSpinner },

        props: {
            addToCart: {
                type: Function,
                required: true,
            },
            cartLoader: {
                type: Boolean,
                required: true,
            },
        },
    };
</script>
