<template>
    <ValidationProvider
        v-slot="{ errors }"
        :rules="rules"
        :name="name"
        tag="div"
    >
        <!-- DESKTOP -->
        <TySelectNoValidation
            v-if="isDesktop"
            v-model="innerValue"
            :errors="errors"
            :name="name"
            :placeholder="placeholder"
            :label="label"
            :options="options"
            :transition="transition"
            :errorMessage="errorMessage"
            :disabled="disabled"
            :mode="mode"
            :optional="optional"
            @change="$emit('change')"
        >
            <template
                v-if="$scopedSlots['custom-label']"
                v-slot:customLabel="{ option }"
            >
                <slot
                    name="custom-label"
                    :option="option"
                />
            </template>
            <template
                v-if="$scopedSlots['single-result']"
                v-slot:singleResult="{ option }"
            >
                <slot
                    name="single-result"
                    :option="option"
                />
            </template>
        </TySelectNoValidation>

        <!-- MOBILE, NATIVE -->
        <TySelectNative
            v-else
            v-model="innerValue"
            :errors="errors"
            :name="name"
            :placeholder="placeholder"
            :label="label"
            :options="options"
            :transition="transition"
            :errorMessage="errorMessage"
            :disabled="disabled"
            :optional="optional"
            @change="$emit('change')"
        >
            <template
                v-if="$scopedSlots['single-result']"
                v-slot:singleResult="{ option }"
            >
                <slot
                    name="single-result"
                    :option="option"
                />
            </template>
        </TySelectNative>
        <div
            v-if="$slots.tooltip"
            class="yoda-select__tooltip p-8 mt-4 mr-12"
        >
            <TyTooltip>
                <template slot="tooltipContent">
                    <slot name="tooltip" />
                </template>
            </TyTooltip>
        </div>
    </ValidationProvider>
</template>
<script>
    import { selectOptionValidator } from '@tylko_ui/helpers';
    import TySelectNoValidation from '@tylko_ui/components/form/_TySelectNoValidation';
    import TySelectNative from '@tylko_ui/components/form/_TySelectNative';
    import TyTooltip from '@tylko_ui/components/TyTooltip';

    export default {
        components: {
            TySelectNoValidation,
            TySelectNative,
            TyTooltip,
        },

        props: {
            name: {
                type: String,
                required: true,
            },

            placeholder: {
                type: String,
                default: 'Select option',
            },

            label: {
                type: String,
                default: 'Select option',
            },

            options: {
                type: Array,
                default: () => [],
                validator: options => (options.every(selectOptionValidator)),
            },

            value: {
                type: [Object, String],
                default: () => {},
            },

            rules: { // vee-validate rules: https://logaretm.github.io/vee-validate/api/rules.html
                type: [String, Object],
                default: '',
            },

            errorMessage: {
                type: String,
                default: '',
            },

            transition: {
                type: [String, Number],
                default: '',
            },

            disabled: {
                type: Boolean,
                default: false,
            },

            optional: {
                type: Boolean,
                default: false,
            },

            mode: {
                type: String,
                default: 'expand',
                validator: val => (val === 'expand' || val === 'absolute'),
            },
        },

        data() {
            return {
                innerValue: null,
            };
        },

        computed: {
            isDesktop() {
                return this.$store.getters['ui/isDesktop'];
            },
        },

        watch: {
            innerValue(val) {
                this.$emit('input', val);
            },
            value(v) {
                this.setInitialOptionWithProp(v);
            },
        },

        created() {
            this.setInitialOption();
        },

        methods: {
            setInitialOption() {
                if (this.value) {
                    const value = typeof this.value === 'object' ? this.value.value : this.value;
                    this.innerValue = this.options.find(item => (item.value === value));
                }
            },
            setInitialOptionWithProp(prop) {
                if (prop) {
                    const value = typeof prop === 'object' ? prop.value : prop;
                    this.innerValue = this.options.find(item => (item.value === value));
                } else this.innerValue = null;
            },
        },
    };
</script>
