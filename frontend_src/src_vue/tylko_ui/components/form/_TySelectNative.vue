<template>
    <div>
        <div
            class="yoda-input-wrapper yoda-country-select yoda-country-select__mobile"
            :class="{
                'yoda-select--error': hasErrors,
                'js-form-error': hasErrors,
                'yoda-select--disabled': disabled,
            }"
            style="overflow:visible"
        >
            <div class="inner">
                <div class="country-select__label">
                    <p
                        class="th-5-m tfc-grey-1150 tml-s"
                        :class="{
                            'yoda-select--error': hasErrors,
                        }"
                    >
                        {{ label }}
                        <span
                            v-if="optional"
                            class="text-grey-900"
                        >
                            ({{ $t('cwatwar_soldout_configurator_popup_checkbox_2') }})
                        </span>
                    </p>
                </div>
                <div class="country-select__container">
                    <select
                        :id="name"
                        v-model="innerValue"
                        :name="name"
                        class="country-native-select"
                        :style="{'color': hasErrors ? '#ff0037' : '', 'background-color': 'transparent'}"
                        @change="onChange"
                    >
                        <option
                            v-for="(option, optionIndex) in innerOptions"
                            :key="`${name}-option-${optionIndex}`"
                            :value="option.value"
                            :disabled="option.disabled"
                        >
                            <slot
                                v-if="$scopedSlots.singleResult"
                                name="singleResult"
                                :option="option"
                            />
                            <template v-else>
                                {{ option.label }}
                            </template>
                        </option>
                    </select>
                </div>
            </div>
        </div>
        <div
            v-if="hasErrors"
            class="yoda-error-message yoda-error-message-- tpl-s tpt-xxs th-5-m tfc-red-base"
            style="color: #ff0037"
        >
            {{ errorMessage || errors[0] }}
        </div>
    </div>
</template>
<script>
    import { selectOptionValidator } from '@tylko_ui/helpers';

    export default {
        props: {
            name: {
                type: String,
                required: true,
            },

            placeholder: {
                type: String,
                default: 'Select option',
            },

            label: {
                type: String,
                default: 'Select option',
            },

            options: {
                type: Array,
                default: () => [],
                validator: options => (options.every(selectOptionValidator)),
            },

            value: {
                type: Object,
                // validator: selectOptionValidator,
                default: () => {},
            },

            errors: {
                type: Array,
                default: () => ([]),
            },

            errorMessage: {
                type: String,
                default: '',
            },

            transition: {
                type: [String, Number],
                default: '',
            },

            disabled: {
                type: Boolean,
                default: false,
            },

            optional: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                innerValue: null,
                innerOptions: [...this.options],
            };
        },

        computed: {
            hasErrors() {
                return (this.errors && this.errors.length);
            },
        },

        watch: {
            value() {
                this.setInitialOption();
            },

            options() {
                this.innerOptions = [...this.options];
            },
        },

        mounted() {
            this.setInitialOption();
        },

        methods: {
            setInitialOption() {
                if (!this.value) {
                    if (this.options && this.options.length) {
                        const placeholderOption = {
                            label: this.placeholder,
                            value: null,
                            currency: '',
                            trans: this.placeholder,
                            dec: '',
                            name: 'placeholder',
                        };
                        const containsPlaceholder = this.innerOptions.find(item => item.value === placeholderOption.value);

                        if (containsPlaceholder) {
                            return;
                        }

                        this.innerOptions = [placeholderOption, ...this.innerOptions];
                        this.innerValue = placeholderOption.value;
                    }

                    return;
                }

                this.innerValue = typeof this.value === 'object' ? this.value.value : this.value;
            },
            async onChange() {
                const value = this.options.find(item => item.value === this.innerValue);
                this.innerOptions = this.innerOptions.filter(item => item.value !== 'placeholder');
                this.$emit('input', value);
                await this.$nextTick();
                this.$emit('change');
            },
        },
    };
</script>
