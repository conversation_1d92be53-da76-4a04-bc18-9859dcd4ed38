<template>
    <ValidationProvider
        v-slot="{ errors }"
        :rules="rules"
        tag="div"
    >
        <TyCheckboxGroupNoValidation
            v-model="innerValue"
            :name="name"
            :options="options"
            :groupOptionsClasses="groupOptionsClasses"
            :errors="errors"
        >
            <slot />
            <template
                #label="{ label }"
            >
                <slot
                    name="label"
                    :label="label"
                />
            </template>
        </TyCheckboxGroupNoValidation>
    </ValidationProvider>
</template>
<script>
    import { selectOptionValidator } from '@tylko_ui/helpers';
    import TyCheckboxGroupNoValidation from '@tylko_ui/components/form/_TyCheckboxGroupNoValidation';

    export default {
        components: {
            TyCheckboxGroupNoValidation,
        },

        props: {
            name: {
                type: String,
                required: true,
            },

            options: {
                type: Array,
                default: () => [],
                validator: items => items.every(selectOptionValidator),
            },

            groupOptionsClasses: {
                type: [Array, String],
                default: () => [],
            },

            value: {
                type: Object,
                default: () => ({}),
            },

            rules: { // vee-validate rules: https://logaretm.github.io/vee-validate/api/rules.html
                type: [String, Object],
                default: '',
            },
        },

        data() {
            return {
                innerValue: null,
            };
        },

        watch: {
            innerValue(val) {
                this.$emit('input', val);
            },

            value(val) {
                this.innerValue = val;
            },
        },

        created() {
            if (this.value) {
                this.innerValue = this.value;
            }
        },
    };
</script>
<style>
    .ty-checkbox-group__options {
        display: flex;
        justify-content: space-between;
    }
</style>
