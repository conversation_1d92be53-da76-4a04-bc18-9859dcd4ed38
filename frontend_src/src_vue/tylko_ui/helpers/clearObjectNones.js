export default function clearObjectNones(data) {
    return Object
        .keys(data)
        .reduce((res, key) => {
            const val = data[key]

            // Fix Strings
            if (typeof val === 'string' && val.toLowerCase() === 'none') {
                res[key] = ''
                return res
            }

            // Handle arrays recursive
            // if (Array.isArray(val)) {
            // TODO: When needed
            // }

            // Handle objects recursive
            if (typeof val === 'object') {
                res[key] = clearObjectNones(val)
                return res
            }

            // All others
            res[key] = val
            return res
        }, {})
}
