var Ds=Object.defineProperty;var Hs=(e,r,o)=>r in e?Ds(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o;var D=(e,r,o)=>(Hs(e,typeof r!="symbol"?r+"":r,o),o);var y={SCALAR:.001},j={DEFAULT_PANEL_THICKNESS:18,LINE:{TYPE_01:0,TYPE_02:1,VENEER_01:2,TYPE_03:3,TYPE_13:4},MASKING_BAR:{DEPTH:15,HEIGHT:30},LIGHT_BAR:{ORIGIN_INSET:{x:0,y:7,z:-24}},BASE:{ORIGIN:{SHORT_LEG_CORRECTION:{x:0,y:10,z:0},NO_CORRECTION:{x:0,y:0,z:0},HANGING_AT:{x:0,y:250,z:0}}}};var re=(e,r=null)=>({x:(e.x2-e.x1||j.DEFAULT_PANEL_THICKNESS)*y.SCALAR,y:(e.y2-e.y1||j.DEFAULT_PANEL_THICKNESS)*y.SCALAR,z:(r||e.z2-e.z1||j.DEFAULT_PANEL_THICKNESS)*y.SCALAR}),Te=(e,r)=>({x:(e.x2-e.x1?e.x1:e.x1-9)*y.SCALAR+r.x/2,y:(e.y2-e.y1?e.y1:e.y1-9)*y.SCALAR+r.y/2,z:(e.z2-e.z1?e.z1:e.z1-9)*y.SCALAR+r.z/2}),h=(e,r)=>({x:e.x*r,y:e.y*r,z:e.z*r}),Pr=(e,r)=>({x:e.x*r.x,y:e.y*r.y,z:e.z*r.z});var vt=(e,r)=>({x:e.x-r.x,y:e.y-r.y,z:e.z-r.z}),v=e=>({x:e.x1+(e.x2-e.x1)/2,y:e.y1+(e.y2-e.y1)/2,z:e.z1+(e.z2-e.z1)/2}),G=e=>({x:e.x2-e.x1,y:e.y2-e.y1,z:e.z2-e.z1});var bi=e=>({x:e.z2-e.z1,y:e.y2-e.y1,z:e.x2-e.x1});var wr=(e,r)=>e.x2===r.x1&&e.y1<=r.y2&&e.y2>=r.y1;var Nr=(e,r)=>e.y2===r.y1&&e.x1<=r.x2&&e.x2>=r.x1,br=(e,r)=>e.y1===r.y2&&e.x1<=r.x2&&e.x2>=r.x1,Qe=(e,r,o)=>e.x1>=r.x1-o&&e.x2<=r.x2+o&&e.y1>=r.y1-o&&e.y2<=r.y2+o,Ws=(e,r,o)=>e.x2<r.x1+o||e.x1>r.x2-o||e.y2<r.y1+o||e.y1>r.y2-o,mt=(e,r,o)=>!Qe(e,r,o)&&!Ws(e,r,o),X=(e,r)=>{let n=r.filter(a=>Qe(e,a,100)||mt(e,a,100));return n.length>0?n[0].uuid:""},qe=(e,r)=>{let o=e.merged?e.merged:e,n=100;return r.filter(s=>Qe(o,s,n)||mt(o,s,n)).map(s=>s.uuid)};var w;(function(e){e.BedsideTable="bedside_table",e.Bookcase="bookcase",e.Chest="chest",e.Desk="desk",e.DressingTable="dressing_table",e.ShoeRack="shoerack",e.Sideboard="sideboard",e.TVStand="tvstand",e.VinylStorage="vinyl_storage",e.WallStorage="wallstorage",e.Wardrobe="wardrobe",e.Sofa="sofa"})(w||(w={}));var b={Type01:"TYPE_01",Type02:"TYPE_02",Type03:"TYPE_03",Type10:"TYPE_10",Type13:"TYPE_13",Veneer01:"VENEER_01",Veneer13:"VENEER_13",Type23:"TYPE_23",Type24:"TYPE_24",Type25:"TYPE_25",Veneer25:"VENEER_25"};var ae=e=>({0:"TYPE_01",1:"TYPE_02",2:"VENEER_01",3:"TYPE_03",4:"TYPE_13",5:"VENEER_13",6:"TYPE_23",7:"TYPE_24",8:"TYPE_25",9:"VENEER_25",10:"TYPE_10"})[e],Lt=e=>"rows"in e&&e.rows.length>0?"row":"col";var Je=(e,r)=>e.furniture_category||e.category||r,P={Original:"original",Tone:"tone",Edge:"edge",Expressions:"expressions",Sofa:"sofa"};var te=e=>{let r=Lt(e);return{col:()=>Ks(e),row:()=>Fs(e)}[r]()},Fs=e=>{let r=Math.min(...e.horizontals.map(N=>N.x1||0)),o=Math.max(...e.horizontals.map(N=>N.x2)),n=Math.min(...e.horizontals.map(N=>N.z1)),a=Math.max(...e.horizontals.map(N=>N.z2)),s=e.configurator_params.row_amount,m=e.configurator_params.row_heights,l=e.configurator_params.stack.top[1],p=e.configurator_params.stack.bottom[1],d=l.active,u=p.active,g=[],S=9,E=0;u&&(g.push({x1:r-1,x2:o+1,z1:n-1,z2:a+1,y1:S-10,y2:S+p.height+10,m_config_id:E,channel_id:E,horizontalOrder:0,verticalOrder:E,type:"row",range:{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}}}),S+=p.height,E++);for(let N=0;N<s;N++)g.push({x1:r-1,x2:o+1,z1:n-1,z2:a+1,y1:S-10,y2:S+m[N]+10,m_config_id:E,channel_id:E,horizontalOrder:0,verticalOrder:E,type:"row",range:{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}}}),S+=m[N],E++;return d&&g.push({x1:r-1,x2:o+1,z1:n-1,z2:a+1,y1:S-10,y2:S+l.height+10,m_config_id:E,channel_id:E,horizontalOrder:0,verticalOrder:E,type:"row",range:{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}}}),g.map((N,f)=>({...N,uuid:Ar(N)}))},Ks=e=>{let r=l=>e.doors.filter(p=>p.x1>=l.x1&&p.x2<=l.x2),o=l=>[...new Set(l.map(p=>p.y1))].length,n=ae(e.shelf_type)==="TYPE_03",a={x:Math.min(...e.components.map(l=>l.x2-l.x1)),y:Math.min(...e.components.map(l=>l.y2-l.y1)),z:Math.min(...e.components.map(l=>l.z2-l.z1))},s={x:Math.max(...e.components.map(l=>l.x2-l.x1)),y:Math.max(...e.components.map(l=>l.y2-l.y1)),z:Math.max(...e.components.map(l=>l.z2-l.z1))};return e.components.map((l,p)=>{let d={...l,range:{min:a,max:s}},u=r(l);if(o(u)>1&&n){let S={...d,y2:u[1].y2,horizontalOrder:p,verticalOrder:0,type:"col"},E={...d,y1:u[0].y1,y2:u[0].y2,horizontalOrder:p,verticalOrder:1,type:"col"};return[S,E]}return[{...d,horizontalOrder:p,verticalOrder:0,type:"col"}]}).flat().map((l,p)=>({...l,uuid:Ar(l)}))},Ar=e=>`${e.horizontalOrder}:${e.verticalOrder}`;var ct=e=>({x:e.width,y:e.height,z:e.depth}),zr=(e,r)=>{let o=Ot(e),n=G(o);return{x:n.x*r.x,y:n.y*r.y,z:n.z*r.z}},Ot=e=>{let r=Math.max(...e.components.map(l=>l.x2)),o=Math.min(...e.components.map(l=>l.x1)),n=Math.max(...e.components.map(l=>l.y2)),a=Math.min(...e.components.map(l=>l.y1)),s=Math.max(...e.components.map(l=>l.z2)),m=Math.min(...e.components.map(l=>l.z1));return{x1:o,x2:r,y1:a,y2:n,z1:m,z2:s}},Mr=(e,r={x:1,y:1,z:1})=>{let o=v(e),n=G(e);return{position:o,size:Pr(n,r)}},Tt=e=>{let r=Ot(e),o=e.components.filter(x=>x.z1===r.z1),n=e.components.filter(x=>x.x1===r.x1),a=e.components.filter(x=>x.x2===r.x2),s=(x,A)=>x.x2===A.x1&&x.z1<=A.z2&&x.z2>=A.z1,m=(x,A)=>x.x1===A.x2&&x.z1<=A.z2&&x.z2>=A.z1,l=(x,A)=>x.z2===A.z1&&x.x1<=A.x2&&x.x2>=A.x1,p=(x,A)=>x.z1===A.z2&&x.x1<=A.x2&&x.x2>=A.x1,d=[];e.components.filter(x=>x.type==="footrest").forEach(x=>{e.components.filter(F=>s(x,F)||m(x,F)||l(x,F)||p(x,F)).length===0&&d.push(x)});let g=[...d].sort((x,A)=>A.z1-x.z1),S=g.length===1&&e.components.length>1?[g[0].m_config_id]:[],E=[],N=[...o].sort((x,A)=>x.x1-A.x1);for(let x=0;x<N.length;x++){let A=N[x],F=N.some(k=>k!==A&&Math.abs(A.x2-k.x1)<.001),V=N.some(k=>k!==A&&Math.abs(A.x1-k.x2)<.001);(F||V)&&E.push(A.m_config_id)}if(N.length===1&&N[0].type!=="footrest"&&E.push(N[0].m_config_id),N.length===2){let x=N.find(A=>A.type!=="footrest");x&&!E.includes(x.m_config_id)&&E.push(x.m_config_id)}let f=[],T=[...n].sort((x,A)=>x.z1-A.z1);for(let x=0;x<T.length;x++){let A=T[x],F=T.some(k=>k!==A&&Math.abs(A.z2-k.z1)<.001),V=T.some(k=>k!==A&&Math.abs(A.z1-k.z2)<.001);(F||V)&&f.push(A.m_config_id)}if(T.length===1&&T[0].type!=="footrest"&&f.push(T[0].m_config_id),T.length===2){let x=T.find(A=>A.type!=="footrest");x&&!f.includes(x.m_config_id)&&f.push(x.m_config_id)}let R=[],C=[...a].sort((x,A)=>x.z1-A.z1);for(let x=0;x<C.length;x++){let A=C[x],F=C.some(k=>k!==A&&Math.abs(A.z2-k.z1)<.001),V=C.some(k=>k!==A&&Math.abs(A.z1-k.z2)<.001);(F||V)&&R.push(A.m_config_id)}if(C.length===1&&C[0].type!=="footrest"&&R.push(C[0].m_config_id),C.length===2){let x=C.find(A=>A.type!=="footrest");x&&!R.includes(x.m_config_id)&&R.push(x.m_config_id)}g.length===1&&e.components.length===1&&(f.push(g[0].m_config_id),R.push(g[0].m_config_id),E.push(g[0].m_config_id)),g.length===2&&e.components.length===2&&(f.push(g[1].m_config_id),R.push(g[1].m_config_id),E.push(g[1].m_config_id),S.push(g[0].m_config_id));let I=N.length===1&&N[0].type==="corner",z=I&&N[0].rotation_z===90,O=I&&N[0].rotation_z===0;return z&&(f=[]),O&&(R=[]),{leftAxis:f,backAxis:E,rightAxis:R,detached:S}},W;(function(e){e.Straight="Straight",e.StraightLeft="StraightLeft",e.StraightRight="StraightRight",e.LShapeLeft="LShapeLeft",e.LShapeRight="LShapeRight",e.UShape="UShape"})(W||(W={}));var dt=(e,r)=>{let o={type:W.Straight,dimensions:{leftAxis:0,backAxis:e.width*y.SCALAR,rightAxis:0}};if(r!==w.Sofa)return o;let n=m=>e.components.find(l=>l.m_config_id===m),a=(m,l)=>{let p=m[m.length-1][`${l}2`]-m[0][`${l}1`],d=m.filter(g=>g.type==="armrest").length*10,u=(m.filter(g=>g.type!=="armrest").length-1)*20;return p-(d+u)},s=Tt(e);return s.leftAxis.length>1&&s.rightAxis.length>1&&(o.type=W.UShape),s.leftAxis.length>1&&s.backAxis.length>1&&s.rightAxis.length<=1&&(o.type=W.LShapeLeft),s.rightAxis.length>1&&s.backAxis.length>1&&s.leftAxis.length<=1&&(o.type=W.LShapeRight),s.leftAxis.length>1&&s.backAxis.length==1&&(o.type=W.StraightLeft),s.rightAxis.length>1&&s.backAxis.length==1&&(o.type=W.StraightRight),o.dimensions.leftAxis=s.leftAxis.length>1?a(s.leftAxis.map(n),"z"):0,o.dimensions.backAxis=s.backAxis.length>1?a(s.backAxis.map(n),"x"):0,o.dimensions.rightAxis=s.rightAxis.length>1?a(s.rightAxis.map(n),"z"):0,o};var Ir={type_01:{main_plate_thickness:18,front_plate_thickness:16,back_plate_thickness:12,insert_plate_thickness:18,support_plate_thickness:18,plinth_height:109},type_01_veneer:{main_plate_thickness:18,front_plate_thickness:18,back_plate_thickness:18,insert_plate_thickness:18,support_plate_thickness:18,plinth_height:109},type_02:{main_plate_thickness:18,front_plate_thickness:18,back_plate_thickness:18,insert_plate_thickness:18,support_plate_thickness:18,long_legs_height:109},type_23:{main_plate_thickness:18,front_plate_thickness:18,back_plate_thickness:18,insert_plate_thickness:18,support_plate_thickness:18,long_legs_height:109},type_24:{main_plate_thickness:18,front_plate_thickness:18,back_plate_thickness:18,insert_plate_thickness:18,support_plate_thickness:18,long_legs_height:109},type_25:{main_plate_thickness:18,front_plate_thickness:18,back_plate_thickness:18,insert_plate_thickness:18,support_plate_thickness:18,long_legs_height:109}};var Yr={bodyTop:{height:800,heightClamp:{min:0,max:1350}}};var Ae;(function(e){e.T01="type_01",e.T02="type_02",e.T01_VENEER="type_01_veneer",e.T23="type_23",e.T24="type_24",e.T25="type_25"})(Ae||(Ae={}));var Ge;(function(e){e.SLANT="slant",e.GRADIENT="gradient",e.PATTERN="pattern",e.GRID="grid",e.FRAME="frame",e.CHARLIE="charlie",e.PORTO="porto"})(Ge||(Ge={}));var q;(function(e){e.Backs="backs",e.Doors="doors",e.Drawers="drawers",e.Inserts="inserts",e.Supports="supports",e.CableManagement="cable_management",e.DeskBeams="desk_beams"})(q||(q={}));var Gt;(function(e){e.H="h",e.D="d",e.V="v"})(Gt||(Gt={}));var kr=(e=0)=>({0:Ge.SLANT,1:Ge.GRADIENT,2:Ge.PATTERN,3:Ge.GRID,4:Ge.FRAME,5:Ge.CHARLIE,6:Ge.PORTO})[e];var Cr=e=>Object.keys(e).length===1?Object.values(e)[0]:e,Br=e=>Ir[e],vr=()=>Yr;function Lr(e){let{x1:r,x2:o,y1:n,y2:a,z1:s,z2:m,subtype:l=null,rotation_value:p=0,pull_value:d=0}=e;return{x1:r,x2:o,y1:n,y2:a,z1:s,z2:m,subtype:l,rotation_value:p,pull_value:d}}var Or=e=>({x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2,z1:e.z1,z2:e.z2,subtype:e.subtype,rotation:e.rotation_value,pull:e.pull_value,center:Vs(e)}),Vs=e=>{let r=e.x1===e.x2&&e.y1===e.y2&&e.z1===e.z2,o=r?e.x1:e.x2-Math.round(Math.abs((e.x2-e.x1)/2)),n=r?e.y1:e.y2-Math.round(Math.abs((e.y2-e.y1)/2));return{x:o,y:n}};var Us=e=>{let r={};return Object.keys(e).length===1?(r.id=Object.keys(e)[0],r.data=Object.values(e)[0]):(r.id=e.id,r.data=e),r},Gr=e=>{let r=Us(e),o=qs(r),n=Xs(o.horizontals),a=$s(n,o.horizontals),s=Zs(o.configurator_params),m=Qs(n,o.verticals,o.halfMainPlate),l=Js(r.data),p=tl(r.data),d={rowsBottomY:n,rowCount:n.length-1,horizontalsInRows:a,verticalsInRows:m,influentialModuleTypes:q,influentialModules:l,workPlaceZones:p,stackStorage:s};return{...o,...d}},qs=({id:e,data:r})=>{let o=al(r.shelf_type),n=kr(r.pattern),a=Br(o),s=js(r,a);return{id:e,width:r.width,widthRange:[r.width/-2,r.width/2],height:r.height,bodyHeight:s,heightRange:[r.height-s,r.height],depth:r.depth,shelfType:o,shelfPattern:n,standards:a,halfMainPlate:Math.floor(a.main_plate_thickness/2),category:r.category,material:r.material,horizontals:r.horizontals,verticals:r.verticals,configurator_params:r.configurator_params}},Dr=e=>e.sort((r,o)=>r-o),js=(e,r)=>{let o=Object.keys(e).includes("plinth")&&e.plinth.length>0,n=Object.keys(e).includes("long_legs")&&e.long_legs.length>0;return o?e.height-r.plinth_height:n?e.height-r.long_legs_height:e.height},Xs=e=>{let r=e.map(n=>n.y1),o=Array.from(new Set(r));return Dr(o)},$s=(e,r)=>{let o={};for(let n of e){let a=[];for(let s of r)s.y1===n&&a.push(s.x1,s.x2);o[n]=Dr(a)}return o},Zs=e=>{let r={bottom:{active:!1},top:{active:!1}};return Object.keys(e).includes("stack")&&(r.bottom=Object.values(e.stack.bottom)[0],r.top=Object.values(e.stack.top)[0]),r},Qs=(e,r,o)=>{let n={};for(let a of e){let s=[];for(let m of r)if(m.y1-o===a){let p={x:m.x1,yTop:m.y2+o};s.push(p)}n[a]=s.sort((m,l)=>m.x-l.x)}return n},Js=e=>{let r={},o=Object.values(q);for(let n of o)r[n]=el(e,n);return r},el=(e,r)=>(Object.keys(e).includes(r)?e[r]:[]).map(n=>{let a=Lr(n);return Or(a)}),tl=e=>{let r=il(e),o=rl(e);return r.map(n=>ol(n,o))},il=e=>{let r=q.DeskBeams;return Hr(e,r)?e[r].map(a=>({x1:a.x1,x2:a.x2,width:Math.abs(a.x2-a.x1),depth:e.depth})):[]},rl=e=>{let r=q.CableManagement;return Hr(e,r)?e[r].filter(n=>Object.keys(n).includes("subtype")&&n.subtype===Gt.H):[]},Hr=(e,r)=>Object.keys(e).includes(r)&&e[r].length>0,ol=(e,r)=>{let o={center:{x:Math.round(e.x1+Math.abs(e.x2-e.x1)/2),z:Math.round(e.depth/2)}};return r.length>0&&r.forEach(n=>{e.x1<n.x1&&n.x1<o.center.x&&(o.cableManagementLeft={x:n.x1,z:n.z1}),o.center.x<n.x1&&n.x1<e.x2&&(o.cableManagementRight={x:n.x1,z:n.z1})}),{...e,anchors:o}},al=(e=0)=>({0:Ae.T01,1:Ae.T02,2:Ae.T01_VENEER,6:Ae.T23,7:Ae.T24,8:Ae.T25})[e];var ue;(function(e){e.TopOpen="topOpen",e.BodyRightOpen="bodyRightOpen",e.BodyLeftOpen="bodyLeftOpen",e.BodyClosed="bodyClosed"})(ue||(ue={}));var Wr=e=>({x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2,z1:e.z1,z2:e.z2,classification:e.classification,inRows:e.inRows,isValid:nl(e),workPlaceZones:e.workPlaceZones}),nl=e=>!Object.values(e).includes(null);var sl=typeof global=="object"&&global&&global.Object===Object&&global,Fr=sl;var ll=typeof self=="object"&&self&&self.Object===Object&&self,ml=Fr||ll||Function("return this")(),Kr=ml;var cl=Kr.Symbol,pt=cl;var Vr=Object.prototype,dl=Vr.hasOwnProperty,pl=Vr.toString,Rt=pt?pt.toStringTag:void 0;function _l(e){var r=dl.call(e,Rt),o=e[Rt];try{e[Rt]=void 0;var n=!0}catch{}var a=pl.call(e);return n&&(r?e[Rt]=o:delete e[Rt]),a}var Ur=_l;var yl=Object.prototype,hl=yl.toString;function gl(e){return hl.call(e)}var qr=gl;var ul="[object Null]",fl="[object Undefined]",jr=pt?pt.toStringTag:void 0;function El(e){return e==null?e===void 0?fl:ul:jr&&jr in Object(e)?Ur(e):qr(e)}var Dt=El;function xl(e){return e!=null&&typeof e=="object"}var Xr=xl;var Sl="[object Symbol]";function Tl(e){return typeof e=="symbol"||Xr(e)&&Dt(e)==Sl}var $r=Tl;var Rl=/\s/;function Pl(e){for(var r=e.length;r--&&Rl.test(e.charAt(r)););return r}var Zr=Pl;var wl=/^\s+/;function Nl(e){return e&&e.slice(0,Zr(e)+1).replace(wl,"")}var Qr=Nl;function bl(e){var r=typeof e;return e!=null&&(r=="object"||r=="function")}var et=bl;var Jr=0/0,Al=/^[-+]0x[0-9a-f]+$/i,zl=/^0b[01]+$/i,Ml=/^0o[0-7]+$/i,Il=parseInt;function Yl(e){if(typeof e=="number")return e;if($r(e))return Jr;if(et(e)){var r=typeof e.valueOf=="function"?e.valueOf():e;e=et(r)?r+"":r}if(typeof e!="string")return e===0?e:+e;e=Qr(e);var o=zl.test(e);return o||Ml.test(e)?Il(e.slice(2),o?2:8):Al.test(e)?Jr:+e}var eo=Yl;var to=1/0,kl=17976931348623157e292;function Cl(e){if(!e)return e===0?e:0;if(e=eo(e),e===to||e===-to){var r=e<0?-1:1;return r*kl}return e===e?e:0}var Ht=Cl;var Bl="[object AsyncFunction]",vl="[object Function]",Ll="[object GeneratorFunction]",Ol="[object Proxy]";function Gl(e){if(!et(e))return!1;var r=Dt(e);return r==vl||r==Ll||r==Bl||r==Ol}var io=Gl;var Dl=9007199254740991,Hl=/^(?:0|[1-9]\d*)$/;function Wl(e,r){var o=typeof e;return r=r==null?Dl:r,!!r&&(o=="number"||o!="symbol"&&Hl.test(e))&&e>-1&&e%1==0&&e<r}var ro=Wl;function Fl(e,r){return e===r||e!==e&&r!==r}var oo=Fl;var Kl=9007199254740991;function Vl(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Kl}var ao=Vl;function Ul(e){return e!=null&&ao(e.length)&&!io(e)}var no=Ul;function ql(e,r,o){if(!et(o))return!1;var n=typeof r;return(n=="number"?no(o)&&ro(r,o.length):n=="string"&&r in o)?oo(o[r],e):!1}var so=ql;var jl=Math.ceil,Xl=Math.max;function $l(e,r,o,n){for(var a=-1,s=Xl(jl((r-e)/(o||1)),0),m=Array(s);s--;)m[n?s:++a]=e,e+=o;return m}var lo=$l;function Zl(e){return function(r,o,n){return n&&typeof n!="number"&&so(r,o,n)&&(o=n=void 0),r=Ht(r),o===void 0?(o=r,r=0):o=Ht(o),n=n===void 0?r<o?1:-1:Ht(n),lo(r,o,n,e)}}var mo=Zl;var Ql=mo(),Ai=Ql;var po=e=>{let r=Jl(e);return{shelf:e,openings:r}},Jl=e=>{let r=[],o=e.rowsBottomY.length;for(let n=1;n<=o;n++){let a=e.rowsBottomY[n-1],s=Math.min(...e.horizontalsInRows[a]),m=Math.max(...e.horizontalsInRows[a]),l={rowBottomY:a,rowTopY:e.rowsBottomY[n],bottomHorizontalsDomainX:e.horizontalsInRows[a],upperHorizontalsDomainX:[],classification:ue.TopOpen,leftEnd:0,rightEnd:0};if(n!==o){let p=e.verticalsInRows[a];for(let d=0;d<p.length;d++){let u=p[d],g=p.length-1,S=e.rowsBottomY[n];l.upperHorizontalsDomainX=e.horizontalsInRows[S],d===0&&s<u.x-e.halfMainPlate&&(l.leftEnd=s-e.halfMainPlate,l.rightEnd=u.x,l.classification=ue.BodyLeftOpen,Wt(Ft(l,e),r)),d!==g&&(l.leftEnd=u.x,l.rightEnd=p[d+1].x,l.classification=ue.BodyClosed,Wt(Ft(l,e),r)),d===g&&m>u.x+e.halfMainPlate&&(l.leftEnd=u.x,l.rightEnd=m+e.halfMainPlate,l.classification=ue.BodyRightOpen,Wt(Ft(l,e),r))}}else l.leftEnd=s-e.halfMainPlate,l.rightEnd=m+e.halfMainPlate,Wt(Ft(l,e),r)}return r},Wt=(e,r)=>{typeof e!="undefined"&&e.isValid&&r.push(e)},Ft=(e,r)=>{let o=e.rightEnd-Math.round(Math.abs((e.rightEnd-e.leftEnd)/2)),n=zi(o,e.bottomHorizontalsDomainX),a=zi(o,e.upperHorizontalsDomainX),s=null,m=null,l=null,p=null,d=[],u=[];if(n)if(s=e.leftEnd+r.halfMainPlate,m=e.rightEnd-r.halfMainPlate,l=e.rowBottomY+r.halfMainPlate,a)p=e.rowTopY-r.halfMainPlate,d=co(l,p,r.rowsBottomY);else if(e.classification===ue.TopOpen){let S=vr();l<=S.bodyTop.heightClamp.max&&(p=l+S.bodyTop.height,u=r.workPlaceZones)}else{let S=em(e.rowBottomY,o,r);typeof S!="undefined"&&(p=S-r.halfMainPlate,d=co(l,p,r.rowsBottomY))}let g={x1:s,x2:m,y1:l,y2:p,z1:0,z2:r.depth,classification:e.classification,inRows:d,workPlaceZones:u};return Wr(g)},em=(e,r,o)=>o.rowsBottomY.filter(s=>e<s).find(s=>zi(r,o.horizontalsInRows[s])),co=(e,r,o)=>{let n=[];for(let a=1;a<=o.length;a++)if(o[a-1]<e&&e<o[a]){n.push(a);for(let s=a+1;s<=o.length;s++)o[s-1]<r&&n.push(s)}return n},zi=(e,r)=>{let n=Ai(0,r.length,2).map(s=>[r[s],r[s+1]]),a=([s,m])=>s<=e&&e<=m;return n.some(a)};var Mi;(function(e){e.Top="expoTop",e.RightOpen="expoRightOpen",e.LeftOpen="expoLeftOpen",e.Hollow="expoHollow",e.Support="expoSupport",e.Back="expoBack",e.BackCable="expoBackCable",e.Inserts="expoInserts",e.InsertsCable="expoInsertsCable"})(Mi||(Mi={}));var Ii;(function(e){e.Door="storageDoor",e.DoorCable="storageDoorCable",e.Drawer="storageDrawer",e.Inserts="storageInserts",e.InsertsCable="storageInsertsCable"})(Ii||(Ii={}));var K={EXPO:Mi,STORAGE:Ii},Ne;(function(e){e.Doors="closedDoors",e.Drawers="closedDrawer"})(Ne||(Ne={}));var ze;(function(e){e.FreeStanding="freeStanding",e.LeaningLeft="leaningLeft",e.LeaningRight="leaningRight",e.LeaningBack="leaningBack"})(ze||(ze={}));var Kt=class{constructor(r){D(this,"x1");D(this,"x2");D(this,"y1");D(this,"y2");D(this,"z1");D(this,"z2");D(this,"inRows");D(this,"bottomAnchor");D(this,"width");D(this,"height");D(this,"depth");D(this,"classification");D(this,"type");D(this,"id");D(this,"workPlaceZones");this.x1=r.x1,this.x2=r.x2,this.y1=r.y1,this.y2=r.y2,this.z1=r.z1,this.z2=r.z2,this.bottomAnchor=this.findBottomAnchor(),this.id=this.generateId(this.bottomAnchor.y,this.bottomAnchor.x),this.width=this.getDistance(this.x1,this.x2),this.height=this.getDistance(this.y1,this.y2),this.depth=this.getDistance(this.z1,this.z2),this.inRows=r.inRows,this.classification=r.classification,this.type=this.translateOpeningClassificationToSlotType(),this.workPlaceZones=r.workPlaceZones}findBottomAnchor(){return{x:Math.round((this.x1+this.x2)/2),y:this.y1,z:Math.round((this.z1+this.z2)/2)}}getDistance(r,o){return Math.abs(r-o)}translateOpeningClassificationToSlotType(){switch(this.classification){case ue.BodyLeftOpen:return K.EXPO.LeftOpen;case ue.BodyRightOpen:return K.EXPO.RightOpen;case ue.TopOpen:return K.EXPO.Top;default:return ue.BodyClosed}}generateId(r,o){return`${r}${o}`}detectModulesInSlot(r){let o=new Set;for(let n of Object.keys(r))for(let a of r[n])this.isModuleInSlot(a)&&(n===q.Doors?a.rotation!==0?o.add(n):o.add(Ne.Doors):n===q.Drawers?a.pull!==0?o.add(n):o.add(Ne.Drawers):o.add(n));return o}isModuleInSlot(r){let o=this.x1<r.center.x&&r.center.x<this.x2,n=this.y1<r.center.y&&r.center.y<this.y2;return o&&n}specifySlotType(r){r.has(Ne.Doors)?this.type=Ne.Doors:r.has(Ne.Drawers)?this.type=Ne.Drawers:r.has(q.Doors)||r.has(q.Drawers)?this.specifyStorageType(r):this.specifyExpoType(r)}specifyStorageType(r){r.has(q.Doors)?(this.type=K.STORAGE.Door,r.has(q.Inserts)?(this.type=K.STORAGE.Inserts,r.has(q.CableManagement)&&(this.type=K.STORAGE.InsertsCable)):r.has(q.CableManagement)&&(this.type=K.STORAGE.DoorCable)):r.has(q.Drawers)&&(this.type=K.STORAGE.Drawer)}specifyExpoType(r){r.has(q.Backs)?(this.type=K.EXPO.Back,r.has(q.Inserts)?(this.type=K.EXPO.Inserts,r.has(q.CableManagement)&&(this.type=K.EXPO.InsertsCable)):r.has(q.CableManagement)&&(this.type=K.EXPO.BackCable)):r.has(q.Supports)?this.type=K.EXPO.Support:this.type=K.EXPO.Hollow}updateSlotDepth(r,o){r.has(q.Backs)&&(this.z1+=o.back_plate_thickness),(r.has(q.Doors)||r.has(q.Drawers))&&(this.z2-=o.front_plate_thickness),r.has(q.Supports)&&(this.z1+=o.support_plate_thickness),this.depth=Math.abs(this.z1-this.z2)}findSubdivisions(r){let o=this.getModulesInSlot(r[q.Inserts]),n=this.findSubSlotsCoordinates(o),a=this.createSubSlots(n),s=this.getModulesInSlot(r[q.CableManagement]);return this.updateSubSlotsTypes(a,s),a}getModulesInSlot(r){let o=[];for(let n of r)this.isModuleInSlot(n)&&o.push(n);return o}findSubSlotsCoordinates(r){return{x1:this.getAllCoors(r,"x1"),x2:this.getAllCoors(r,"x2"),y1:this.getAllCoors(r,"y1"),y2:this.getAllCoors(r,"y2")}}sortArray(r){return r.sort((o,n)=>o-n)}getAllCoors(r,o){let n=[];if(o==="x1"){n=[this.x1];for(let a of r)a.subtype==="v"&&n.push(a.x2)}if(o==="x2"){n=[this.x2];for(let a of r)a.subtype==="v"&&n.push(a.x1)}if(o==="y1"){n=[this.y1];for(let a of r)a.subtype==="h"&&n.push(a.y2)}if(o==="y2"){n=[this.y2];for(let a of r)a.subtype==="h"&&n.push(a.y1)}return this.sortArray(n)}createSubSlots(r){let o=[],n=r.x1.values(),a=r.x2.values(),s=r.y1.values(),m=r.y2.values(),l=[];for(let d of Object.keys(r))l.push(r[d].length);let p=Math.max(...l);for(let d=0;d<p;d++){let u=this.findCoordinate(n,this.x1),g=this.findCoordinate(a,this.x2),S=this.findCoordinate(s,this.y1),E=this.findCoordinate(m,this.y2),N={x1:u,x2:g,y1:S,y2:E,z1:this.z1,z2:this.z2,bottomAnchor:{x:Math.round((u+g)/2),y:S,z:Math.round((this.z1+this.z2)/2)},id:this.generateId(S,Math.round((u+g)/2)),width:this.getDistance(u,g),height:this.getDistance(S,E),depth:this.getDistance(this.z1,this.z2),inRows:this.inRows,type:this.type};o.push(N)}return o}findCoordinate(r,o){let n=r.next().value;return typeof n!="undefined"?n:o}updateSubSlotsTypes(r,o){if(r.forEach(n=>{n.type===K.EXPO.InsertsCable&&(n.type=K.EXPO.Inserts),n.type===K.STORAGE.InsertsCable&&(n.type=K.STORAGE.Inserts)}),o.length>0)for(let n of r)for(let a of o){let s=n.x1<a.center.x&&a.center.x<n.x2,m=n.y1<a.center.y&&a.center.y<n.y2;s&&m&&(n.type===K.EXPO.Inserts?n.type=K.EXPO.InsertsCable:n.type===K.STORAGE.Inserts&&(n.type=K.STORAGE.InsertsCable))}}};var Yi=e=>{let{slots:r,closedStorageCount:o}=tm(e),n=r.length,a=e.openings.length,s=rm(r),m=im(a,o),l=om(s,n);return{shelf:e.shelf,slots:r,slotCount:n,closedStorageCount:o,exposurePercentage:m,expoSlotsCount:s,storageSlotsCount:_t(n-s),expoSlotsPercentage:l,storageSlotsPercentage:_t(100-l)}},tm=({shelf:e,openings:r})=>{let o=0,n=[];for(let s of r){let m=new Kt(s);if(m.classification===ue.BodyClosed){let l=m.detectModulesInSlot(e.influentialModules);if(m.specifySlotType(l),m.updateSlotDepth(l,e.standards),m.type!==Ne.Doors&&m.type!==Ne.Drawers)if(l.has(q.Inserts)){let p=m.findSubdivisions(e.influentialModules);n.push(...p)}else n.push(m);else o++}else n.push(m)}let a=[...n];return{slots:n.map(s=>({...s,...sm(s,e,a)})),closedStorageCount:o}},im=(e,r)=>{let o=e-r;return _t(o/e*100)},rm=e=>e.filter(({type:o})=>Object.values(K.EXPO).includes(o)).length,om=(e,r)=>_t(e/r*100),am=({type:e})=>[K.EXPO.BackCable,K.EXPO.InsertsCable,K.STORAGE.DoorCable,K.STORAGE.InsertsCable].includes(e),nm=({type:e})=>{let r=Object.values(ze),o=[];return e===K.EXPO.Hollow||e===K.EXPO.Support?o=[ze.LeaningBack]:e===K.EXPO.LeftOpen?o=[ze.LeaningBack,ze.LeaningLeft]:e===K.EXPO.RightOpen?o=[ze.LeaningBack,ze.LeaningRight]:e===K.EXPO.Top&&(o=[ze.LeaningLeft,ze.LeaningRight]),r.filter(n=>!o.includes(n))},sm=(e,r,o)=>({slotCountInRow:mm(e,o),indexInRow:cm(e,o),positionalContext:lm(e,r),itemsOrientations:nm(e),hasCableManagement:am(e)}),lm=(e,{widthRange:r,heightRange:o})=>({width:_o(e.x1,e.x2,r),height:_o(e.y1,e.y2,o)}),_o=(e,r,o)=>{let n=yo(e,o),a=yo(r,o);return{start:n,end:a,center:pm([n,a])}},mm=(e,r)=>ho(e.inRows[0],r).length,ho=(e,r)=>r.filter(o=>o.inRows[0]===e),cm=(e,r)=>{let o=ho(e.inRows[0],r).map(n=>n.x1);return dm(o).indexOf(e.x1)},dm=e=>e.sort((r,o)=>r.x1-o.x1),yo=(e,r)=>{let o=(e-Math.min(...r))*100/(Math.max(...r)-Math.min(...r));return _t(o)},pm=e=>{let r=e.reduce((o,n)=>o+n,0);return _t(r/e.length)},_t=e=>Math.round(e*10)/10;var ki=e=>({shelfId:e.shelf.id,shelfType:e.shelf.shelfType,shelfPattern:e.shelf.shelfPattern,shelfMaterial:e.shelf.material,shelfCategory:e.shelf.category,width:e.shelf.width,height:e.shelf.height,bodyHeight:e.shelf.bodyHeight,depth:e.shelf.depth,rowCount:e.shelf.rowCount,exposurePercentage:e.exposurePercentage,slotsCount:{expo:e.expoSlotsCount,storage:e.storageSlotsCount,total:e.slotCount},slotsRatio:{expo:e.expoSlotsPercentage,storage:e.storageSlotsPercentage},closedStorageCount:e.closedStorageCount,stackStorage:e.shelf.stackStorage,slots:_m(e.slots)}),_m=e=>e.map(r=>({id:r.id,x1:r.x1,x2:r.x2,y1:r.y1,y2:r.y2,z1:r.z1,z2:r.z2,width:r.width,height:r.height,depth:r.depth,type:r.type,positionalContext:r.positionalContext,inRows:r.inRows,slotCountInRow:r.slotCountInRow,indexInRow:r.indexInRow,bottomAnchor:r.bottomAnchor,itemsOrientations:r.itemsOrientations,hasCableManagement:r.hasCableManagement,workPlaceZones:r.workPlaceZones}));var go=e=>{let r=Cr(e),o=Gr(r),n=po(o),a=Yi(n);return ki(a)},uo=e=>{let r=ym(e),o=Yi(r);return ki(o)},ym=e=>{let r=e.slabs.find(s=>s.subtype==="t"),o={classification:"topOpen",inRows:[],isValid:!0,workPlaceZones:[],x1:r.x1,x2:r.x2,z1:r.z1,z2:r.z2,y1:r.y2,y2:r.y2+900},a=[w.Chest,w.BedsideTable,"bedside_table"].includes(e.category)?w.Sideboard:e.category;return{shelf:{width:r.x2-r.x1,height:r.y2,depth:r.z2-r.z1,widthRange:[r.x1,r.x2],heightRange:[0,r.y2],shelfType:Ae.T01,category:a,shelfMaterial:e.material,shelfPattern:"grid"},openings:[o]}};var je;(function(e){e.Area="area",e.Random="random",e.Max="max",e.betaTvStandTester="betaTvStandTester"})(je||(je={}));var Ci;(function(e){e.LivingRoom="livingRoom",e.DinningRoom="dinningRoom",e.BedRoom="bedRoom",e.KidsRoom="kidsRoom",e.Hallway="hallway",e.Office="office"})(Ci||(Ci={}));var Vt;(function(e){e.Chest="chest",e.TvStand="tvstand",e.SideBoard="sideboard",e.Bookcase="bookcase",e.Shoerack="shoerack",e.Wallstorage="wallstorage",e.Desk="desk",e.Unknown="unknown"})(Vt||(Vt={}));var fo={mode:je.Area,space:Ci.LivingRoom,activeAreas:{top:!0,body:!0}};var Eo={"tvStand_v1.0.0":{id:"tvStand_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["tvstand"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:1,max:8},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[{id:"ST_001",isActive:!0,priorityRate:8,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[2091,2540],value:1050},{from:"left",inRange:[2541,2960],value:1480}],relative:[{inRange:[1400,2090],value:50},{inRange:[2961,4500],value:50}],special:[]},y:"bottom",z:"center"},size:{depth:{max:500,min:50},height:{max:760,min:600},width:{max:1240,min:800}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#elec0069",".electronics","TV_55","black"]}},{id:"ST_002",isActive:!0,priorityRate:5,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[2301,2410],value:2030},{from:"left",inRange:[2701,3230],value:440},{from:"right",inRange:[3231,3450],value:2800}],relative:[{inRange:[3451,4500],value:19},{inRange:[2411,2700],value:16.2},{inRange:[2100,2300],value:12}],special:[]},y:"bottom",z:"center"},size:{depth:{max:400,min:100},height:{max:200,min:140},width:{max:290,min:179}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#cera0008",".ceramics","vase","terracotta","orange","round","balls"]}},{id:"ST_003",isActive:!0,priorityRate:3,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[3510,3710],value:420}],relative:[{inRange:[3711,4500],value:11.5}],special:[]},y:"bottom",z:"center"},size:{depth:{max:900,min:100},height:{max:800,min:700},width:{max:620,min:550}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0014",".plants","monstera","grey","pot"]}},{id:"ST_004",isActive:!0,priorityRate:10,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[300,400],value:150},{from:"right",inRange:[3810,4500],value:300},{from:"left",inRange:[401,1090],value:250}],relative:[{inRange:[1091,1390],value:23.6}],special:[]},y:"bottom",z:"center"},size:{depth:{max:500,min:100},height:{max:400,min:172},width:{max:260,min:196}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#lamp0056",".lamps","classic","metal","silver","standing","design"]}},{id:"ST_005",isActive:!0,priorityRate:9,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[710,1390],value:68.7},{inRange:[4060,4500],value:29.1}],special:[]},y:"bottom",z:"center"},size:{depth:{max:500,min:100},height:{max:50,min:20},width:{max:340,min:196}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#book0021",".books","horizontal","album","design","bauhaus"]}},{id:"ST_006",isActive:!0,priorityRate:4,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[2810,2990],value:220},{from:"left",inRange:[3401,3800],value:3160}],relative:[{inRange:[3801,4500],value:83.1},{inRange:[2991,3400],value:92.8}],special:[]},y:"bottom",z:"center"},size:{depth:{max:600,min:100},height:{max:500,min:350},width:{max:360,min:200}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0038",".plants","rubber","beige","pot"]}},{id:"ST_007",isActive:!0,priorityRate:6,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[2051,2300],value:290},{from:"left",inRange:[3811,4090],value:2810},{from:"left",inRange:[1901,2050],value:1770},{from:"left",inRange:[2631,3140],value:2310}],relative:[{inRange:[4091,4500],value:68.4},{inRange:[1600,1900],value:92.8},{inRange:[3141,3810],value:73.4},{inRange:[2301,2630],value:87.7}],special:[]},y:"bottom",z:"center"},size:{depth:{max:400,min:40},height:{max:40,min:9},width:{max:100,min:70}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#elec0068",".electronics","apple_tv","dark_gray","station"]}},{id:"ST_008",isActive:!0,priorityRate:7,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[1460,1590],value:50},{from:"right",inRange:[2021,2110],value:190},{from:"left",inRange:[3741,4090],value:2890},{from:"left",inRange:[1881,2020],value:1840},{from:"left",inRange:[2621,3090],value:2390}],relative:[{inRange:[4091,4500],value:70.4},{inRange:[1591,1880],value:97.5},{inRange:[3091,3740],value:77.2},{inRange:[2111,2620],value:91}],special:[]},y:"bottom",z:"center"},size:{depth:{max:400,min:10},height:{max:10,min:5},width:{max:40,min:10}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#elec0021",".electronics","apple_tv","grey","remote"]}}]},shelfBody:{itemsCount:{min:0,max:12},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:16,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:80,inRange:[1400,2900],start:20},{end:75,inRange:[2901,3910],start:25},{end:70,inRange:[3911,4500],start:30}]},y:{absolute:[],relative:[{end:100,inRange:[0,1350],start:30}]},z:"center"},rows:[1,2,3,4,5,6],size:{depth:{max:551,min:300},height:{max:390,min:170},width:{max:870,min:520}},types:["expoBackCable","expoHollow","expoInsertsCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"gravityFront"}},tags:["#elec0003",".electronics","audio_video_receiver","black"]}},{id:"SB_002",isActive:!0,priorityRate:14,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,9,10,11]},maxCount:1,position:{x:{absolute:[{end:0,from:"right",inRange:[3491,3900],start:2730}],relative:[{end:100,inRange:[1400,4500],start:0}]},y:{absolute:[{end:1e3,from:"bottom",inRange:[1101,1350],start:0}],relative:[{end:85,inRange:[400,1100],start:0}]},z:"center"},rows:[1,2,3,4,5],size:{depth:{max:500,min:200},height:{max:450,min:300},width:{max:850,min:160}},types:["expoBackCable","expoHollow","expoInsertsCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"center"}},tags:["#elec0014",".electronics","xbox","vertical","black","gaming"]}},{id:"SB_003",isActive:!0,priorityRate:12,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:96.5,inRange:[0,2e3],start:0},{end:93,inRange:[2001,4500],start:0}]},y:{absolute:[],relative:[{end:85,inRange:[500,900],start:0},{end:80,inRange:[901,1350],start:8}]},z:"center"},rows:[1,2,3,4,5,6],size:{depth:{max:500,min:200},height:{max:850,min:400},width:{max:850,min:270}},types:["expoBack","expoHollow","expoBackCable","expoSupport","expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#cera0011",".ceramics","tall","vertical","vase","design"]}},{id:"SB_004",isActive:!0,priorityRate:13,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,2340],start:0},{end:90,inRange:[2341,4500],start:10}]},y:{absolute:[],relative:[{end:88,inRange:[700,1350],start:0}]},z:"center"},rows:[1,2,3,4,5,6],size:{depth:{max:500,min:300},height:{max:850,min:650},width:{max:850,min:270}},types:["expoBack","expoHollow","expoBackCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#cera0022",".ceramics","tall","vertical","vase","decorative","colorful","design"]}},{id:"SB_005",isActive:!0,priorityRate:9,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,9,10,11]},maxCount:1,position:{x:{absolute:[{end:1180,from:"right",inRange:[1301,1530],start:0},{end:2500,from:"right",inRange:[3301,3690],start:170}],relative:[{end:100,inRange:[300,1300],start:10},{end:95,inRange:[1531,3300],start:24},{end:95,inRange:[3691,4500],start:32.5}]},y:{absolute:[{end:550,from:"bottom",inRange:[751,1350],start:0}],relative:[{end:74,inRange:[400,750],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:500,min:250},height:{max:390,min:350},width:{max:850,min:230}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:65},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0022",".books","vertical","design","fasion","culture","style","colors"]}},{id:"SB_006",isActive:!0,priorityRate:8,slotRequirements:{indexInRow:{from:"left",index:[0,1,2]},maxCount:1,position:{x:{absolute:[{end:1180,from:"left",inRange:[1301,1780],start:0},{end:2180,from:"left",inRange:[3301,3690],start:0}],relative:[{end:90,inRange:[300,1300],start:0},{end:66,inRange:[1781,3300],start:0},{end:59,inRange:[3691,4500],start:0}]},y:{absolute:[{end:530,from:"top",inRange:[601,1040],start:0}],relative:[{end:100,inRange:[260,600],start:13},{end:100,inRange:[1041,1350],start:45}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:500,min:200},height:{max:440,min:250},width:{max:850,min:240}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:65},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0023",".books","vertical","design","fasion","culture","style","colors"]}},{id:"SB_007",isActive:!0,priorityRate:7,slotRequirements:{indexInRow:{from:"right",index:[0]},maxCount:1,position:{x:{absolute:[{end:1085,from:"right",inRange:[1601,2750],start:0},{end:1460,from:"right",inRange:[3701,4500],start:0}],relative:[{end:100,inRange:[300,1600],start:33},{end:100,inRange:[2751,3700],start:60.5}]},y:{absolute:[{end:400,from:"top",inRange:[701,1350],start:0}],relative:[{end:100,inRange:[500,700],start:40}]},z:"center"},rows:[1,2,3,4,5],size:{depth:{max:500,min:250},height:{max:440,min:160},width:{max:850,min:300}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:25}},tags:["#book0007",".books","horizontal","design","fasion","culture","style","pastel"]}},{id:"SB_008",isActive:!0,priorityRate:15,slotRequirements:{indexInRow:{from:"left",index:[0,1]},maxCount:1,position:{x:{absolute:[{end:800,from:"left",inRange:[2300,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[300,1350],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:500,min:220},height:{max:800,min:270},width:{max:850,min:170}},types:["expoBack","expoHollow","expoBackCable","expoSupport","expoInsertsCable","expoInserts","expoLeftOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#elec0071",".electronics","speaker","audio","vertical","aluminium","design","classic"]}},{id:"SB_009",isActive:!0,priorityRate:10,slotRequirements:{indexInRow:{from:"right",index:[0]},maxCount:1,position:{x:{absolute:[{end:700,from:"right",inRange:[3e3,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[300,1350],start:0}]},z:"center"},rows:[1,2,3,4,5],size:{depth:{max:500,min:220},height:{max:800,min:270},width:{max:850,min:170}},types:["expoBack","expoHollow","expoBackCable","expoSupport","expoInsertsCable","expoInserts","expoRightOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#elec0072",".electronics","speaker","audio","vertical","aluminium","cover","black","design","classic"]}},{id:"SB_010",isActive:!0,priorityRate:6,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[340,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[400,1350],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:500,min:250},height:{max:500,min:360},width:{max:850,min:120}},types:["expoInserts","expoInsertsCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"center"}},tags:["#book0024",".books","magazines","thin","vertical","many","colorful","high","dense"]}},{id:"SB_011",isActive:!0,priorityRate:5,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[200,1350],start:0}]},z:"center"},rows:[1,2,3,4,5,6],size:{depth:{max:500,min:100},height:{max:500,min:150},width:{max:360,min:90}},types:["expoLeftOpen","expoRightOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:75}},tags:["#acces0003",".accessories","rubik's cube"]}},{id:"SB_012",isActive:!0,priorityRate:11,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:88,inRange:[0,4500],start:12}]},y:{absolute:[],relative:[{end:100,inRange:[500,1350],start:0}]},z:"center"},rows:[1,2,3,4,5,6],size:{depth:{max:500,min:200},height:{max:850,min:450},width:{max:850,min:220}},types:["expoBack","expoHollow","expoBackCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#cera0023",".ceramics","tall","vertical","vase","decorative","colorful","design"]}},{id:"SB_013",isActive:!0,priorityRate:4,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:73,inRange:[2400,4500],start:0}]},y:{absolute:[],relative:[{end:70,inRange:[400,1350],start:0}]},z:"center"},rows:[1],size:{depth:{max:500,min:350},height:{max:450,min:350},width:{max:850,min:170}},types:["expoBack","expoHollow","expoBackCable","expoSupport","expoInserts","expoInsertsCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#acces0010",".accessories","binder","documents","tall","vertical","colorful","office"]}},{id:"SB_014",isActive:!0,priorityRate:3,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:65,inRange:[2400,4500],start:0}]},y:{absolute:[],relative:[{end:79.9,inRange:[400,1350],start:16.8}]},z:"center"},rows:[2],size:{depth:{max:500,min:350},height:{max:850,min:100},width:{max:850,min:390}},types:["expoBack","expoHollow","expoBackCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:25},z:{mode:"explicit",percent:75}},tags:["#book0025",".books","horozontal","black","album"]}}]}}},"desk_v1.0.0":{id:"desk_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["desk"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:1,max:8},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[{id:"ST_001",isActive:!0,priorityRate:8,slotRequirements:{position:{x:{absolute:[],relative:[],special:[{anchor:"center",offset:{x:0,z:0},type:"workPlaceZone",zoneDepth:{max:600,min:50},zoneWidth:{max:2e3,min:300}}]},y:"bottom",z:"center"},size:{depth:{max:700,min:50},height:{max:203,min:160},width:{max:305,min:140}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"special"},z:{mode:"special"}},tags:["#elec0073",".electronics","macbook_13","silver","aluminium"]}},{id:"ST_002",isActive:!0,priorityRate:7,slotRequirements:{position:{x:{absolute:[],relative:[],special:[{anchor:"cableManagementLeft",offset:{x:16,z:88},type:"workPlaceZone",zoneDepth:{max:600,min:50},zoneWidth:{max:2e3,min:620}}]},y:"bottom",z:"center"},size:{depth:{max:700,min:50},height:{max:500,min:160},width:{max:305,min:140}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"special"},z:{mode:"special"}},tags:["#lamp0058",".lamps","classic","metal","silver","standing","design"]}},{id:"ST_003",isActive:!0,priorityRate:6,slotRequirements:{position:{x:{absolute:[],relative:[],special:[{anchor:"cableManagementRight",offset:{x:-16,z:88},type:"workPlaceZone",zoneDepth:{max:600,min:50},zoneWidth:{max:2e3,min:620}}]},y:"bottom",z:"center"},size:{depth:{max:700,min:50},height:{max:500,min:160},width:{max:305,min:140}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"special"},z:{mode:"special"}},tags:["#lamp0059",".lamps","classic","metal","silver","standing","design"]}},{id:"ST_004",isActive:!1,priorityRate:5,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[1500,4500],value:80}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:100},height:{max:500,min:350},width:{max:360,min:200}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0038",".plants","rubber","beige","pot"]}}]},shelfBody:{itemsCount:{min:0,max:12},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:8,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[{end:530,from:"top",inRange:[601,1040],start:0}],relative:[{end:100,inRange:[260,1500],start:13}]},z:"center"},rows:[1],size:{depth:{max:700,min:200},height:{max:440,min:250},width:{max:850,min:240}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:65},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0023",".books","vertical","design","fasion","culture","style","colors"]}},{id:"SB_002",isActive:!0,priorityRate:6,slotRequirements:{indexInRow:{from:"right",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[200,1500],start:0}]},z:"center"},rows:[2,3],size:{depth:{max:700,min:250},height:{max:440,min:160},width:{max:850,min:300}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:25}},tags:["#book0007",".books","horizontal","design","fasion","culture","style","pastel"]}},{id:"SB_003",isActive:!0,priorityRate:5,slotRequirements:{indexInRow:{from:"left",index:[0,1]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[200,1350],start:0}]},z:"center"},rows:[3],size:{depth:{max:700,min:100},height:{max:500,min:150},width:{max:900,min:90}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:40}},tags:["#acces0003",".accessories","rubik's cube"]}},{id:"SB_004",isActive:!0,priorityRate:7,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[400,1500],start:55}]},z:"center"},rows:[2,3],size:{depth:{max:700,min:350},height:{max:850,min:100},width:{max:850,min:390}},types:["expoBack","expoHollow","expoBackCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:25},z:{mode:"explicit",percent:75}},tags:["#book0025",".books","horozontal","black","album"]}}]}}},"dressing_table_v1.0.0":{id:"dressing_table_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["dressing_table"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:1,max:8},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[{id:"ST_001",isActive:!0,priorityRate:8,slotRequirements:{position:{x:{absolute:[],relative:[],special:[{anchor:"center",offset:{x:0,z:0},type:"workPlaceZone",zoneDepth:{max:600,min:50},zoneWidth:{max:2e3,min:300}}]},y:"bottom",z:"center"},size:{depth:{max:700,min:50},height:{max:203,min:160},width:{max:305,min:140}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"special"},z:{mode:"special"}},tags:["#acces0032",".electronics","macbook_13","silver","aluminium"]}},{id:"ST_004",isActive:!1,priorityRate:5,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[1500,4500],value:80}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:100},height:{max:500,min:350},width:{max:360,min:200}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0038",".plants","rubber","beige","pot"]}}]},shelfBody:{itemsCount:{min:0,max:12},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:8,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[{end:530,from:"top",inRange:[601,1040],start:0}],relative:[{end:100,inRange:[260,1500],start:13}]},z:"center"},rows:[1],size:{depth:{max:700,min:200},height:{max:440,min:250},width:{max:850,min:240}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:65},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0069",".books","vertical","design","fasion","culture","style","colors"]}},{id:"SB_002",isActive:!0,priorityRate:6,slotRequirements:{indexInRow:{from:"right",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[200,1500],start:0}]},z:"center"},rows:[2,3],size:{depth:{max:700,min:250},height:{max:440,min:160},width:{max:850,min:300}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:25}},tags:["#box0032",".books","horizontal","design","fasion","culture","style","pastel"]}},{id:"SB_003",isActive:!0,priorityRate:5,slotRequirements:{indexInRow:{from:"left",index:[0,1]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[200,1350],start:0}]},z:"center"},rows:[3],size:{depth:{max:700,min:100},height:{max:500,min:150},width:{max:900,min:90}},types:["expoBack","expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:40}},tags:["#acces0031",".accessories","rubik's cube"]}},{id:"SB_004",isActive:!0,priorityRate:7,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[400,1500],start:55}]},z:"center"},rows:[2,3],size:{depth:{max:700,min:350},height:{max:850,min:100},width:{max:850,min:390}},types:["expoBack","expoHollow","expoBackCable","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:25},z:{mode:"explicit",percent:75}},tags:["#book0070",".books","horozontal","black","album"]}}]}}},"sideboard_v1.0.0":{id:"sideboard_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["sideboard","chest","bedside_table"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:1,max:8},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[{id:"ST_001",isActive:!0,priorityRate:10,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[300,400],value:150},{from:"left",inRange:[2201,2800],value:520},{from:"left",inRange:[401,1090],value:250}],relative:[{inRange:[1091,2200],value:23.6},{inRange:[2801,4500],value:18.5}],special:[]},y:"bottom",z:"center"},size:{depth:{max:500,min:100},height:{max:400,min:172},width:{max:260,min:196}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#lamp0056",".lamps","classic","metal","silver","standing","design"]}},{id:"ST_002",isActive:!0,priorityRate:9,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[1291,4500],value:408}],relative:[{inRange:[710,1290],value:68.7}],special:[]},y:"bottom",z:"center"},size:{depth:{max:600,min:100},height:{max:138,min:20},width:{max:301,min:135}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:65}},tags:["#book0029",".books","horizontal","album","design","bauhaus","sculpture"]}},{id:"ST_003",isActive:!0,priorityRate:8,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[2351,3600],value:1050}],relative:[{inRange:[3601,4500],value:29},{inRange:[1600,2350],value:45}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:350},height:{max:113,min:9},width:{max:423,min:70}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#elec0076",".electronics","turntable","dark_gray","vinyl"]}},{id:"ST_004",isActive:!0,priorityRate:7,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[2351,3600],value:885}],relative:[{inRange:[3601,4500],value:24.6},{inRange:[1200,2350],value:37.6}],special:[]},y:"bottom",z:"center"},size:{depth:{max:340,min:30},height:{max:130,min:9},width:{max:105,min:60}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0040",".plants","agave","small"]}},{id:"ST_005",isActive:!0,priorityRate:6,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[1200,1599],value:37.6}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:350},height:{max:130,min:9},width:{max:105,min:60}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0040",".plants","agave","small"]}},{id:"ST_006",isActive:!0,priorityRate:5,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[2330,4500],value:75}],special:[]},y:"bottom",z:"center"},size:{depth:{max:900,min:280},height:{max:806,min:700},width:{max:576,min:500}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"gravityBack"}},tags:["#deco0006",".deco","poster","yellow","orange"]}},{id:"ST_007",isActive:!0,priorityRate:4,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[2901,3140],value:2111},{from:"right",inRange:[4401,4500],value:1461}],relative:[{inRange:[2600,2900],value:72.9},{inRange:[3141,4400],value:66.7}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:70},height:{max:90,min:50},width:{max:198,min:120}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#cera0024",".ceramics","vase","terracotta","orange","round"]}},{id:"ST_008",isActive:!0,priorityRate:3,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[2900,3200],value:1256},{from:"right",inRange:[4201,4500],value:1625}],relative:[{inRange:[3201,4200],value:61.1}],special:[]},y:"bottom",z:"center"},size:{depth:{max:600,min:100},height:{max:500,min:350},width:{max:360,min:200}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#plant0039",".plants","rubber","grey","pot"]}},{id:"ST_009",isActive:!0,priorityRate:2,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[3180,3400],value:2430},{from:"right",inRange:[3401,4500],value:764}],relative:[{inRange:[3150,3779],value:79.8}],special:[]},y:"bottom",z:"center"},size:{depth:{max:600,min:10},height:{max:72,min:5},width:{max:76,min:10}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:70}},tags:["#deco0007",".deco","sculpture","wooden","geometric"]}}]},shelfBody:{itemsCount:{min:0,max:12},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:339,min:130}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0001",".vinyl"]}},{id:"SB_002",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:376,min:124}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0002",".vinyl"]}},{id:"SB_003",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:110,min:50}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0003",".vinyl"]}},{id:"SB_004",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:557,min:126}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0004",".vinyl"]}},{id:"SB_005",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:643,min:121}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0005",".vinyl"]}},{id:"SB_006",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:681,min:128}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#vinyl0007",".vinyl"]}},{id:"SB_007",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:571,min:126}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0006",".vinyl"]}},{id:"SB_008",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:664,min:123}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#vinyl0008",".vinyl"]}},{id:"SB_009",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:529,min:127}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0009",".vinyl"]}},{id:"SB_010",isActive:!0,priorityRate:40,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:65,inRange:[1600,4500],start:15}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:460},height:{max:520,min:170},width:{max:920,min:470}},types:["expoHollow","expoSupport","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"gravityFront"}},tags:["#elec0078",".electronics"]}},{id:"SB_011",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1600,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:220},height:{max:820,min:620},width:{max:920,min:270}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#elec0077",".electronics"]}},{id:"SB_012",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1600,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:220},height:{max:620,min:420},width:{max:920,min:270}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#elec0081",".electronics"]}},{id:"SB_013",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1600,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:220},height:{max:420,min:220},width:{max:920,min:270}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#elec0080",".electronics"]}},{id:"SB_014",isActive:!0,priorityRate:37,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:33.3,inRange:[0,1599],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2,3,4],size:{depth:{max:520,min:220},height:{max:420,min:120},width:{max:920,min:230}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:62},z:{mode:"explicit",percent:66}},tags:["#elec0079",".electronics"]}},{id:"SB_015",isActive:!0,priorityRate:38,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1099],start:0},{end:100,inRange:[1100,2300],start:50},{end:100,inRange:[2301,4500],start:64}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:820,min:420},width:{max:920,min:160}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:53},z:{mode:"explicit",percent:63}},tags:["#cera0028",".ceramics"]}},{id:"SB_016",isActive:!0,priorityRate:38,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[1100,2300],start:0},{end:66.6,inRange:[2301,4500],start:30}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:820,min:530},width:{max:920,min:230}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:55}},tags:["#cera0027",".ceramics"]}},{id:"SB_017",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:49,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:420,min:320},width:{max:920,min:325}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0039",".books"]}},{id:"SB_018",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:51,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:320,min:220},width:{max:920,min:155}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0051",".books"]}},{id:"SB_019",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:51,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:220,min:120},width:{max:920,min:289}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0037",".books"]}},{id:"SB_020",isActive:!0,priorityRate:29,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,2300],start:50},{end:100,inRange:[2301,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:40}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:420,min:120},width:{max:920,min:270}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:60},z:{mode:"gravityFront"}},tags:["#acces0012",".accessories"]}},{id:"SB_021",isActive:!0,priorityRate:29,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:49,inRange:[0,2300],start:0},{end:33.3,inRange:[2301,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:50}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:320,min:120},width:{max:920,min:130}},types:["expoHollow","expoSupport","expoBack","expoBackCable","expoLeftOpen","expoRightOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:12},z:{mode:"explicit",percent:80}},tags:["#deco0008",".decorations"]}},{id:"SB_022",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:420,min:320},width:{max:920,min:380}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0031",".books"]}},{id:"SB_023",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50.1},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:320,min:220},width:{max:920,min:286}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0040",".books"]}},{id:"SB_024",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50.1},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:220,min:120},width:{max:920,min:310}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0032",".books"]}},{id:"SB_025",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:0,inRange:[2301,3500],start:0},{end:50,inRange:[3501,4500],start:25.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:420,min:320},width:{max:920,min:285}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0036",".books"]}},{id:"SB_026",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:0,inRange:[2301,3500],start:0},{end:50,inRange:[3501,4500],start:25.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:320,min:220},width:{max:920,min:356}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0045",".books"]}},{id:"SB_027",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:0,inRange:[2301,3500],start:0},{end:50,inRange:[3501,4500],start:25.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:220,min:120},width:{max:920,min:295}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0038",".books"]}},{id:"SB_028",isActive:!0,priorityRate:33,slotRequirements:{indexInRow:{from:"left",index:[1,2]},maxCount:1,position:{x:{absolute:[],relative:[{end:33.3,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:200},height:{max:420,min:220},width:{max:920,min:165}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:66}},tags:["#cera0025",".ceramics"]}},{id:"SB_029",isActive:!0,priorityRate:34,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[2300,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:53}]},z:"center"},rows:[2,3,4],size:{depth:{max:520,min:175},height:{max:220,min:120},width:{max:920,min:200}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:80}},tags:["#deco0009",".decorations"]}},{id:"SB_030",isActive:!0,priorityRate:35,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[0,4500],start:33.4}]},y:{absolute:[],relative:[{end:40,inRange:[0,4030],start:0}]},z:"center"},rows:[1],size:{depth:{max:520,min:240},height:{max:420,min:220},width:{max:920,min:200}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:80}},tags:["#acces0011",".accessories"]}},{id:"SB_031",isActive:!0,priorityRate:30,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[2310,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:140},height:{max:820,min:220},width:{max:920,min:135}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:58},z:{mode:"explicit",percent:62}},tags:["#cera0026",".ceramics"]}},{id:"SB_032",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:550,min:440},width:{max:920,min:120}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0034",".books"]}},{id:"SB_033",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:320,min:220},width:{max:920,min:270}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#book0047",".books"]}},{id:"SB_034",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:220,min:120},width:{max:920,min:426}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#book0033",".books"]}}]}}},"bookcase+wallstorage_v1.0.0":{id:"bookcase+wallstorage_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["bookcase","wallstorage"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:0,max:8},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[]},shelfBody:{itemsCount:{min:0,max:50},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:100,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3]},maxCount:1,position:{x:{absolute:[{end:1560,from:"left",inRange:[1801,4500],start:1200}],relative:[{end:100,inRange:[1200,1800],start:68}]},y:{absolute:[{end:1300,from:"bottom",inRange:[1301,4030],start:0}],relative:[{end:100,inRange:[700,1300],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:600,min:200},height:{max:1e3,min:620},width:{max:1074,min:140}},types:["expoHollow","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#elec0077",".electronics","speaker","vertical","silver"]}},{id:"SB_002",isActive:!0,priorityRate:98,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3]},maxCount:1,position:{x:{absolute:[{end:1560,from:"left",inRange:[1801,4500],start:1200}],relative:[{end:100,inRange:[1200,1800],start:68}]},y:{absolute:[{end:1200,from:"bottom",inRange:[1201,4030],start:0}],relative:[{end:100,inRange:[470,1200],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:600,min:200},height:{max:619,min:460},width:{max:1039,min:140}},types:["expoHollow","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#elec0081",".electronics","speaker","vertical","silver"]}},{id:"SB_003",isActive:!0,priorityRate:96,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[3600,4500],start:76}]},y:{absolute:[{end:1300,from:"bottom",inRange:[1301,4030],start:0}],relative:[{end:100,inRange:[700,1300],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:600,min:200},height:{max:1e3,min:620},width:{max:1042,min:140}},types:["expoHollow","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#elec0082",".electronics","speaker","vertical","silver"]}},{id:"SB_004",isActive:!0,priorityRate:94,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[3600,4500],start:76}]},y:{absolute:[{end:1200,from:"bottom",inRange:[1201,4030],start:0}],relative:[{end:100,inRange:[470,1200],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:600,min:200},height:{max:619,min:460},width:{max:1078,min:140}},types:["expoHollow","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#elec0083",".electronics","speaker","vertical","silver"]}},{id:"SB_005",isActive:!0,priorityRate:92,slotRequirements:{indexInRow:{from:"left",index:[0,1]},maxCount:1,position:{x:{absolute:[{end:900,from:"left",inRange:[1001,4500],start:400}],relative:[{end:100,inRange:[600,1e3],start:40}]},y:{absolute:[{end:1600,from:"bottom",inRange:[1601,4030],start:0}],relative:[{end:100,inRange:[700,1600],start:0}]},z:"center"},rows:[2,3],size:{depth:{max:600,min:200},height:{max:1e3,min:420},width:{max:1020,min:150}},types:["expoHollow"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:65}},tags:["#cera0028",".ceramics","curves","white"]}},{id:"SB_006",isActive:!0,priorityRate:90,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1001,4500],start:0}],relative:[{end:100,inRange:[0,1e3],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[1400,4030],start:21}]},z:"center"},rows:[6,7],size:{depth:{max:600,min:260},height:{max:1e3,min:420},width:{max:1062,min:260}},types:["expoHollow","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:56}},tags:["#lamp0056",".lamps"]}},{id:"SB_007",isActive:!0,priorityRate:88,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7]},maxCount:1,position:{x:{absolute:[{end:2650,from:"left",inRange:[2651,4500],start:1900}],relative:[{end:100,inRange:[2200,2650],start:75}]},y:{absolute:[],relative:[{end:100,inRange:[700,1600],start:0},{end:100,inRange:[1601,4030],start:20}]},z:"center"},rows:[2,3,6,7],size:{depth:{max:600,min:290},height:{max:1e3,min:500},width:{max:1032,min:200}},types:["expoHollow","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:56}},tags:["#plant0041",".plants"]}},{id:"SB_008",isActive:!0,priorityRate:86,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10]},maxCount:1,position:{x:{absolute:[{end:2650,from:"left",inRange:[2651,4500],start:1900}],relative:[{end:100,inRange:[1300,3500],start:0},{end:100,inRange:[3501,4500],start:20}]},y:{absolute:[],relative:[{end:90,inRange:[1300,4030],start:10}]},z:"center"},rows:[5,6],size:{depth:{max:600,min:220},height:{max:1e3,min:530},width:{max:1023,min:220}},types:["expoHollow","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:60}},tags:["#cera0027",".ceramics"]}},{id:"SB_009",isActive:!0,priorityRate:84,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2650,from:"left",inRange:[2651,4500],start:1900}],relative:[{end:100,inRange:[1300,3500],start:0},{end:100,inRange:[3501,4500],start:40}]},y:{absolute:[],relative:[{end:100,inRange:[1300,4030],start:38}]},z:"center"},rows:[8,9],size:{depth:{max:600,min:270},height:{max:1e3,min:440},width:{max:1015,min:140}},types:["expoHollow","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0054",".books"]}},{id:"SB_010",isActive:!0,priorityRate:82,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:3e3,from:"left",inRange:[3001,4500],start:2100},{end:800,from:"right",inRange:[1501,2600],start:0}],relative:[{end:100,inRange:[2601,3e3],start:70},{end:100,inRange:[300,1500],start:45}]},y:{absolute:[],relative:[{end:95,inRange:[0,4030],start:0}]},z:"center"},rows:[1],size:{depth:{max:600,min:235},height:{max:1e3,min:260},width:{max:1007,min:190}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#acces0011",".accessories"]}},{id:"SB_011",isActive:!0,priorityRate:80,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:1807,from:"left",inRange:[2e3,4500],start:800}],relative:[{end:100,inRange:[995,1999],start:50}]},y:{absolute:[],relative:[{end:95,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5],size:{depth:{max:600,min:289},height:{max:1e3,min:450},width:{max:1029,min:137}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0066",".books"]}},{id:"SB_012",isActive:!0,priorityRate:79,slotRequirements:{indexInRow:{from:"right",index:[0,1,2]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[3597,4500],start:80}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4],size:{depth:{max:600,min:131},height:{max:429,min:144},width:{max:1038,min:166}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#deco0009",".deco"]}},{id:"SB_013",isActive:!0,priorityRate:78,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:999,from:"left",inRange:[1e3,4500],start:0}],relative:[{end:100,inRange:[0,999],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[5],size:{depth:{max:600,min:172},height:{max:1e3,min:154},width:{max:1003,min:370}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:8},z:{mode:"explicit",percent:85}},tags:["#box0017",".boxes"]}},{id:"SB_014",isActive:!0,priorityRate:77,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2200,from:"left",inRange:[2201,4500],start:1450}],relative:[{end:100,inRange:[1800,2200],start:65}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[8],size:{depth:{max:600,min:150},height:{max:900,min:230},width:{max:1081,min:160}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:72},z:{mode:"explicit",percent:75}},tags:["#cera0025",".ceramics"]}},{id:"SB_015",isActive:!0,priorityRate:76,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:1950,from:"left",inRange:[1951,4500],start:1150}],relative:[{end:100,inRange:[1200,1950],start:60}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3],size:{depth:{max:600,min:100},height:{max:900,min:250},width:{max:1081,min:129}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:28},z:{mode:"explicit",percent:68}},tags:["#cera0026",".ceramics"]}},{id:"SB_016",isActive:!0,priorityRate:74,slotRequirements:{indexInRow:{from:"left",index:[0,1,2]},maxCount:1,position:{x:{absolute:[{end:770,from:"left",inRange:[341,4500],start:0}],relative:[]},y:{absolute:[{end:1633,from:"bottom",inRange:[0,4030],start:0}],relative:[]},z:"center"},rows:[1],size:{depth:{max:600,min:300},height:{max:450,min:310},width:{max:1047,min:340}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"gravityFront"}},tags:["#box0011",".boxes"]}},{id:"SB_017",isActive:!0,priorityRate:73,slotRequirements:{indexInRow:{from:"left",index:[0,1,2]},maxCount:1,position:{x:{absolute:[{end:770,from:"left",inRange:[341,4500],start:0}],relative:[]},y:{absolute:[{end:1633,from:"bottom",inRange:[0,4030],start:0}],relative:[]},z:"center"},rows:[1],size:{depth:{max:600,min:260},height:{max:450,min:250},width:{max:1085,min:395}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"gravityFront"}},tags:["#box0013",".boxes"]}},{id:"SB_018",isActive:!0,priorityRate:72,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2501,from:"left",inRange:[2501,4500],start:1735}],relative:[{end:100,inRange:[1900,2500],start:70}]},y:{absolute:[{end:1633,from:"bottom",inRange:[0,4030],start:0}],relative:[]},z:"center"},rows:[2],size:{depth:{max:600,min:188},height:{max:461,min:50},width:{max:1092,min:114}},types:["expoHollow","expoBack","expoSupport","expoRightOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:85}},tags:["#plant0040",".plants"]}},{id:"SB_019",isActive:!0,priorityRate:70,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2301,from:"left",inRange:[2301,4500],start:1664}],relative:[{end:100,inRange:[1801,2300],start:70}]},y:{absolute:[{end:2009,from:"bottom",inRange:[0,4030],start:697}],relative:[]},z:"center"},rows:[5],size:{depth:{max:600,min:150},height:{max:450,min:200},width:{max:1145,min:220}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:40},z:{mode:"explicit",percent:85}},tags:["#acces0013",".accessories"]}},{id:"SB_020",isActive:!0,priorityRate:68,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:807,from:"left",inRange:[0,4500],start:300}],relative:[]},y:{absolute:[{end:4030,from:"bottom",inRange:[0,4030],start:1670}],relative:[]},z:"center"},rows:[8],size:{depth:{max:600,min:100},height:{max:350,min:130},width:{max:1149,min:130}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:85}},tags:["#acces0017",".accessories"]}},{id:"SB_021",isActive:!0,priorityRate:66,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:3172,from:"left",inRange:[2501,4500],start:2501}],relative:[]},y:{absolute:[{end:2104,from:"bottom",inRange:[701,4030],start:701}],relative:[]},z:"center"},rows:[4],size:{depth:{max:600,min:260},height:{max:453,min:100},width:{max:1144,min:270}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#acces0012",".accessories"]}},{id:"SB_022",isActive:!0,priorityRate:64,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2565,from:"left",inRange:[1803,4500],start:1803}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[1802,4030],start:70.9}]},z:"center"},rows:[9],size:{depth:{max:600,min:250},height:{max:455,min:320},width:{max:1166,min:370}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#acces0015",".accessories"]}},{id:"SB_023",isActive:!0,priorityRate:62,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:903,from:"left",inRange:[300,4500],start:300}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[2257,4030],start:75}]},z:"center"},rows:[10],size:{depth:{max:600,min:185},height:{max:455,min:190},width:{max:1097,min:370}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:15},z:{mode:"explicit",percent:85}},tags:["#box0016",".boxes"]}},{id:"SB_024",isActive:!0,priorityRate:60,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:2900,from:"left",inRange:[2301,4500],start:2301}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[2257,4030],start:75}]},z:"center"},rows:[9],size:{depth:{max:600,min:170},height:{max:455,min:210},width:{max:1085,min:265}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:60},z:{mode:"explicit",percent:85}},tags:["#box0015",".boxes"]}},{id:"SB_025",isActive:!0,priorityRate:58,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:3100,from:"left",inRange:[2501,4500],start:2501}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[1915,4030],start:60}]},z:"center"},rows:[7],size:{depth:{max:600,min:100},height:{max:455,min:350},width:{max:1037,min:390}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:55},z:{mode:"gravityBack"}},tags:["#acces0014",".accessories"]}},{id:"SB_026",isActive:!0,priorityRate:56,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:3502,from:"left",inRange:[2801,4500],start:2801}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[8],size:{depth:{max:600,min:170},height:{max:455,min:172},width:{max:1131,min:295}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:40},z:{mode:"explicit",percent:85}},tags:["#box0014",".boxes"]}},{id:"SB_027",isActive:!0,priorityRate:54,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:4500,from:"left",inRange:[3701,4500],start:3701}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[1683,4030],start:55.7}]},z:"center"},rows:[6],size:{depth:{max:600,min:250},height:{max:455,min:320},width:{max:1110,min:280}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#acces0016",".accessories"]}},{id:"SB_028",isActive:!0,priorityRate:40,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:49,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:240},height:{max:455,min:350},width:{max:1069,min:217}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0063",".books"]}},{id:"SB_029",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:51,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:170},height:{max:455,min:240},width:{max:1105,min:350}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0045",".books"]}},{id:"SB_030",isActive:!0,priorityRate:38,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:49,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:240},height:{max:350,min:165},width:{max:1106,min:308}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:58},z:{mode:"explicit",percent:85}},tags:["#book0055",".books"]}},{id:"SB_031",isActive:!0,priorityRate:37,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:51,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:250},height:{max:455,min:301},width:{max:1039,min:375}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0042",".books"]}},{id:"SB_032",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:49,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:190},height:{max:455,min:270},width:{max:1109,min:177}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0067",".books"]}},{id:"SB_033",isActive:!0,priorityRate:35,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:49,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:250},height:{max:351,min:110},width:{max:1073,min:305}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:43},z:{mode:"explicit",percent:85}},tags:["#book0032",".books"]}},{id:"SB_034",isActive:!0,priorityRate:34,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:49,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:236},height:{max:455,min:325},width:{max:1061,min:410}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0064",".books"]}},{id:"SB_035",isActive:!0,priorityRate:33,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:51,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:185},height:{max:455,min:265},width:{max:1179,min:483}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0058",".books"]}},{id:"SB_036",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1200],start:0},{end:51,inRange:[1201,2700],start:0},{end:33.3,inRange:[2701,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:225},height:{max:351,min:130},width:{max:1046,min:285}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:29},z:{mode:"explicit",percent:85}},tags:["#book0038",".books"]}},{id:"SB_037",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:240},height:{max:455,min:300},width:{max:1055,min:196}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0044",".books"]}},{id:"SB_038",isActive:!0,priorityRate:30,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:185},height:{max:455,min:270},width:{max:1021,min:220}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0061",".books"]}},{id:"SB_039",isActive:!0,priorityRate:29,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:49},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:238},height:{max:335,min:120},width:{max:1096,min:307}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:85}},tags:["#book0035",".books"]}},{id:"SB_040",isActive:!0,priorityRate:28,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:240},height:{max:455,min:295},width:{max:1044,min:285}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0040",".books"]}},{id:"SB_041",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:200},height:{max:455,min:265},width:{max:1105,min:452}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0059",".books"]}},{id:"SB_042",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:49},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:200},height:{max:335,min:150},width:{max:1041,min:310}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:64},z:{mode:"explicit",percent:85}},tags:["#book0029",".books"]}},{id:"SB_043",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:49},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:241},height:{max:455,min:308},width:{max:1179,min:287}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0036",".books"]}},{id:"SB_044",isActive:!0,priorityRate:24,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:170},height:{max:455,min:260},width:{max:1049,min:225}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0052",".books"]}},{id:"SB_045",isActive:!0,priorityRate:23,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[1201,2700],start:51},{end:66.6,inRange:[2701,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:252},height:{max:348,min:120},width:{max:1034,min:307}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:64},z:{mode:"explicit",percent:85}},tags:["#book0007",".books"]}},{id:"SB_046",isActive:!0,priorityRate:22,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:233},height:{max:455,min:323},width:{max:1018,min:442}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0060",".books"]}},{id:"SB_047",isActive:!0,priorityRate:21,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:184},height:{max:455,min:236},width:{max:1041,min:436}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0062",".books"]}},{id:"SB_048",isActive:!0,priorityRate:20,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3],size:{depth:{max:600,min:250},height:{max:351,min:110},width:{max:1077,min:340}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:85}},tags:["#book0021",".books"]}},{id:"SB_049",isActive:!0,priorityRate:19,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:248},height:{max:455,min:302},width:{max:1114,min:532}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:85}},tags:["#book0056",".books"]}},{id:"SB_050",isActive:!0,priorityRate:18,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:200},height:{max:455,min:265},width:{max:1132,min:235}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0057",".books"]}},{id:"SB_051",isActive:!0,priorityRate:17,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4,5,6],size:{depth:{max:600,min:260},height:{max:351,min:100},width:{max:1155,min:431}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:85}},tags:["#book0033",".books"]}},{id:"SB_052",isActive:!0,priorityRate:16,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:225},height:{max:455,min:320},width:{max:1083,min:284}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0065",".books"]}},{id:"SB_053",isActive:!0,priorityRate:15,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:170},height:{max:455,min:250},width:{max:1186,min:265}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0047",".books"]}},{id:"SB_054",isActive:!0,priorityRate:14,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[2701,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[7,8,9,10],size:{depth:{max:600,min:214},height:{max:351,min:100},width:{max:1063,min:290}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:78},z:{mode:"explicit",percent:85}},tags:["#book0037",".books"]}},{id:"SB_055",isActive:!0,priorityRate:13,slotRequirements:{indexInRow:{from:"left",index:[0,1,2]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1201,4500],start:0}],relative:[{end:42,inRange:[0,1200],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[4],size:{depth:{max:600,min:222},height:{max:455,min:275},width:{max:1083,min:190}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0030",".books"]}},{id:"SB_056",isActive:!0,priorityRate:12,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4]},maxCount:1,position:{x:{absolute:[],relative:[{end:80,inRange:[0,1200],start:12},{end:46,inRange:[1201,2100],start:14},{end:38,inRange:[2101,4500],start:16}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[6],size:{depth:{max:600,min:165},height:{max:455,min:250},width:{max:1083,min:150}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"explicit",percent:92}},tags:["#book0051",".books"]}},{id:"SB_057",isActive:!0,priorityRate:11,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12]},maxCount:1,position:{x:{absolute:[{end:1750,from:"left",inRange:[2251,4500],start:0}],relative:[{end:79,inRange:[0,2250],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[9],size:{depth:{max:600,min:165},height:{max:455,min:245},width:{max:1083,min:170}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"explicit",percent:85}},tags:["#book0046",".books"]}},{id:"SB_058",isActive:!0,priorityRate:10,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[300,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4,5,6,7,8,9,10],size:{depth:{max:600,min:130},height:{max:230,min:110},width:{max:1083,min:130}},types:["expoHollow","expoBack","expoSupport"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:19},z:{mode:"explicit",percent:77}},tags:["#deco0008",".deco"]}}]}}},"shoerack_v1.0.0":{id:"shoerack_v1.0.0",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom"],categories:["shoerack"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:1,max:6},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","porto"],items:[{id:"ST_001",isActive:!0,priorityRate:1,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[300,400],value:150},{from:"left",inRange:[2201,2800],value:520},{from:"left",inRange:[401,1090],value:250}],relative:[{inRange:[1091,2200],value:23.6},{inRange:[2801,4500],value:18.5}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:300},height:{max:100,min:50},width:{max:300,min:189}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#acces0024",".accessories","scarf"]}},{id:"ST_002",isActive:!0,priorityRate:2,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[1500,3450],value:1080}],relative:[{inRange:[900,1499],value:73.1},{inRange:[3451,4500],value:31.5}],special:[]},y:"bottom",z:"center"},size:{depth:{max:1e3,min:225},height:{max:104,min:5},width:{max:176,min:10}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:70}},tags:["#cloth0005",".clothing","hat","basketball"]}},{id:"ST_003",isActive:!0,priorityRate:3,slotRequirements:{position:{x:{absolute:[],relative:[{inRange:[2330,4500],value:75}],special:[]},y:"bottom",z:"center"},size:{depth:{max:900,min:280},height:{max:806,min:700},width:{max:576,min:500}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"gravityBack"}},tags:["#deco0006",".deco","poster","yellow","orange"]}},{id:"ST_004",isActive:!0,priorityRate:4,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[2901,3140],value:2111},{from:"right",inRange:[4401,4500],value:1461}],relative:[{inRange:[2600,2900],value:72.9},{inRange:[3141,4400],value:66.7}],special:[]},y:"bottom",z:"center"},size:{depth:{max:700,min:70},height:{max:90,min:50},width:{max:198,min:120}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!0,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:75}},tags:["#cera0024",".ceramics","vase","terracotta","orange","round"]}},{id:"ST_005",isActive:!0,priorityRate:5,slotRequirements:{position:{x:{absolute:[{from:"left",inRange:[3180,3400],value:2430},{from:"right",inRange:[3401,4500],value:764}],relative:[{inRange:[3150,3779],value:79.8}],special:[]},y:"bottom",z:"center"},size:{depth:{max:1e3,min:275},height:{max:104,min:5},width:{max:276,min:10}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:70}},tags:["#acces0018",".accessories","sculpture","round","geometric","ceramic"]}},{id:"ST_006",isActive:!0,priorityRate:6,slotRequirements:{position:{x:{absolute:[{from:"right",inRange:[1600,3400],value:350}],relative:[{inRange:[3401,4500],value:89.8}],special:[]},y:"bottom",z:"center"},size:{depth:{max:1e3,min:52},height:{max:225,min:5},width:{max:52,min:10}},types:["expoTop"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:70}},tags:["#acces0020",".accessories","perfume"]}}]},shelfBody:{itemsCount:{min:0,max:40},compatibleShelfPatterns:["slant","gradient","pattern","grid","frame","charlie","porto"],items:[{id:"SB_001",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:339,min:130}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0001",".vinyl"]}},{id:"SB_002",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:376,min:124}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0002",".vinyl"]}},{id:"SB_003",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:110,min:50}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0003",".vinyl"]}},{id:"SB_004",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:557,min:126}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0004",".vinyl"]}},{id:"SB_005",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:643,min:121}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0005",".vinyl"]}},{id:"SB_006",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:681,min:128}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#vinyl0007",".vinyl"]}},{id:"SB_007",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:571,min:126}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0006",".vinyl"]}},{id:"SB_008",isActive:!0,priorityRate:25,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:50}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:664,min:123}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#vinyl0008",".vinyl"]}},{id:"SB_009",isActive:!0,priorityRate:26,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[3,4],size:{depth:{max:520,min:370},height:{max:520,min:320},width:{max:529,min:127}},types:["expoInserts"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#vinyl0009",".vinyl"]}},{id:"SB_011",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1600,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:280},height:{max:820,min:510},width:{max:920,min:390}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#cloth0002",".clothing"]}},{id:"SB_013",isActive:!0,priorityRate:39,slotRequirements:{indexInRow:{from:"left",index:[0]},maxCount:1,position:{x:{absolute:[{end:500,from:"left",inRange:[1600,4500],start:0}],relative:[]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:300},height:{max:420,min:260},width:{max:920,min:340}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:85}},tags:["#shoe0013",".shoes"]}},{id:"SB_014",isActive:!0,priorityRate:37,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:33.3,inRange:[0,1599],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[2,3,4],size:{depth:{max:520,min:220},height:{max:420,min:120},width:{max:920,min:230}},types:["expoHollow","expoSupport","expoBackCable","expoBack"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:62},z:{mode:"explicit",percent:66}},tags:["#elec0079",".electronics"]}},{id:"SB_015",isActive:!0,priorityRate:38,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1099],start:0},{end:100,inRange:[1100,2300],start:50},{end:100,inRange:[2301,4500],start:64}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:230},height:{max:820,min:400},width:{max:920,min:310}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:53},z:{mode:"explicit",percent:63}},tags:["#shoe0010",".shoes"]}},{id:"SB_016",isActive:!0,priorityRate:38,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:50,inRange:[1100,2300],start:0},{end:66.6,inRange:[2301,4500],start:30}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:260},height:{max:820,min:530},width:{max:920,min:370}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"explicit",percent:35}},tags:["#box0021",".boxes"]}},{id:"SB_017",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:49,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:220},height:{max:420,min:171},width:{max:920,min:275}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#shoe0007",".shoes"]}},{id:"SB_018",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:51,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:211},height:{max:400,min:143},width:{max:920,min:282}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:65}},tags:["#shoe0024",".shoes"]}},{id:"SB_019",isActive:!0,priorityRate:27,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,1350],start:0},{end:51,inRange:[1351,2300],start:0},{end:33.3,inRange:[2301,3500],start:0},{end:25,inRange:[3501,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:230},height:{max:418,min:121},width:{max:920,min:230}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"center"},z:{mode:"center"}},tags:["#box0020",".boxes"]}},{id:"SB_020",isActive:!0,priorityRate:29,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:100,inRange:[0,2300],start:50},{end:100,inRange:[2301,4500],start:66.7}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:40}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:221},height:{max:420,min:164},width:{max:920,min:192}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:60},z:{mode:"gravityFront"}},tags:["#shoe0022",".shoes"]}},{id:"SB_021",isActive:!0,priorityRate:29,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:49,inRange:[0,2300],start:0},{end:33.3,inRange:[2301,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:50}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:262},height:{max:450,min:126},width:{max:920,min:203}},types:["expoHollow","expoSupport","expoBack","expoBackCable","expoLeftOpen","expoRightOpen"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:12},z:{mode:"explicit",percent:80}},tags:["#acces0028",".accessories"]}},{id:"SB_022",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:200},height:{max:420,min:140},width:{max:920,min:260}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:77},z:{mode:"explicit",percent:65}},tags:["#shoe0015",".shoes"]}},{id:"SB_023",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50.1},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:260},height:{max:320,min:90},width:{max:920,min:310}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityFront"}},tags:["#shoe0008",".shoes"]}},{id:"SB_024",isActive:!0,priorityRate:31,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:100,inRange:[1351,2300],start:50.1},{end:100,inRange:[2301,3500],start:66.7},{end:100,inRange:[3501,4500],start:75.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:240},height:{max:220,min:120},width:{max:920,min:140}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#box0018",".boxes"]}},{id:"SB_025",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:0,inRange:[2301,3500],start:0},{end:50,inRange:[3501,4500],start:25.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:260},height:{max:420,min:120},width:{max:920,min:310}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:65}},tags:["#shoe0018",".shoes"]}},{id:"SB_027",isActive:!0,priorityRate:32,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:0,inRange:[2301,3500],start:0},{end:50,inRange:[3501,4500],start:25.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:240},height:{max:220,min:80},width:{max:920,min:240}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#shoe0016",".shoes"]}},{id:"SB_028",isActive:!0,priorityRate:33,slotRequirements:{indexInRow:{from:"left",index:[1,2]},maxCount:1,position:{x:{absolute:[],relative:[{end:33.3,inRange:[0,4500],start:0}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:320},height:{max:420,min:130},width:{max:920,min:240}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:70},z:{mode:"explicit",percent:66}},tags:["#shoe0014",".shoes"]}},{id:"SB_029",isActive:!0,priorityRate:34,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[2300,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:53}]},z:"center"},rows:[2,3,4],size:{depth:{max:520,min:175},height:{max:220,min:120},width:{max:920,min:200}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:75},z:{mode:"explicit",percent:80}},tags:["#deco0009",".decorations"]}},{id:"SB_030",isActive:!0,priorityRate:35,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[0,4500],start:33.4}]},y:{absolute:[],relative:[{end:40,inRange:[0,4030],start:0}]},z:"center"},rows:[1],size:{depth:{max:520,min:240},height:{max:420,min:220},width:{max:920,min:200}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:85},z:{mode:"explicit",percent:80}},tags:["#acces0011",".accessories"]}},{id:"SB_031",isActive:!0,priorityRate:30,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:66.6,inRange:[2310,4500],start:33.4}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2],size:{depth:{max:520,min:230},height:{max:820,min:110},width:{max:920,min:300}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:58},z:{mode:"explicit",percent:62}},tags:["#shoe0017",".shoes"]}},{id:"SB_032",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:250},height:{max:550,min:370},width:{max:920,min:370}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityRight"},z:{mode:"gravityBack"}},tags:["#box0019",".boxes"]}},{id:"SB_033",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"right",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:160},height:{max:320,min:210},width:{max:920,min:260}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"explicit",percent:15},z:{mode:"explicit",percent:80}},tags:["#shoe0019",".shoes"]}},{id:"SB_034",isActive:!0,priorityRate:36,slotRequirements:{indexInRow:{from:"left",index:[0,1,2,3,4,5,6,7,8,10,11,12,13,14,15,16]},maxCount:1,position:{x:{absolute:[],relative:[{end:0,inRange:[0,1350],start:0},{end:0,inRange:[1351,2300],start:0},{end:66.6,inRange:[2301,3500],start:33.4},{end:75,inRange:[3501,4500],start:50.1}]},y:{absolute:[],relative:[{end:100,inRange:[0,4030],start:0}]},z:"center"},rows:[1,2,3,4],size:{depth:{max:520,min:250},height:{max:220,min:90},width:{max:920,min:210}},types:["expoHollow","expoSupport","expoBack","expoBackCable"]},specification:{customBehavior:{ignoreSlotDepth:!1,parameterName:!1},localDensityTarget:{max:100,min:0},localOrientation:{x:{mode:"gravityLeft"},z:{mode:"gravityFront"}},tags:["#shoe0012",".shoes"]}}]}}}};var xo={expoTvStand001:{id:"expoTvStand_001",globalProperties:{density:{min:20,max:80},spaces:["office","livingRoom","hallway"],categories:["tvstand","unknown"],types:["type_01","type_02","type_01_veneer"]},areas:{shelfTop:{itemsCount:{min:0,max:1},items:[{priorityRate:10,slotRequirements:{types:["expoTop"],item_size:{width:{min:1e3,max:4500},height:{min:200,max:1200},depth:{min:100,max:500}},position:{x:{relative:{value:50,inRange:[0,4500]},absolute:{end:"left",value:null,inRange:[]}},y:"bottom",z:"middle"}},tags:["TV","black"]}]},shelfBody:{itemsCount:{min:1,max:10},items:[{priorityRate:3,slotRequirements:{types:["expoHollow","expoSupport","expoBack","expoBackCable","expoInserts","expoInsertsCable"],maxCount:10},tags:["vase","ceramic","gentle","grey","deco"]}]}}}};var So=(e,r)=>{let o=um(e),n={};return r.mode===je.Area?n=fm(o,r.space):r.mode===je.betaTvStandTester&&(n=xo.expoTvStand001),{...r,...n}},um=e=>{let{shelfCategory:r=Vt.Unknown}=e;return{shelfType:e.shelfType,shelfCategory:r,shelfMaterial:e.shelfMaterial}},fm=(e,r)=>Object.values(Eo).filter(n=>Em(n,e,r))[0],Em=(e,r,o)=>xm(e,o)&&Sm(e,r)&&Tm(e,r),xm=(e,r)=>e.globalProperties.spaces.includes(r),Sm=(e,{shelfType:r})=>e.globalProperties.types.includes(r),Tm=(e,r)=>{let o=!1;return Object.keys(r).includes("shelfCategory")&&(o=e.globalProperties.categories.includes(r.shelfCategory)),o};var ne;(function(e){e.Absolute="absolute",e.Relative="relative",e.BeyondShelfRange="beyondShelfRange"})(ne||(ne={}));var De;(function(e){e.Left="left",e.Right="right",e.Bottom="bottom",e.Top="top"})(De||(De={}));var oe;(function(e){e.ShelfTop="shelfTop",e.ShelfBody="shelfBody"})(oe||(oe={}));var Q;(function(e){e.X="x",e.Y="y",e.Z="z"})(Q||(Q={}));var tt=(e,r,o)=>{let n={mode:ne.BeyondShelfRange};if(e.absolute.length>0){let a=e.absolute.filter(s=>Me(s.inRange,r));a.length===1&&(o===oe.ShelfTop?(n.mode=ne.Absolute,n.value=a[0].value,n.from=a[0].from):o===oe.ShelfBody&&(n.mode=ne.Absolute,n.start=a[0].start,n.end=a[0].end,n.from=a[0].from))}if(e.relative.length>0){let a=e.relative.filter(s=>Me(s.inRange,r));a.length===1&&(o===oe.ShelfTop?(n.mode=ne.Relative,n.value=a[0].value):o===oe.ShelfBody&&(n.mode=ne.Relative,n.start=a[0].start,n.end=a[0].end))}return n},ke=e=>Math.round(e*10)/10,Ut=e=>e.sort((r,o)=>o.priorityRate-r.priorityRate),Me=(e,r)=>e[0]<=r&&r<=e[1],Pt=(e,r,o)=>{let{width:n,height:a,bodyHeight:s}=r;if(o===Q.X){let m=n*e/100,l={min:0,max:n},p={min:n/-2,max:n/2};return Math.round(p.min+(p.max-p.min)*(m-l.min)/(l.max-l.min))}else{let l=Rm(a,s)*e/100,p={min:0,max:s},d={min:a-s,max:a};return Math.round(d.min+(d.max-d.min)*(l-p.min)/(p.max-p.min))}},wt=(e,r,o)=>{let{width:n,height:a,bodyHeight:s}=r;if(o===Q.X)return e.from===De.Left?n/-2+e.value:n/2-e.value;if(o===Q.Y)return e.from===De.Bottom?a-s+e.value:a-e.value},Rm=(e,r)=>{let o=e;return e!==r&&(o=e-100),o},Nt=(e,r,o)=>o?!0:r.min<=e&&e<=r.max;var To=(e,r,o)=>({id:`${r.bottomAnchor.y}${e.x}_${o.id}`,area:oe.ShelfTop,item:{configId:o.id,priorityRate:o.priorityRate,size:o.slotRequirements.size,position:{default:{x:e.x,y:r.bottomAnchor.y,z:e.z}},localOrientation:o.specification.localOrientation,tags:o.specification.tags},slot:{id:r.id,x1:r.x1,x2:r.x2,y1:r.y1,y2:r.y2,z1:r.z1,z2:r.x2,width:r.width,height:r.height,depth:r.depth,type:r.type,positionalContext:r.positionalContext,bottomAnchor:r.bottomAnchor,itemsOrientations:r.itemsOrientations}}),Bi=(e,r)=>({id:`${e.id}_${r.id}`,area:oe.ShelfBody,item:{configId:r.id,priorityRate:r.priorityRate,size:{width:{min:0,max:e.width},height:{min:0,max:e.height},depth:{min:0,max:e.depth}},position:{default:{x:e.bottomAnchor.x,y:e.bottomAnchor.y,z:e.bottomAnchor.z}},localOrientation:r.specification.localOrientation,localDensityTarget:r.specification.localDensityTarget,tags:r.specification.tags},slot:{id:e.id,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2,z1:e.z1,z2:e.x2,width:e.width,height:e.height,depth:e.depth,type:e.type,positionalContext:e.positionalContext,inRows:e.inRows,bottomAnchor:e.bottomAnchor,itemsOrientations:e.itemsOrientations,hasCableManagement:e.hasCableManagement}});var Ro=(e,r)=>{if(r.activeAreas.top){let o=r.areas.shelfTop,n=o.compatibleShelfPatterns.includes(e.shelfPattern),a=e.slots.map(s=>s.type).includes(K.EXPO.Top);return n&&a?Pm(o,e):[]}else return[]},Pm=(e,r)=>{let o=Ut(e.items),n=[];for(let a of o)if(a.isActive){let s=wm(a,r);if((s.x||s.x===0)&&(s.z||s.z===0)){let m=To(s,Po(r),a);n.push(m)}}return n},wm=(e,r)=>{let o=e.slotRequirements.position.x,n={x:null,z:null},a=Po(r),s=a.workPlaceZones.length>0?a.workPlaceZones[0]:null,m=e.slotRequirements.size.depth,l=e.specification.customBehavior.ignoreSlotDepth;if(o.relative.length+o.absolute.length===0){if(s!==null&&o.special.length>0){let{anchor:p,zoneWidth:d,zoneDepth:u,offset:g}=o.special[0],S=Object.keys(s.anchors).includes(p),E=d.min<=s.width&&s.width<=d.max,N=Nt(s.depth,u,l);S&&E&&N&&(n.x=s.anchors[p].x+g.x,n.z=s.anchors[p].z+g.z)}}else{let p=tt(o,r.width,oe.ShelfTop),d=bm(p,r),u=Nt(a.depth,m,l);Nm(s,d)&&u&&(n.x=d,n.z=a.bottomAnchor.z)}return n},Nm=(e,r)=>!(e!==null&&Me([e.x1,e.x2],r)),bm=(e,r)=>e.mode===ne.Relative?Pt(e.value,r,Q.X):wt(e,r,Q.X),Po=e=>e.slots.filter(r=>r.type===K.EXPO.Top)[0];var bo=(e,r)=>{if(r.activeAreas.body){let o=r.areas.shelfBody;if(o.compatibleShelfPatterns.includes(e.shelfPattern)){let a=Ut(o.items);return Am(a,e)}else return[]}else return[]},Am=(e,r)=>{let o=[],n=[];for(let a of e)if(a.isActive){let s=a.slotRequirements.types,m=a.slotRequirements.maxCount,l=0;for(let p of s)if(l<m){let d=Im(p,a,r);if(d.length===1&&wo(o,d[0].id)){let u=d.pop(),g=Bi(u,a);n.push(g),o.push(g.slot.id),l++}d.length>1&&zm(d,a,r).forEach(g=>{if(wo(o,g.id)&&l<m){let S=Bi(g,a);n.push(S),o.push(S.slot.id),l++}})}}return n},wo=(e,r)=>!e.includes(r),zm=(e,r,o)=>{let n=Mm(r,o);return e.sort((a,s)=>No(a,n)-No(s,n))},Mm=(e,r)=>{let o=e.slotRequirements.position.x,n=e.slotRequirements.position.y,a=tt(o,r.width,oe.ShelfBody),s=tt(n,r.bodyHeight,oe.ShelfBody),m={};if(a.mode===ne.Relative){let l=Math.round((a.start+a.end)/2);m.x=Pt(l,r,Q.X)}if(a.mode===ne.Absolute&&(a.value=Math.round((a.start+a.end)/2),m.x=wt(a,r,Q.X)),s.mode===ne.Relative){let l=Math.round((s.start+s.end)/2);m.y=Pt(l,r,Q.Y)}return s.mode===ne.Absolute&&(s.value=Math.round((s.start+s.end)/2),m.y=wt(s,r,Q.Y)),m},No=(e,r)=>{let o=ke(Math.abs(e.bottomAnchor.x-r.x)),n=ke(Math.abs(Math.round(e.bottomAnchor.y+e.height/2)-r.y));return o===0?n:n===0?o:Math.hypot(o,n)},Im=(e,r,o)=>o.slots.filter(l=>l.type===e).filter(l=>Ym(l,r)).filter(l=>km(l,r.slotRequirements.rows,o)).filter(l=>Cm(l,r.slotRequirements.indexInRow)).filter(l=>vm(l,r.slotRequirements.position,o)),Ym=(e,r)=>{let o=r.slotRequirements.size,n=r.specification.customBehavior.ignoreSlotDepth,a=o.width.min<=e.width&&e.width<=o.width.max,s=o.height.min<=e.height&&e.height<=o.height.max,m=Nt(e.depth,o.depth,n);return a&&s&&m},km=(e,r,o)=>e.inRows.length>0?(o.stackStorage.bottom.active?e.inRows.map(a=>a-1):e.inRows).every(a=>r.includes(a)):!1,Cm=(e,r)=>{let{indexInRow:o,slotCountInRow:n}=e;return r.from===De.Left?r.index.includes(o):r.from===De.Right?r.index.includes(Bm(o,n)):!1},Bm=(e,r)=>r-e-1,vm=(e,r,o)=>{let n=!1,a=!1,s=tt(r.x,o.width,oe.ShelfBody),m=tt(r.y,o.bodyHeight,oe.ShelfBody);return s.mode===ne.Relative&&(n=Me([s.start,s.end],e.positionalContext.width.center)),m.mode===ne.Relative&&(a=Me([m.start,m.end],e.positionalContext.height.center)),s.mode===ne.Absolute&&(n=Lm(e,s,o)),m.mode===ne.Absolute&&(a=Om(e,m,o)),n&&a},Lm=(e,r,{width:o})=>{let n=ke((e.x1+e.x2)/2);return r.from===De.Left?Me([o/-2+r.start,o/-2+r.end],n):Me([o/2-r.end,o/2-r.start],n)},Om=(e,r,o)=>{let{height:n,bodyHeight:a}=o,s=ke((e.y1+e.y2)/2);if(r.from===De.Bottom){let m=n-a;return Me([m+r.start,m+r.end],s)}else return Me([n-r.end,n-r.start],s)};var Ao=(e,r)=>{let o=[],n=[];return Object.keys(e).includes("id")&&e.mode===je.Area&&(o=Ro(r,e),n=bo(r,e)),[...o,...n]};var Pe;(function(e){e.Center="center",e.GravityLeft="gravityLeft",e.GravityRight="gravityRight",e.GravityFront="gravityFront",e.GravityBack="gravityBack",e.Explicit="explicit",e.Special="special",e.Random="random"})(Pe||(Pe={}));var bt;(function(e){e.FreeStanding="freeStanding",e.LeaningLeft="leaningLeft",e.LeaningRight="leaningRight",e.LeaningBack="leaningBack"})(bt||(bt={}));var zo;(function(e){e.X="x",e.Y="y",e.Z="z"})(zo||(zo={}));var qt={offset:{left:4,right:4,front:6,back:1}},Yo=e=>e.itemOrientation.x.mode===Pe.Center?e.defaultPosition.x:e.itemOrientation.x.mode===Pe.GravityLeft?e.allowedOrientationsInSlot.includes(bt.LeaningLeft)?Li(e):e.defaultPosition.x:e.itemOrientation.x.mode===Pe.GravityRight?e.allowedOrientationsInSlot.includes(bt.LeaningRight)?Oi(e):e.defaultPosition.x:e.itemOrientation.x.mode===Pe.Random?ko(e,Q.X):e.itemOrientation.x.mode===Pe.Explicit?Co(e,Q.X):e.defaultPosition.x,vi=e=>e.itemOrientation.z.mode===Pe.Center?e.defaultPosition.z:e.itemOrientation.z.mode===Pe.GravityFront?Gi(e):e.itemOrientation.z.mode===Pe.GravityBack?Di(e):e.itemOrientation.z.mode===Pe.Random?ko(e,Q.Z):e.itemOrientation.z.mode===Pe.Explicit?Co(e,Q.Z):e.defaultPosition.z,Li=e=>{let r=qt.offset.left,o=e.defaultPosition.x-e.slotDim.width/2+e.itemDim.width/2+r;return ke(o)},Oi=e=>{let r=qt.offset.right,o=e.defaultPosition.x+e.slotDim.width/2-e.itemDim.width/2-r;return ke(o)},Gi=e=>{let r=qt.offset.front,o=e.defaultPosition.z+e.slotDim.depth/2-e.itemDim.depth/2-r;return ke(o)},Di=e=>{let r=qt.offset.back,o=e.defaultPosition.z-e.slotDim.depth/2+e.itemDim.depth/2+r;return ke(o)},ko=(e,r)=>{if(r===Q.X){let o=Li(e),n=Oi(e);return Mo(o,n)}if(r===Q.Z){let o=Di(e),n=Gi(e);return Mo(o,n)}return 0},Co=(e,r)=>{if(r===Q.X){let o=e.itemOrientation.x.percent,n=Li(e),a=Oi(e);return Io(n,a,o)}if(r===Q.Z){let o=e.itemOrientation.z.percent,n=Di(e),a=Gi(e);return Io(n,a,o)}return 0},Mo=(e,r)=>{let o=Math.ceil(e),n=Math.floor(r);return Math.floor(Math.random()*(n-o+1))+o},Io=(e,r,o)=>Math.round(e+Math.abs(r-e)*(o/100));var Bo=(e,r)=>({defaultPosition:Gm(e),slotDim:Dm(e),itemDim:Hm(r),allowedOrientationsInSlot:e.slot.itemsOrientations,itemOrientation:e.item.localOrientation}),Gm=e=>{let r=e.item.position.default.x,o=e.item.position.default.y,n=e.item.position.default.z;return{x:r,y:o,z:n}},Dm=e=>{let r=e.slot.width,o=e.slot.height,n=e.slot.depth;return{width:r,height:o,depth:n}},Hm=e=>{let r=e.item_space.dimensions_mm.widthx,o=e.item_space.dimensions_mm.heighty,n=e.item_space.dimensions_mm.depthz;return{width:r,height:o,depth:n}};var vo=(e,r)=>{let o=Bo(e,r);if(e.area===oe.ShelfTop){let n={x:o.defaultPosition.x,y:o.defaultPosition.y,z:vi(o)};return e.item.position.final=n,n}if(e.area===oe.ShelfBody){let n={x:Yo(o),y:o.defaultPosition.y,z:vi(o)};return e.item.position.final=n,n}return{x:0,y:0,z:0}};var Lo={categories:["ceramics","books","boxes","electronics","lamps","shoes","deco","plants","flowers","accessories","posters","vinyl","clothing"],items:[{item_identity:{item_id:"acces0003",objfilename:"acces0003",bakedtexturefilename:"acces0003",blendfilename:"acces0003",category:"accessories",source_licence:"rubics"},item_space:{dimensions_mm:{widthx:79,heighty:56,depthz:79}}},{item_identity:{item_id:"acces0010",objfilename:"acces0010",bakedtexturefilename:"acces0010",blendfilename:"acces0010",category:"accessories",source_licence:"binders"},item_space:{dimensions_mm:{widthx:159,heighty:320,depthz:290}}},{item_identity:{item_id:"acces0011",objfilename:"acces0011",bakedtexturefilename:"acces0011",blendfilename:"acces0011",category:"accessories",source_licence:"boxes"},item_space:{dimensions_mm:{widthx:171,heighty:247,depthz:229}}},{item_identity:{item_id:"acces0012",objfilename:"acces0012",bakedtexturefilename:"acces0012",blendfilename:"acces0012",category:"accessories",source_licence:"jewellery_case"},item_space:{dimensions_mm:{widthx:250,heighty:108,depthz:250}}},{item_identity:{item_id:"acces0013",objfilename:"acces0013",bakedtexturefilename:"acces0013",blendfilename:"acces0013",category:"accessories",source_licence:"clock"},item_space:{dimensions_mm:{widthx:192,heighty:192,depthz:51}}},{item_identity:{item_id:"acces0014",objfilename:"acces0014",bakedtexturefilename:"acces0014",blendfilename:"acces0014",category:"accessories",source_licence:"frames"},item_space:{dimensions_mm:{widthx:383,heighty:343,depthz:73}}},{item_identity:{item_id:"acces0015",objfilename:"acces0015",bakedtexturefilename:"acces0015",blendfilename:"acces0015",category:"accessories",source_licence:"binders"},item_space:{dimensions_mm:{widthx:367,heighty:310,depthz:248}}},{item_identity:{item_id:"acces0016",objfilename:"acces0016",bakedtexturefilename:"acces0016",blendfilename:"acces0016",category:"accessories",source_licence:"binders"},item_space:{dimensions_mm:{widthx:275,heighty:310,depthz:248}}},{item_identity:{item_id:"acces0017",objfilename:"acces0017",bakedtexturefilename:"acces0017",blendfilename:"acces0017",category:"accessories",source_licence:"wooden_sculpture"},item_space:{dimensions_mm:{widthx:109,heighty:126,depthz:113}}},{item_identity:{item_id:"acces0018",objfilename:"acces0018",bakedtexturefilename:"acces0018",blendfilename:"acces0018",category:"accessories",source_licence:"tray_with_deco"},item_space:{dimensions_mm:{widthx:274,heighty:103,depthz:274}}},{item_identity:{item_id:"acces0020",objfilename:"acces0020",bakedtexturefilename:"acces0020",blendfilename:"acces0020",category:"accessories",source_licence:"perfume"},item_space:{dimensions_mm:{widthx:51,heighty:223,depthz:72}}},{item_identity:{item_id:"acces0024",objfilename:"acces0024",bakedtexturefilename:"acces0024",blendfilename:"acces0024",category:"accessories",source_licence:"scarf"},item_space:{dimensions_mm:{widthx:298,heighty:98,depthz:298}}},{item_identity:{item_id:"acces0028",objfilename:"acces0028",bakedtexturefilename:"acces0028",blendfilename:"acces0028",category:"accessories",source_licence:"helmet"},item_space:{dimensions_mm:{widthx:202,heighty:125,depthz:262}}},{item_identity:{item_id:"book0007",objfilename:"book0007",bakedtexturefilename:"book0007",blendfilename:"book0007",category:"books",source_licence:"horizontal_many"},item_space:{dimensions_mm:{widthx:297,heighty:103,depthz:252}}},{item_identity:{item_id:"book0021",objfilename:"book0021",bakedtexturefilename:"book0021",blendfilename:"book0021",category:"books",source_licence:"horizontal"},item_space:{dimensions_mm:{widthx:339,heighty:48,depthz:250}}},{item_identity:{item_id:"book0022",objfilename:"book0022",bakedtexturefilename:"book0022",blendfilename:"book0022",category:"books",source_licence:"vertical_many"},item_space:{dimensions_mm:{widthx:225,heighty:339,depthz:224}}},{item_identity:{item_id:"book0023",objfilename:"book0023",bakedtexturefilename:"book0023",blendfilename:"book0023",category:"books",source_licence:"vertical_many"},item_space:{dimensions_mm:{widthx:238,heighty:260,depthz:186}}},{item_identity:{item_id:"book0024",objfilename:"book0024",bakedtexturefilename:"book0024",blendfilename:"book0024",category:"books",source_licence:"vertical_many"},item_space:{dimensions_mm:{widthx:115,heighty:338,depthz:252}}},{item_identity:{item_id:"book0025",objfilename:"book0025",bakedtexturefilename:"book0025",blendfilename:"book0025",category:"books",source_licence:"horizontal"},item_space:{dimensions_mm:{widthx:381,heighty:61,depthz:294}}},{item_identity:{item_id:"book0029",objfilename:"book0029",bakedtexturefilename:"book0029",blendfilename:"book0029",category:"books",source_licence:"horizontal_stock"},item_space:{dimensions_mm:{widthx:300,heighty:137,depthz:189}}},{item_identity:{item_id:"book0030",objfilename:"book0030",bakedtexturefilename:"book0030",blendfilename:"book0030",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:181,heighty:270,depthz:222}}},{item_identity:{item_id:"book0031",objfilename:"book0031",bakedtexturefilename:"book0031",blendfilename:"book0031",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:371,heighty:290,depthz:229}}},{item_identity:{item_id:"book0032",objfilename:"book0032",bakedtexturefilename:"book0032",blendfilename:"book0032",category:"books",source_licence:"horizontal_stock"},item_space:{dimensions_mm:{widthx:297,heighty:65,depthz:242}}},{item_identity:{item_id:"book0033",objfilename:"book0033",bakedtexturefilename:"book0033",blendfilename:"book0033",category:"books",source_licence:"horizontal"},item_space:{dimensions_mm:{widthx:416,heighty:58,depthz:257}}},{item_identity:{item_id:"book0034",objfilename:"book0034",bakedtexturefilename:"book0034",blendfilename:"book0034",category:"books",source_licence:"vertical"},item_space:{dimensions_mm:{widthx:75,heighty:418,depthz:257}}},{item_identity:{item_id:"book0035",objfilename:"book0035",bakedtexturefilename:"book0035",blendfilename:"book0035",category:"books",source_licence:"horizontal"},item_space:{dimensions_mm:{widthx:300,heighty:89,depthz:238}}},{item_identity:{item_id:"book0036",objfilename:"book0036",bakedtexturefilename:"book0036",blendfilename:"book0036",category:"books",source_licence:"vertical_many"},item_space:{dimensions_mm:{widthx:278,heighty:299,depthz:241}}},{item_identity:{item_id:"book0037",objfilename:"book0037",bakedtexturefilename:"book0037",blendfilename:"book0037",category:"books",source_licence:"horizontal_stock"},item_space:{dimensions_mm:{widthx:285,heighty:65,depthz:214}}},{item_identity:{item_id:"book0038",objfilename:"book0038",bakedtexturefilename:"book0038",blendfilename:"book0038",category:"books",source_licence:"horizontal_stock"},item_space:{dimensions_mm:{widthx:279,heighty:96,depthz:219}}},{item_identity:{item_id:"book0039",objfilename:"book0039",bakedtexturefilename:"book0039",blendfilename:"book0039",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:322,heighty:286,depthz:238}}},{item_identity:{item_id:"book0040",objfilename:"book0040",bakedtexturefilename:"book0040",blendfilename:"book0040",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:275,heighty:281,depthz:240}}},{item_identity:{item_id:"book0042",objfilename:"book0042",bakedtexturefilename:"book0042",blendfilename:"book0042",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:370,heighty:300,depthz:247}}},{item_identity:{item_id:"book0044",objfilename:"book0044",bakedtexturefilename:"book0044",blendfilename:"book0044",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:184,heighty:289,depthz:240}}},{item_identity:{item_id:"book0045",objfilename:"book0045",bakedtexturefilename:"book0045",blendfilename:"book0045",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:342,heighty:226,depthz:158}}},{item_identity:{item_id:"book0046",objfilename:"book0046",bakedtexturefilename:"book0046",blendfilename:"book0046",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:164,heighty:241,depthz:163}}},{item_identity:{item_id:"book0047",objfilename:"book0047",bakedtexturefilename:"book0047",blendfilename:"book0047",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:260,heighty:240,depthz:165}}},{item_identity:{item_id:"book0051",objfilename:"book0051",bakedtexturefilename:"book0051",blendfilename:"book0051",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:145,heighty:242,depthz:164}}},{item_identity:{item_id:"book0052",objfilename:"book0052",bakedtexturefilename:"book0052",blendfilename:"book0052",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:218,heighty:243,depthz:165}}},{item_identity:{item_id:"book0054",objfilename:"book0054",bakedtexturefilename:"book0054",blendfilename:"book0054",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:135,heighty:436,depthz:269}}},{item_identity:{item_id:"book0055",objfilename:"book0055",bakedtexturefilename:"book0055",blendfilename:"book0055",category:"books",source_licence:"horizontal_stock"},item_space:{dimensions_mm:{widthx:299,heighty:159,depthz:238}}},{item_identity:{item_id:"book0056",objfilename:"book0056",bakedtexturefilename:"book0056",blendfilename:"book0056",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:531,heighty:300,depthz:247}}},{item_identity:{item_id:"book0057",objfilename:"book0057",bakedtexturefilename:"book0057",blendfilename:"book0057",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:226,heighty:261,depthz:195}}},{item_identity:{item_id:"book0058",objfilename:"book0058",bakedtexturefilename:"book0058",blendfilename:"book0058",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:480,heighty:258,depthz:180}}},{item_identity:{item_id:"book0059",objfilename:"book0059",bakedtexturefilename:"book0059",blendfilename:"book0059",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:450,heighty:257,depthz:194}}},{item_identity:{item_id:"book0060",objfilename:"book0060",bakedtexturefilename:"book0060",blendfilename:"book0060",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:440,heighty:314,depthz:233}}},{item_identity:{item_id:"book0061",objfilename:"book0061",bakedtexturefilename:"book0061",blendfilename:"book0061",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:211,heighty:267,depthz:185}}},{item_identity:{item_id:"book0062",objfilename:"book0062",bakedtexturefilename:"book0062",blendfilename:"book0062",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:434,heighty:227,depthz:184}}},{item_identity:{item_id:"book0063",objfilename:"book0063",bakedtexturefilename:"book0063",blendfilename:"book0063",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:209,heighty:331,depthz:238}}},{item_identity:{item_id:"book0064",objfilename:"book0064",bakedtexturefilename:"book0064",blendfilename:"book0064",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:405,heighty:312,depthz:234}}},{item_identity:{item_id:"book0065",objfilename:"book0065",bakedtexturefilename:"book0065",blendfilename:"book0065",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:273,heighty:309,depthz:225}}},{item_identity:{item_id:"book0066",objfilename:"book0066",bakedtexturefilename:"book0066",blendfilename:"book0066",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:131,heighty:435,depthz:278}}},{item_identity:{item_id:"book0067",objfilename:"book0067",bakedtexturefilename:"book0067",blendfilename:"book0067",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:164,heighty:267,depthz:185}}},{item_identity:{item_id:"box0011",objfilename:"box0011",bakedtexturefilename:"box0011",blendfilename:"box0011",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:311,heighty:303,depthz:280}}},{item_identity:{item_id:"box0013",objfilename:"box0013",bakedtexturefilename:"box0013",blendfilename:"box0013",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:390,heighty:243,depthz:255}}},{item_identity:{item_id:"box0014",objfilename:"box0014",bakedtexturefilename:"box0014",blendfilename:"box0014",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:284,heighty:142,depthz:166}}},{item_identity:{item_id:"box0015",objfilename:"box0015",bakedtexturefilename:"box0015",blendfilename:"box0015",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:245,heighty:205,depthz:229}}},{item_identity:{item_id:"box0016",objfilename:"box0016",bakedtexturefilename:"box0016",blendfilename:"box0016",category:"boxes",source_licence:"stack"},item_space:{dimensions_mm:{widthx:352,heighty:179,depthz:169}}},{item_identity:{item_id:"box0017",objfilename:"box0017",bakedtexturefilename:"box0017",blendfilename:"box0017",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:360,heighty:142,depthz:165}}},{item_identity:{item_id:"box0018",objfilename:"box0018",bakedtexturefilename:"box0018",blendfilename:"box0018",category:"boxes",source_licence:"single"},item_space:{dimensions_mm:{widthx:134,heighty:115,depthz:239}}},{item_identity:{item_id:"box0019",objfilename:"box0019",bakedtexturefilename:"box0019",blendfilename:"box0019",category:"boxes",source_licence:"stack_with_shoes"},item_space:{dimensions_mm:{widthx:361,heighty:367,depthz:244}}},{item_identity:{item_id:"box0020",objfilename:"box0020",bakedtexturefilename:"box0020",blendfilename:"box0020",category:"boxes",source_licence:"round"},item_space:{dimensions_mm:{widthx:226,heighty:86,depthz:226}}},{item_identity:{item_id:"box0021",objfilename:"box0021",bakedtexturefilename:"box0021",blendfilename:"box0021",category:"boxes",source_licence:"stack_with_shoes"},item_space:{dimensions_mm:{widthx:361,heighty:499,depthz:255}}},{item_identity:{item_id:"cera0008",objfilename:"cera0008",bakedtexturefilename:"cera0008",blendfilename:"cera0008",category:"ceramics",source_licence:"vase"},item_space:{dimensions_mm:{widthx:290,heighty:199,depthz:261}}},{item_identity:{item_id:"cera0011",objfilename:"cera0011",bakedtexturefilename:"cera0011",blendfilename:"cera0011",category:"ceramics",source_licence:"vases"},item_space:{dimensions_mm:{widthx:228,heighty:375,depthz:165}}},{item_identity:{item_id:"cera0022",objfilename:"cera0022",bakedtexturefilename:"cera0022",blendfilename:"cera0022",category:"ceramics",source_licence:"vases"},item_space:{dimensions_mm:{widthx:200,heighty:525,depthz:200}}},{item_identity:{item_id:"cera0023",objfilename:"cera0023",bakedtexturefilename:"cera0023",blendfilename:"cera0023",category:"ceramics",source_licence:"vases"},item_space:{dimensions_mm:{widthx:205,heighty:415,depthz:146}}},{item_identity:{item_id:"cera0024",objfilename:"cera0024",bakedtexturefilename:"cera0024",blendfilename:"cera0024",category:"ceramics",source_licence:"bowl"},item_space:{dimensions_mm:{widthx:197,heighty:89,depthz:197}}},{item_identity:{item_id:"cera0025",objfilename:"cera0025",bakedtexturefilename:"cera0025",blendfilename:"cera0025",category:"ceramics",source_licence:"vase"},item_space:{dimensions_mm:{widthx:141,heighty:225,depthz:143}}},{item_identity:{item_id:"cera0026",objfilename:"cera0026",bakedtexturefilename:"cera0026",blendfilename:"cera0026",category:"ceramics",source_licence:"vase"},item_space:{dimensions_mm:{widthx:114,heighty:247,depthz:108}}},{item_identity:{item_id:"cera0027",objfilename:"cera0027",bakedtexturefilename:"cera0027",blendfilename:"cera0027",category:"ceramics",source_licence:"vase"},item_space:{dimensions_mm:{widthx:200,heighty:524,depthz:200}}},{item_identity:{item_id:"cera0028",objfilename:"cera0028",bakedtexturefilename:"cera0028",blendfilename:"cera0028",category:"ceramics",source_licence:"sculpture"},item_space:{dimensions_mm:{widthx:134,heighty:378,depthz:72}}},{item_identity:{item_id:"cloth0001",objfilename:"cloth0001",bakedtexturefilename:"cloth0001",blendfilename:"cloth0001",category:"clothing",source_licence:"bag"},item_space:{dimensions_mm:{widthx:219,heighty:145,depthz:174}}},{item_identity:{item_id:"cloth0002",objfilename:"cloth0002",bakedtexturefilename:"cloth0002",blendfilename:"cloth0002",category:"clothing",source_licence:"bagPack"},item_space:{dimensions_mm:{widthx:382,heighty:504,depthz:279}}},{item_identity:{item_id:"cloth0003",objfilename:"cloth0003",bakedtexturefilename:"cloth0003",blendfilename:"cloth0003",category:"clothing",source_licence:"hat"},item_space:{dimensions_mm:{widthx:271,heighty:120,depthz:320}}},{item_identity:{item_id:"cloth0005",objfilename:"cloth0005",bakedtexturefilename:"cloth0005",blendfilename:"cloth0005",category:"clothing",source_licence:"basketball hat"},item_space:{dimensions_mm:{widthx:175,heighty:103,depthz:254}}},{item_identity:{item_id:"deco0006",objfilename:"deco0006",bakedtexturefilename:"deco0006",blendfilename:"deco0006",category:"deco",source_licence:"large_picture"},item_space:{dimensions_mm:{widthx:575,heighty:805,depthz:73}}},{item_identity:{item_id:"deco0007",objfilename:"deco0007",bakedtexturefilename:"deco0007",blendfilename:"deco0007",category:"deco",source_licence:"mini_sculpture"},item_space:{dimensions_mm:{widthx:75,heighty:71,depthz:82}}},{item_identity:{item_id:"deco0008",objfilename:"deco0008",bakedtexturefilename:"deco0008",blendfilename:"deco0008",category:"deco",source_licence:"cube_of_balls"},item_space:{dimensions_mm:{widthx:113,heighty:101,depthz:113}}},{item_identity:{item_id:"deco0009",objfilename:"deco0009",bakedtexturefilename:"deco0009",blendfilename:"deco0009",category:"deco",source_licence:"pipe_piramid"},item_space:{dimensions_mm:{widthx:158,heighty:141,depthz:173}}},{item_identity:{item_id:"deco0019",objfilename:"deco0019",bakedtexturefilename:"deco0019",blendfilename:"deco0019",category:"deco",source_licence:"seat_pillow"},item_space:{dimensions_mm:{widthx:311,heighty:69,depthz:311}}},{item_identity:{item_id:"elec0003",objfilename:"elec0003",bakedtexturefilename:"elec0003",blendfilename:"elec0003",category:"electronics",source_licence:"audio_video_receiver"},item_space:{dimensions_mm:{widthx:440,heighty:134,depthz:303}}},{item_identity:{item_id:"elec0014",objfilename:"elec0014",bakedtexturefilename:"elec0014",blendfilename:"elec0014",category:"electronics",source_licence:"Xbox"},item_space:{dimensions_mm:{widthx:150,heighty:289,depthz:152}}},{item_identity:{item_id:"elec0021",objfilename:"elec0021",bakedtexturefilename:"elec0021",blendfilename:"elec0021",category:"electronics",source_licence:"apple_tv"},item_space:{dimensions_mm:{widthx:36,heighty:9,depthz:136}}},{item_identity:{item_id:"elec0068",objfilename:"elec0068",bakedtexturefilename:"elec0068",blendfilename:"elec0068",category:"electronics",source_licence:"apple_tv"},item_space:{dimensions_mm:{widthx:98,heighty:35,depthz:98}}},{item_identity:{item_id:"elec0069",objfilename:"elec0069",bakedtexturefilename:"elec0069",blendfilename:"elec0069",category:"electronics",source_licence:"TV_55"},item_space:{dimensions_mm:{widthx:1230,heighty:740,depthz:232}}},{item_identity:{item_id:"elec0071",objfilename:"elec0071",bakedtexturefilename:"elec0071",blendfilename:"elec0071",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:162,heighty:268,depthz:220}}},{item_identity:{item_id:"elec0072",objfilename:"elec0072",bakedtexturefilename:"elec0072",blendfilename:"elec0072",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:162,heighty:268,depthz:220}}},{item_identity:{item_id:"elec0073",objfilename:"elec0073",bakedtexturefilename:"elec0073",blendfilename:"elec0073",category:"electronics",source_licence:"macbook_pro_13"},item_space:{dimensions_mm:{widthx:304,heighty:202,depthz:287}}},{item_identity:{item_id:"elec0076",objfilename:"elec0076",bakedtexturefilename:"elec0076",blendfilename:"elec0076",category:"electronics",source_licence:"turntable"},item_space:{dimensions_mm:{widthx:422,heighty:112,depthz:343}}},{item_identity:{item_id:"elec0077",objfilename:"elec0077",bakedtexturefilename:"elec0077",blendfilename:"elec0077",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:132,heighty:607,depthz:200}}},{item_identity:{item_id:"elec0078",objfilename:"elec0078",bakedtexturefilename:"elec0078",blendfilename:"elec0078",category:"electronics",source_licence:"amplifier"},item_space:{dimensions_mm:{widthx:450,heighty:143,depthz:474}}},{item_identity:{item_id:"elec0079",objfilename:"elec0079",bakedtexturefilename:"elec0079",blendfilename:"elec0079",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:161,heighty:69,depthz:78}}},{item_identity:{item_id:"elec0080",objfilename:"elec0080",bakedtexturefilename:"elec0080",blendfilename:"elec0080",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:132,heighty:251,depthz:200}}},{item_identity:{item_id:"elec0081",objfilename:"elec0081",bakedtexturefilename:"elec0081",blendfilename:"elec0081",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:132,heighty:444,depthz:200}}},{item_identity:{item_id:"elec0082",objfilename:"elec0082",bakedtexturefilename:"elec0082",blendfilename:"elec0082",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:132,heighty:607,depthz:200}}},{item_identity:{item_id:"elec0083",objfilename:"elec0083",bakedtexturefilename:"elec0083",blendfilename:"elec0083",category:"electronics",source_licence:"speaker"},item_space:{dimensions_mm:{widthx:132,heighty:444,depthz:200}}},{item_identity:{item_id:"lamp0056",objfilename:"lamp0056",bakedtexturefilename:"lamp0056",blendfilename:"lamp0056",category:"lamps",source_licence:"classic"},item_space:{dimensions_mm:{widthx:250,heighty:390,depthz:250}}},{item_identity:{item_id:"lamp0057",objfilename:"lamp0057",bakedtexturefilename:"lamp0057",blendfilename:"lamp0057",category:"lamps",source_licence:"classic"},item_space:{dimensions_mm:{widthx:250,heighty:390,depthz:250}}},{item_identity:{item_id:"lamp0058",objfilename:"lamp0058",bakedtexturefilename:"lamp0058",blendfilename:"lamp0058",category:"lamps",source_licence:"classic"},item_space:{dimensions_mm:{widthx:223,heighty:426,depthz:301}}},{item_identity:{item_id:"lamp0059",objfilename:"lamp0059",bakedtexturefilename:"lamp0059",blendfilename:"lamp0059",category:"lamps",source_licence:"classic"},item_space:{dimensions_mm:{widthx:223,heighty:426,depthz:301}}},{item_identity:{item_id:"plant0014",objfilename:"plant0014",bakedtexturefilename:"plant0014",blendfilename:"plant0014",category:"plants",source_licence:"monstera"},item_space:{dimensions_mm:{widthx:619,heighty:791,depthz:659}}},{item_identity:{item_id:"plant0038",objfilename:"plant0038",bakedtexturefilename:"plant0038",blendfilename:"plant0038",category:"plants",source_licence:"rubber"},item_space:{dimensions_mm:{widthx:360,heighty:499,depthz:263}}},{item_identity:{item_id:"plant0039",objfilename:"plant0039",bakedtexturefilename:"plant0039",blendfilename:"plant0039",category:"plants",source_licence:"rubber"},item_space:{dimensions_mm:{widthx:359,heighty:500,depthz:263}}},{item_identity:{item_id:"plant0040",objfilename:"plant0040",bakedtexturefilename:"plant0040",blendfilename:"plant0040",category:"plants",source_licence:"agave"},item_space:{dimensions_mm:{widthx:103,heighty:126,depthz:97}}},{item_identity:{item_id:"plant0041",objfilename:"plant0041",bakedtexturefilename:"plant0041",blendfilename:"plant0041",category:"plants",source_licence:"cactus"},item_space:{dimensions_mm:{widthx:189,heighty:488,depthz:289}}},{item_identity:{item_id:"shoe0007",objfilename:"shoe0007",bakedtexturefilename:"shoe0007",blendfilename:"shoe0007",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:261,heighty:170,depthz:205}}},{item_identity:{item_id:"shoe0008",objfilename:"shoe0008",bakedtexturefilename:"shoe0008",blendfilename:"shoe0008",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:302,heighty:87,depthz:259}}},{item_identity:{item_id:"shoe0009",objfilename:"shoe0009",bakedtexturefilename:"shoe0009",blendfilename:"shoe0009",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:299,heighty:168,depthz:270}}},{item_identity:{item_id:"shoe0010",objfilename:"shoe0010",bakedtexturefilename:"shoe0010",blendfilename:"shoe0010",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:306,heighty:397,depthz:223}}},{item_identity:{item_id:"shoe0011",objfilename:"shoe0011",bakedtexturefilename:"shoe0011",blendfilename:"shoe0011",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:278,heighty:116,depthz:228}}},{item_identity:{item_id:"shoe0012",objfilename:"shoe0012",bakedtexturefilename:"shoe0012",blendfilename:"shoe0012",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:206,heighty:86,depthz:249}}},{item_identity:{item_id:"shoe0013",objfilename:"shoe0013",bakedtexturefilename:"shoe0013",blendfilename:"shoe0013",category:"shoes",source_licence:"rollers"},item_space:{dimensions_mm:{widthx:263,heighty:248,depthz:240}}},{item_identity:{item_id:"shoe0014",objfilename:"shoe0014",bakedtexturefilename:"shoe0014",blendfilename:"shoe0014",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:229,heighty:121,depthz:310}}},{item_identity:{item_id:"shoe0015",objfilename:"shoe0015",bakedtexturefilename:"shoe0015",blendfilename:"shoe0015",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:258,heighty:132,depthz:180}}},{item_identity:{item_id:"shoe0016",objfilename:"shoe0016",bakedtexturefilename:"shoe0016",blendfilename:"shoe0016",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:229,heighty:72,depthz:232}}},{item_identity:{item_id:"shoe0017",objfilename:"shoe0017",bakedtexturefilename:"shoe0017",blendfilename:"shoe0017",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:290,heighty:102,depthz:290}}},{item_identity:{item_id:"shoe0018",objfilename:"shoe0018",bakedtexturefilename:"shoe0018",blendfilename:"shoe0018",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:303,heighty:114,depthz:256}}},{item_identity:{item_id:"shoe0019",objfilename:"shoe0019",bakedtexturefilename:"shoe0019",blendfilename:"shoe0019",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:250,heighty:157,depthz:200}}},{item_identity:{item_id:"shoe0022",objfilename:"shoe0022",bakedtexturefilename:"shoe0022",blendfilename:"shoe0022",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:191,heighty:163,depthz:220}}},{item_identity:{item_id:"shoe0023",objfilename:"shoe0023",bakedtexturefilename:"shoe0023",blendfilename:"shoe0023",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:229,heighty:102,depthz:290}}},{item_identity:{item_id:"shoe0024",objfilename:"shoe0024",bakedtexturefilename:"shoe0024",blendfilename:"shoe0024",category:"shoes",source_licence:"shoes"},item_space:{dimensions_mm:{widthx:281,heighty:142,depthz:210}}},{item_identity:{item_id:"vinyl0001",objfilename:"vinyl0001",bakedtexturefilename:"vinyl0001",blendfilename:"vinyl0001",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:120,heighty:320,depthz:339}}},{item_identity:{item_id:"vinyl0002",objfilename:"vinyl0002",bakedtexturefilename:"vinyl0002",blendfilename:"vinyl0002",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:120,heighty:318,depthz:342}}},{item_identity:{item_id:"vinyl0003",objfilename:"vinyl0003",bakedtexturefilename:"vinyl0003",blendfilename:"vinyl0003",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:122,heighty:315,depthz:330}}},{item_identity:{item_id:"vinyl0004",objfilename:"vinyl0004",bakedtexturefilename:"vinyl0004",blendfilename:"vinyl0004",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:121,heighty:320,depthz:337}}},{item_identity:{item_id:"vinyl0005",objfilename:"vinyl0005",bakedtexturefilename:"vinyl0005",blendfilename:"vinyl0005",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:76,heighty:320,depthz:336}}},{item_identity:{item_id:"vinyl0006",objfilename:"vinyl0006",bakedtexturefilename:"vinyl0006",blendfilename:"vinyl0006",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:100,heighty:320,depthz:330}}},{item_identity:{item_id:"vinyl0007",objfilename:"vinyl0007",bakedtexturefilename:"vinyl0007",blendfilename:"vinyl0007",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:120,heighty:315,depthz:327}}},{item_identity:{item_id:"vinyl0008",objfilename:"vinyl0008",bakedtexturefilename:"vinyl0008",blendfilename:"vinyl0008",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:105,heighty:316,depthz:326}}},{item_identity:{item_id:"vinyl0009",objfilename:"vinyl0009",bakedtexturefilename:"vinyl0009",blendfilename:"vinyl0009",category:"vinyl",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:119,heighty:311,depthz:323}}},{item_identity:{item_id:"acces0032",objfilename:"acces0032",bakedtexturefilename:"acces0032",blendfilename:"acces0032",category:"accessories",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:180,heighty:372,depthz:218}}},{item_identity:{item_id:"acces0031",objfilename:"acces0031",bakedtexturefilename:"acces0031",blendfilename:"acces0031",category:"accessories",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:157,heighty:40,depthz:135}}},{item_identity:{item_id:"book0069",objfilename:"book0069",bakedtexturefilename:"book0069",blendfilename:"book0069",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:184,heighty:255,depthz:159}}},{item_identity:{item_id:"book0070",objfilename:"book0070",bakedtexturefilename:"book0070",blendfilename:"book0070",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:316,heighty:100,depthz:263}}},{item_identity:{item_id:"box0032",objfilename:"box0032",bakedtexturefilename:"box0032",blendfilename:"box0032",category:"books",source_licence:"vertical_stock"},item_space:{dimensions_mm:{widthx:297,heighty:65,depthz:242}}}]};var Go=(e,r=!1)=>{let o=r?uo(e):go(e),n=So(o,fo);return Ao(n,o).map(s=>{let l=Fm(s)[0],p=l.item_identity.item_id,d=l.item_identity.category,u=vo(s,l);return{asset:p,groupTag:d,position:h(u,y.SCALAR),size:h({x:1,y:1,z:1},y.SCALAR),rotation:null,mirror:null,material:null}})},Oo=Lo,Fm=e=>{let r=e.item.tags,o=r.some(l=>l[0]=="#"),n=r.filter(l=>l[0]==".")[0],a=n.substring(1,n.length),m=Oo.items.filter(l=>l.item_identity.category===a).filter(Km(e));if(o){let p=r.filter(g=>g[0]=="#")[0],d=p.substring(1,p.length);return[Oo.items.find(g=>g==null||!g.item_identity?!1:g.item_identity.item_id==d)]}else return m},Km=e=>r=>{let o=r.item_space.dimensions_mm;return!(o.widthx>e.item.size.width.max||o.widthx<e.item.size.width.min||o.heighty<e.item.size.height.min||o.heighty>e.item.size.height.max||o.depthz<e.item.size.depth.min||o.depthz>e.item.size.depth.max)};var Hi={name:"Studio",tone:"default",shotPreset:"center",furnishing:"default",mood:"default"},Do={veneer:{ash:{tone:"veneer",mood:"veneer",furnishing:"veneer"},oak:{tone:"veneer",mood:"veneer",furnishing:"veneer"},walnut:{tone:"veneer",mood:"veneer",furnishing:"veneer"}},laminate:{reisingerPink:{tone:"reisingerPink",mood:"default",furnishing:"default"}}},Ho={TYPE_01:{},TYPE_02:{},TYPE_03:{},TYPE_10:{},TYPE_13:{},VENEER_01:{},VENEER_13:{},TYPE_23:{tone:"default",mood:"clean",furnishing:"floating-furniture"},TYPE_24:{tone:"default",mood:"clean",furnishing:"floating-furniture"},TYPE_25:{tone:"default",mood:"clean",furnishing:"floating-furniture"},VENEER_25:{tone:"veneer",mood:"veneer",furnishing:"floating-furniture"}},Wi=e=>e.name==="Studio",Vm=e=>Object.keys(Do).includes(e)?Do[e]:null,Um=e=>Object.keys(Ho).includes(e)?Ho[e]:{},qm=(e,r)=>e?Object.keys(e).includes(r)?e[r]:{}:{},Wo=(e,r=Hi)=>{if(!Wi(r))return r;let o=qm(Vm(e.mainMaterial),e.mainColor),n=Um(e.shelfType);return{...r,...o,...n}};var Fi=class{constructor(){D(this,"steps");D(this,"fallbackStep");this.steps=[],this.fallbackStep=null}isReady(){return this.steps.length>0}configureWith(r,o=1/0){return this.steps.length<=o?this.steps.push(r):this.steps.splice(o,0,r),this}configureFallback(r){return this.fallbackStep=r,this}executeFor(r){for(let o of this.steps)r=o?o(r):r;return r}},jt=class extends Fi{constructor(){super()}executeFor(r){for(let o of this.steps)if(r=o?o(r):r,r.fallback){r=this.fallbackStep?this.fallbackStep(r):r;break}return r}},Fo=e=>{let r=e;return{executeFor:o=>{for(let n of r){let a=n(o);if(a)return a}return null}}};var _={SCENE_1:"Env_Interior_Modular_01",SCENE_2:"Env_Interior_Modular_02",SCENE_3:"Env_Interior_Modular_03",SCENE_4:"Env_Interior_Modular_04",SCENE_5:"Env_Interior_Modular_08",SCENE_6:"Env_Interior_Modular_09",SCENE_7:"Env_Interior_Modular_10",SCENE_8:"Env_Interior_Modular_11",SCENE_9:"Env_Interior_Modular_12",SCENE_10:"Env_Interior_Modular_15",SCENE_11:"Env_Interior_Modular_14",SCENE_12:"Env_Interior_Modular_16",SCENE_21:"Env_Interior_Modular_21",SCENE_22:"Env_Interior_Modular_22",SCENE_23:"Env_Interior_Modular_23",SCENE_24:"Env_Interior_Modular_24",SCENE_25:"Env_Interior_Modular_25",SCENE_FALLBACK:"Blender",SCENE_FALLBACK_SOFA:"Blender_Sofa",STUDIO:"Studio"},H={LIFESTYLE:"lifestyle",FEED:"feed",LIFESTYLE_MOBILE:"lifestyle-mobile"};var t={Default:"default",Style70s:"style70s",NordicMinimal:"nordic-minimal",SwedishModern:"swedish-modern"};var Ki=e=>(e.outputParameters={name:e.category===w.Sofa?_.SCENE_FALLBACK_SOFA:_.SCENE_FALLBACK,shotPreset:e.category===w.Sofa&&e.context===H.LIFESTYLE_MOBILE?"centerMobile":"center",tone:t.Default,furnishing:t.Default,mood:t.Default},e);var i={TYPE_01:{WHITE:0,BLACK:1,GRAY:3,AUBERGINE:4,NATURAL:5,RED:6,YELLOW:7,DUSTY_PINK:8,BLUE:9,DARK_BROWN:10,GREEN_AGAVA:11},TYPE_02:{WHITE:0,TERRACOTTA:1,MIDNIGHT_BLUE:2,SAND:3,MINT:4,MATTE_BLACK:6,SKYBLUE:7,BURGUNDY:8,COTTON:9,GRAY:10,DARK_GRAY:11,MUSTARD_YELLOW:12,LILAC:13,FOREST_GREEN:14,REISINGER_PINK:15,SAGE_GREEN:16,STONE_GRAY:17,WALNUT_STONE:18},VENEER_01:{ASH:0,OAK:1,WALNUT:2},TYPE_03:{WHITE:0,BEIGE:1,GRAPHITE:2,BEIGE_PINK:3,WHITE_PINK:4,GRAPHITE_WHITE:5,GRAPHITE_PINK:6,GRAPHITE_BEIGE:7,WHITE_BEIGE:8,WHITE_GRAPHITE:9,BEIGE_GRAPHITE:10,BEIGE_WHITE:11,GRAPHITE_STONE_GRAY:12,GRAPHITE_SAGE_GREEN:13,GRAPHITE_MISTY_BLUE:14,WHITE_STONE_GRAY:15,WHITE_SAGE_GREEN:16,WHITE_MISTY_BLUE:17,CASHMERE_STONE_GRAY:18,CASHMERE_SAGE_GREEN:19,CASHMERE_MISTY_BLUE:20},TYPE_13:{WHITE:0,SAND_MIDNIGHT:1,MUSTARD_YELLOW:2,GRAY:3,DARK_GRAY:4,WHITE_PLYWOOD:5,GRAY_PLYWOOD:6,BLACK_PLYWOOD:7,CLAY_BROWN:8,OLIVE_GREEN:9,SAND_BEIGE:10,BLACK:11},VENEER_13:{LIGHT_CLEAF:0,DARK_CLEAF:1},TYPE_23:{OFF_WHITE:0,OYSTER_BEIGE:1,PISTACHIO_GREEN:2,INKY_BLACK:3,POWDER_PINK:4},TYPE_24:{OFF_WHITE:0,OYSTER_BEIGE:1,PISTACHIO_GREEN:2,INKY_BLACK:3,POWDER_PINK:4},TYPE_25:{OFF_WHITE:0,OYSTER_BEIGE:1,PISTACHIO_GREEN:2,INKY_BLACK:3,POWDER_PINK:4},VENEER_25:{LIGHT_CLEAF:0,DARK_CLEAF:1}};var Xt=e=>{switch(e){case i.TYPE_01.WHITE:return{horizontals:{name:"laminatedPlywood",colors:{primary:"white",secondary:"neutral",tertiary:"white"}},verticals:{name:"laminatedPlywood",colors:{primary:"white",secondary:"neutral",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},supports:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},plinth:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}}};case i.TYPE_01.BLACK:return{horizontals:{name:"laminatedPlywood",colors:{primary:"black",secondary:"neutral",tertiary:"black"}},verticals:{name:"laminatedPlywood",colors:{primary:"black",secondary:"neutral",tertiary:"black"}},backs:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},supports:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},doors:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},drawers:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},plinth:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}}};case i.TYPE_01.GRAY:return{horizontals:{name:"laminatedPlywood",colors:{primary:"gray",secondary:"neutral",tertiary:"gray"}},verticals:{name:"laminatedPlywood",colors:{primary:"gray",secondary:"neutral",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},supports:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},plinth:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}}};case i.TYPE_01.RED:return{horizontals:{name:"laminatedPlywood",colors:{primary:"red",secondary:"neutral",tertiary:"red"}},verticals:{name:"laminatedPlywood",colors:{primary:"red",secondary:"neutral",tertiary:"red"}},backs:{name:"laminate",colors:{primary:"red",secondary:"red",tertiary:"red"}},supports:{name:"laminate",colors:{primary:"red",secondary:"red",tertiary:"red"}},doors:{name:"laminate",colors:{primary:"red",secondary:"red",tertiary:"red"}},drawers:{name:"laminate",colors:{primary:"red",secondary:"red",tertiary:"red"}},plinth:{name:"laminate",colors:{primary:"red",secondary:"red",tertiary:"red"}}};case i.TYPE_01.YELLOW:return{horizontals:{name:"laminatedPlywood",colors:{primary:"yellow",secondary:"neutral",tertiary:"yellow"}},verticals:{name:"laminatedPlywood",colors:{primary:"yellow",secondary:"neutral",tertiary:"yellow"}},backs:{name:"laminate",colors:{primary:"yellow",secondary:"yellow",tertiary:"yellow"}},supports:{name:"laminate",colors:{primary:"yellow",secondary:"yellow",tertiary:"yellow"}},doors:{name:"laminate",colors:{primary:"yellow",secondary:"yellow",tertiary:"yellow"}},drawers:{name:"laminate",colors:{primary:"yellow",secondary:"yellow",tertiary:"yellow"}},plinth:{name:"laminate",colors:{primary:"yellow",secondary:"yellow",tertiary:"yellow"}}};case i.TYPE_01.DUSTY_PINK:return{horizontals:{name:"laminatedPlywood",colors:{primary:"dustyPink",secondary:"neutral",tertiary:"dustyPink"}},verticals:{name:"laminatedPlywood",colors:{primary:"dustyPink",secondary:"neutral",tertiary:"dustyPink"}},backs:{name:"laminate",colors:{primary:"dustyPink",secondary:"dustyPink",tertiary:"dustyPink"}},supports:{name:"laminate",colors:{primary:"dustyPink",secondary:"dustyPink",tertiary:"dustyPink"}},doors:{name:"laminate",colors:{primary:"dustyPink",secondary:"dustyPink",tertiary:"dustyPink"}},drawers:{name:"laminate",colors:{primary:"dustyPink",secondary:"dustyPink",tertiary:"dustyPink"}},plinth:{name:"laminate",colors:{primary:"dustyPink",secondary:"dustyPink",tertiary:"dustyPink"}}};case i.TYPE_01.BLUE:return{horizontals:{name:"laminatedPlywood",colors:{primary:"blue",secondary:"neutral",tertiary:"blue"}},verticals:{name:"laminatedPlywood",colors:{primary:"blue",secondary:"neutral",tertiary:"blue"}},backs:{name:"laminate",colors:{primary:"blue",secondary:"blue",tertiary:"blue"}},supports:{name:"laminate",colors:{primary:"blue",secondary:"blue",tertiary:"blue"}},doors:{name:"laminate",colors:{primary:"blue",secondary:"blue",tertiary:"blue"}},drawers:{name:"laminate",colors:{primary:"blue",secondary:"blue",tertiary:"blue"}},plinth:{name:"laminate",colors:{primary:"blue",secondary:"blue",tertiary:"blue"}}};case i.TYPE_01.DARK_BROWN:return{horizontals:{name:"laminatedPlywood",colors:{primary:"darkBrown",secondary:"neutral",tertiary:"darkBrown"}},verticals:{name:"laminatedPlywood",colors:{primary:"darkBrown",secondary:"neutral",tertiary:"darkBrown"}},backs:{name:"laminate",colors:{primary:"darkBrown",secondary:"darkBrown",tertiary:"darkBrown"}},supports:{name:"laminate",colors:{primary:"darkBrown",secondary:"darkBrown",tertiary:"darkBrown"}},doors:{name:"laminate",colors:{primary:"darkBrown",secondary:"darkBrown",tertiary:"darkBrown"}},drawers:{name:"laminate",colors:{primary:"darkBrown",secondary:"darkBrown",tertiary:"darkBrown"}},plinth:{name:"laminate",colors:{primary:"darkBrown",secondary:"darkBrown",tertiary:"darkBrown"}}};case i.TYPE_01.GREEN_AGAVA:return{horizontals:{name:"laminatedPlywood",colors:{primary:"greenAgava",secondary:"neutral",tertiary:"greenAgava"}},verticals:{name:"laminatedPlywood",colors:{primary:"greenAgava",secondary:"neutral",tertiary:"greenAgava"}},backs:{name:"laminate",colors:{primary:"greenAgava",secondary:"greenAgava",tertiary:"greenAgava"}},supports:{name:"laminate",colors:{primary:"greenAgava",secondary:"greenAgava",tertiary:"greenAgava"}},doors:{name:"laminate",colors:{primary:"greenAgava",secondary:"greenAgava",tertiary:"greenAgava"}},drawers:{name:"laminate",colors:{primary:"greenAgava",secondary:"greenAgava",tertiary:"greenAgava"}},plinth:{name:"laminate",colors:{primary:"greenAgava",secondary:"greenAgava",tertiary:"greenAgava"}}};default:return{horizontals:{name:"laminatedPlywood",colors:{primary:"white",secondary:"neutral",tertiary:"white"}},verticals:{name:"laminatedPlywood",colors:{primary:"white",secondary:"neutral",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},supports:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},plinth:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}}}}};var $t=e=>{switch(e){case i.TYPE_02.WHITE:return{verticals:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},supports:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},horizontals:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},handles:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},legs:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}}};case i.TYPE_02.TERRACOTTA:return{verticals:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},backs:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},supports:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},horizontals:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},doors:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},drawers:{name:"laminate",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},handles:{name:"paintedMetal",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}},legs:{name:"paintedMetal",colors:{primary:"terracotta",secondary:"terracotta",tertiary:"terracotta"}}};case i.TYPE_02.MIDNIGHT_BLUE:return{verticals:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},backs:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},supports:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},horizontals:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},doors:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},drawers:{name:"laminate",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},handles:{name:"paintedMetal",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}},legs:{name:"paintedMetal",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"midnightBlue"}}};case i.TYPE_02.SAND:return{verticals:{name:"laminate",colors:{primary:"sand",secondary:"midnightBlue",tertiary:"sand"}},backs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},supports:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},horizontals:{name:"laminate",colors:{primary:"sand",secondary:"midnightBlue",tertiary:"sand"}},doors:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},drawers:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},handles:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},legs:{name:"paintedMetal",colors:{primary:"midnightBlue",secondary:"midnightBlue",tertiary:"sand"}}};case i.TYPE_02.MINT:return{verticals:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},backs:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},supports:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},horizontals:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},doors:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},drawers:{name:"laminate",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},handles:{name:"paintedMetal",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}},legs:{name:"paintedMetal",colors:{primary:"mint",secondary:"mint",tertiary:"mint"}}};case i.TYPE_02.MATTE_BLACK:return{verticals:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},backs:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},supports:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},horizontals:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},doors:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},drawers:{name:"laminate",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},handles:{name:"paintedMetal",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}},legs:{name:"paintedMetal",colors:{primary:"matteBlack",secondary:"matteBlack",tertiary:"matteBlack"}}};case i.TYPE_02.SKYBLUE:return{verticals:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},backs:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},supports:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},horizontals:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},doors:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},drawers:{name:"laminate",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},handles:{name:"paintedMetal",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}},legs:{name:"paintedMetal",colors:{primary:"skyBlue",secondary:"skyBlue",tertiary:"skyBlue"}}};case i.TYPE_02.BURGUNDY:return{verticals:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},backs:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},supports:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},horizontals:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},doors:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},drawers:{name:"laminate",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},handles:{name:"paintedMetal",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}},legs:{name:"paintedMetal",colors:{primary:"burgundy",secondary:"burgundy",tertiary:"burgundy"}}};case i.TYPE_02.COTTON:return{verticals:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},backs:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},supports:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},horizontals:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},doors:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},drawers:{name:"laminate",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},handles:{name:"paintedMetal",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}},legs:{name:"paintedMetal",colors:{primary:"cotton",secondary:"cotton",tertiary:"cotton"}}};case i.TYPE_02.GRAY:return{verticals:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},supports:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},horizontals:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},handles:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}}};case i.TYPE_02.DARK_GRAY:return{verticals:{name:"laminate",colors:{primary:"gray",secondary:"darkGray",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},supports:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},horizontals:{name:"laminate",colors:{primary:"gray",secondary:"darkGray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},handles:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},legs:{name:"paintedMetal",colors:{primary:"darkGray",secondary:"darkGray",tertiary:"gray"}}};case i.TYPE_02.MUSTARD_YELLOW:return{verticals:{name:"laminate",colors:{primary:"sand",secondary:"mustardYellow",tertiary:"sand"}},backs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},supports:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},horizontals:{name:"laminate",colors:{primary:"sand",secondary:"mustardYellow",tertiary:"sand"}},doors:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},drawers:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},handles:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},legs:{name:"paintedMetal",colors:{primary:"mustardYellow",secondary:"mustardYellow",tertiary:"sand"}}};case i.TYPE_02.LILAC:return{verticals:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},backs:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},supports:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},horizontals:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},doors:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},drawers:{name:"laminate",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},handles:{name:"paintedMetal",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}},legs:{name:"paintedMetal",colors:{primary:"lilac",secondary:"lilac",tertiary:"lilac"}}};case i.TYPE_02.FOREST_GREEN:return{verticals:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},backs:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},supports:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},horizontals:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},doors:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},drawers:{name:"laminate",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},handles:{name:"paintedMetal",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}},legs:{name:"paintedMetal",colors:{primary:"forestGreen",secondary:"forestGreen",tertiary:"forestGreen"}}};case i.TYPE_02.REISINGER_PINK:return{verticals:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},backs:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},supports:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},horizontals:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},doors:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},drawers:{name:"laminate",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},handles:{name:"paintedMetal",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}},legs:{name:"paintedMetal",colors:{primary:"reisingerPink",secondary:"reisingerPink",tertiary:"reisingerPink"}}};case i.TYPE_02.SAGE_GREEN:return{verticals:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},backs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},supports:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},horizontals:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},doors:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},drawers:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},handles:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},legs:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}}};case i.TYPE_02.STONE_GRAY:return{verticals:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},backs:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},supports:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},horizontals:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},doors:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},drawers:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},handles:{name:"paintedMetal",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},legs:{name:"paintedMetal",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}}};case i.TYPE_02.WALNUT_STONE:return{verticals:{name:"laminatedVeneer",colors:{primary:"stoneLightGray",secondary:"walnut",tertiary:"stoneLightGray"}},backs:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},supports:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},horizontals:{name:"laminatedVeneer",colors:{primary:"stoneLightGray",secondary:"walnut",tertiary:"stoneLightGray"}},doors:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},drawers:{name:"laminate",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},handles:{name:"paintedMetal",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}},legs:{name:"paintedMetal",colors:{primary:"stoneLightGray",secondary:"stoneLightGray",tertiary:"stoneLightGray"}}};default:return{verticals:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},supports:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},horizontals:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},handles:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},legs:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}}}}};var Zt=e=>{switch(e){case i.TYPE_03.WHITE:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},lighting:{name:"paintedMetal",colors:{primary:"white",secondary:"ledWhite",tertiary:"white"}}};case i.TYPE_03.BEIGE:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},walls:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},backs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},slabs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},lighting:{name:"paintedMetal",colors:{primary:"beige",secondary:"ledBeige",tertiary:"beige"}}};case i.TYPE_03.GRAPHITE:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},lighting:{name:"paintedMetal",colors:{primary:"graphite",secondary:"ledGraphite",tertiary:"graphite"}}};case i.TYPE_03.BEIGE_PINK:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},walls:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},backs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},slabs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},lighting:{name:"paintedMetal",colors:{primary:"pink",secondary:"ledPink",tertiary:"pink"}}};case i.TYPE_03.BEIGE_GRAPHITE:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},lighting:{name:"paintedMetal",colors:{primary:"graphite",secondary:"ledGraphite",tertiary:"graphite"}}};case i.TYPE_03.BEIGE_WHITE:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},lighting:{name:"paintedMetal",colors:{primary:"white",secondary:"ledWhite",tertiary:"white"}}};case i.TYPE_03.CASHMERE_STONE_GRAY:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},walls:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},backs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},slabs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},lighting:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"ledStone",tertiary:"stoneGray"}}};case i.TYPE_03.CASHMERE_SAGE_GREEN:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},walls:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},backs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},slabs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},lighting:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"ledGreen",tertiary:"sageGreen"}}};case i.TYPE_03.CASHMERE_MISTY_BLUE:return{doors:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},frame:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersFront:{name:"lacquer",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},drawersStructure:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},walls:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},backs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},slabs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},plinth:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},bars:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},lighting:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"ledBlue",tertiary:"mistyBlue"}}};case i.TYPE_03.WHITE_PINK:return{doors:{name:"lacquer",colors:{primary:"white",secondary:"white",tertiary:"white"}},frame:{name:"lacquer",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawersFront:{name:"lacquer",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawersStructure:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},walls:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},backs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},slabs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},plinth:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},bars:{name:"paintedMetal",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},lighting:{name:"paintedMetal",colors:{primary:"pink",secondary:"ledPink",tertiary:"pink"}}};case i.TYPE_03.WHITE_BEIGE:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},walls:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},backs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},slabs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},lighting:{name:"paintedMetal",colors:{primary:"beige",secondary:"ledBeige",tertiary:"beige"}}};case i.TYPE_03.WHITE_GRAPHITE:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},lighting:{name:"paintedMetal",colors:{primary:"graphite",secondary:"ledGraphite",tertiary:"graphite"}}};case i.TYPE_03.WHITE_STONE_GRAY:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},walls:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},backs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},slabs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},lighting:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"ledStone",tertiary:"stoneGray"}}};case i.TYPE_03.WHITE_SAGE_GREEN:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},walls:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},backs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},slabs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},lighting:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"ledGreen",tertiary:"sageGreen"}}};case i.TYPE_03.WHITE_MISTY_BLUE:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},walls:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},backs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},slabs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},lighting:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"ledBlue",tertiary:"mistyBlue"}}};case i.TYPE_03.GRAPHITE_WHITE:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},lighting:{name:"paintedMetal",colors:{primary:"white",secondary:"ledWhite",tertiary:"white"}}};case i.TYPE_03.GRAPHITE_PINK:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},walls:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},backs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},slabs:{name:"laminate",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"pink",secondary:"pink",tertiary:"pink"}},lighting:{name:"paintedMetal",colors:{primary:"pink",secondary:"ledPink",tertiary:"pink"}}};case i.TYPE_03.GRAPHITE_BEIGE:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},walls:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},backs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},slabs:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},lighting:{name:"paintedMetal",colors:{primary:"beige",secondary:"ledBeige",tertiary:"beige"}}};case i.TYPE_03.GRAPHITE_STONE_GRAY:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},walls:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},backs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},slabs:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},lighting:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"ledStone",tertiary:"stoneGray"}}};case i.TYPE_03.GRAPHITE_SAGE_GREEN:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},walls:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},backs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},slabs:{name:"laminate",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"sageGreen",tertiary:"sageGreen"}},lighting:{name:"paintedMetal",colors:{primary:"sageGreen",secondary:"ledGreen",tertiary:"sageGreen"}}};case i.TYPE_03.GRAPHITE_MISTY_BLUE:return{doors:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},frame:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},walls:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},backs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},slabs:{name:"laminate",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},plinth:{name:"paintedMetal",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},bars:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"mistyBlue",tertiary:"mistyBlue"}},lighting:{name:"paintedMetal",colors:{primary:"mistyBlue",secondary:"ledBlue",tertiary:"mistyBlue"}}};default:return{doors:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},frame:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},plinth:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},bars:{name:"paintedMetal",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},lighting:{name:"paintedMetal",colors:{primary:"white",secondary:"ledWhite",tertiary:"white"}}}}};var Qt=e=>{switch(e){case i.TYPE_13.WHITE_PLYWOOD:return{walls:{name:"laminatedPlywood",colors:{primary:"white",secondary:"neutral",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},slabs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},plinth:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},bars:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}}};case i.TYPE_13.BLACK_PLYWOOD:return{walls:{name:"laminatedPlywood",colors:{primary:"black",secondary:"neutral",tertiary:"black"}},backs:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},slabs:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},doors:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},drawers:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},plinth:{name:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"}},bars:{name:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"}}};case i.TYPE_13.GRAY_PLYWOOD:return{walls:{name:"laminatedPlywood",colors:{primary:"gray",secondary:"neutral",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},slabs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},plinth:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},bars:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}}};case i.TYPE_13.WHITE:return{walls:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},slabs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},plinth:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},bars:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}}};case i.TYPE_13.GRAY:return{walls:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},slabs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},plinth:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},bars:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}}};case i.TYPE_13.DARK_GRAY:return{walls:{name:"laminate",colors:{primary:"gray",secondary:"darkGray",tertiary:"gray"}},backs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},slabs:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},doors:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},drawers:{name:"laminate",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},plinth:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}},bars:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"gray"}}};case i.TYPE_13.SAND_MIDNIGHT:return{walls:{name:"laminate",colors:{primary:"sand",secondary:"midnightBlue",tertiary:"sand"}},backs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},slabs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},doors:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},drawers:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},plinth:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},bars:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}}};case i.TYPE_13.MUSTARD_YELLOW:return{walls:{name:"laminate",colors:{primary:"sand",secondary:"mustardYellow",tertiary:"sand"}},backs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},slabs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},doors:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},drawers:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},plinth:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},bars:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}}};case i.TYPE_13.CLAY_BROWN:return{walls:{name:"laminate",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},backs:{name:"laminate",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},slabs:{name:"laminate",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},doors:{name:"laminate",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},drawers:{name:"laminate",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},plinth:{name:"paintedMetal",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}},bars:{name:"paintedMetal",colors:{primary:"clayBrown",secondary:"clayBrown",tertiary:"clayBrown"}}};case i.TYPE_13.OLIVE_GREEN:return{walls:{name:"laminate",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},backs:{name:"laminate",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},slabs:{name:"laminate",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},doors:{name:"laminate",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},drawers:{name:"laminate",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},plinth:{name:"paintedMetal",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}},bars:{name:"paintedMetal",colors:{primary:"oliveGreen",secondary:"oliveGreen",tertiary:"oliveGreen"}}};case i.TYPE_13.SAND_BEIGE:return{walls:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},backs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},slabs:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},doors:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},drawers:{name:"laminate",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},plinth:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}},bars:{name:"paintedMetal",colors:{primary:"sand",secondary:"sand",tertiary:"sand"}}};case i.TYPE_13.BLACK:return{walls:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},backs:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},slabs:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},doors:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},drawers:{name:"laminate",colors:{primary:"black",secondary:"black",tertiary:"black"}},plinth:{name:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"}},bars:{name:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"}}};default:return{walls:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},backs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},slabs:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},doors:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},drawers:{name:"laminate",colors:{primary:"white",secondary:"white",tertiary:"white"}},plinth:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}},bars:{name:"paintedMetal",colors:{primary:"white",secondary:"white",tertiary:"white"}}}}};var Jt=e=>{switch(e){case i.TYPE_23.OFF_WHITE:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}};case i.TYPE_23.OYSTER_BEIGE:return{outer:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_23.PISTACHIO_GREEN:return{outer:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_23.INKY_BLACK:return{outer:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},doors:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}}};case i.TYPE_23.POWDER_PINK:return{outer:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};default:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}}}};var ei=e=>{switch(e){case i.TYPE_24.OFF_WHITE:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}};case i.TYPE_24.OYSTER_BEIGE:return{outer:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_24.PISTACHIO_GREEN:return{outer:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_24.INKY_BLACK:return{outer:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},doors:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}}};case i.TYPE_24.POWDER_PINK:return{outer:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};default:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"paintedMetal",colors:{primary:"gray",secondary:"gray",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}}}};var ti=e=>{switch(e){case i.TYPE_25.OFF_WHITE:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}};case i.TYPE_25.OYSTER_BEIGE:return{outer:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},legs:{name:"lacquer",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_25.PISTACHIO_GREEN:return{outer:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},legs:{name:"lacquer",colors:{primary:"pistachioGreen",secondary:"pistachioGreen",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};case i.TYPE_25.INKY_BLACK:return{outer:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},drawersFront:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},doors:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},legs:{name:"lacquer",colors:{primary:"inkyBlack",secondary:"inkyBlack",tertiary:"graphite"}},drawersStructure:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},walls:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},backs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}},slabs:{name:"laminate",colors:{primary:"graphite",secondary:"graphite",tertiary:"graphite"}}};case i.TYPE_25.POWDER_PINK:return{outer:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},drawersFront:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},doors:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},legs:{name:"lacquer",colors:{primary:"powderPink",secondary:"powderPink",tertiary:"oysterBeige"}},drawersStructure:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},walls:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},backs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}},slabs:{name:"laminate",colors:{primary:"oysterBeige",secondary:"oysterBeige",tertiary:"oysterBeige"}}};default:return{outer:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},drawersFront:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},doors:{name:"lacquer",colors:{primary:"offWhite",secondary:"offWhite",tertiary:"whiteSmoke"}},legs:{name:"lacquer",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},drawersStructure:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},backs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},slabs:{name:"laminate",colors:{primary:"whiteSmoke",secondary:"whiteSmoke",tertiary:"whiteSmoke"}}}}};var ii=e=>{switch(e){case i.VENEER_01.ASH:return{horizontals:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},verticals:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},backs:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},supports:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},doors:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},drawers:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},fittings:{name:"paintedMetal",colors:{primary:"ashish",secondary:"ashish",tertiary:"ashish"}}};case i.VENEER_01.OAK:return{horizontals:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},verticals:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},backs:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},supports:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},doors:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},drawers:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"veneer",colors:{primary:"oak",secondary:"oak",tertiary:"oak"}},fittings:{name:"paintedMetal",colors:{primary:"oakish",secondary:"oakish",tertiary:"oakish"}}};case i.VENEER_01.WALNUT:return{horizontals:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},verticals:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},backs:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},supports:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},doors:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},drawers:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},drawersStructure:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},plinth:{name:"veneer",colors:{primary:"walnut",secondary:"walnut",tertiary:"walnut"}},fittings:{name:"paintedMetal",colors:{primary:"walnutish",secondary:"walnutish",tertiary:"walnutish"}}};default:return{horizontals:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},verticals:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},backs:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},supports:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},doors:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},drawers:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"veneer",colors:{primary:"ash",secondary:"ash",tertiary:"ash"}},fittings:{name:"paintedMetal",colors:{primary:"ashish",secondary:"ashish",tertiary:"ashish"}}}}};var ri=e=>{switch(e){case i.VENEER_13.LIGHT_CLEAF:return{walls:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},backs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},slabs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},doors:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawers:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},bars:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},fittings:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}}};case i.VENEER_13.DARK_CLEAF:return{walls:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},backs:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},slabs:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},doors:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},drawers:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},drawersStructure:{name:"laminate",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},plinth:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},bars:{name:"paintedMetal",colors:{primary:"stoneGray",secondary:"stoneGray",tertiary:"stoneGray"}},fittings:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}}};default:return{walls:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},backs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},slabs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},doors:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawers:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersStructure:{name:"laminate",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},plinth:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},bars:{name:"paintedMetal",colors:{primary:"beige",secondary:"beige",tertiary:"beige"}},fittings:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}}}}};var oi=e=>{switch(e){case i.VENEER_25.LIGHT_CLEAF:return{outer:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersFront:{name:"veneer",colors:{primary:"offWhite",secondary:"lightCleaf",tertiary:"lightCleaf"}},doors:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},legs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersStructure:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},walls:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},backs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},slabs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}}};case i.VENEER_25.DARK_CLEAF:return{outer:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},drawersFront:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},doors:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},legs:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},drawersStructure:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},walls:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},backs:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}},slabs:{name:"veneer",colors:{primary:"darkCleaf",secondary:"darkCleaf",tertiary:"darkCleaf"}}};default:return{outer:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersFront:{name:"veneer",colors:{primary:"offWhite",secondary:"lightCleaf",tertiary:"lightCleaf"}},doors:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},legs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},drawersStructure:{name:"veneer",colors:{primary:"lightCleaf",secondary:"whiteSmoke",tertiary:"whiteSmoke"}},walls:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},backs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}},slabs:{name:"veneer",colors:{primary:"lightCleaf",secondary:"lightCleaf",tertiary:"lightCleaf"}}}}};var c={rewool_brown:0,rewool_oliveGreen:1,rewool_lightGrey:2,rewool_butterYellow:3,rewool_shadowPink:4,rewool_green:5,rewool_babyBlue:6,darven_ecru:7,darven_rock:8,darven_darkBrown:9,darven_steel:10,darven_tobacco:11,darven_pink:12,darven_camouflage:13,darven_klein:14};var Vi=e=>{let r=[b.Type01,b.Type02,b.Veneer01],o=[b.Type03],n=[b.Type13,b.Veneer13],a=[b.Type23,b.Type24,b.Type25,b.Veneer25];return[b.Type10].includes(e)?P.Sofa:r.includes(e)?P.Original:o.includes(e)?P.Tone:a.includes(e)?P.Expressions:P.Edge},ai={matchAll:{min:{x:0,y:0,z:0},max:{x:1/0,y:1/0,z:1/0}},matchNone:{min:{x:-1/0,y:-1/0,z:-1/0},max:{x:-1/0,y:-1/0,z:-1/0}}},ni=(e,r)=>{let o=r.min.x<=e.x&&e.x<=r.max.x,n=r.min.y<=e.y&&e.y<=r.max.y,a=r.min.z<=e.z&&e.z<=r.max.z;return o&&n&&a};var Ko=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Vo=(e,r)=>[W.Straight].includes(e.type),Uo=(e,r,o)=>"center",qo=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Sofa]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var jo=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.Style70s,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Xo=(e,r)=>[W.Straight].includes(e.type),$o=(e,r,o)=>"center",Zo=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Edge]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Tone]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Sofa]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"}})[e][r](o);var Qo=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Jo=(e,r)=>[W.Straight].includes(e.type),ea=(e,r,o)=>"center",ta=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var ia=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.Style70s,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],ra=(e,r)=>[W.Straight].includes(e.type),oa=(e,r,o)=>"center",aa=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"left",[t.SwedishModern]:a=>"left",[t.Style70s]:a=>"left",[t.Default]:a=>"left"},[P.Edge]:{[t.NordicMinimal]:a=>"left",[t.SwedishModern]:a=>"left",[t.Style70s]:a=>"left",[t.Default]:a=>"left"},[P.Tone]:{[t.NordicMinimal]:a=>"left",[t.SwedishModern]:a=>"left",[t.Style70s]:a=>"left",[t.Default]:a=>"left"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var na=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],sa=(e,r)=>[W.Straight].includes(e.type),la=(e,r,o)=>"center",ma=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"flatAngle",[t.SwedishModern]:a=>"centerzoom",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"flatAngle",[t.SwedishModern]:a=>"centerzoom",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"flatAngle",[t.SwedishModern]:a=>"centerzoom",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var ca=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],da=(e,r)=>[W.Straight].includes(e.type),pa=(e,r,o)=>"center",_a=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Edge]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Tone]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Expressions]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"}})[e][r](o);var ya=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],ha=(e,r)=>[W.Straight].includes(e.type),ga=(e,r,o)=>"center",ua=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Edge]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Tone]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"},[P.Expressions]:{[t.NordicMinimal]:a=>a===H.LIFESTYLE?"center":"zoom",[t.SwedishModern]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Style70s]:a=>a===H.LIFESTYLE?"center":"zoom",[t.Default]:a=>"zoom"}})[e][r](o);var fa=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ea=(e,r)=>[W.Straight].includes(e.type),xa=(e,r,o)=>"center",Sa=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Ta=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_24.INKY_BLACK]:t.NordicMinimal,[i.TYPE_24.OFF_WHITE]:t.NordicMinimal,[i.TYPE_24.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_24.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_24.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_25.INKY_BLACK]:t.NordicMinimal,[i.TYPE_25.OFF_WHITE]:t.NordicMinimal,[i.TYPE_25.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_25.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_25.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ra=(e,r)=>[W.Straight].includes(e.type),Pa=(e,r,o)=>"center",wa=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Edge]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Tone]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Expressions]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"}})[e][r](o);var Na=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_24.INKY_BLACK]:t.NordicMinimal,[i.TYPE_24.OFF_WHITE]:t.NordicMinimal,[i.TYPE_24.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_24.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_24.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_25.INKY_BLACK]:t.NordicMinimal,[i.TYPE_25.OFF_WHITE]:t.NordicMinimal,[i.TYPE_25.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_25.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_25.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],ba=(e,r)=>[W.Straight].includes(e.type),Aa=(e,r,o)=>"center",za=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Edge]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Tone]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Expressions]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"}})[e][r](o);var Ma=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ia=(e,r)=>[W.Straight].includes(e.type),Ya=(e,r,o)=>"center",ka=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Ca=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.SwedishModern,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ba=(e,r)=>[W.Straight].includes(e.type),va=(e,r,o)=>"center",La=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Oa=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.SwedishModern,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.Style70s,[c.rewool_oliveGreen]:t.Style70s,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.Style70s,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.SwedishModern,[c.rewool_babyBlue]:t.SwedishModern,[c.darven_ecru]:t.SwedishModern,[c.darven_rock]:t.SwedishModern,[c.darven_darkBrown]:t.Style70s,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.Style70s,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.Style70s,[c.darven_klein]:t.SwedishModern},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ga=(e,r)=>r>1&&[W.Straight,W.StraightLeft,W.StraightRight].includes(e.type),Da=(e,r,o)=>e===H.LIFESTYLE_MOBILE?"vertical":e===H.FEED?"center":"horizontal",Ha=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Wa=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.SwedishModern,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.Style70s,[c.rewool_oliveGreen]:t.Style70s,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.Style70s,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.SwedishModern,[c.rewool_babyBlue]:t.SwedishModern,[c.darven_ecru]:t.SwedishModern,[c.darven_rock]:t.SwedishModern,[c.darven_darkBrown]:t.Style70s,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.Style70s,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.Style70s,[c.darven_klein]:t.SwedishModern},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Fa=(e,r)=>[W.LShapeLeft,W.LShapeRight].includes(e.type),Ka=(e,r,o)=>e===H.LIFESTYLE_MOBILE?"vertical":e===H.FEED?"center":"horizontal",Va=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Ua=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.SwedishModern,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.Style70s,[c.rewool_oliveGreen]:t.Style70s,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.Style70s,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.SwedishModern,[c.rewool_babyBlue]:t.SwedishModern,[c.darven_ecru]:t.SwedishModern,[c.darven_rock]:t.SwedishModern,[c.darven_darkBrown]:t.Style70s,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.Style70s,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.Style70s,[c.darven_klein]:t.SwedishModern},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],qa=(e,r)=>r===1&&[W.Straight,W.StraightLeft,W.StraightRight].includes(e.type),ja=(e,r,o)=>e===H.LIFESTYLE_MOBILE?"vertical":e===H.FEED?"center":"horizontal",Xa=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var $a=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.SwedishModern,[i.TYPE_01.YELLOW]:t.SwedishModern,[i.TYPE_01.BLUE]:t.SwedishModern,[i.TYPE_01.RED]:t.SwedishModern,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.SwedishModern,[i.TYPE_01.NATURAL]:t.Default,[i.TYPE_01.AUBERGINE]:t.Default},TYPE_02:{[i.TYPE_02.WHITE]:t.SwedishModern,[i.TYPE_02.SAND]:t.SwedishModern,[i.TYPE_02.MIDNIGHT_BLUE]:t.Style70s,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.Style70s,[i.TYPE_02.SKYBLUE]:t.SwedishModern,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.SwedishModern,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_02.BURGUNDY]:t.Style70s,[i.TYPE_02.DARK_GRAY]:t.SwedishModern,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.SwedishModern,[i.TYPE_02.MINT]:t.Default,[i.TYPE_02.LILAC]:t.Default,[i.TYPE_02.FOREST_GREEN]:t.Default},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.Style70s,[i.VENEER_01.WALNUT]:t.Style70s},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.SwedishModern,[i.TYPE_03.BEIGE_PINK]:t.SwedishModern,[i.TYPE_03.GRAPHITE_PINK]:t.SwedishModern},TYPE_10:{[c.rewool_brown]:t.Style70s,[c.rewool_oliveGreen]:t.Style70s,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.Style70s,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.SwedishModern,[c.rewool_babyBlue]:t.SwedishModern,[c.darven_ecru]:t.SwedishModern,[c.darven_rock]:t.SwedishModern,[c.darven_darkBrown]:t.Style70s,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.Style70s,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.Style70s,[c.darven_klein]:t.SwedishModern},TYPE_13:{[i.TYPE_13.WHITE]:t.SwedishModern,[i.TYPE_13.GRAY]:t.SwedishModern,[i.TYPE_13.BLACK]:t.SwedishModern,[i.TYPE_13.CLAY_BROWN]:t.Style70s,[i.TYPE_13.OLIVE_GREEN]:t.Style70s,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.SwedishModern,[i.TYPE_13.SAND_MIDNIGHT]:t.SwedishModern,[i.TYPE_13.DARK_GRAY]:t.SwedishModern,[i.TYPE_13.SAND_BEIGE]:t.SwedishModern},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.Style70s},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r],Ui=(e,r)=>[W.UShape].includes(e.type),Za=(e,r,o)=>e===H.LIFESTYLE_MOBILE?"vertical":e===H.FEED?"center":"horizontal",Qa=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Edge]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Tone]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"},[P.Expressions]:{[t.NordicMinimal]:a=>"center",[t.SwedishModern]:a=>"center",[t.Style70s]:a=>"center",[t.Default]:a=>"center"}})[e][r](o);var Ja=(e,r)=>({TYPE_01:{[i.TYPE_01.WHITE]:t.NordicMinimal,[i.TYPE_01.BLACK]:t.NordicMinimal,[i.TYPE_01.GRAY]:t.NordicMinimal,[i.TYPE_01.DUSTY_PINK]:t.NordicMinimal,[i.TYPE_01.YELLOW]:t.NordicMinimal,[i.TYPE_01.BLUE]:t.NordicMinimal,[i.TYPE_01.RED]:t.NordicMinimal,[i.TYPE_01.DARK_BROWN]:t.NordicMinimal,[i.TYPE_01.GREEN_AGAVA]:t.NordicMinimal,[i.TYPE_01.NATURAL]:t.NordicMinimal,[i.TYPE_01.AUBERGINE]:t.NordicMinimal},TYPE_02:{[i.TYPE_02.WHITE]:t.NordicMinimal,[i.TYPE_02.SAND]:t.NordicMinimal,[i.TYPE_02.MIDNIGHT_BLUE]:t.NordicMinimal,[i.TYPE_02.MATTE_BLACK]:t.NordicMinimal,[i.TYPE_02.TERRACOTTA]:t.NordicMinimal,[i.TYPE_02.SKYBLUE]:t.NordicMinimal,[i.TYPE_02.STONE_GRAY]:t.NordicMinimal,[i.TYPE_02.GRAY]:t.NordicMinimal,[i.TYPE_02.COTTON]:t.NordicMinimal,[i.TYPE_02.SAGE_GREEN]:t.NordicMinimal,[i.TYPE_02.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_02.BURGUNDY]:t.NordicMinimal,[i.TYPE_02.DARK_GRAY]:t.NordicMinimal,[i.TYPE_02.WALNUT_STONE]:t.NordicMinimal,[i.TYPE_02.REISINGER_PINK]:t.NordicMinimal,[i.TYPE_02.MINT]:t.NordicMinimal,[i.TYPE_02.LILAC]:t.NordicMinimal,[i.TYPE_02.FOREST_GREEN]:t.NordicMinimal},VENEER_01:{[i.VENEER_01.ASH]:t.NordicMinimal,[i.VENEER_01.OAK]:t.NordicMinimal,[i.VENEER_01.WALNUT]:t.NordicMinimal},TYPE_03:{[i.TYPE_03.WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_WHITE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_WHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE]:t.NordicMinimal,[i.TYPE_03.WHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_BEIGE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.BEIGE_GRAPHITE]:t.NordicMinimal,[i.TYPE_03.WHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.CASHMERE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_MISTY_BLUE]:t.NordicMinimal,[i.TYPE_03.WHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.CASHMERE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_SAGE_GREEN]:t.NordicMinimal,[i.TYPE_03.WHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.CASHMERE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_STONE_GRAY]:t.NordicMinimal,[i.TYPE_03.WHITE_PINK]:t.NordicMinimal,[i.TYPE_03.BEIGE_PINK]:t.NordicMinimal,[i.TYPE_03.GRAPHITE_PINK]:t.NordicMinimal},TYPE_10:{[c.rewool_brown]:t.NordicMinimal,[c.rewool_oliveGreen]:t.NordicMinimal,[c.rewool_lightGrey]:t.NordicMinimal,[c.rewool_butterYellow]:t.NordicMinimal,[c.rewool_shadowPink]:t.NordicMinimal,[c.rewool_green]:t.NordicMinimal,[c.rewool_babyBlue]:t.NordicMinimal,[c.darven_ecru]:t.NordicMinimal,[c.darven_rock]:t.NordicMinimal,[c.darven_darkBrown]:t.NordicMinimal,[c.darven_steel]:t.NordicMinimal,[c.darven_tobacco]:t.NordicMinimal,[c.darven_pink]:t.NordicMinimal,[c.darven_camouflage]:t.NordicMinimal,[c.darven_klein]:t.NordicMinimal},TYPE_13:{[i.TYPE_13.WHITE]:t.NordicMinimal,[i.TYPE_13.GRAY]:t.NordicMinimal,[i.TYPE_13.BLACK]:t.NordicMinimal,[i.TYPE_13.CLAY_BROWN]:t.NordicMinimal,[i.TYPE_13.OLIVE_GREEN]:t.NordicMinimal,[i.TYPE_13.WHITE_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.BLACK_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.GRAY_PLYWOOD]:t.NordicMinimal,[i.TYPE_13.MUSTARD_YELLOW]:t.NordicMinimal,[i.TYPE_13.SAND_MIDNIGHT]:t.NordicMinimal,[i.TYPE_13.DARK_GRAY]:t.NordicMinimal,[i.TYPE_13.SAND_BEIGE]:t.NordicMinimal},VENEER_13:{[i.VENEER_13.LIGHT_CLEAF]:t.NordicMinimal,[i.VENEER_13.DARK_CLEAF]:t.NordicMinimal},TYPE_23:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_24:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},TYPE_25:{[i.TYPE_23.INKY_BLACK]:t.NordicMinimal,[i.TYPE_23.OFF_WHITE]:t.NordicMinimal,[i.TYPE_23.OYSTER_BEIGE]:t.NordicMinimal,[i.TYPE_23.PISTACHIO_GREEN]:t.NordicMinimal,[i.TYPE_23.POWDER_PINK]:t.NordicMinimal},VENEER_25:{[i.VENEER_25.DARK_CLEAF]:t.NordicMinimal,[i.VENEER_25.LIGHT_CLEAF]:t.NordicMinimal}})[e][r];var en=(e,r,o)=>(e===H.LIFESTYLE_MOBILE,"right"),tn=(e,r,o)=>({[P.Original]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Edge]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Tone]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"},[P.Expressions]:{[t.NordicMinimal]:a=>"right",[t.SwedishModern]:a=>"right",[t.Style70s]:a=>"right",[t.Default]:a=>"right"}})[e][r](o);var At={[_.SCENE_1]:{min:{x:1500,y:1e3,z:10},max:{x:6e3,y:3e3,z:1e3}},[_.SCENE_2]:{min:{x:1500,y:700,z:10},max:{x:2600,y:1400,z:1e3}},[_.SCENE_3]:{min:{x:500,y:1400,z:10},max:{x:1600,y:2e3,z:1e3}},[_.SCENE_4]:{min:{x:2500,y:340,z:10},max:{x:3500,y:3e3,z:1e3}},[_.SCENE_5]:{min:{x:1e3,y:330,z:10},max:{x:2500,y:1e3,z:1e3}},[_.SCENE_6]:{min:{x:0,y:730,z:10},max:{x:1e4,y:1530,z:1e3}},[_.SCENE_7]:{min:{x:1800,y:400,z:10},max:{x:1e4,y:730,z:1e3}},[_.SCENE_8]:{min:{x:800,y:450,z:10},max:{x:1e4,y:730,z:1e3}},[_.SCENE_9]:{min:{x:0,y:0,z:10},max:{x:800,y:450,z:1e3}},[_.SCENE_10]:{min:{x:800,y:100,z:10},max:{x:1500,y:1800,z:1e3}},[_.SCENE_11]:{min:{x:1200,y:330,z:10},max:{x:2e3,y:1200,z:1e3}},[_.SCENE_12]:{min:{x:300,y:2300,z:10},max:{x:4e3,y:4e3,z:1e3}},[_.SCENE_21]:{min:{x:1480,y:10,z:10},max:{x:3335,y:1e4,z:1e4}},[_.SCENE_22]:{min:{x:1730,y:10,z:1730},max:{x:3555,y:1e4,z:3555}},[_.SCENE_23]:{min:{x:750,y:10,z:10},max:{x:1650,y:1e4,z:1e4}},[_.SCENE_24]:{min:{x:2710,y:10,z:1730},max:{x:5045,y:1e4,z:4315}},[_.SCENE_25]:{min:{x:200,y:100,z:10},max:{x:5e3,y:2e3,z:1e3}},[_.SCENE_FALLBACK]:{min:{x:30,y:50,z:10},max:{x:6e3,y:5e3,z:1e4}},[_.SCENE_FALLBACK_SOFA]:{min:{x:30,y:50,z:10},max:{x:6e3,y:5e3,z:1e4}},[_.STUDIO]:{min:{x:0,y:0,z:0},max:{x:1e4,y:1e4,z:1e4}}};var rn={[_.SCENE_1]:Ko,[_.SCENE_2]:jo,[_.SCENE_3]:Qo,[_.SCENE_4]:ia,[_.SCENE_5]:na,[_.SCENE_6]:ca,[_.SCENE_7]:ya,[_.SCENE_8]:fa,[_.SCENE_9]:Ta,[_.SCENE_10]:Na,[_.SCENE_11]:Ma,[_.SCENE_12]:Ca,[_.SCENE_21]:Oa,[_.SCENE_22]:Wa,[_.SCENE_23]:Ua,[_.SCENE_24]:$a,[_.SCENE_25]:Ja},on={[_.SCENE_1]:qo,[_.SCENE_2]:Zo,[_.SCENE_3]:ta,[_.SCENE_4]:aa,[_.SCENE_5]:ma,[_.SCENE_6]:_a,[_.SCENE_7]:ua,[_.SCENE_8]:Sa,[_.SCENE_9]:wa,[_.SCENE_10]:za,[_.SCENE_11]:ka,[_.SCENE_12]:La,[_.SCENE_21]:Ha,[_.SCENE_22]:Va,[_.SCENE_23]:Xa,[_.SCENE_24]:Qa,[_.SCENE_25]:tn},an={[_.SCENE_1]:Uo,[_.SCENE_2]:$o,[_.SCENE_3]:ea,[_.SCENE_4]:oa,[_.SCENE_5]:la,[_.SCENE_6]:pa,[_.SCENE_7]:ga,[_.SCENE_8]:xa,[_.SCENE_9]:Pa,[_.SCENE_10]:Aa,[_.SCENE_11]:Ya,[_.SCENE_12]:va,[_.SCENE_21]:Da,[_.SCENE_22]:Ka,[_.SCENE_23]:ja,[_.SCENE_24]:Za,[_.SCENE_25]:en},nn={[_.SCENE_1]:Vo,[_.SCENE_2]:Xo,[_.SCENE_3]:Jo,[_.SCENE_4]:ra,[_.SCENE_5]:sa,[_.SCENE_6]:da,[_.SCENE_7]:ha,[_.SCENE_8]:Ea,[_.SCENE_9]:Ra,[_.SCENE_10]:ba,[_.SCENE_11]:Ia,[_.SCENE_12]:Ba,[_.SCENE_21]:Ga,[_.SCENE_22]:Fa,[_.SCENE_23]:qa,[_.SCENE_24]:Ui,[_.SCENE_25]:Ui},sn=(e,r,o)=>{let n=Object.keys(rn).includes(e)?rn[e]:null;return n?n(r,o):null},si=(e,r,o)=>{let n=Object.keys(nn).includes(e)?nn[e]:null;return n?n(r,o):!1},ln=(e,r,o,n)=>{let a=Object.keys(an).includes(e)?an[e]:null;return a?a(r,o,n):"default"},mn=(e,r,o,n)=>{let a=Object.keys(on).includes(e)?on[e]:null;return a?a(r,o,n):"default"};var jm={[w.Sideboard]:[_.SCENE_5,_.SCENE_2,_.SCENE_4,_.SCENE_1,_.SCENE_3],[w.Wardrobe]:[_.SCENE_4,_.SCENE_1,_.SCENE_2,_.SCENE_12],[w.Chest]:[_.SCENE_5,_.SCENE_2,_.SCENE_4,_.SCENE_1,_.SCENE_3],[w.VinylStorage]:[_.SCENE_5,_.SCENE_2,_.SCENE_1,_.SCENE_3,_.SCENE_4,_.SCENE_12],[w.Bookcase]:[_.SCENE_1,_.SCENE_3,_.SCENE_2,_.SCENE_4,_.SCENE_12],[w.Desk]:[_.SCENE_2,_.SCENE_4,_.SCENE_10],[w.DressingTable]:[_.SCENE_25,_.SCENE_2,_.SCENE_10],[w.WallStorage]:[_.SCENE_1,_.SCENE_3,_.SCENE_2,_.SCENE_4,_.SCENE_12],[w.TVStand]:[_.SCENE_5],[w.BedsideTable]:[_.SCENE_9,_.SCENE_8],[w.ShoeRack]:[_.SCENE_11],[w.Sofa]:[_.SCENE_23,_.SCENE_21,_.SCENE_22,_.SCENE_24]},Xm={[_.SCENE_1]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_2]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_3]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_4]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_5]:[b.Type23,b.Type24,b.Type25,b.Veneer25]},cn=e=>{let r=jm[e.category];return r?r.filter(o=>{let n=Xm[o];return n?!n.includes(e.shelfType):!0}):null};var $m={[b.Type10]:[_.SCENE_23,_.SCENE_21,_.SCENE_22,_.SCENE_24],[b.Type23]:[_.SCENE_6,_.SCENE_7,_.SCENE_8],[b.Type24]:[_.SCENE_6,_.SCENE_7,_.SCENE_8],[b.Type25]:[_.SCENE_6,_.SCENE_7,_.SCENE_8]},Zm={[_.SCENE_6]:[w.Wardrobe,w.ShoeRack,w.TVStand,w.BedsideTable,w.DressingTable],[_.SCENE_7]:[w.Wardrobe,w.Desk,w.ShoeRack,w.BedsideTable,w.DressingTable],[_.SCENE_8]:[w.Wardrobe,w.Desk,w.Bookcase,w.ShoeRack,w.WallStorage,w.TVStand,w.DressingTable]},dn=e=>{let r=$m[e.shelfType];return r?r.filter(o=>{let n=Zm[o];return n?!n.includes(e.category):!0}):null};var pn={getScenesByShelfCategory:cn,getScenesByShelfType:dn};var Qm={[w.Sideboard]:[_.SCENE_5,_.SCENE_2,_.SCENE_4,_.SCENE_1,_.SCENE_3],[w.Wardrobe]:[_.SCENE_4,_.SCENE_1,_.SCENE_2,_.SCENE_12],[w.Chest]:[_.SCENE_5,_.SCENE_2,_.SCENE_4,_.SCENE_1,_.SCENE_3],[w.VinylStorage]:[_.SCENE_5,_.SCENE_2,_.SCENE_1,_.SCENE_3,_.SCENE_4,_.SCENE_12],[w.Bookcase]:[_.SCENE_1,_.SCENE_3,_.SCENE_2,_.SCENE_4,_.SCENE_12],[w.Desk]:[_.SCENE_2,_.SCENE_4,_.SCENE_10],[w.DressingTable]:[_.SCENE_25,_.SCENE_2,_.SCENE_10],[w.WallStorage]:[_.SCENE_1,_.SCENE_3,_.SCENE_2,_.SCENE_4,_.SCENE_12],[w.TVStand]:[_.SCENE_5],[w.BedsideTable]:[_.SCENE_9,_.SCENE_8],[w.ShoeRack]:[_.SCENE_11],[w.Sofa]:[_.SCENE_23,_.SCENE_21,_.SCENE_22,_.SCENE_24]},Jm={[_.SCENE_1]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_2]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_3]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_4]:[b.Type23,b.Type24,b.Type25,b.Veneer25],[_.SCENE_5]:[b.Type23,b.Type24,b.Type25,b.Veneer25]},_n=e=>{let r=Qm[e.category];return r?r.filter(o=>{let n=Jm[o];return n?!n.includes(e.shelfType):!0}):null};var ec={[b.Type10]:[_.SCENE_23,_.SCENE_21,_.SCENE_22,_.SCENE_24],[b.Type23]:[_.SCENE_6,_.SCENE_7,_.SCENE_8],[b.Type24]:[_.SCENE_6,_.SCENE_7,_.SCENE_8],[b.Type25]:[_.SCENE_6,_.SCENE_7,_.SCENE_8]},tc={[_.SCENE_6]:[w.Wardrobe,w.ShoeRack,w.TVStand,w.BedsideTable,w.DressingTable],[_.SCENE_7]:[w.Wardrobe,w.Desk,w.ShoeRack,w.BedsideTable,w.DressingTable],[_.SCENE_8]:[w.Wardrobe,w.Desk,w.Bookcase,w.ShoeRack,w.WallStorage,w.TVStand,w.DressingTable]},yn=e=>{let r=ec[e.shelfType];return r?r.filter(o=>{let n=tc[o];return n?!n.includes(e.category):!0}):null};var li={getScenesByShelfCategory:_n,getScenesByShelfType:yn};var mi=e=>{switch(e){case H.FEED:return pn;case H.LIFESTYLE:return li;case H.LIFESTYLE_MOBILE:return li;default:return li}};var hn=e=>{let r=mi(e.context).getScenesByShelfType(e);if(!r||e.category===w.Sofa&&(r=r.filter(n=>si(n,e.intermediate.furnitureLayout,e.intermediate.componentCount)),!r))return null;let o=r.find(n=>ni(e.shelfSize,At[n]||ai.matchNone));return o||null};var gn=e=>{let r=mi(e.context).getScenesByShelfCategory(e);if(!r||(e.category===w.Sofa&&(r=r.filter(n=>si(n,e.intermediate.furnitureLayout,e.intermediate.componentCount))),!r))return null;let o=r.find(n=>ni(e.shelfSize,At[n]||ai.matchNone));return o||null};var un=[hn,gn];var qi=e=>{let r=Fo(un).executeFor(e);return r?e.outputParameters.name=r:e.fallback=!0,e};var ji=e=>{let r=sn(e.outputParameters.name,e.shelfType,e.mainColor);return r?(e.intermediate.preselectedStyle=r,e.outputParameters.mood=e.outputParameters.furnishing=e.outputParameters.tone=r):e.fallback=!0,e};var Xi=e=>{let r=`${e.intermediate.shelfLine}-vp-${e.intermediate.preselectedStyle}`;return e.outputParameters.mood=e.outputParameters.tone=e.outputParameters.furnishing=r,e};var $i=e=>(e.outputParameters.shotPreset=e.category===w.Sofa?ln(e.outputParameters.name,e.context,e.intermediate.furnitureLayout,e.intermediate.componentCount):mn(e.outputParameters.name,e.intermediate.shelfLine,e.intermediate.preselectedStyle,e.context),e);var ic=()=>new jt().configureFallback(Ki).configureWith(qi).configureWith(ji).configureWith(Xi).configureWith($i),Zi=e=>ic().executeFor(e).outputParameters;var fn={name:"Blender",tone:"default",shotPreset:"center",furnishing:"default",mood:"default"};var ie=(e,r)=>({TYPE_01:Xt(r),TYPE_02:$t(r),VENEER_01:ii(r),TYPE_03:Zt(r),TYPE_13:Qt(r),VENEER_13:ri(r),TYPE_23:Jt(r),TYPE_24:ei(r),TYPE_25:ti(r),VENEER_25:oi(r)})[e],ci=e=>{if(e.material!=null&&e.material>=0)return e.material;if(e.materials!=null&&e.materials.length>0){let r={};return e.materials.forEach(o=>{r[o]=(r[o]||0)+1}),Number(Object.keys(r).reduce((o,n)=>r[o]>r[n]?o:n))}return 0},En=(e,r)=>({TYPE_01:Xt(r).horizontals,TYPE_02:$t(r).horizontals,VENEER_01:ii(r).horizontals,TYPE_03:Zt(r).walls,TYPE_13:Qt(r).walls,VENEER_13:ri(r).slabs,TYPE_23:Jt(r).walls,TYPE_24:ei(r).walls,TYPE_25:ti(r).walls,VENEER_25:oi(r).walls,TYPE_10:Qi(r)})[e],Qi=e=>{let r=["rewool_brown","rewool_oliveGreen","rewool_lightGrey","rewool_butterYellow","rewool_shadowPink","rewool_green","rewool_babyBlue","darven_ecru","darven_rock","darven_darkBrown","darven_steel","darven_tobacco","darven_pink","darven_camouflage","darven_klein"],o=e<7?"rewool":"darven",n=r[e]||"default";return{name:o,colors:{primary:n,secondary:n,tertiary:n}}};var rc={defaultConfiguration:Hi,isStudioInterior:Wi,adaptToShelves:Wo},oc={defaultConfiguration:fn},ac={getInteriorRecommendation:(e,r,o)=>Zi({...r,fallback:!1,intermediate:{furnitureLayout:e,componentCount:-1,preselectedStyle:t.Default,shelfLine:Vi(r.shelfType)},context:o,outputParameters:{name:_.SCENE_FALLBACK,shotPreset:"center",tone:t.Default,furnishing:t.Default,mood:t.Default}}),getInteriorRecommendationForShelf:(e,r,o)=>{let n=Je(e,r),a=ci(e),s={category:n,mainColor:a,shelfType:ae(e.shelf_type),shelfSize:ct(e)};return Zi({...s,fallback:!1,intermediate:{furnitureLayout:dt(e,s.category),componentCount:r===w.Sofa?e.seaters.length:e.components.length,preselectedStyle:t.Default,shelfLine:Vi(s.shelfType)},context:o,outputParameters:{name:_.SCENE_FALLBACK,shotPreset:"center",tone:t.Default,furnishing:t.Default,mood:t.Default}})}},it={studio:rc,configurator:oc,recommendations:ac};var Ce=(e,r)=>{let o=v(e),n=G(e),a=h(o,y.SCALAR),s=h(n,y.SCALAR);return{position:a,size:{x:0,y:0,z:0},name:"back",segmentTag:{},geometries:[{asset:"frontalPanel",groupTag:`column_axis_${a.x}`,position:{x:0,y:0,z:0},size:s,rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:s.x,v:s.y,w:s.z}}}]}};var ee=(e,r,o,n=!1)=>{let a={x:1,y:1,z:1},s=3,m={width:{offsetX:0,offsetY:1,offsetZ:.5},height:{offsetX:1.9,offsetY:0,offsetZ:.5},depth:{offsetX:0,offsetY:0,offsetZ:0}},l={value:e.value,unit:e.valueUnit,context:e.properties.context,measurement:e.properties.measurement,visibility:e.properties.visibility};if(e.properties.context==="exterior"&&e.properties.visibility==="whenClosed"){let C=o.some(I=>e.labelPoint.x>=I.x1&&e.labelPoint.x<=I.x2&&e.labelPoint.y>=I.y1&&e.labelPoint.y<=I.y2);l.visibility=C?"whenClosed":"always"}n&&e.properties.context==="interior"&&(l.visibility="always");let p={x:e.range.end.x-e.range.start.x,y:e.range.end.y-e.range.start.y,z:e.range.end.z-e.range.start.z},d={width:{x:p.x,y:s,z:s},height:{x:s,y:p.y,z:s},depth:{x:s,y:s,z:p.z}},u=l.context==="feature"||l.context==="exterior"?{x:34*m[l.measurement].offsetX,y:34*m[l.measurement].offsetY,z:34*m[l.measurement].offsetZ}:{x:0,y:0,z:0},g={width:{x:s,y:50,z:s},height:{x:50,y:s,z:s},depth:{x:s,y:50,z:s}},S={x:u.x,y:u.y,z:u.z},E={start:{width:{x:-p.x/2+u.x,y:0+u.y,z:0+u.z},height:{x:0+u.x,y:-p.y/2+u.y,z:0+u.z},depth:{x:0+u.x,y:0+u.y,z:-p.z/2+u.z}},end:{width:{x:p.x/2+u.x,y:0+u.y,z:0+u.z},height:{x:0+u.x,y:p.y/2+u.y,z:0+u.z},depth:{x:0+u.x,y:0+u.y,z:p.z/2+u.z}}},N=l.context!=="silhouette"?{x:34*m[l.measurement].offsetX,y:34*m[l.measurement].offsetY,z:34*m[l.measurement].offsetZ}:{x:0,y:0,z:0},f={width:{primary:"graphiteFill",secondary:"neutral",tertiary:"neutral"},height:{primary:"stormGrayFill",secondary:"neutral",tertiary:"neutral"},depth:{primary:"graphiteFill",secondary:"neutral",tertiary:"neutral"}},T=l.context==="interior",R=[{asset:"line",groupTag:l.context,position:h(S,y.SCALAR),size:h(d[l.measurement],y.SCALAR),rotation:null,mirror:null,material:{name:"basic",colors:{primary:"grayLine",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null}},{asset:"line",groupTag:l.context,position:h(E.start[l.measurement],y.SCALAR),size:h(g[l.measurement],y.SCALAR),rotation:null,mirror:null,material:{name:"basic",colors:{primary:"grayLine",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null}},{asset:"line",groupTag:l.context,position:h(E.end[l.measurement],y.SCALAR),size:h(g[l.measurement],y.SCALAR),rotation:null,mirror:null,material:{name:"basic",colors:{primary:"grayLine",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null}}];return{position:h(e.labelPoint,y.SCALAR),size:{x:0,y:0,z:0},name:"dimension",segmentTag:l,geometries:[{asset:"text",groupTag:e.value.toString(),position:h({x:N.x,y:N.y,z:N.z},y.SCALAR),size:a,rotation:l.measurement==="depth"?{axis:"y",angle:-Math.PI/2}:null,mirror:null,material:{name:"stroke",colors:{primary:"neutral",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null}},{asset:"pill",groupTag:l.context,position:h({x:N.x,y:N.y,z:N.z},y.SCALAR),size:a,rotation:l.measurement==="depth"?{axis:"y",angle:-Math.PI/2}:null,mirror:null,material:{name:"fill",colors:f[l.measurement],mapAngle:0,mapRatio:null}},...T?[]:R]}};var Ji=(e,r,o,n)=>{let a=r.filter(Y=>Y.x1>=e.x1&&Y.x2<=e.x2&&Y.y1>=e.y1&&Y.y2<=e.y2),s=e.direction===2,m=e.direction===1,l=e.handle===1,p=3,d=3,u=1109,g={top:Y=>{Y.y=Y.y+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR},bottom:Y=>{Y.y=Y.y-(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR},right:Y=>{Y.x=Y.x+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR},left:Y=>{Y.x=Y.x+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR},other:Y=>{Y.x=Y.x-d*y.SCALAR,Y.y=Y.y-d*y.SCALAR}},S={top:Y=>{Y.y=Y.y+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR/2},bottom:Y=>{Y.y=Y.y+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR/2},right:Y=>{Y.x=Y.x+(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR/2},left:Y=>{Y.x=Y.x-(j.DEFAULT_PANEL_THICKNESS/2-p)*y.SCALAR/2}},E={x:0,y:0,z:0},N=re(e),f=Te(e,N),T=e.x2-e.x1,R=e.y1+(e.y2-e.y1)/2,C=(m?T/2:-T/2)*y.SCALAR,I={...e},z={...E},O=re(I,16);g.other(O),e.extremes.forEach(Y=>{S[Y](z),g[Y](O)});let x=h({x:s?E.x-(T/2-39):E.x+(T/2-39),y:u-R,z:10},y.SCALAR),A={...E,x:s?E.x+N.x/2-20*y.SCALAR:E.x-N.x/2+20*y.SCALAR,z:-O.z/2},F=a.map(Y=>{let me={x:1,y:1,z:1},_e=Y.y1+(Y.y2-Y.y1)/2-R;return{asset:"hingeWing",groupTag:"wing",position:{...A,x:A.x+C,y:_e*y.SCALAR},size:me,rotation:{axis:"z",angle:s?0:Math.PI},mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:O.x,v:O.y,w:O.z}}}}),V=l?[{asset:"gripHandle",groupTag:"wing",position:{...x,x:x.x+C},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:"paintedMetal",colors:o.colors,mapAngle:0,mapRatio:{u:O.x,v:O.y,w:O.z}}}]:[],U=e.subtype==="t"?{close:0,open:105,ajar:65}:{close:0,open:95,ajar:70},Z=s?{...U}:{...U,open:-U.open,ajar:-U.ajar};return{position:{...f,x:f.x-C},size:{x:0,y:0,z:0},name:"door",segmentTag:{sectionId:n.toString(),direction:s?"right":"left",animationOffset:C.toString(),motion:Z},geometries:[{asset:"frontalPanel",groupTag:"wing",position:{...z,x:z.x+C},size:O,rotation:null,mirror:null,material:{name:o.name,colors:o.colors,mapAngle:0,mapRatio:{u:O.x,v:O.y,w:O.z}}},...V,...F]}};var er=(e,r,o,n,a,s,m)=>{let l=r.filter(B=>B.x1===e.x1||e.x2===B.x2).sort((B,xt)=>xt.y1-B.y1),d=(l.findIndex(B=>B.y1===e.y1)+1)/l.length,u=e.subtype==="e",g=B=>({x:B.x*y.SCALAR,y:B.y*y.SCALAR,z:B.z*y.SCALAR}),S=12,E=17,N=2,f={S:167,M:224,L:338},T=9,R=0,C={top:B=>{B.y=B.y+T},bottom:B=>{B.y=B.y},right:B=>{B.x=B.x+T},left:B=>{B.x=B.x+T},other:B=>{B.x=B.x,B.y=B.y}},I={top:B=>{B.y=B.y+T/2},bottom:B=>{B.y=B.y},right:B=>{B.x=B.x+T/2},left:B=>{B.x=B.x-T/2}},z={x:e.x2-e.x1-j.DEFAULT_PANEL_THICKNESS,y:e.y2-e.y1+E,z:e.z2-e.z1},O={x:0,y:u?-E/2:E/2,z:0},x=B=>B===122?"S":B===179?"M":"L",A=B=>f[x(B)],F={x:u?z.x+2*j.DEFAULT_PANEL_THICKNESS-2*N:z.x,y:u?A(z.y):z.y,z:S},V={...z,z:S},k={x:S,y:z.y,z:z.z-S*2},U={x:z.x-S*2,y:S,z:z.z-S*2},Z={x:O.x,y:O.y,z:O.z+(z.z/2-F.z/2)},Y={x:O.x,y:O.y,z:O.z-(z.z/2-V.z/2)},me={x:O.x-(z.x/2-k.x/2),y:O.y,z:O.z},$={x:O.x+(z.x/2-k.x/2),y:O.y,z:O.z},_e={x:0,y:O.y-(z.y/2-U.y/2),z:O.z};C.other(F),e.extremes.forEach(B=>{I[B](Z),C[B](F)});let M=[{position:g(Z),size:g(F),materialName:u?n.name:a.name,materialColors:u?n.colors:a.colors,asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:g(Y),size:g(V),materialName:a.name,materialColors:a.colors,asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:g(me),size:g(k),materialName:a.name,materialColors:a.colors,asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:g($),size:g(k),materialName:a.name,materialColors:a.colors,asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:g(_e),size:g(U),materialName:a.name,materialColors:a.colors,asset:"horizontalPanel",tag:"motion",ratioKey:{u:"x",v:"z",w:"y"}}].map(B=>({asset:B.asset,groupTag:B.tag,position:B.position,size:B.size,rotation:null,mirror:null,material:{name:B.materialName,colors:B.materialColors,mapAngle:0,mapRatio:{u:B.size[B.ratioKey.u],v:B.size[B.ratioKey.v],w:B.size[B.ratioKey.w]}}})),ce=re(e),Se=Te(e,ce),ge={...Se,z:Se.z+(u?S*y.SCALAR:0)},lt={close:ge.z,ajar:ge.z+.55*d*(.95*ce.z),open:ge.z+d*(.95*ce.z)};return{position:ge,size:ce,name:"drawer",segmentTag:{sectionId:s.toString(),motion:lt},geometries:M}};var tr=e=>{let r=v(e),o=G(e),n=h(r,y.SCALAR),a=h({...o,z:o.z+10},y.SCALAR);return{position:n,size:{x:0,y:0,z:0},name:"hoverBox",segmentTag:{sectionId:e.m_config_id.toString(),segmentId:e.uuid,selectable:e.verticalOrder===0,linkedSegment:e.verticalOrder===0?null:`${e.horizontalOrder}:0`},linkedSegments:[],geometries:[{asset:"boundingBox",groupTag:"colHover",position:{x:0,y:0,z:0},size:a,rotation:null,mirror:null,material:{name:"hover",colors:{primary:"void",secondary:"void",tertiary:"void"},mapAngle:0,mapRatio:null}}]}};var ir=(e,r,o,n,a)=>{let s=r.filter(U=>U.x1>=e.x1&&U.x1<=e.x2),m=e.direction===2,l=e.direction===1,p=e.handle===0,d=4,u=a.some(U=>e.x1===U.x1&&e.x2===U.x2&&e.y1-U.y2===d),g={x:0,y:0,z:0},S=re(e),E=Te(e,S),N=e.x2-e.x1,f=e.y1+(e.y2-e.y1)/2,T=(l?N/2-20:-N/2+20)*y.SCALAR,R={...e,x1:m?e.x1+30:e.x1,x2:l?e.x2-10:e.x2},C={...g,x:m?g.x+15*y.SCALAR:g.x-5*y.SCALAR},I=re(R,16),z={...e,x1:m?e.x1:e.x2-10,x2:m?e.x1+30:e.x2},O={...g,x:m?g.x-I.x/2+15*y.SCALAR:g.x+I.x/2-5*y.SCALAR},x=re(z),A={...g,x:m?g.x+S.x/2-20*y.SCALAR:g.x-S.x/2+20*y.SCALAR,z:-I.z/2},F=s.map(U=>{let Z={x:1,y:1,z:1},me=U.y1+(U.y2-U.y1)/2-f;return{asset:"hingeWing",groupTag:"wing",position:{...A,x:A.x+T,y:me*y.SCALAR},size:Z,rotation:{axis:"z",angle:m?0:Math.PI},mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:I.x,v:I.y,w:I.z}}}}),V=m?{close:0,open:95,ajar:60}:{close:0,open:-95,ajar:-60},k=m?{close:0,open:90,ajar:70}:{close:0,open:-90,ajar:-70};return{position:{...E,x:E.x-T},size:{x:0,y:0,z:0},name:"door",segmentTag:{sectionId:n.toString(),direction:m?"right":"left",animationOffset:T.toString(),motion:u?V:k},geometries:[{asset:"frontalPanel",groupTag:"wing",position:{...C,x:C.x+T},size:I,rotation:null,mirror:null,material:{name:o.name,colors:o.colors,mapAngle:0,mapRatio:{u:I.x,v:I.y,w:I.z}}},{asset:p?"fullHandleRight":"fullHandleLeft",groupTag:"wing",position:{...O,x:O.x+T},size:{...x,z:x.z*2},rotation:null,mirror:p&&l?"x":null,material:{name:"paintedMetal",colors:o.colors,mapAngle:0,mapRatio:{u:I.x,v:I.y,w:I.z}}},...F]}};var He={VERTICAL:50,HORIZONTAL:21},Be=e=>({externalVeneer:19,externalLaminate:16,externalLayered:16,internalLaminate:12,internalPlywood:12,structuralPlywood:18,structuralVeneer:18,structuralLaminate:18})[e],xn=e=>({182:106,282:206,382:306})[e],Sn=e=>({182:86,282:186,382:286})[e],Tn=e=>({320:258,400:338,500:438,600:538})[e],di=e=>({none:{width:0,height:0,offset:0},horizontalFullHandle:{width:1/0,height:30,offset:0},verticalRightFullHandle:{width:30,height:1/0,offset:0},verticalLeftFullHandle:{width:10,height:1/0,offset:0},slimHandle:{width:0,height:1,offset:65},anode:{width:0,height:30,offset:0}})[e];var fe;(function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"})(fe||(fe={}));var Rn;(function(e){e[e.Default=0]="Default",e[e.Bottom=1]="Bottom"})(Rn||(Rn={}));var pi=e=>{let r=Be(e.frontType),o=di(e.handleType),n=e.direction,a=e.parity,s=e.handleYPos===1,m=a==="double"&&n===fe.Left,l=a==="double"&&n===fe.Right,p={...e.envelope,z1:e.envelope.z2-r,x1:e.envelope.x1+(l?3/2:3),x2:e.envelope.x2-(m?3/2:3),y1:e.envelope.y1+3,y2:e.envelope.y2-3},d=G(p),u=v(p),g={positions:[d.y/2-He.VERTICAL,-d.y/2+He.VERTICAL],offset:He.HORIZONTAL};return{boundingBoxSize:d,boundingBoxPosition:u,handleSize:o,verticalFlip:s,direction:n,hinges:g,parity:a}};var rr=(e,r,o,n,a,s)=>{let m=o.filter(L=>e.y1>=L.y1&&e.y2<=L.y2),l=m.filter(L=>L.x1===e.x1&&L.direction===fe.Left),p=m.filter(L=>L.x2===e.x2&&L.direction===fe.Right),d=r.filter(L=>L.x1===e.x1||e.x2===L.x2).sort((L,Ni)=>Ni.y1-L.y1),g=(d.findIndex(L=>L.y1===e.y1)+1)/d.length,S=p.length>0,E=l.length>0,N=S&&E,f=e.extremes&&e.extremes.includes("bottom"),T=e.subtype==="e",R=L=>({x:L.x*y.SCALAR,y:L.y*y.SCALAR,z:L.z*y.SCALAR}),C=30,I=8,z=12,O=10,x=6,A=28,F=30,V=N?(C+I)*2:S||E?C+I:0,k={x:e.x2-e.x1-V,y:e.y2-e.y1,z:e.z2-e.z1},U=O,Z=f?A:x,Y={...k,y:k.y+U+Z,z},me={...k,z},$={y:k.y-F,x:z,z:k.z-z*2},_e={x:k.x-z*2,y:z,z:k.z-z*2},J={x:0,y:0,z:0},M={x:0,y:(U-Z)/2,z:J.z+(k.z/2-Y.z/2)},ce={x:0,y:0,z:J.z-(k.z/2-me.z/2)},Se={x:J.x-(k.x/2-$.x/2),y:0,z:J.z},ge={x:J.x+(k.x/2-$.x/2),y:0,z:J.z},lt={x:0,y:J.x-(k.y/2-_e.y/2),z:J.z},B=[{position:R(M),size:R(Y),material:"laminate",asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:R(ce),size:R(me),material:"laminate",asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:R(Se),size:R($),material:"laminate",asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:R(ge),size:R($),material:"laminate",asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:R(lt),size:R(_e),material:"laminate",asset:"horizontalPanel",tag:"motion",ratioKey:{u:"x",v:"z",w:"y"}}];T&&B.push({position:R({...M,y:M.y+Y.y/2}),size:R({...Y,y:1,z:1}),material:"paintedMetal",asset:"fullHandleTop",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}});let xt=B.map(L=>({asset:L.asset,groupTag:L.tag,position:L.position,size:L.size,rotation:null,mirror:null,material:{name:n.name,colors:n.colors,mapAngle:0,mapRatio:{u:L.size[L.ratioKey.u],v:L.size[L.ratioKey.v],w:L.size[L.ratioKey.w]}}})),Le=re(e),St=Te(e,Le),Pi=N?0:S?-V/2:E?V/2:0,Oe={...St,x:St.x+Pi*y.SCALAR},wi={close:Oe.z,ajar:Oe.z+.55*g*(.95*Le.z),open:Oe.z+g*(.95*Le.z)};return{position:Oe,size:Le,name:"drawer",segmentTag:{sectionId:a.toString(),motion:wi},geometries:xt}};var we=(e,r={x1:0,y1:0,z1:0,x2:0,y2:18,z2:-9})=>{let o={x1:e.x1+r.x1,x2:e.x2+r.x2,y1:0+r.y1,y2:e.y2+r.y2,z1:0+r.z1,z2:e.z2+r.z2},n=v(o),a=G(o),s=h(n,y.SCALAR),m=h(a,y.SCALAR),l=e.range?{min:h(e.range.min,y.SCALAR),max:h(e.range.max,y.SCALAR)}:{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}};return{position:s,size:{x:0,y:0,z:0},name:"hoverBox",segmentTag:{sectionId:e.m_config_id.toString(),segmentId:e.uuid,selectable:e.verticalOrder===0,linkedSegment:e.verticalOrder===0?null:`${e.horizontalOrder}:0`,channelId:e.channel_id,range:l},geometries:[{asset:"boundingBox",groupTag:"colHover",position:{x:0,y:0,z:0},size:m,rotation:null,mirror:null,material:null}]}};var zt=(e,r)=>{let o={...e,y2:e.y1,y1:e.y1-j.MASKING_BAR.HEIGHT,z2:e.z2,z1:e.z2-j.MASKING_BAR.DEPTH},n=v(o),a=G(o),s=h(n,y.SCALAR),m=h(a,y.SCALAR);return{position:{...s,y:s.y+m.y/2},size:{x:0,y:0,z:0},name:"plinth",segmentTag:{},geometries:[{asset:"maskingProfile",groupTag:`column_axis_${s.x}`,position:{x:0,y:0,z:0},size:{x:a.x*.1,y:1,z:1},rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:m.x,v:m.z,w:m.y}}}]}};var rt=(e,r)=>{let o=e.sort((p,d)=>p.y1-d.y1).sort((p,d)=>p.x1-d.x1),n=o.reduce((p,d,u)=>{let g=o[u-1];return u===0||d.x1>g.x1?p:[...p,{...d,y1:g.y2,y2:d.y1,z1:18}]},[]),a=(p,d)=>{let g=d.filter(S=>Qe(p,S,100)||mt(p,S,100));return g.length>0&&g[0].channel_id?g[0].channel_id.toString():"0"},s=0,m="";return n.sort((p,d)=>d.y1-p.y1).sort((p,d)=>p.x1-d.x1).map(p=>{let d=v(p),u=G(p),g={x:u.x-2,y:u.y-2,z:u.z-2},S=a(p,r);S!==m&&(m=S,s=0);let E=`${m}-${s}`;return s++,{position:h(d,y.SCALAR),size:{x:0,y:0,z:0},name:"opening",segmentTag:{sectionId:m},geometries:[{asset:"ambientOcclusionBox",groupTag:"frontalBox",position:{x:0,y:0,z:0},size:h(g,y.SCALAR),rotation:null,mirror:null,material:{name:"basic",colors:{primary:"void",secondary:"void",tertiary:"void"},mapAngle:0,mapRatio:null}},{asset:"boundingBox",groupTag:E,position:{x:0,y:0,z:0},size:h(g,y.SCALAR),rotation:null,mirror:null,material:null}]}})};var Ee=(e,r,o,n=!1,a=!1)=>{let s=e.subtype==="t"&&n,m=v(e),l=G(e),p=h(m,y.SCALAR),d=h(l,y.SCALAR),u=a&&s,g=u?"roundedPanel":"horizontalPanel",S=s?o.outer:o.slabs,E=S.name==="veneer"?90:0;return{position:p,size:{x:0,y:0,z:0},name:"slab",segmentTag:{},geometries:[{asset:g,groupTag:`column_axis_${m.x}`,position:{x:0,y:0,z:0},size:u?{x:d.x,y:1,z:d.z}:d,rotation:null,mirror:null,material:{name:S.name,colors:S.colors,mapAngle:E,mapRatio:{u:d.x,v:d.z,w:d.y}}}]}};var xe=(e,r,o,n,a,s,m=!1)=>{let l=v(e),p=G(e),d={left:Math.min(...s.map(M=>M.x1)),right:Math.max(...s.map(M=>M.x2))},u=e.x1===d.left,g=e.x2===d.right,S=n.filter(M=>M.x1===e.x2&&M.direction===fe.Left).map(M=>({...M,isRight:!1})),E=n.filter(M=>M.x2===e.x1&&M.direction===fe.Right).map(M=>({...M,isRight:!0})),N=r.filter(M=>M.x1===e.x2&&M.y1>=e.y1&&M.y2<=e.y2).map(M=>({...M,isRight:!1})),f=r.filter(M=>M.x2===e.x1&&M.y1>=e.y1&&M.y2<=e.y2).map(M=>({...M,isRight:!0})),T=(M,ce)=>ce.some(Se=>M.y1>=Se.y1&&M.y2<=Se.y2),R=M=>M.x2===e.x1&&M.y2<=e.y2&&M.y1>=e.y1,C=M=>M.x1===e.x2&&M.y2<=e.y2&&M.y1>=e.y1,I=o.filter(M=>C(M)&&T(M,S)).map(M=>({...M,isRight:!1})),z=o.filter(M=>R(M)&&T(M,E)).map(M=>({...M,isRight:!0})),O=z.length>0,x=I.length>0,A=30,F=12,V=M=>({size:G(M),position:v(M),isRight:M.isRight}),k=h(l,y.SCALAR),U=h(p,y.SCALAR),Z={x:0,y:0,z:0},Y=[...O?z.map(V):[],...x?I.map(V):[]].map(M=>{let ce=h({...M.size,x:27},y.SCALAR),Se=h({...Z,y:M.position.y-p.y/2+j.DEFAULT_PANEL_THICKNESS/2,x:M.isRight?Z.x-(p.x/2+A/2):Z.x+(p.x/2+A/2),z:M.size.z/2-p.z/2+F},y.SCALAR),ge=a.drawers;return{asset:"verticalPanel",groupTag:"anchor",position:Se,size:ce,rotation:null,mirror:null,material:{name:ge.name,colors:{primary:ge.colors.primary,secondary:ge.colors.primary,tertiary:ge.colors.primary},mapAngle:0,mapRatio:{u:ce.z,v:ce.y,w:ce.x}}}}),me=[...N,...f].map(M=>{let ce={x:1,y:1,z:1},Se=v(M),ge={...Z,y:Se.y-l.y,x:M.isRight?Z.x-p.x/2-20:Z.x+p.x/2+20,z:Se.z-p.z/2+20};return{asset:"hingeAnchor",groupTag:"anchor",position:h(ge,y.SCALAR),size:ce,rotation:{axis:"z",angle:M.isRight?0:Math.PI},mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:1,v:1,w:1}}}}),_e=m&&(u||g)?a.outer:a.walls,J=m&&u?{axis:"y",angle:Math.PI}:null;return{position:k,size:{x:0,y:0,z:0},name:"wall",segmentTag:{},geometries:[{asset:"verticalPanel",groupTag:`column_axis_${k.x}`,position:{x:0,y:0,z:0},size:U,rotation:J,mirror:null,material:{name:_e.name,colors:_e.colors,mapAngle:0,mapRatio:{u:U.z,v:U.y,w:U.x}}},...me,...Y]}};var ot=(e,r)=>{let o=v(e),n=G(e),a=h(o,y.SCALAR),s=h(n,y.SCALAR),m={...a,y:a.y+s.y/2},l={x:n.x*.1,y:1,z:1};return{position:e.subtype==="z"?m:a,size:{x:0,y:0,z:0},name:"wardrobeBar",segmentTag:{},geometries:[{asset:e.subtype==="z"?"crossWardrobeBar":"frontWardrobeBar",groupTag:`column_axis_${a.x}`,position:{x:0,y:0,z:0},size:e.subtype==="z"?{x:1,y:1,z:s.z}:l,rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:e.subtype==="z"?{u:s.z,v:s.y,w:s.x}:{u:s.x,v:s.y,w:s.z}}}]}};var or=(e,r)=>{let o=v(e),n=G(e),a=h(o,y.SCALAR),s=h(n,y.SCALAR);return{position:a,size:{x:1,y:1,z:1},name:"frame",segmentTag:{},geometries:[{asset:e.subtype==="t"?"horizontalPanel":"verticalPanel",groupTag:`column_axis_${n.x}`,position:{x:0,y:0,z:0},size:s,rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:e.subtype==="t"?{u:s.x,v:s.z,w:s.y}:{u:s.z,v:s.y,w:s.x}}}]}};var Mt=(e,r)=>{let o=v({...e,y2:e.y1}),n=G(e),a=h(o,y.SCALAR),s=h(n,y.SCALAR),m=e.subtype==="s",l=m&&e.direction===1,p=m?s.y:1,d=e.subtype==="m"?"thickLeg":"pillarLeg",u={axis:"y",angle:l?0:Math.PI};return{position:a,size:{x:1,y:1,z:1},name:"leg",segmentTag:{},geometries:[{asset:d,groupTag:`column_axis_${o.x}`,position:{x:0,y:0,z:0},size:{x:1,y:p,z:1},rotation:m?u:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:s.x,v:s.z,w:s.y}}}]}};var ar=(e,r)=>{let o=v({...e,y2:e.y1}),n=G(e),a=h(o,y.SCALAR),s=h(n,y.SCALAR);return{position:a,size:{x:1,y:1,z:1},name:"slab",segmentTag:{},geometries:[{asset:"liftLeg",groupTag:`column_axis_${o.x}`,position:{x:0,y:0,z:0},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:s.x,v:s.z,w:s.y}}}]}};var We=(e,r,o,n,a)=>{let s=(f,T)=>{let C=T.filter(I=>Qe(f,I,100)||mt(f,I,100));return C.length>0&&C[0].channel_id?C[0].channel_id.toString():"0"},m=o[0],l=0,p="",d=n.sort((f,T)=>f.x1-T.x1),u=o.sort((f,T)=>f.y1-T.y1),g=d.map((f,T)=>{let R=d[T+1],C=u.filter(z=>R&&(wr(f,z)||Nr(f,z)||br(f,z)));return C.map((z,O)=>{let x=C[O+1];return x?nc(f,R,z,x):null}).filter(z=>z!==null)}).filter(f=>f.length>0),S=sc(a,m);return[...g.flatMap(f=>f),...S].sort((f,T)=>T.y1-f.y1).sort((f,T)=>f.x1-T.x1).map(f=>{let T=v(f),R=G(f),C={x:R.x-2,y:R.y-2,z:R.z-2},I=f.hoverTag,z=f.hoverTag===null;if(z){let x=s(f,a);x!==p&&(p=x,l=0),I=`${p}-${l}`,l++}let O=[{asset:"boundingBox",groupTag:I,position:{x:0,y:0,z:0},size:h(C,y.SCALAR),rotation:null,mirror:null,material:null}];return z&&O.push({asset:"ambientOcclusionBox",groupTag:"frontalBox",position:{x:0,y:0,z:0},size:h(C,y.SCALAR),rotation:null,mirror:null,material:{name:"basic",colors:{primary:"void",secondary:"void",tertiary:"void"},mapAngle:0,mapRatio:null}}),{position:h(T,y.SCALAR),size:{x:0,y:0,z:0},name:"opening",segmentTag:{sectionId:(I==null?void 0:I.split("-")[0])||""},geometries:O}})},nc=(e,r,o,n)=>{let a=Math.min(e.z2,r.z2,o.z2,n.z2),s=Math.max(e.z1,r.z1,o.z1,n.z1);return{x1:e.x2,x2:r.x1,y1:o.y2,y2:n.y1,z1:s,z2:a,hoverTag:null}},sc=(e,r)=>{let o=[];return e.forEach(n=>{o.push({x1:n.x1,x2:n.x1+180,y1:n.y2,y2:n.y2+240,z1:r.z1,z2:r.z2,hoverTag:`${n.channel_id}-top`}),o.push({x1:n.x1+180,x2:n.x2-180,y1:n.y2,y2:n.y2+240,z1:r.z1,z2:r.z2,hoverTag:`${n.channel_id}-top`}),o.push({x1:n.x2-180,x2:n.x2,y1:n.y2,y2:n.y2+240,z1:r.z1,z2:r.z2,hoverTag:`${n.channel_id}-top`})}),o};var at=(e,r,o)=>{let a=o.find(E=>E.subtype!=="h"&&E.x1>e.x1&&E.x2<e.x2&&E.y1>e.y1&&E.y2<e.y2),s=v(e),m=G(e),l=h(s,y.SCALAR),p=h(m,y.SCALAR),d=[];a&&(d.push({...e,y1:a.y2+60/2}),d.push({...e,y2:a.y1-60/2}),d.push({...e,x1:a.x2+60/2,y1:a.y1-60/2,y2:a.y2+60/2}),d.push({...e,x2:a.x1-60/2,y1:a.y1-60/2,y2:a.y2+60/2}));let u=E=>{let N=v(E),f=G(E),T={x:N.x-s.x,y:N.y-s.y,z:N.z-s.z},R=h(T,y.SCALAR),C=h(f,y.SCALAR);return{asset:"frontalPanel",groupTag:"back",position:R,size:C,rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:C.x,v:C.y,w:C.z}}}},g=d.map(u);if(a){let E=v(a),N={x:E.x-s.x,y:E.y-s.y,z:E.z-s.z+6},f=h(N,y.SCALAR);g.push({asset:"backHole",groupTag:"hole",position:f,size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:p.x,v:p.y,w:p.z}}})}let S=d.length?g:[{asset:"frontalPanel",groupTag:"back",position:{x:0,y:0,z:0},size:p,rotation:null,mirror:null,material:{name:r.name,colors:r.colors,mapAngle:0,mapRatio:{u:p.x,v:p.y,w:p.z}}}];return{position:l,size:{x:0,y:0,z:0},name:"back",segmentTag:{},geometries:S}};var _i=e=>{let r=Be(e.frontType),o=Be(e.boardType),n=di(e.handleType),a=e.envelope.y2-e.envelope.y1,s=Sn(a),m=xn(a),l=Tn(e.shelfDepth)+r+o,p={...e.envelope,z1:e.envelope.z2-l,x1:e.envelope.x1+3,x2:e.envelope.x2-3,y1:e.envelope.y1+3,y2:e.envelope.y2-3},d=G(p),u=v(p),g={innerWidth:d.x-(6+o)*2,innerHeight:s-o,innerDepth:d.z-r-o,outerWidth:d.x-6*2,outerHeight:m,outerDepth:d.z-r},S={handleSize:n,frontThickness:r,boardThickness:o,compartmentSideOffset:6,compartmentBottomOffset:32,compartmentLowestOffset:32-(m-s),handleDirection:e.flip?1:2};return{boundingBoxSize:d,boundingBoxPosition:u,compartment:g,structure:S}};var Re=class{constructor(r){D(this,"_position");D(this,"_size");D(this,"_sectionId","");D(this,"_origin",{x:0,y:0,z:0});D(this,"_motion",{close:0,ajar:0,open:0});D(this,"_parts",[]);this._position=h(r.boundingBoxPosition,y.SCALAR),this._size=h(r.boundingBoxSize,y.SCALAR)}withSectionId(r){return this._sectionId=r,this}createPart(r,o){this._parts.push({asset:o.asset,groupTag:o.tag,position:r.position,size:r.size,rotation:r.rotation,mirror:r.mirror,material:{name:o.material,colors:o.colors,mapAngle:r.mapAngle,mapRatio:r.mapRatio}})}build(r){return{position:this._position,size:this._size,name:r,segmentTag:{sectionId:this._sectionId,motion:this._motion},geometries:this._parts}}};var yt=class extends Re{constructor(o){super(o);D(this,"_rawDrawerData");this._rawDrawerData=o}withHandle(o){let a={x:this._rawDrawerData.structure.handleSize.width>this._rawDrawerData.boundingBoxSize.x?this._rawDrawerData.boundingBoxSize.x:this._rawDrawerData.structure.handleSize.width,y:this._rawDrawerData.structure.handleSize.height,z:this._rawDrawerData.structure.frontThickness},s={x:this._origin.x-this._rawDrawerData.boundingBoxSize.x/2+a.x/2+this._rawDrawerData.structure.handleSize.offset,y:this._origin.y+this._rawDrawerData.boundingBoxSize.y/2-this._rawDrawerData.structure.handleSize.height,z:this._origin.z+this._rawDrawerData.boundingBoxSize.z/2-a.z/2},m=h(a,y.SCALAR),l=h(s,y.SCALAR);return this.createPart({position:l,size:m,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:m.x,v:m.y,w:m.z}},o),this}withAttachedHandle(o){let n={...this._rawDrawerData.boundingBoxSize,x:this._rawDrawerData.structure.handleSize.width,y:this._rawDrawerData.structure.handleSize.height},a={x:this._origin.x+{1:this._rawDrawerData.boundingBoxSize.x/2-this._rawDrawerData.structure.handleSize.width-this._rawDrawerData.structure.handleSize.offset,2:-this._rawDrawerData.boundingBoxSize.x/2+this._rawDrawerData.structure.handleSize.width+this._rawDrawerData.structure.handleSize.offset}[this._rawDrawerData.structure.handleDirection],y:this._origin.y+this._rawDrawerData.boundingBoxSize.y/2-n.y/2,z:this._origin.z+this._rawDrawerData.boundingBoxSize.z/2-this._rawDrawerData.structure.frontThickness/2},s=h(a,y.SCALAR);return this.createPart({position:s,size:{x:1,y:1,z:1},rotation:null,mirror:null,mapAngle:0,mapRatio:{u:1,v:1,w:1}},o),this}withBackHandle(o){let n={x:this._rawDrawerData.boundingBoxSize.x,y:this._rawDrawerData.structure.handleSize.height+15,z:6},a={x:this._origin.x,y:this._origin.y+this._rawDrawerData.boundingBoxSize.y/2-17.5,z:this._origin.z+this._rawDrawerData.boundingBoxSize.z/2-this._rawDrawerData.structure.frontThickness},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.y,w:s.z}},o),this}withAnodeHandle(o){let n={x:this._origin.x,y:this._origin.y+this._rawDrawerData.boundingBoxSize.y/2-this._rawDrawerData.structure.handleSize.height,z:this._origin.z+this._rawDrawerData.boundingBoxSize.z/2-this._rawDrawerData.structure.frontThickness/2},a=h(n,y.SCALAR);return this.createPart({position:a,size:{x:1,y:1,z:1},rotation:null,mirror:null,mapAngle:0,mapRatio:{u:1,v:1,w:1}},o),this}withFront(o){let n={...this._rawDrawerData.boundingBoxSize,y:this._rawDrawerData.boundingBoxSize.y-this._rawDrawerData.structure.handleSize.height,z:this._rawDrawerData.structure.frontThickness},a={...this._origin,y:this._origin.y-this._rawDrawerData.structure.handleSize.height/2,z:this._origin.z+this._rawDrawerData.boundingBoxSize.z/2-n.z/2},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.y,w:s.z}},o),this}withSides(o){let n={x:this._rawDrawerData.structure.boardThickness,y:this._rawDrawerData.compartment.outerHeight,z:this._rawDrawerData.compartment.outerDepth},a={...this._origin,x:this._origin.x-this._rawDrawerData.boundingBoxSize.x/2+n.x/2+this._rawDrawerData.structure.compartmentSideOffset,y:this._origin.y-this._rawDrawerData.boundingBoxSize.y/2+n.y/2+this._rawDrawerData.structure.compartmentLowestOffset},s={...this._origin,x:this._origin.x+this._rawDrawerData.boundingBoxSize.x/2-n.x/2-this._rawDrawerData.structure.compartmentSideOffset,y:this._origin.y-this._rawDrawerData.boundingBoxSize.y/2+n.y/2+this._rawDrawerData.structure.compartmentLowestOffset},m=h(n,y.SCALAR),l=h(a,y.SCALAR),p=h(s,y.SCALAR);return this.createPart({position:l,size:m,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:m.z,v:m.y,w:m.x}},o),this.createPart({position:p,size:m,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:m.z,v:m.y,w:m.x}},o),this}withBottom(o){let n={x:this._rawDrawerData.compartment.innerWidth,y:this._rawDrawerData.structure.boardThickness,z:this._rawDrawerData.compartment.innerDepth},a={...this._origin,y:this._origin.y-this._rawDrawerData.boundingBoxSize.y/2+n.y/2+this._rawDrawerData.structure.compartmentBottomOffset},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.z,w:s.y}},o),this}withBack(o){let n={x:this._rawDrawerData.compartment.innerWidth,y:this._rawDrawerData.compartment.innerHeight+this._rawDrawerData.structure.boardThickness,z:this._rawDrawerData.structure.boardThickness},a={...this._origin,y:this._origin.y-this._rawDrawerData.boundingBoxSize.y/2+n.y/2+this._rawDrawerData.structure.compartmentBottomOffset,z:this._origin.z-this._rawDrawerData.boundingBoxSize.z/2+n.z},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.y,w:s.z}},o),this}withMotion(o){return this._motion.close=this._position.z+o.close*this._size.z,this._motion.ajar=this._position.z+o.ajar*this._size.z,this._motion.open=this._position.z+o.open*this._size.z,this}};var wn=(e,r,o,n)=>{let a=_i({envelope:e,shelfDepth:n,frontType:"externalVeneer",boardType:"internalPlywood",handleType:"anode",flip:!1});return new yt(a).withSectionId(o.toString()).withFront({tag:"drawerFront",asset:"frontalPanel",material:"veneer",colors:r.drawers.colors}).withBackHandle({tag:"drawerBackHandle",asset:"frontalPanel",material:"veneer",colors:r.drawers.colors}).withAnodeHandle({tag:"drawerHandle",asset:"anodeHandle",material:"anodeMetal",colors:r.fittings.colors}).withSides({tag:"drawerSide",asset:"verticalPanel",material:"laminate",colors:r.drawersStructure.colors}).withBottom({tag:"drawerBottom",asset:"horizontalPanel",material:"laminate",colors:r.drawersStructure.colors}).withBack({tag:"drawerBack",asset:"frontalPanel",material:"laminate",colors:r.drawersStructure.colors}).withMotion({close:0,ajar:.55,open:.95}).build("veneerDrawer")},Nn=(e,r,o,n)=>{let a=_i({envelope:e,shelfDepth:n,frontType:"externalLaminate",boardType:"internalLaminate",handleType:"horizontalFullHandle",flip:!1});return new yt(a).withSectionId(o.toString()).withHandle({tag:"drawerHandle",asset:"fullHandleTop",material:"paintedMetal",colors:r.colors}).withFront({tag:"drawerFront",asset:"frontalPanel",material:"laminate",colors:r.colors}).withSides({tag:"drawerSide",asset:"verticalPanel",material:"laminate",colors:r.colors}).withBottom({tag:"drawerBottom",asset:"horizontalPanel",material:"laminate",colors:r.colors}).withBack({tag:"drawerBack",asset:"frontalPanel",material:"laminate",colors:r.colors}).withMotion({close:0,ajar:.55,open:.95}).build("drawer")},bn=(e,r,o,n)=>{let a=_i({envelope:e,shelfDepth:n,frontType:"externalLaminate",boardType:"internalLaminate",handleType:"slimHandle",flip:e.flip===1});return new yt(a).withSectionId(o.toString()).withAttachedHandle({tag:"drawerHandle",asset:"slimHandle",material:"paintedMetal",colors:r.colors}).withFront({tag:"drawerFront",asset:"frontalPanel",material:"laminate",colors:r.colors}).withSides({tag:"drawerSide",asset:"verticalPanel",material:"laminate",colors:r.colors}).withBottom({tag:"drawerBottom",asset:"horizontalPanel",material:"laminate",colors:r.colors}).withBack({tag:"drawerBack",asset:"frontalPanel",material:"laminate",colors:r.colors}).withMotion({close:0,ajar:.55,open:.95}).build("drawer")};var ht=class extends Re{constructor(o){super(o);D(this,"_rawDoorData");D(this,"_motionOffset",0);this._rawDoorData=o,this._motionOffset={1:-this._size.x/2+20*y.SCALAR,2:this._size.x/2-20*y.SCALAR}[this._rawDoorData.direction]}withFront(o){let n=this._rawDoorData.handleSize.height<1/0?this._rawDoorData.handleSize.height:0,a=this._rawDoorData.verticalFlip,s={...this._rawDoorData.boundingBoxSize,y:this._rawDoorData.boundingBoxSize.y-n,x:this._rawDoorData.boundingBoxSize.x-this._rawDoorData.handleSize.width},m={...this._origin,y:this._origin.y+(a?n/2:-n/2),x:{1:this._origin.x-this._rawDoorData.handleSize.width/2,2:this._origin.x+this._rawDoorData.handleSize.width/2}[this._rawDoorData.direction]},l=h(s,y.SCALAR),p=h(m,y.SCALAR);return this.createPart({position:p,size:l,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:l.x,v:l.y,w:l.z}},o),this}withAnodeHandle(o){let n=this._rawDoorData.verticalFlip,a=n?-(this._rawDoorData.boundingBoxSize.y/2-this._rawDoorData.handleSize.height):this._rawDoorData.boundingBoxSize.y/2-this._rawDoorData.handleSize.height,m={x:{1:this._origin.x+this._rawDoorData.boundingBoxSize.x/2-74,2:this._origin.x-this._rawDoorData.boundingBoxSize.x/2+74}[this._rawDoorData.direction],y:this._origin.y+a,z:this._origin.z},l=h(m,y.SCALAR);return this.createPart({position:l,size:{x:1,y:1,z:1},rotation:n?{axis:"z",angle:Math.PI}:null,mirror:null,mapAngle:0,mapRatio:{u:1,v:1,w:1}},o),this}withBackHandle(o){let a=this._rawDoorData.verticalFlip?-(this._rawDoorData.boundingBoxSize.y/2-17.5):this._rawDoorData.boundingBoxSize.y/2-17.5,s={x:this._rawDoorData.boundingBoxSize.x,y:this._rawDoorData.handleSize.height+15,z:6},m={x:this._origin.x,y:this._origin.y+a,z:this._origin.z-this._rawDoorData.boundingBoxSize.z/2},l=h(s,y.SCALAR),p=h(m,y.SCALAR);return this.createPart({position:p,size:l,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:l.x,v:l.y,w:l.z}},o),this}withHandle(o){let n=this._rawDoorData.handleSize.height>this._rawDoorData.boundingBoxSize.y,a={...this._rawDoorData.boundingBoxSize,x:this._rawDoorData.handleSize.width,y:n?this._rawDoorData.boundingBoxSize.y:this._rawDoorData.handleSize.height},s={x:this._origin.x+{1:this._rawDoorData.boundingBoxSize.x/2-this._rawDoorData.handleSize.width-this._rawDoorData.handleSize.offset,2:-this._rawDoorData.boundingBoxSize.x/2+this._rawDoorData.handleSize.width+this._rawDoorData.handleSize.offset}[this._rawDoorData.direction],y:this._origin.y+this._rawDoorData.boundingBoxSize.y/2-a.y/2,z:this._origin.z+this._rawDoorData.boundingBoxSize.z/2-a.z/2},m=h(a,y.SCALAR),l=h(s,y.SCALAR);return this.createPart({position:l,size:m,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:m.x,v:m.y,w:m.z}},o),this}withAttachedHandle(o){let n={...this._rawDoorData.boundingBoxSize,x:this._rawDoorData.handleSize.width,y:this._rawDoorData.handleSize.height},a=this._rawDoorData.verticalFlip,s={x:this._origin.x+{1:this._rawDoorData.boundingBoxSize.x/2-this._rawDoorData.handleSize.width-this._rawDoorData.handleSize.offset,2:-this._rawDoorData.boundingBoxSize.x/2+this._rawDoorData.handleSize.width+this._rawDoorData.handleSize.offset}[this._rawDoorData.direction],y:this._origin.y+(a?-this._rawDoorData.boundingBoxSize.y/2+n.y/2:this._rawDoorData.boundingBoxSize.y/2-n.y/2),z:this._origin.z+this._rawDoorData.boundingBoxSize.z/2-n.z/2},m=h(s,y.SCALAR);return this._rawDoorData.direction===fe.Left&&this._rawDoorData.parity==="double"||this.createPart({position:m,size:{x:1,y:1,z:1},rotation:a?{axis:"z",angle:Math.PI}:null,mirror:null,mapAngle:0,mapRatio:{u:1,v:1,w:1}},o),this}withHinges(o){return this._rawDoorData.hinges.positions.map(a=>({x:this._origin.x+{1:-this._rawDoorData.boundingBoxSize.x/2+this._rawDoorData.hinges.offset,2:this._rawDoorData.boundingBoxSize.x/2-this._rawDoorData.hinges.offset}[this._rawDoorData.direction],y:this._origin.y+a,z:this._origin.z-this._rawDoorData.boundingBoxSize.z/2})).forEach(a=>{this.createPart({position:h(a,y.SCALAR),size:{x:1,y:1,z:1},rotation:null,mirror:null,mapAngle:0,mapRatio:{u:1,v:1,w:1}},o)}),this}withMotion(o){return this._motion={1:{close:-o.close,ajar:-o.ajar,open:-o.open},2:{close:o.close,ajar:o.ajar,open:o.open}}[this._rawDoorData.direction],this}createPart(o,n){this._parts.push({asset:n.asset,groupTag:n.tag,position:{...o.position,x:o.position.x-this._motionOffset},size:o.size,rotation:o.rotation,mirror:o.mirror,material:{name:n.material,colors:n.colors,mapAngle:0,mapRatio:o.mapRatio}})}build(o){return{position:{...this._position,x:this._position.x+this._motionOffset},size:this._size,name:o,segmentTag:{sectionId:this._sectionId,motion:this._motion},geometries:this._parts}}};var An=(e,r,o)=>{let n=pi({envelope:e,frontType:"externalVeneer",handleType:"anode",handleYPos:e.handleYPos,direction:e.direction,parity:e.parity});return new ht(n).withSectionId(o.toString()).withFront({tag:"doorFront",asset:"frontalPanel",material:"veneer",colors:r.doors.colors}).withBackHandle({tag:"doorBackHandle",asset:"frontalPanel",material:"veneer",colors:r.doors.colors}).withAnodeHandle({tag:"doorHandle",asset:"anodeHandle",material:"anodeMetal",colors:r.fittings.colors}).withHinges({tag:"doorHinge",asset:"hingeWing",material:"chrome",colors:r.doors.colors}).withMotion({close:0,open:85,ajar:66}).build("veneerDoor")},zn=(e,r,o)=>{let n={1:{type:"verticalLeftFullHandle",asset:"fullHandleLeft"},2:{type:"verticalRightFullHandle",asset:"fullHandleRight"}}[e.direction],a=pi({envelope:e,frontType:"externalVeneer",handleType:n.type,handleYPos:e.handleYPos,direction:e.direction,parity:e.parity});return new ht(a).withSectionId(o.toString()).withFront({tag:"doorFront",asset:"frontalPanel",material:"laminate",colors:r.colors}).withHandle({tag:"doorHandle",asset:n.asset,material:"paintedMetal",colors:r.colors}).withHinges({tag:"doorHinge",asset:"hingeWing",material:"chrome",colors:r.colors}).withMotion({close:0,open:85,ajar:66}).build("door")},Mn=(e,r,o)=>{let n=pi({envelope:e,frontType:"externalLaminate",handleType:"slimHandle",handleYPos:e.handleYPos,direction:e.direction,parity:e.parity});return new ht(n).withSectionId(o.toString()).withFront({tag:"doorFront",asset:"frontalPanel",material:"laminate",colors:r.colors}).withAttachedHandle({tag:"doorHandle",asset:"slimHandle",material:"paintedMetal",colors:r.colors}).withHinges({tag:"doorHinge",asset:"hingeWing",material:"chrome",colors:r.colors}).withMotion({close:0,open:85,ajar:66}).build("door")};var yi=class extends Re{constructor(o){super(o);D(this,"_rawHoverData");D(this,"_segmentId","");D(this,"_linkedSegment",null);D(this,"_selectable",!1);D(this,"_range",{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}});this._rawHoverData=o}withSegment(o,n){return this._segmentId=o,this._linkedSegment=n,this}withSelectableArea(o){return this._selectable=o,this}withSizeRange(o){return this._range.min=h(o.min,y.SCALAR),this._range.max=h(o.max,y.SCALAR),this}withBox(o){let n={...this._rawHoverData.boundingBoxSize},a={...this._origin};return this.createPart({position:h(a,y.SCALAR),size:h(n,y.SCALAR),rotation:null,mirror:null,mapRatio:{u:1,v:1,w:1},mapAngle:0},o),this}build(o){return{position:this._position,size:this._size,name:o,segmentTag:{sectionId:this._sectionId,segmentId:this._segmentId,linkedSegment:this._linkedSegment,selectable:this._selectable,motion:this._motion,range:this._range},geometries:this._parts}}};var In=e=>{let r=v(e),o=G(e);return{boundingBoxSize:{...o,z:o.z+10},boundingBoxPosition:r,range:e.range}};var gt=e=>{let r=In(e);return new yi(r).withSectionId(e.m_config_id.toString()).withSegment(e.uuid,null).withSelectableArea(!0).withSizeRange(e.range).withBox({tag:e.type==="row"?"rowHover":"colHover",asset:"boundingBox",material:"hover",colors:{primary:"void",secondary:"void",tertiary:"void"}}).build("hoverBox")};var Yn=e=>{let r=v(e.envelope);return{boundingBoxSize:{...G(e.envelope),x:20,z:20},boundingBoxPosition:r,rotationInYAxis:0,alignment:"frontal"}},sr=e=>{let r=v(e.envelope),o=G(e.envelope),n={e:"frontal",x:"frontal",z:"vertical",b:"vertical",f:"vertical"}[e.subtype];return{boundingBoxSize:o,boundingBoxPosition:r,rotationInYAxis:0,alignment:n}},kn=e=>{let r=v(e.envelope);return{boundingBoxSize:{...G(e.envelope),x:20,z:20},boundingBoxPosition:r,rotationInYAxis:e.rotation_z,alignment:"frontal"}};var nt=class extends Re{constructor(o){super(o);D(this,"_rawBaseData");this._rawBaseData=o}withLegs(o){let n={...this._origin};return this.createPart({position:h(n,y.SCALAR),size:{x:1,y:1,z:1},rotation:null,mirror:null,mapRatio:{u:1,v:1,w:1},mapAngle:0},o),this}withLongLegs(o){let n={...this._origin,y:this._origin.y-this._rawBaseData.boundingBoxSize.y/2};return this.createPart({position:h(n,y.SCALAR),size:{x:1,y:1,z:1},rotation:{axis:"y",angle:this._rawBaseData.rotationInYAxis},mirror:null,mapRatio:{u:1,v:1,w:1},mapAngle:0},o),this}withPlinth(o){let n={...this._rawBaseData.boundingBoxSize},a={...this._origin},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.z,v:s.y,w:s.x}},o),this}};var ut=(e,r,o)=>{let n=Yn({envelope:e,rotation_z:0});return new nt(n).withSectionId(o.toString()).withLegs({tag:"leg",asset:"shortLeg",material:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"}}).build("base")},Cn=(e,r,o)=>{let n=sr({envelope:e,subtype:e.subtype}),a={frontal:"frontalPanel",vertical:"verticalPanel"}[n.alignment];return new nt(n).withSectionId(o.toString()).withPlinth({tag:"plinth",asset:a,material:"laminate",colors:r.colors}).build("base")},Bn=(e,r,o)=>{let n=sr({envelope:e,subtype:e.subtype}),a={frontal:"frontalPanel",vertical:"verticalPanel"}[n.alignment];return new nt(n).withSectionId(o.toString()).withPlinth({tag:"plinth",asset:a,material:"veneer",colors:r.colors}).build("base")},vn=(e,r,o)=>{let n=kn({envelope:e,rotation_z:Math.PI*(180-e.rotation_z)/180});return new nt(n).withSectionId(o.toString()).withLongLegs({tag:"leg",asset:"longLeg",material:"paintedMetal",colors:r.colors}).build("base")};var Ln=e=>{let r=Be(e.boardType),o=v(e.envelope);return{boundingBoxSize:G(e.envelope),boundingBoxPosition:o,panelThickness:r,alignment:"frontal"}},On=(e,r,o,n=!1)=>{let a=e.subtype==="h"?"horizontal":"frontal",s=(N,f)=>N.find(T=>f.every(R=>T[`${R}1`]<=e.envelope[`${R}1`]&&T[`${R}2`]>=e.envelope[`${R}1`])),m=s(r,["x","y"]),l=s(o,["x","z"]),p=Be(e.boardType),d=(m==null?void 0:m.y1)===(m==null?void 0:m.y2)?((m==null?void 0:m.y1)||0)+p/2:(m==null?void 0:m.y2)||0,u=(l==null?void 0:l.z1)===(l==null?void 0:l.z2)?((l==null?void 0:l.z1)||0)+p/2:(l==null?void 0:l.z2)||0,g={...e.envelope,y1:a==="horizontal"?d:e.envelope.y1,y2:a==="horizontal"?d:e.envelope.y2};n&&a==="frontal"&&(g.z1=(l==null?void 0:l.z1)||0,g.z2=(l==null?void 0:l.z2)||0),!n&&a==="frontal"&&(g.z1=u,g.z2=(l==null?void 0:l.z1)===0?u+6:u);let S=v(g);return{boundingBoxSize:G(g),boundingBoxPosition:S,panelThickness:p,alignment:a}};var It=class extends Re{constructor(o){super(o);D(this,"_rawBaseData");this._rawBaseData=o}withCableOpening(o){return this.createPart({position:h(this._origin,y.SCALAR),size:{x:1,y:1,z:1},rotation:this._rawBaseData.alignment==="horizontal"?{axis:"x",angle:-Math.PI/2}:null,mirror:null,mapRatio:{u:1,v:1,w:1},mapAngle:0},o),this}withDeskBeam(o){let n={...this._rawBaseData.boundingBoxSize},a={...this._origin,y:this._origin.y+n.y/2},s={x:n.x*.1,y:1,z:1},m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.z,v:s.y,w:s.x}},o),this}};var se=(e,r,o,n,a,s=!1)=>{let m=On({envelope:{...e},boardType:"structuralLaminate",subtype:e.subtype},r,o,s),l=s&&m.alignment==="frontal"?"grommetWithoutCap":"grommet";return new It(m).withSectionId(a.toString()).withCableOpening({tag:"cableOpening",asset:l,material:n.name,colors:n.colors}).build("feature")},ft=(e,r,o)=>{let n=Ln({envelope:e,boardType:"structuralLaminate",subtype:""});return new It(n).withSectionId(o.toString()).withDeskBeam({tag:"deskBeam",asset:"maskingProfile",material:"paintedMetal",colors:r.colors}).build("feature")};var le=class extends Re{constructor(o){super(o);D(this,"_rawStructureData");this._rawStructureData=o}withHorizontalPanel(o){let n={...this._rawStructureData.boundingBoxSize},a={...this._origin},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.z,w:s.y}},o),this}withVerticalPanel(o){let n={...this._rawStructureData.boundingBoxSize},a={...this._origin},s=h(n,y.SCALAR),m=h(a,y.SCALAR),l=o.material==="veneer"?90:0;return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:l,mapRatio:{u:s.z,v:s.y,w:s.x}},o),this}withFrontalPanel(o){let n={...this._rawStructureData.boundingBoxSize},a={...this._origin},s=h(n,y.SCALAR),m=h(a,y.SCALAR);return this.createPart({position:m,size:s,rotation:null,mirror:null,mapAngle:0,mapRatio:{u:s.x,v:s.y,w:s.z}},o),this}withHinges(o){return this._rawStructureData.hinges.map(a=>({x:this._origin.x+{1:-21,2:21}[a.direction],y:this._origin.y+a.position,z:this._origin.z+this._rawStructureData.boundingBoxSize.z/2-21})).forEach(a=>{this.createPart({position:h(a,y.SCALAR),rotation:{axis:"z",angle:a.x>0?0:Math.PI},mirror:null,size:{x:1,y:1,z:1},mapAngle:0,mapRatio:{u:1,v:1,w:1}},o)}),this}withDeskBeam(o){return this}withCableOpening(o){return this}};var he=e=>{let r=Be(e.boardType),o={horizontal:"y",vertical:"x",frontal:"z"}[e.boardAlignment],n=v(e.envelope),a={...G(e.envelope),[o]:r},s=[];return e.attachedDoor&&e.attachedDoor.onLeft&&s.push({position:a.y/2-He.VERTICAL-3,direction:1},{position:-a.y/2+He.VERTICAL+3,direction:1}),e.attachedDoor&&e.attachedDoor.onRight&&s.push({position:a.y/2-He.VERTICAL-3,direction:2},{position:-a.y/2+He.VERTICAL+3,direction:2}),{boundingBoxSize:a,boundingBoxPosition:n,hinges:s}},hi=(e,r)=>{let o=r.filter(a=>a.x2===e.x1-9&&a.y1>=e.y1&&a.y2<=e.y2&&a.direction===2),n=r.filter(a=>a.x1===e.x2+9&&a.y1>=e.y1&&a.y2<=e.y2&&a.direction===1);return{onLeft:o.length>0,onRight:n.length>0}};var Gn=(e,r,o,n)=>{let a=Math.abs(n.y-e.y2)<30,s=he({envelope:e,boardType:"structuralPlywood",boardAlignment:"horizontal"}),m=`horizontal-${a?"top":"inner"}`;return new le(s).withSectionId(o.toString()).withHorizontalPanel({tag:m,asset:"horizontalPanel",material:"laminatedPlywood",colors:r.colors}).build("horizontal")},Dn=(e,r,o,n)=>{let a=Math.abs(n.y-e.y2)<30,s=he({envelope:e,boardType:"structuralVeneer",boardAlignment:"horizontal"}),m=`horizontal-${a?"top":"inner"}`;return new le(s).withSectionId(o.toString()).withHorizontalPanel({tag:m,asset:"horizontalPanel",material:"veneer",colors:r.colors}).build("horizontal")},Hn=(e,r,o,n)=>{let a=Math.abs(n.y-e.y2)<30,s=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"horizontal"}),m=`horizontal-${a?"top":"inner"}`;return new le(s).withSectionId(o.toString()).withHorizontalPanel({tag:m,asset:"horizontalPanel",material:r.name,colors:r.colors}).build("horizontal")},Wn=(e,r,o,n,a)=>{let s=a.x/2-Math.abs(e.x1)<30||a.x/2-Math.abs(e.x2)<30,m=s&&e.x1<0,l=s&&e.x2>0,p=he({envelope:e,boardType:"structuralPlywood",boardAlignment:"vertical",attachedDoor:hi(e,n)}),d=`vertical-${m?"left":l?"right":"inner"}`;return new le(p).withSectionId(o.toString()).withVerticalPanel({tag:d,asset:"verticalPanel",material:"laminatedPlywood",colors:r.colors}).withHinges({tag:"hinge",asset:"hingeAnchor",material:"chrome",colors:r.colors}).build("vertical")},Fn=(e,r,o,n,a)=>{let s=a.x/2-Math.abs(e.x1)<30||a.x/2-Math.abs(e.x2)<30,m=s&&e.x1<0,l=s&&e.x2>0,p=he({envelope:e,boardType:"structuralVeneer",boardAlignment:"vertical",attachedDoor:hi(e,n)}),d=`vertical-${m?"left":l?"right":"inner"}`;return new le(p).withSectionId(o.toString()).withVerticalPanel({tag:d,asset:"verticalPanel",material:"veneer",colors:r.colors}).withHinges({tag:"hinge",asset:"hingeAnchor",material:"chrome",colors:r.colors}).build("vertical")},Kn=(e,r,o,n,a)=>{let s=a.x/2-Math.abs(e.x1)<30||a.x/2-Math.abs(e.x2)<30,m=s&&e.x1<0,l=s&&e.x2>0,p=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"vertical",attachedDoor:hi(e,n)}),d=`vertical-${m?"left":l?"right":"inner"}`;return new le(p).withSectionId(o.toString()).withVerticalPanel({tag:d,asset:"verticalPanel",material:r.name,colors:r.colors}).withHinges({tag:"hinge",asset:"hingeAnchor",material:"chrome",colors:r.colors}).build("vertical")},Vn=(e,r,o)=>{let n={h:{alignment:"horizontal",asset:"horizontalPanel"},v:{alignment:"vertical",asset:"verticalPanel"},t:{alignment:"horizontal",asset:"horizontalPanel"}}[e.subtype],a=he({envelope:e,boardType:"structuralPlywood",boardAlignment:n.alignment});return new le(a).withSectionId(o.toString()).withHorizontalPanel({tag:"insert",asset:n.asset,material:"laminatedPlywood",colors:r.colors}).build("insert")},Un=(e,r,o)=>{let n={h:{alignment:"horizontal",asset:"horizontalPanel"},v:{alignment:"vertical",asset:"verticalPanel"},t:{alignment:"horizontal",asset:"horizontalPanel"}}[e.subtype],a=he({envelope:e,boardType:"structuralVeneer",boardAlignment:n.alignment});return new le(a).withSectionId(o.toString()).withHorizontalPanel({tag:"insert",asset:n.asset,material:"veneer",colors:r.colors}).build("insert")},qn=(e,r,o)=>{let n={h:{alignment:"horizontal",asset:"horizontalPanel"},v:{alignment:"vertical",asset:"verticalPanel"},t:{alignment:"horizontal",asset:"horizontalPanel"}}[e.subtype],a=he({envelope:e,boardType:"structuralLaminate",boardAlignment:n.alignment});return new le(a).withSectionId(o.toString()).withHorizontalPanel({tag:"insert",asset:n.asset,material:r.name,colors:r.colors}).build("insert")},jn=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"back-inner",asset:"frontalPanel",material:"laminate",colors:r.colors}).build("back")},Xn=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralVeneer",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"back-inner",asset:"frontalPanel",material:"veneer",colors:r.colors}).build("back")},$n=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"back-inner",asset:"frontalPanel",material:"laminate",colors:r.colors}).build("back")},Zn=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"support-inner",asset:"frontalPanel",material:"laminate",colors:r.colors}).build("support")},Qn=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralVeneer",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"support-inner",asset:"frontalPanel",material:"veneer",colors:r.colors}).build("support")},Jn=(e,r,o)=>{let n=he({envelope:e,boardType:"structuralLaminate",boardAlignment:"frontal"});return new le(n).withSectionId(o.toString()).withFrontalPanel({tag:"support-inner",asset:"frontalPanel",material:"laminate",colors:r.colors}).build("support")};var es=e=>{let r=ie(b.Type01,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:[]},{key:"hovers",children:n.map(s=>gt(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors,!0))},{key:"horizontals",children:e.horizontals.map(s=>Gn(s,r.horizontals,1234,o))},{key:"verticals",children:e.verticals.map(s=>Wn(s,r.verticals,1234,e.doors,o))},{key:"slabs",children:[]},{key:"walls",children:[]},{key:"inserts",children:e.inserts.map(s=>Vn(s,r.horizontals,1234))},{key:"backs",children:e.backs.map(s=>jn(s,r.backs,1234))},{key:"supports",children:e.supports.map(s=>Zn(s,r.supports,1234))},{key:"doors",children:e.doors.map(s=>zn(s,r.doors,X(s,n)))},{key:"drawers",children:e.drawers.map(s=>Nn(s,r.drawers,X(s,n),e.depth))},{key:"plinth",children:e.plinth.map(s=>Cn(s,r.plinth,1234))},{key:"legs",children:e.legs.map(s=>ut(s,r.plinth,1234))},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.horizontals,e.backs,r.backs,123))},{key:"deskBeams",children:e.desk_beams.map(s=>ft(s,r.horizontals,1234))}]};var ts=e=>{let r=ie(b.Veneer01,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:[]},{key:"hovers",children:n.map(s=>gt(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors,!0))},{key:"horizontals",children:e.horizontals.map(s=>Dn(s,r.horizontals,1234,o))},{key:"verticals",children:e.verticals.map(s=>Fn(s,r.verticals,1234,e.doors,o))},{key:"slabs",children:[]},{key:"walls",children:[]},{key:"inserts",children:e.inserts.map(s=>Un(s,r.horizontals,1234))},{key:"backs",children:e.backs.map(s=>Xn(s,r.backs,1234))},{key:"supports",children:e.supports.map(s=>Qn(s,r.supports,1234))},{key:"doors",children:e.doors.map(s=>An(s,r,X(s,n)))},{key:"drawers",children:e.drawers.map(s=>wn(s,r,X(s,n),e.depth))},{key:"plinth",children:e.plinth.map(s=>Bn(s,r.plinth,1234))},{key:"legs",children:e.legs.map(s=>ut(s,r.plinth,1234))},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.horizontals,e.backs,r.fittings,123))},{key:"deskBeams",children:e.desk_beams.map(s=>ft(s,r.fittings,1234))}]};var is=e=>{let r=ie(b.Type02,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:[]},{key:"hovers",children:n.map(s=>gt(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors,!0))},{key:"horizontals",children:e.horizontals.map(s=>Hn(s,r.horizontals,1234,o))},{key:"verticals",children:e.verticals.map(s=>Kn(s,r.verticals,1234,e.doors,o))},{key:"slabs",children:[]},{key:"walls",children:[]},{key:"inserts",children:e.inserts.map(s=>qn(s,r.horizontals,1234))},{key:"backs",children:e.backs.map(s=>$n(s,r.backs,1234))},{key:"supports",children:e.supports.map(s=>Jn(s,r.supports,1234))},{key:"doors",children:e.doors.map(s=>Mn(s,r.doors,X(s,n)))},{key:"drawers",children:e.drawers.map(s=>bn(s,r.drawers,X(s,n),e.depth))},{key:"plinth",children:[]},{key:"legs",children:e.legs.map(s=>ut(s,r.legs,1234))},{key:"longLegs",children:e.long_legs.map(s=>vn(s,r.legs,1234))},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.horizontals,e.backs,r.backs,123))},{key:"deskBeams",children:e.desk_beams.map(s=>ft(s,r.horizontals,1234))}]};var rs=(e,r,o,n)=>{let a=(f,T)=>Math.sign(T.y2-f.y2),s=n.find(f=>{let T=f.x1<=e.x1&&f.x2>=e.x2,R=f.y1<=e.y1&&f.y2>=e.y2;return T&&R}),m=r?r.filter(f=>(f.x1===e.x1||e.x2===f.x2)&&f.y2<e.y1).sort(a):[],l=f=>h({x:f.x1+(f.x2-f.x1)*.5+j.LIGHT_BAR.ORIGIN_INSET.x,y:f.y1+j.LIGHT_BAR.ORIGIN_INSET.y,z:f.z2+j.LIGHT_BAR.ORIGIN_INSET.z},y.SCALAR),p=(e.x2-e.x1)*.1,d=m.length>0?(e.y1-m[0].y2)*y.SCALAR:0,u=(e.x2-e.z1)*y.SCALAR,g=l(e),S={asset:"lightBar",groupTag:`column_axis_${g.x}`,position:{x:0,y:0,z:0},size:{x:p,y:1,z:1},rotation:null,mirror:null,material:{name:o.name,colors:o.colors,mapAngle:0,mapRatio:{u:p,v:1,w:1}}},E={asset:"lightDiffuser",groupTag:`column_axis_${g.x}`,position:{x:0,y:0,z:0},size:{x:p,y:1,z:1},rotation:null,mirror:null,material:{name:"ledStrip",colors:{primary:o.colors.secondary,secondary:o.colors.secondary,tertiary:o.colors.secondary},mapAngle:0,mapRatio:null}},N={asset:"fakedLightRays",groupTag:`column_axis_${g.x}`,position:{x:0,y:0,z:0},size:{x:p,y:1,z:1},rotation:null,mirror:null,material:{name:"ledStrip",colors:{primary:o.colors.secondary,secondary:o.colors.secondary,tertiary:o.colors.secondary},mapAngle:0,mapRatio:null}};return{position:g,size:{x:0,y:0,z:0},name:"light",segmentTag:{sectionId:s==null?void 0:s.uuid,colorTag:o.colors.secondary},geometries:[S,E,N]}};var os=e=>{let r=ie(b.Type03,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:rt(e.slabs,n)},{key:"hovers",children:n.map(s=>tr(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>Ce(s,r.backs))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>Ji(s,e.hinges,r.doors,X(s,n)))},{key:"drawers",children:e.drawers.map(s=>er(s,e.drawers,e.doors,r.drawersFront,r.drawersStructure,X(s,n),o.z))},{key:"plinth",children:e.masking_bars.map(s=>Ce(s,r.plinth))},{key:"legs",children:[]},{key:"longLegs",children:[]},{key:"wardrobeBars",children:e.bars.map(s=>ot(s,r.bars))},{key:"lighting",children:e.lighting.map(s=>rs(s,e.slabs,r.lighting,n))},{key:"frame",children:e.frame.map(s=>or(s,r.frame))},{key:"grommets",children:[]},{key:"deskBeams",children:[]}]};var as=e=>{let r=ie(b.Type13,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:rt(e.slabs,n)},{key:"hovers",children:n.map(s=>we(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>Ce(s,r.backs))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>ir(s,e.hinges,r.doors,X(s,n),e.doors))},{key:"drawers",children:e.drawers.map(s=>rr(s,e.drawers,e.doors,r.drawers,X(s,n),o.z))},{key:"plinth",children:e.slabs.filter(s=>s.subtype==="b").map(s=>zt(s,r.plinth))},{key:"legs",children:[]},{key:"longLegs",children:[]},{key:"wardrobeBars",children:e.bars.map(s=>ot(s,r.bars))},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.backs,123))},{key:"deskBeams",children:[]}]};var ns=(e,r,o,n,a,s)=>{let m=o.filter(L=>e.y1>=L.y1&&e.y2<=L.y2),l=m.filter(L=>L.x1===e.x1&&L.direction===fe.Left),p=m.filter(L=>L.x2===e.x2&&L.direction===fe.Right),d=r.filter(L=>L.x1===e.x1||e.x2===L.x2).sort((L,Ni)=>Ni.y1-L.y1),g=(d.findIndex(L=>L.y1===e.y1)+1)/d.length,S=p.length>0,E=l.length>0,N=S&&E,f=e.extremes&&e.extremes.includes("bottom"),T=e.subtype==="e",R=L=>({x:L.x*y.SCALAR,y:L.y*y.SCALAR,z:L.z*y.SCALAR}),C=30,I=8,z=12,O=10+30,x=6,A=28,F=30,V=N?(C+I)*2:S||E?C+I:0,k={x:e.x2-e.x1-V,y:e.y2-e.y1,z:e.z2-e.z1},U=O,Z=f?A:x,Y={...k,y:k.y+U+Z,z},me={...k,z},$={y:k.y-F,x:z,z:k.z-z*2},_e={x:k.x-z*2,y:z,z:k.z-z*2},J={x:0,y:0,z:0},M={x:0,y:(U-Z)/2,z:J.z+(k.z/2-Y.z/2)},ce={x:0,y:0,z:J.z-(k.z/2-me.z/2)},Se={x:J.x-(k.x/2-$.x/2),y:0,z:J.z},ge={x:J.x+(k.x/2-$.x/2),y:0,z:J.z},lt={x:0,y:J.x-(k.y/2-_e.y/2),z:J.z},xt=[{position:R(M),size:R(Y),material:n.drawers,asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:R(ce),size:R(me),material:n.drawersStructure,asset:"frontalPanel",tag:"motion",ratioKey:{u:"x",v:"y",w:"z"}},{position:R(Se),size:R($),material:n.drawersStructure,asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:R(ge),size:R($),material:n.drawersStructure,asset:"verticalPanel",tag:"motion",ratioKey:{u:"z",v:"y",w:"x"}},{position:R(lt),size:R(_e),material:n.drawersStructure,asset:"horizontalPanel",tag:"motion",ratioKey:{u:"x",v:"z",w:"y"}}].map(L=>({asset:L.asset,groupTag:L.tag,position:L.position,size:L.size,rotation:null,mirror:null,material:{name:L.material.name,colors:L.material.colors,mapAngle:0,mapRatio:{u:L.size[L.ratioKey.u],v:L.size[L.ratioKey.v],w:L.size[L.ratioKey.w]}}})),Le=re(e),St=Te(e,Le),Pi=N?0:S?-V/2:E?V/2:0,Oe={...St,x:St.x+Pi*y.SCALAR},wi={close:Oe.z,ajar:Oe.z+.55*g*(.95*Le.z),open:Oe.z+g*(.95*Le.z)};return{position:Oe,size:Le,name:"drawer",segmentTag:{sectionId:a.toString(),motion:wi},geometries:xt}};var ss=(e,r,o,n,a)=>{let s=r.filter(Y=>Y.x1>=e.x1&&Y.x1<=e.x2),m=e.direction===2,l=e.direction===1,p=4,d=55,u=993,g=a.some(Y=>e.x1===Y.x1&&e.x2===Y.x2&&e.y1-Y.y2===p),S=e.y2<u&&e.y1<u,E=e.y1>u&&e.y2>u,N={x:0,y:0,z:0},f=re(e),T=Te(e,f),R=e.x2-e.x1,C=e.y1+(e.y2-e.y1)/2,I=(l?R/2-20:-R/2+20)*y.SCALAR,z={...e,x1:e.x1+p/2,x2:e.x2-p/2},O={...N},x=re(z,16),A={...N,x:m?N.x+f.x/2-20*y.SCALAR:N.x-f.x/2+20*y.SCALAR,z:-x.z/2},F=s.map(Y=>{let me={x:1,y:1,z:1},_e=Y.y1+(Y.y2-Y.y1)/2-C;return{asset:"hingeWing",groupTag:"wing",position:{...A,x:A.x+I,y:_e*y.SCALAR},size:me,rotation:{axis:"z",angle:m?0:Math.PI},mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:x.x,v:x.y,w:x.z}}}}),V=S?O.y+(x.y/2-d*y.SCALAR):E?O.y-(x.y/2-d*y.SCALAR):u*y.SCALAR-T.y,k={x:m?O.x-x.x/2+d*y.SCALAR:O.x+x.x/2-d*y.SCALAR,y:V,z:x.z/2},U=m?{close:0,open:95,ajar:60}:{close:0,open:-95,ajar:-60},Z=m?{close:0,open:90,ajar:70}:{close:0,open:-90,ajar:-70};return{position:{...T,x:T.x-I},size:{x:0,y:0,z:0},name:"door",segmentTag:{sectionId:n.toString(),direction:m?"right":"left",animationOffset:I.toString(),motion:g?U:Z},geometries:[{asset:"frontalPanel",groupTag:"wing",position:{...O,x:O.x+I},size:x,rotation:null,mirror:null,material:{name:o.name,colors:o.colors,mapAngle:0,mapRatio:{u:x.x,v:x.y,w:x.z}}},{asset:"ringHandle",groupTag:"wing",position:{...k,x:k.x+I},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:x.x,v:x.y,w:x.z}}},{asset:"ringHole",groupTag:"wing",position:{...k,x:k.x+I,z:k.z+.001},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:"veneerHole",colors:o.colors,mapAngle:0,mapRatio:{u:1,v:1,w:1}}},...F]}};var ls=e=>{let r=ie(b.Veneer13,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:rt(e.slabs,n)},{key:"hovers",children:n.map(s=>we(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>Ce(s,r.backs))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>ss(s,e.hinges,r.doors,X(s,n),e.doors))},{key:"drawers",children:e.drawers.map(s=>ns(s,e.drawers,e.doors,r,X(s,n),o.z))},{key:"plinth",children:e.slabs.filter(s=>s.subtype==="b").map(s=>zt(s,r.plinth))},{key:"legs",children:[]},{key:"longLegs",children:[]},{key:"wardrobeBars",children:e.bars.map(s=>ot(s,r.bars))},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.fittings,123))},{key:"deskBeams",children:[]}]};var ms;(function(e){e[e.LEFT=1]="LEFT",e[e.RIGHT=2]="RIGHT"})(ms||(ms={}));var Xe=(e,r,o,n,a,s=!1)=>{let m=r.filter($=>$.x1>=e.x1&&$.x1<=e.x2),l=e.direction===2,p=e.direction===1,d=e.extremes.includes("left"),u=e.extremes.includes("right"),g=d&&u,S=!g&&(d||u),E=4,f=18/2-(s?E:0),T=re(e),R=Te(e,T),C=e.x2-e.x1,I=e.y1+(e.y2-e.y1)/2,z=(p?C/2-20:-C/2+20)*y.SCALAR,O={x:0,y:0,z:0},x={...O},A=re({...e,x1:e.x1+E/2,x2:e.x2-E/2},16),F=g?2*f:S?f:0;A.x+=F*y.SCALAR;let V=d&&!u?0-f/2:!d&&u?0+f/2:0;x.x+=V*y.SCALAR;let k={...O,x:l?O.x+T.x/2-20*y.SCALAR:O.x-T.x/2+20*y.SCALAR,z:-A.z/2},U=m.map($=>{let _e={x:1,y:1,z:1},M=$.y1+($.y2-$.y1)/2-I;return{asset:"hingeWing",groupTag:"wing",position:{...k,x:k.x+z,y:M*y.SCALAR},size:_e,rotation:{axis:"z",angle:l?0:Math.PI},mirror:null,material:{name:"chrome",colors:{primary:"white",secondary:"white",tertiary:"white"},mapAngle:0,mapRatio:{u:A.x,v:A.y,w:A.z}}}}),Z=a.some($=>e.x1===$.x1&&e.x2===$.x2&&e.y1-$.y2===E),Y=l?{close:0,open:95,ajar:60}:{close:0,open:-95,ajar:-60},me=l?{close:0,open:90,ajar:70}:{close:0,open:-90,ajar:-70};return{position:{...R,x:R.x-z},size:{x:0,y:0,z:0},name:"door",segmentTag:{sectionId:n.toString(),direction:l?"right":"left",animationOffset:z.toString(),motion:Z?Y:me},geometries:[{asset:"frontalPanel",groupTag:"wing",position:{...x,x:x.x+z},size:A,rotation:null,mirror:null,material:{name:o.name,colors:o.colors,mapAngle:0,mapRatio:{u:A.x,v:A.y,w:A.z}}},...U]}};var ps=2,lc=16,kt=42,mc=21,_s=18,ys=18,lr=12,cs=12,cc=12,dc=ys/2,pc=ys/2-ps*2,gi=e=>({x:e.x*y.SCALAR,y:e.y*y.SCALAR,z:e.z*y.SCALAR}),Fe=e=>{let r=[],o=s=>s.merge.includes("left"),n=s=>s.merge.includes("right"),a=s=>!n(s)&&!o(s);return e.sort((s,m)=>s.x1-m.x1).sort((s,m)=>m.y1-s.y1).forEach(s=>{if(n(s)){let m=e.find(l=>o(l)&&l.x1===s.x2&&l.y1===s.y1&&l.y2===s.y2);m&&r.push({merged:{...s,x2:m.x2},envelopes:[s,m],extremes:[...s.extremes,...m.extremes]})}a(s)&&r.push({merged:{...s},envelopes:[s],extremes:s.extremes})}),r},$e=(e,r,o,n,a=!1)=>{let s=e.extremes.includes("left"),m=e.extremes.includes("right"),l=s&&m,p=!l&&(s||m),d=a?pc:dc,u=l?2*d:p?d:0,g=2*ps-u,S=s&&!m?-d/2:!s&&m?d/2:0,E=uc(e),f=e.envelopes.map((F,V)=>ds(F,o,{origin:E[V],position:{x:0,y:0,z:0},size:{x:0,y:0,z:0}})).flatMap(F=>F.geometries).filter(F=>F.groupTag!=="front"),{position:T,size:R,geometries:C}=ds(e.merged,o,{origin:{x:0,y:0,z:0},position:{x:S,y:0,z:0},size:{x:g,y:0,z:0}}),I=C.filter(F=>F.groupTag==="front"),z=gc(r,e.merged),O={close:T.z,ajar:T.z+.55*z*(.95*R.z),open:T.z+z*(.95*R.z)},x=n[0],A=n[1];return{position:T,size:{x:1,y:1,z:1},name:"drawer",segmentTag:{sectionId:x.toString(),siblingSectionId:A,motion:O},geometries:[...f,...I]}},_c=(e,r,o)=>{let n={x:e.x-o,y:e.y,z:_s},a={x:cs,y:r.y,z:r.z-lr},s={x:r.x,y:r.y,z:lr},m={x:r.x-2*cs,y:cc,z:r.z-lr};return{frontSize:n,sideSize:a,backSize:s,bottomSize:m}},yc=(e,r,o,n,a)=>{let s={x:n.x,y:n.y-e.y/2+kt+o.backSize.y/2,z:n.z-e.z/2+o.backSize.z/2},m={x:n.x-r.x/2+o.sideSize.x/2,y:n.y-e.y/2+kt+o.sideSize.y/2,z:n.z-e.z/2+o.backSize.z+o.sideSize.z/2},l={x:n.x+r.x/2-o.sideSize.x/2,y:n.y-e.y/2+kt+o.sideSize.y/2,z:n.z-e.z/2+o.backSize.z+o.sideSize.z/2},p={x:n.x,y:n.y-e.y/2+kt+o.bottomSize.y/2,z:n.z-e.z/2+o.backSize.z+o.bottomSize.z/2};return{frontCentroid:{x:n.x+a,y:n.y,z:n.z+e.z/2-o.frontSize.z/2},leftSideCentroid:m,rigthSideCentroid:l,bottomCentroid:p,backCentroid:s}},Yt=(e,r,o,n,a)=>({position:gi(e.centroid),size:gi(e.size),material:r,asset:o,tag:n,mapRatio:a}),hc=e=>({asset:e.asset,groupTag:e.tag,position:e.position,size:e.size,rotation:null,mirror:null,material:{name:e.material.name,colors:e.material.colors,mapAngle:0,mapRatio:{u:e.size[e.mapRatio.u],v:e.size[e.mapRatio.v],w:e.size[e.mapRatio.w]}}}),ds=(e,r,o)=>{let n=v(e),a=G(e),s={x:a.x-o.size.x-2*lc,y:a.y-mc-kt,z:a.z-_s},{frontSize:m,sideSize:l,backSize:p,bottomSize:d}=_c(a,s,o.size.x),{frontCentroid:u,leftSideCentroid:g,rigthSideCentroid:S,bottomCentroid:E,backCentroid:N}=yc(a,s,{frontSize:m,sideSize:l,backSize:p,bottomSize:d},o.origin,o.position.x),T=[Yt({centroid:u,size:m},r.drawersFront,"frontalPanel","front",{u:"x",v:"y",w:"z"}),Yt({centroid:g,size:l},r.drawersStructure,"verticalPanel","leftSide",{u:"z",v:"y",w:"x"}),Yt({centroid:S,size:l},r.drawersStructure,"verticalPanel","rightSide",{u:"z",v:"y",w:"x"}),Yt({centroid:N,size:p},r.drawersStructure,"frontalPanel","back",{u:"x",v:"y",w:"z"}),Yt({centroid:E,size:d},r.drawersStructure,"horizontalPanel","bottom",{u:"x",v:"z",w:"y"})].map(hc),R=gi(n),C=gi(a);return{position:R,size:C,geometries:T}},gc=(e,r)=>{let o=Fe(e).filter(s=>s.merged.x1===r.x1||r.x2===s.merged.x2).sort((s,m)=>m.merged.y1-s.merged.y1);return(o.findIndex(s=>s.merged.y1===r.y1)+1)/o.length},uc=e=>{let r=v(e.merged);return e.envelopes.map(o=>{let n=v(o);return{x:n.x-r.x,y:n.y-r.y,z:n.z-r.z}})};var hs=e=>{let r=ie(b.Type23,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:We(e.drawers,e.doors,e.slabs,e.walls,n)},{key:"hovers",children:n.map(s=>we(s,{x1:0,y1:30,z1:0,x2:0,y2:9,z2:-9}))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r,!0))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls,!0))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>at(s,r.backs,e.cable_management))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>Xe(s,e.hinges,r.doors,X(s,n),e.doors))},{key:"drawers",children:Fe(e.drawers).map(s=>$e(s,e.drawers,r,qe(s,n)))},{key:"plinth",children:[]},{key:"legs",children:e.legs.map(s=>ar(s,r.legs))},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.outer,123,!0))},{key:"deskBeams",children:[]}]};var gs=e=>{let r=ie(b.Type24,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:We(e.drawers,e.doors,e.slabs,e.walls,n)},{key:"hovers",children:n.map(s=>we(s,{x1:0,y1:0,z1:0,x2:0,y2:9,z2:-9}))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r,!0))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls,!0))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>at(s,r.backs,e.cable_management))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>Xe(s,e.hinges,r.doors,X(s,n),e.doors))},{key:"drawers",children:Fe(e.drawers).map(s=>$e(s,e.drawers,r,qe(s,n)))},{key:"plinth",children:[]},{key:"legs",children:[]},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.outer,123,!0))},{key:"deskBeams",children:[]}]};var us=e=>{let r=ie(b.Type25,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:We(e.drawers,e.doors,e.slabs,e.walls,n)},{key:"hovers",children:n.map(s=>we(s,{x1:0,y1:106,z1:0,x2:0,y2:9,z2:-9}))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r,!0,!0))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls,!0))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>at(s,r.backs,e.cable_management))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>Xe(s,e.hinges,r.doors,X(s,n),e.doors,!0))},{key:"drawers",children:Fe(e.drawers).map(s=>$e(s,e.drawers,r,qe(s,n),!0))},{key:"plinth",children:[]},{key:"legs",children:e.legs.map(s=>Mt(s,r.legs))},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.outer,123,!0))},{key:"deskBeams",children:[]}]};var fs=e=>{let r=ie(b.Veneer25,e.material),o={x:e.width,y:e.height,z:e.depth},n=te(e);return[{key:"openings",children:We(e.drawers,e.doors,e.slabs,e.walls,n)},{key:"hovers",children:n.map(s=>we(s))},{key:"dimensions",children:e.dimensions.map(s=>ee(s,o,e.doors))},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:e.slabs.map(s=>Ee(s,e.lighting||[],r,!0,!0))},{key:"walls",children:e.walls.map(s=>xe(s,e.hinges,e.drawers,e.doors,r,e.walls))},{key:"inserts",children:[]},{key:"backs",children:e.backs.map(s=>Ce(s,r.backs))},{key:"supports",children:[]},{key:"doors",children:e.doors.map(s=>Xe(s,e.hinges,r.doors,X(s,n),e.doors,!0))},{key:"drawers",children:Fe(e.drawers).map(s=>$e(s,e.drawers,r,qe(s,n),!0))},{key:"plinth",children:[]},{key:"legs",children:e.legs.map(s=>Mt(s,r.legs))},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:e.cable_management.map(s=>se(s,e.slabs,e.backs,r.outer,123))},{key:"deskBeams",children:[]}]};var Es={S01_AR_D07_W02:{legs:[{x:10,y:4,z:-296},{x:10,y:4,z:300}]},S01_CN_D08_W08:{legs:[{x:-386,y:4,z:397},{x:374,y:4,z:397},{x:374,y:4,z:-393},{x:-386,y:4,z:-393}]},S01_CN_D09_W09:{legs:[{x:-433,y:4,z:437},{x:432,y:4,z:437},{x:432,y:4,z:-459},{x:-433,y:4,z:-459}]},S01_ST_D08_W06:{legs:[{x:264,y:4,z:386},{x:264,y:4,z:-374},{x:-262,y:4,z:-374},{x:-262,y:4,z:386}]},S01_ST_D08_W07:{legs:[{x:329,y:4,z:386},{x:329,y:4,z:-374},{x:-329,y:4,z:-374},{x:-329,y:4,z:386}]},S01_ST_D08_W08:{legs:[{x:394,y:4,z:386},{x:394,y:4,z:-374},{x:-392,y:4,z:-374},{x:-392,y:4,z:386}]},S01_ST_D08_W09:{legs:[{x:456,y:4,z:386},{x:456,y:4,z:-374},{x:-454,y:4,z:-374},{x:-454,y:4,z:386}]},S01_ST_D09_W06:{legs:[{x:266,y:4,z:433},{x:266,y:4,z:-432},{x:-260,y:4,z:-432},{x:-260,y:4,z:433}]},S01_ST_D09_W07:{legs:[{x:328,y:4,z:433},{x:328,y:4,z:-432},{x:-328,y:4,z:-432},{x:-328,y:4,z:433}]},S01_ST_D09_W08:{legs:[{x:394,y:4,z:433},{x:394,y:4,z:-432},{x:-392,y:4,z:-432},{x:-392,y:4,z:433}]},S01_ST_D09_W09:{legs:[{x:456,y:4,z:433},{x:456,y:4,z:-432},{x:-454,y:4,z:-432},{x:-454,y:4,z:433}]},S01_CL_D13_W07:{legs:[{x:329,y:4,z:686},{x:329,y:4,z:-703},{x:-329,y:4,z:-703},{x:-329,y:4,z:686}]},S01_CL_D13_W08:{legs:[{x:388,y:4,z:686},{x:388,y:4,z:-703},{x:-389,y:4,z:-703},{x:-389,y:4,z:686}]},S01_CL_D13_W09:{legs:[{x:456,y:4,z:686},{x:456,y:4,z:-703},{x:-453,y:4,z:-703},{x:-453,y:4,z:686}]},S01_FR_D05_W06:{legs:[{x:-265,y:4,z:177},{x:264,y:4,z:-178},{x:-262,y:4,z:-178},{x:-265,y:4,z:-178},{x:264,y:4,z:177},{x:-262,y:4,z:177}]},S01_FR_D05_W07:{legs:[{x:-331,y:4,z:177},{x:328,y:4,z:-178},{x:-328,y:4,z:-178},{x:-331,y:4,z:-178},{x:328,y:4,z:177},{x:-328,y:4,z:177}]},S01_FR_D05_W08:{legs:[{x:-394,y:4,z:177},{x:396,y:4,z:-178},{x:-391,y:4,z:-178},{x:-394,y:4,z:-178},{x:396,y:4,z:177},{x:-391,y:4,z:177}]},S01_FR_D05_W09:{legs:[{x:-457,y:4,z:177},{x:456,y:4,z:-178},{x:-454,y:4,z:-178},{x:-457,y:4,z:-178},{x:456,y:4,z:177},{x:-454,y:4,z:177}]},S01_FR_D06_W06:{legs:[{x:-265,y:4,z:241},{x:264,y:4,z:-240},{x:-262,y:4,z:-240},{x:-265,y:4,z:-240},{x:264,y:4,z:241},{x:-262,y:4,z:241}]},S01_FR_D06_W07:{legs:[{x:-330,y:4,z:241},{x:328,y:4,z:-240},{x:-328,y:4,z:-240},{x:-330,y:4,z:-240},{x:328,y:4,z:241},{x:-328,y:4,z:241}]},S01_FR_D06_W08:{legs:[{x:-392,y:4,z:241},{x:392,y:4,z:-240},{x:-390,y:4,z:-240},{x:-392,y:4,z:-240},{x:392,y:4,z:241},{x:-390,y:4,z:241}]},S01_FR_D06_W09:{legs:[{x:-456,y:4,z:241},{x:455,y:4,z:-240},{x:-453,y:4,z:-240},{x:-456,y:4,z:-240},{x:455,y:4,z:241},{x:-453,y:4,z:241}]},S01_FR_D07_W07:{legs:[{x:-331,y:4,z:305},{x:328,y:4,z:-303},{x:-328,y:4,z:-303},{x:-331,y:4,z:-303},{x:328,y:4,z:305},{x:-328,y:4,z:305}]},S01_FR_D07_W08:{legs:[{x:-398,y:4,z:305},{x:398,y:4,z:-303},{x:-396,y:4,z:-303},{x:-398,y:4,z:-303},{x:398,y:4,z:305},{x:-396,y:4,z:305}]},S01_FR_D07_W09:{legs:[{x:-457,y:4,z:305},{x:458,y:4,z:-303},{x:-454,y:4,z:-303},{x:-457,y:4,z:-303},{x:458,y:4,z:305},{x:-454,y:4,z:305}]},S01_FR_D08_W08:{legs:[{x:-398,y:4,z:369},{x:398,y:4,z:-368},{x:-396,y:4,z:-368},{x:-398,y:4,z:-368},{x:398,y:4,z:369},{x:-396,y:4,z:369}]},S01_FR_D08_W09:{legs:[]},S01_FR_D09_W09:{legs:[{x:-432,y:4,z:-460},{x:432,y:4,z:459},{x:432,y:4,z:-457},{x:432,y:4,z:-460},{x:-432,y:4,z:459},{x:-432,y:4,z:-457}]}};var fc=(e,r,o,n,a)=>({position:h(r,y.SCALAR),rotation:o,size:{x:0,y:0,z:0},name:"hoverBox",segmentTag:{sectionId:e,segmentId:e,selectable:!0,linkedSegment:null,channelId:e,axes:a,range:{min:{x:0,y:0,z:0},max:{x:0,y:0,z:0}}},geometries:n}),Ke=(e,r,o)=>{let n=h(e,y.SCALAR),a=h(r,y.SCALAR);return{asset:"boundingBox",groupTag:"colHover",position:n,size:a,rotation:o,mirror:null,material:{name:"hover",colors:{primary:"void",secondary:"void",tertiary:"void"},mapAngle:0,mapRatio:{u:1,v:1,w:1}}}},Ec=(e,r)=>{let o=r?vt(v(e),v(r)):{x:0,z:0,y:0},n=v(e),a=G(e),s=Ke({...o,y:o.y+n.y-a.y+200},{...a,y:400}),m=Ke({...o,z:o.z+-a.z/2+130,y:o.y+n.y-a.y+400+95},{...a,z:260,y:190}),l=Ke({...o,z:o.z+-a.z/2+80,y:o.y+n.y-a.y+400+195+122.5},{...a,z:160,y:245}),p=Ke({...o,x:o.x+-a.z/2+130,y:o.y+n.y-a.y+400+95},{...a,x:260,y:190}),d=Ke({...o,x:o.x+-a.z/2+80,y:o.y+n.y-a.y+400+195+122.5},{...a,x:160,y:245});return[s,m,l,p,d]},mr=(e,r)=>{let o=r?vt(v(e),v(r)):{x:0,z:0,y:0},n=v(e),a=e.rotation_z!=0?bi(e):G(e),s=Ke({...o,y:o.y+n.y-a.y+200},{...a,y:400}),m=Ke({...o,z:o.z+-a.z/2+130,y:o.y+n.y-a.y+400+95},{...a,z:260,y:190}),l=Ke({...o,z:o.z+-a.z/2+80,y:o.y+n.y-a.y+400+195+122.5},{...a,z:160,y:245});return[s,m,l]},cr=(e,r)=>{let o=r?vt(v(e),v(r)):{x:0,z:0,y:0},n=e.rotation_z===90?bi(e):G(e);return[Ke(o,{...n})]},xc=(e,r)=>e.components.map(n=>{var m;let a=[];if(n.type==="seater"){let l=e.seaters.find(p=>p.m_config_id===n.m_config_id);l&&a.push(...mr(l,n))}if(n.type==="chaise_longue"){let l=e.chaise_longues.find(p=>p.m_config_id===n.m_config_id);l&&a.push(...mr(l,n))}if(n.type==="armrest"){let l=e.armrests.find(p=>p.m_config_id===n.m_config_id);l&&a.push(...cr(l,n))}if(n.type==="footrest"){let l=e.footrests.find(p=>p.m_config_id===n.m_config_id);l&&a.push(...cr(l,n))}if(n.type==="corner"){let l=e.corners.find(p=>p.m_config_id===n.m_config_id);l&&a.push(...Ec(l,n))}if(n.type==="split"){let l=e.seaters.find(d=>d.m_config_id===n.m_config_id),p=e.footrests.find(d=>d.m_config_id===n.m_config_id);l&&a.push(...mr(l,n)),p&&a.push(...cr(p,n))}let s=[];return r.backAxis.includes(n.m_config_id)&&s.push("center"),r.leftAxis.includes(n.m_config_id)&&s.push("left"),r.rightAxis.includes(n.m_config_id)&&s.push("right"),fc(((m=n.m_config_id)==null?void 0:m.toString())||"-1",v(n),n.rotation_z!=0?{axis:"y",angle:-n.rotation_z*(Math.PI/180)}:null,a,s)}),Sc=(e,r)=>{let o=(d,u,g)=>{let E=Qi(d[{SEAT:"material",BACKREST:"material_backrest",HEADREST:"material_cushion"}[u]||"material"]),N=g==="corner"&&d.fabric_direction==="L",f=g==="footrest"&&d.fabric_direction==="R",C=["S01_FR_D06_W06","S01_FR_D07_W07","S01_FR_D08_W08","S01_FR_D09_W09"].includes(d.id)&&f;return C&&console.warn(`Incorect fabric direction: ${d.fabric_direction} for module: ${d.id}`),{asset:(N||f)&&!C?`${d.id}_N_${u}`:`${d.id}_${u}`,groupTag:u,position:{x:0,y:0,z:0},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:E.name,colors:E.colors,mapAngle:0,mapRatio:{u:1,v:1,w:1}}}},n=(d,u,g)=>{var C,I;let S=v(d),E=G(d),N=h(S,y.SCALAR),f=(C=Es[d.id])==null?void 0:C.legs,T=[];if(f){let z=f.map((O,x)=>{let A=h(O,y.SCALAR);return{asset:"S01_LEG",groupTag:`LEG_${x}`,position:{x:A.x,y:0,z:A.z},size:{x:1,y:1,z:1},rotation:null,mirror:null,material:{name:"paintedMetal",colors:{primary:"black",secondary:"black",tertiary:"black"},mapAngle:0,mapRatio:{u:1,v:1,w:1}}}});T.push(...z)}let R=u.map(z=>o(d,z,g));return T.push(...R),{position:{...N,y:0},size:{x:1,y:1,z:1},rotation:{axis:"y",angle:-d.rotation_z*(Math.PI/180)},name:d.id,segmentTag:{sectionId:((I=d.m_config_id)==null?void 0:I.toString())||"-1",size:h(E,y.SCALAR),detached:r.detached.includes(d.m_config_id)},geometries:T}},a=e.armrests.map(d=>n(d,["SEAT"],"armrest")),s=e.chaise_longues.map(d=>n(d,["SEAT","BACKREST","HEADREST"],"chaise_longue")),m=e.corners.map(d=>n(d,["SEAT","BACKREST","HEADREST"],"corner")),l=e.footrests.map(d=>n(d,["SEAT"],"footrest")),p=e.seaters.map(d=>n(d,["SEAT","BACKREST","HEADREST"],"seater"));return[a,s,m,l,p].flat()},xs=e=>{let r=Tt(e);return[{key:"openings",children:[]},{key:"hovers",children:xc(e,r)},{key:"dimensions",children:[]},{key:"horizontals",children:[]},{key:"verticals",children:[]},{key:"slabs",children:[]},{key:"walls",children:[]},{key:"inserts",children:[]},{key:"backs",children:[]},{key:"supports",children:[]},{key:"doors",children:[]},{key:"drawers",children:[]},{key:"plinth",children:[]},{key:"legs",children:[]},{key:"longLegs",children:[]},{key:"wardrobeBars",children:[]},{key:"lighting",children:[]},{key:"frame",children:[]},{key:"grommets",children:[]},{key:"deskBeams",children:[]},{key:"sofaParts",children:Sc(e,r)}]};var be=.003,ui=.014,dr=.05,Ie=e=>{let r=pr(e.value),o=_r(),n=yr(e.value,st.WIDTH,e.visibility,e.context),a=e.showLine?Tc(e.position,e.value/100):[];return hr([r,o,...a],n,e.position)},Et=e=>{let r=pr(e.value),o=_r("stormGrayFill"),n=yr(e.value,st.HEIGHT,e.visibility,e.context),a=e.showLine?Rc(e.position,e.value/100):[];return hr([r,o,...a],n,e.position)},Ue=e=>{let r={axis:"y",angle:-Math.PI/2},o=pr(e.value,r),n=_r("graphiteFill",r),a=yr(e.value,st.DEPTH,e.visibility,e.context),s=e.showLine?Pc(e.position,e.value/100):[];return hr([o,n,...s],a,e.position)},Tc=(e,r)=>{let o={x:r/-2,y:0,z:ui},n={x:be,y:dr,z:be},a=Ve(o,n),s={x:r/2,y:0,z:ui},m=Ve(s,n),d=Ve({x:0,y:0,z:.014},{x:r,y:be,z:be});return[a,d,m]},Rc=(e,r)=>{let o={x:0,y:r/-2,z:ui},n={x:dr,y:be,z:be},a=Ve(o,n),s={x:0,y:r/2,z:ui},m=Ve(s,n),d=Ve({x:0,y:0,z:.014},{x:be,y:r,z:be});return[a,d,m]},Pc=(e,r)=>{let o={x:-.014,y:0,z:r/-2},n={x:be,y:dr,z:be},a=Ve(o,n),s={x:-.014,y:0,z:r/2},m=Ve(s,n),d=Ve({x:-.014,y:0,z:0},{x:be,y:be,z:r});return[a,d,m]},Ve=(e,r)=>({asset:"line",groupTag:"exterior",position:e,size:r,rotation:null,mirror:null,material:{name:"basic",colors:{primary:"grayLine",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null}}),pr=(e,r=null)=>{let o=r?{x:-.017,y:0,z:0}:{x:0,y:0,z:.017};return{asset:"text",groupTag:e.toString(),material:{colors:{primary:"neutral",secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null,name:"stroke"},mirror:null,position:o,rotation:r,size:{x:1,y:1,z:1}}},_r=(e="graphiteFill",r=null)=>({asset:"pill",groupTag:"exterior",material:{colors:{primary:e,secondary:"neutral",tertiary:"neutral"},mapAngle:0,mapRatio:null,name:"fill"},mirror:null,position:r?{x:-.017,y:0,z:0}:{x:0,y:0,z:.017},rotation:r,size:{x:1,y:1,z:1}}),yr=(e,r=st.WIDTH,o=de.ALWAYS,n=pe.EXTERIOR)=>({value:e,unit:"cm",context:n,measurement:r,visibility:o}),hr=(e,r,o)=>({geometries:e,name:"dimensions",position:o,rotation:null,segmentTag:r,size:{x:0,y:0,z:0}});var gr=1050,Ze=950,ur=400,fi=200,Ss=25,Ye=e=>{var r,o,n,a;return{position:h(e.position,y.SCALAR),value:Math.ceil(e.value/10),visibility:(r=e.visibility)!=null?r:de.ALWAYS,context:(o=e.context)!=null?o:pe.SILHOUETTE,showLine:(n=e.showLine)!=null?n:!0,flip:(a=e.flip)!=null?a:!1}},Ts=(e,r)=>{let o=[];if(r.dimensions.backAxis>0){let n=r.dimensions.backAxis,a={x:0,y:gr,z:-ur};o.push(Ie(Ye({position:a,value:n})))}if(r.dimensions.leftAxis>0){let n=r.dimensions.leftAxis,a={x:-(e.width/2)-ur,y:gr,z:r.dimensions.leftAxis/2};o.push(Ue(Ye({position:a,value:n,flip:!0})))}if(r.dimensions.rightAxis>0){let n=r.dimensions.rightAxis,a={x:e.width/2+ur,y:gr,z:r.dimensions.rightAxis/2};o.push(Ue(Ye({position:a,value:n})))}return o},Rs=e=>{let r=[];return e.components.forEach(o=>{switch(o.type){case"corner":let n=bc(e,o);r.push(...n);break;case"footrest":let a=Nc(e,o);r.push(...a);break;default:let s=wc(e,o);r.push(...s);break}}),r},wc=(e,r)=>{let o=[];if(r.rotation_z===0){let n={x:(r.x1+r.x2)/2,y:Ze,z:-fi},a={x:(r.x1+r.x2)/2,y:Ze,z:(r.z1+r.z2)/2};o.push(Ie(Ye({position:n,value:r.width}))),o.push(Ue(Ye({position:a,value:r.depth})))}if(r.rotation_z===90||r.rotation_z===-90){let n=r.rotation_z===90?1:-1,a={x:(e.width/2+fi)*n,y:Ze,z:(r.z1+r.z2)/2},s={x:(r.x1+r.x2)/2,y:Ze,z:(r.z1+r.z2)/2};o.push(Ue(Ye({position:a,value:r.width}))),o.push(Ie(Ye({position:s,value:r.depth})))}return o},Nc=(e,r)=>{let o=[],n={x:(r.x1+r.x2)/2,y:Ze,z:r.z1-Ss},a={x:r.x2+Ss,y:Ze,z:(r.z1+r.z2)/2};return o.push(Ie(Ye({position:n,value:r.width}))),o.push(Ue(Ye({position:a,value:r.depth}))),o},bc=(e,r)=>{let o=[],n=r.rotation_z===0?-1:1,a={x:(r.x1+r.x2)/2,y:Ze,z:-fi},s={x:(e.width/2+fi)*n,y:Ze,z:(r.z1+r.z2)/2};return o.push(Ie(Ye({position:a,value:r.width}))),o.push(Ue(Ye({position:s,value:r.depth}))),o};var fr=.06,Er=.05,Ps=e=>{var s,m;let r=[],o=((s=e.schema.filter(l=>l.key==="openings")[0])==null?void 0:s.children)||[],n=((m=e.schema.filter(l=>l.key==="drawers")[0])==null?void 0:m.children)||[];return Ac(o,n).forEach(l=>{l.geometries.filter(d=>!d.groupTag.includes("top")).forEach(d=>{let u={x:d.position.x+l.position.x,y:d.position.y+l.position.y-d.size.y/2+Er,z:e.size.z},g={x:d.position.x+l.position.x-d.size.x/2+fr,y:d.position.y+l.position.y,z:e.size.z},S=Math.floor(d.size.x*100),E=Math.floor(d.size.y*100),N={position:u,value:S,visibility:de.WHEN_OPEN,context:pe.INTERIOR,showLine:!1},f={position:g,value:E,visibility:de.WHEN_OPEN,context:pe.INTERIOR,showLine:!1},T=Ie(N),R=Et(f);r.push(T,R)})}),r},Ac=(e,r)=>{if(e.length===0)return[];if(r.length===0)return e;let o=[];return e.forEach(n=>{let a=n.geometries.find(l=>l.asset==="boundingBox"&&!l.groupTag.includes("top"));if(!a)return;let s={x1:n.position.x+a.position.x-a.size.x/2,x2:n.position.x+a.position.x+a.size.x/2,y1:n.position.y+a.position.y-a.size.y/2,y2:n.position.y+a.position.y+a.size.y/2};r.some(l=>{let p=l.geometries.find(u=>u.asset==="frontalPanel"&&u.groupTag==="front");if(!p)return!1;let d={x1:l.position.x+p.position.x-p.size.x/2,x2:l.position.x+p.position.x+p.size.x/2,y1:l.position.y+p.position.y-p.size.y/2,y2:l.position.y+p.position.y+p.size.y/2};return zc(s,d)})||o.push(n)}),o},zc=(e,r)=>{let o=e.x1<r.x2&&e.x2>r.x1,n=e.y1<r.y2&&e.y2>r.y1;return o&&n},ws=e=>{var a;let r=[],o=((a=e.schema.filter(s=>s.key==="drawers")[0])==null?void 0:a.children)||[],n=new Map;return o.forEach(s=>{let m=s.segmentTag.sectionId;n.has(m)||n.set(m,[]),n.get(m).push(s)}),n.forEach(s=>{s.sort((m,l)=>m.position.y-l.position.y),s.forEach((m,l)=>{m.geometries.filter(d=>d.asset==="frontalPanel"&&d.groupTag==="front").forEach(d=>{if(l===0){let N={x:d.position.x+m.position.x,y:d.position.y+m.position.y-d.size.y/2+Er,z:d.position.z+m.position.z},f=Math.floor(d.size.x*100),T={position:N,value:f,visibility:de.ALWAYS,context:pe.EXTERIOR,showLine:!1},R=Ie(T);r.push(R)}let u={x:d.position.x+m.position.x-d.size.x/2+fr,y:d.position.y+m.position.y,z:d.position.z+m.position.z},g=Math.floor(d.size.y*100),S={position:u,value:g,visibility:de.ALWAYS,context:pe.EXTERIOR,showLine:!1},E=Et(S);r.push(E)})})}),r},Ns=e=>{var n;let r=[];return(((n=e.schema.filter(a=>a.key==="doors")[0])==null?void 0:n.children)||[]).forEach(a=>{a.geometries.filter(m=>m.asset==="frontalPanel").forEach(m=>{let l={x:m.position.x+a.position.x,y:m.position.y+a.position.y-m.size.y/2+Er,z:m.position.z+a.position.z},p={x:m.position.x+a.position.x-m.size.x/2+fr,y:m.position.y+a.position.y,z:m.position.z+a.position.z},d=Math.floor(m.size.x*100),u=Math.floor(m.size.y*100),g={position:l,value:d,visibility:de.WHEN_CLOSED,context:pe.EXTERIOR,showLine:!1},S={position:p,value:u,visibility:de.WHEN_CLOSED,context:pe.EXTERIOR,showLine:!1},E=Ie(g),N=Et(S);r.push(E,N)})}),r},bs=e=>{let r=[],o=e.width/1e3,n=e.height/1e3,a=e.depth/1e3,m={position:{x:0,y:n+.09,z:a},value:Math.ceil(o*100),visibility:de.ALWAYS,context:pe.EXTERIOR,showLine:!0};r.push(Ie(m));let p={position:{x:o/2+.1,y:n/2,z:a},value:Math.ceil(n*100),visibility:de.ALWAYS,context:pe.EXTERIOR,showLine:!0};r.push(Et(p));let u={position:{x:o/-2-.02,y:.15,z:a/2},value:Math.ceil(a*100),visibility:de.ALWAYS,context:pe.EXTERIOR,showLine:!0};return r.push(Ue(u)),r},As=e=>{let r=e.size.x,o=175,a={position:{x:r/2*-1-1,y:o/2*.01-e.position.y,z:.025},value:o,visibility:de.ALWAYS,context:pe.SILHOUETTE,showLine:!0};return Et(a)};var de;(function(e){e.ALWAYS="always",e.WHEN_OPEN="whenOpen",e.WHEN_CLOSED="whenClosed"})(de||(de={}));var pe;(function(e){e.EXTERIOR="exterior",e.INTERIOR="interior",e.SILHOUETTE="silhouette"})(pe||(pe={}));var st;(function(e){e.WIDTH="width",e.HEIGHT="height",e.DEPTH="depth"})(st||(st={}));var zs=(e,r)=>r.map((n,a)=>{var m;let s=((m=n.schema.find(l=>l.key==="dimensions"))==null?void 0:m.children)||[];switch(n.meta.shelfType){case"TYPE_25":return xr(e[a],n);case"TYPE_24":return xr(e[a],n);case"TYPE_23":return xr(e[a],n);case"TYPE_10":return Mc(e[a],n);default:return s}})||[[]],xr=(e,r)=>{let o=[],n=Ps(r),a=ws(r),s=Ns(r),m=bs(e),l=As(r);return o.push(...n,...a,...s,...m,l),o},Mc=(e,r)=>{let o=dt(e,w.Sofa),n=[],a=Ts(e,o),s=Rs(e);return n.push(...a,...s),n};var Is=e=>e*(Math.PI/180),Ic=e=>e*(180/Math.PI),Yc=(e,r,o)=>{let n=[],a=[e.x-r.x/2-o.left,e.x+r.x/2+o.right,e.y-r.y/2-o.bottom,e.y+r.y/2+o.top,e.z-r.z/2-o.back,e.z+r.z/2+o.front];return n.push({x:a[0],y:a[2],z:a[4]}),n.push({x:a[1],y:a[3],z:a[5]}),n.push({x:a[0],y:a[2],z:a[5]}),n.push({x:a[0],y:a[3],z:a[4]}),n.push({x:a[0],y:a[3],z:a[5]}),n.push({x:a[1],y:a[2],z:a[4]}),n.push({x:a[1],y:a[2],z:a[5]}),n.push({x:a[1],y:a[3],z:a[4]}),n},kc=(e,r,o,n,a)=>({x:e.x+r.x*n.x+(o.right-o.left)*.5+a.x,y:e.y+r.y*n.y+(o.bottom-o.top)*.5+a.y,z:e.z+r.z*n.z+(o.back-o.front)*.5+a.z}),Cc=(e,r)=>{let o=(d,u)=>e*(1-d/u),n=r.x/r.y,a=Bc(e,n),s=a.horizontal,m=a.horizontal,l=a.vertical,p=a.vertical;return{left:s,right:m,top:l,bottom:p}},Bc=(e,r)=>({horizontal:Ic(2*Math.atan(Math.tan(Is(e)/2)*r)),vertical:e}),vc=(e,r)=>{let o={left:[],right:[],top:[],bottom:[],front:[],back:[]};return r.forEach(({x:n,y:a,z:s})=>{let m=Math.abs(n-e.x),l=Math.abs(a-e.y),p=Math.abs(s-e.z);n<e.x?o.left.push(m):o.right.push(m),a<e.y?o.bottom.push(l):o.top.push(l),s>e.z?o.front.push(p):o.back.push(p)}),o},Lc=(e,r)=>{let o=g=>Math.max(...g.filter(isFinite)),n=(g,S)=>o(g)/Math.tan(Is(S)/2),a=Math.min(r.left,r.right,r.top,r.bottom),s=n([...e.left,...e.front,...e.back],a),m=n([...e.right,...e.front,...e.back],a),l=n([...e.top,...e.back,...e.left,...e.right],a),p=n([...e.bottom,...e.front,...e.left,...e.right],a),d=o([...e.front,...e.top]);return Math.max(s,m,l,p)+d},Ms=(e,r,o,n,a="")=>{let s={x:o*Math.sin(e)*Math.sin(r),y:o*Math.cos(e),z:o*Math.sin(e)*Math.cos(r)};return{...{x:n.x+s.x,y:n.y+s.y,z:n.z+s.z},name:a}},Ei=(e,r,o,n,a,s=1)=>{let m=kc(r,e,n.viewBoxOffset,n.targetAlignment,n.targetOffset),l=Yc(m,e,n.viewBoxOffset),p=Cc(n.fieldOfView,o),d=vc(m,l),u=Lc(d,p)*s,g={origin:{x:0,y:0,z:0},target:m,fov:n.fieldOfView,resolution:o,aspect:o.x/o.y},S=a.snapOrbitAngles.map(E=>Ms(E.polar*(Math.PI/180),E.azimuth*(Math.PI/180),u,m,E.name));return{...g,origin:Ms(n.orbitAngles.polar*(Math.PI/180),n.orbitAngles.azimuth*(Math.PI/180),u,m,"origin"),snapActive:a.snapActive,snapPoints:S,polarOrbitRange:a.polarOrbitRange,azimuthOrbitRange:a.azimuthOrbitRange,animationActive:a.animationActive,animationDuration:a.animationDuration,easingFunction:a.easingFunction}};var ve={left:1,right:1,top:.2,bottom:.2,front:0,back:0},xi={right:0,left:0,top:0,bottom:0},Oc=15,Gc={x:1e3,y:1e3},Tr={left:"left",right:"right",center:"center"},Dc=e=>({min:{x:-e.x/2,y:0,z:0},max:{x:e.x/2,y:e.y,z:e.z}}),Hc=e=>{let r=[],{min:o,max:n}=Dc(e),a=[o.x-ve.left,n.x+ve.right,o.y-ve.bottom,n.y+ve.top,o.z-ve.back,n.z+ve.front];return r.push({x:a[0],y:a[2],z:a[4]}),r.push({x:a[1],y:a[3],z:a[5]}),r.push({x:a[0],y:a[2],z:a[5]}),r.push({x:a[0],y:a[3],z:a[4]}),r.push({x:a[0],y:a[3],z:a[5]}),r.push({x:a[1],y:a[2],z:a[4]}),r.push({x:a[1],y:a[2],z:a[5]}),r.push({x:a[1],y:a[3],z:a[4]}),r},Wc=e=>({x:(ve.right-ve.left)*.5,y:.5*e.y+.055+(ve.bottom-ve.top)*.5,z:0}),Fc=(e,r)=>{let o=(p,d)=>e*(1-p/d),n=r.x/r.y,a=o(xi.left,r.x)*n,s=o(xi.right,r.x)*n,m=o(xi.top,r.y),l=o(xi.bottom,r.y);return{left:a,right:s,top:m,bottom:l}},Kc=(e,r)=>{let o={left:[],right:[],top:[],bottom:[],forward:[]};return r.forEach(({x:n,y:a,z:s})=>{let m=Math.abs(n-e.x),l=Math.abs(a-e.y),p=Math.abs(s-e.z);n<e.x?o.left.push(m):o.right.push(m),a<e.y?o.bottom.push(l):o.top.push(l),s>e.z&&o.forward.push(p)}),o},Vc=(e,r)=>{let o=d=>Math.max(...d.filter(isFinite)),n=(d,u)=>o(d)/(2*Math.atan(Math.PI*u/360)),a=n(e.left,r.left),s=n(e.right,r.right),m=n(e.top,r.top),l=n(e.bottom,r.bottom);return o(e.forward)+2*Math.max(a,s,m,l)},Sr=(e,r,o,n)=>{let a={x:o*Math.sin(e)*Math.sin(r),y:o*Math.cos(e),z:o*Math.sin(e)*Math.cos(r)};return{x:n.x+a.x,y:n.y+a.y,z:n.z+a.z}},Ct=(e,r=Gc,o=Oc)=>{let n={right:{theta:.52,phi:1.35},left:{theta:-.52,phi:1.35},center:{theta:0,phi:1.35}},a=Wc(e),s=Hc(e),m=Fc(o,r),l=Kc(a,s),p=Vc(l,m),d={origin:{x:0,y:0,z:0},target:a,fov:o,resolution:r,aspect:r.x/r.y},u={...d,origin:Sr(n.right.phi,n.right.theta,p,a)},g={...d,origin:Sr(n.left.phi,n.left.theta,p,a)},S={...d,origin:Sr(n.center.phi,n.center.theta,p,a)};return{left:g,right:u,center:S}};var Uc=[{id:"default",filters:{shelfCategories:["all"],shelfTypes:["all"],shelfMaterials:["all"]},fieldOfView:30,screenSize:{x:1e3,y:1e3},clippingPlanes:{near:.1,far:1e3},orbitAngles:{azimuth:-30,polar:69},viewBoxOffset:{front:.15,back:.15,left:.15,right:.15,top:.15,bottom:.15},targetOffset:{x:0,y:0,z:0},targetAlignment:{x:0,y:0,z:0}}],qc=[{id:"default",filters:{shelfCategories:["all"],shelfTypes:["all"],shelfMaterials:["all"]},polarOrbitRange:{min:0,max:90},azimuthOrbitRange:{min:-120,max:120},zoomRange:{min:.1,max:1},snapActive:!0,snapOrbitAngles:[{name:"fa-left",azimuth:-80,polar:69},{name:"left",azimuth:-30,polar:69},{name:"center",azimuth:0,polar:69},{name:"top",azimuth:0,polar:5},{name:"right",azimuth:30,polar:69},{name:"far-right",azimuth:80,polar:69}],animationActive:!0,animationDuration:.5,easingFunction:"power2.in"}];var Ys=()=>{let e=Uc.find(r=>r.id==="default");if(!e)throw new Error("Default camera view configuration not found");return e},ks=()=>{let e=qc.find(r=>r.id==="default");if(!e)throw new Error("Default camera orbit configuration not found");return e};var Rr=class{constructor(r){D(this,"compatibility");D(this,"plugins");D(this,"_driver");this.compatibility=["camera-setup-plugin","camera-gui-plugin"],this.plugins=new Map,this._driver=r}isCompatibleWith(r){return this.compatibility.includes(r)}registerPlugin(r){this.isCompatibleWith(r.name)&&(this.plugins.set(r.name,r),r.onRegister(this))}getPlugin(r){return this.plugins.get(r)}getQueryParameters(){return this._driver.getShelfParameters()}setViewConfig(r){this._driver.setViewConfig(r)}setOrbitConfig(r){this._driver.setOrbitConfig(r)}onUpdateCalls(){this.plugins.forEach(r=>{r.onUpdate()})}onInitCalls(){this.plugins.forEach(r=>{r.onInit()})}},Si=class{constructor(){D(this,"_orbitConfig");D(this,"_viewConfig");D(this,"_resolution");D(this,"_viewBox");D(this,"_target");D(this,"_shelfParameters");D(this,"_pluginHost");this._orbitConfig=ks(),this._viewConfig=Ys(),this._resolution={x:1e3,y:1e3},this._shelfParameters={shelfCategory:"all",shelfType:"all",shelfMaterial:"all"},this._pluginHost=new Rr(this)}registerPlugin(r){return this._pluginHost.isCompatibleWith(r.name)&&this._pluginHost.registerPlugin(r),this}initialize(){return this._pluginHost.onInitCalls(),this}setShelfParameters(r){return this._shelfParameters=r,this}setResolution(r){return this._resolution=r,this}setViewConfig(r){return this._viewConfig=r,this}setOrbitConfig(r){return this._orbitConfig=r,this}getShelfParameters(){return this._shelfParameters}calculateFor(r,o={x:0,y:0,z:0}){return this._pluginHost.onUpdateCalls(),this._viewBox=r,this._target=o,Ei(this._viewBox,this._target,this._resolution,this._viewConfig,this._orbitConfig)}recalculate(){return this._pluginHost.onUpdateCalls(),Ei(this._viewBox,this._target,this._resolution,this._viewConfig,this._orbitConfig)}recalculateForScreenshot(r){this._pluginHost.onUpdateCalls();let o=Ei(this._viewBox,this._target,r,this._viewConfig,this._orbitConfig,1.5);return o.snapActive=!1,o.animationActive=!1,o.resolution=r,o}getStartCameraParametersForShelf(r){let o=h({x:r.width,y:r.height,z:r.depth},y.SCALAR);return Ct(o)[Tr.left]}getCameraParametersForShelfAndConfig(r,o){let n=h({x:r.width,y:r.height,z:r.depth},y.SCALAR);return Ct(n,o.resolution)[o.snapKey]}getStartCameraParameters(r,o=Tr.left){return Ct(r)[o]}getCameraParametersForConfig(r,o,n){return Ct(r,o)[n]}};var Bt=(e=[],r)=>{let o=e[0];if(!o)return{environmentPreset:"cubeIBLWithGrayBG",lightPreset:"defaultLighting",children:[]};if(r===w.Sofa)return{environmentPreset:"hdrIBLWithGrayBG",lightPreset:"sofaLighting",children:[]};let n=ct(o),a=ae(o.shelf_type),s=e.filter(p=>a==="TYPE_02"&&p.material===15).length>0,m={position:{x:0,y:0,z:0},size:{x:1,y:1,z:1},name:"studio-background",segmentTag:{castShadow:!1,receiveShadow:!0,visible:!s},linkedSegments:[],geometries:[{asset:"background",groupTag:"studio",position:{x:0,y:0,z:0},size:h(n,y.SCALAR),rotation:null,mirror:null,material:null}]},l={position:{x:-(h(n,y.SCALAR).x/2+.66),y:0,z:.03},size:{x:1,y:1,z:1},name:"studio-shadowman",segmentTag:{castShadow:!1,receiveShadow:!1,visible:!0},linkedSegments:[],geometries:[{asset:"shadowMan",groupTag:"studio",position:{x:0,y:0,z:0},size:{x:.01,y:.01,z:.01},rotation:null,mirror:null,material:null}]};return{environmentPreset:s?"cubeIBLWithPinkBG":"cubeIBLWithGrayBG",lightPreset:jc(a,o.material),children:[m,l]}},jc=(e,r)=>e==="TYPE_01"?Xc(r):e==="VENEER_01"?$c(r):e==="TYPE_02"?Zc(r):"defaultLighting",Xc=e=>[i.TYPE_01.WHITE,i.TYPE_01.DUSTY_PINK].includes(e)?"originalLowIntenseLighting":"originalMediumIntenseLighting",$c=e=>[i.VENEER_01.ASH].includes(e)?"originalLowIntenseLighting":"originalMediumIntenseLighting",Zc=e=>{let r=[i.TYPE_02.MIDNIGHT_BLUE,i.TYPE_02.MATTE_BLACK,i.TYPE_02.BURGUNDY];return[i.TYPE_02.WHITE,i.TYPE_02.SKYBLUE,i.TYPE_02.SAND,i.TYPE_02.COTTON,i.TYPE_02.MUSTARD_YELLOW,i.TYPE_02.SAGE_GREEN,i.TYPE_02.REISINGER_PINK].includes(e)?"originalLowIntenseLighting":r.includes(e)?"originalVeryIntenseLighting":"originalMediumIntenseLighting"},Cs=(e,r)=>{let o=-(e/2+1e3),n=1750;return{properties:{context:"silhouette",measurement:"height",visibility:"always"},labelPoint:{x:o,y:n/2,z:60},range:{start:{x:o,y:0,z:0},end:{x:o,y:n,z:r}},value:175,valueUnit:"cm"}};var Bs=e=>{if(e.components.length===0||e.components.some(f=>!("add_module_options"in f)))return{detachTargets:[],detachCoordiinates:[]};let r=Tt(e),o=150,n=f=>e.components.find(T=>T.m_config_id===f),a=r.leftAxis.map(n).filter(Boolean),s=r.backAxis.map(n).filter(Boolean),m=r.rightAxis.map(n).filter(Boolean),l=[],p=(f,T,R)=>R==="horizontal"?T.add_module_options.left>=0&&f.add_module_options.right>=0&&T.add_module_options.left===f.add_module_options.right:T.add_module_options.up>=0&&f.add_module_options.down>=0&&T.add_module_options.up===f.add_module_options.down,d=0;for(let f=0;f<s.length;f++){let T=s[f],R=s[f-1];R?(p(R,T,"horizontal")&&(d+=o),l.push({transition:h({x:d,y:0,z:0},y.SCALAR),id:T.m_config_id})):l.push({transition:h({x:0,y:0,z:0},y.SCALAR),id:T.m_config_id})}let u=0;for(let f=0;f<a.length;f++){let T=a[f],R=a[f-1];R?(p(R,T,"vertical")&&(u+=o),l.push({transition:h({x:0,y:0,z:u},y.SCALAR),id:T.m_config_id})):l.push({transition:h({x:0,y:0,z:0},y.SCALAR),id:T.m_config_id})}let g=0;for(let f=0;f<m.length;f++){let T=m[f],R=m[f-1];R?(p(R,T,"vertical")&&(g+=o),l.push({transition:h({x:d,y:0,z:g},y.SCALAR),id:T.m_config_id})):l.push({transition:h({x:0,y:0,z:0},y.SCALAR),id:T.m_config_id})}let S=450,E=[],N=f=>{let T=h(v(f),y.SCALAR),R=h(G(f),y.SCALAR),C=l.find(V=>V.id===f.m_config_id),I={x:T.x+C.transition.x,y:0,z:T.z+C.transition.z},z=V=>E.some(k=>k.id===V);f.add_module_options.left>=0&&!z(f.add_module_options.left)&&E.push({position:{...I,z:f.z1+S*y.SCALAR,x:I.x-R.x/2-o*.5*y.SCALAR},id:f.add_module_options.left}),f.add_module_options.right>=0&&!z(f.add_module_options.right)&&E.push({position:{...I,z:f.z1+S*y.SCALAR,x:I.x+R.x/2+o*.5*y.SCALAR},id:f.add_module_options.right}),f.add_module_options.up>=0&&!z(f.add_module_options.up)&&E.push({position:{...I,z:I.z-R.z/2-o*.5*y.SCALAR},id:f.add_module_options.up}),f.add_module_options.down>=0&&!z(f.add_module_options.down)&&E.push({position:{...I,z:I.z+R.z/2+o*.5*y.SCALAR},id:f.add_module_options.down})};return[].concat(a,s,m).forEach(N),{detachTargets:l,detachCoordiinates:E}};var vs=(e,r)=>{if(r===w.Sofa){let o=Bs(e);return{buttons3D:{active:!0,coordinates:o.detachCoordiinates},detachSofaParts:{active:!0,transitions:o.detachTargets}}}return{buttons3D:{active:!1,coordinates:[]},detachSofaParts:{active:!1,transitions:[]}}};var vS=it,Ls=new Si,LS=(e=[],r)=>{let o=Je(e[0],r.category),n=it.configurator.defaultConfiguration;return{position:{x:0,y:0,z:0},size:{x:0,y:0,z:0},name:r.name||"space",shelves:e.map(a=>Ri(a,o)),dimensions:[],items:e.map(a=>Ti(a,o)),interior:{...n},studio:Bt(e,o)}},OS=(e=[],r)=>{let o=Je(e[0],r.category),n=it.studio.adaptToShelves(Gs(e[0],o),it.studio.defaultConfiguration);return{position:{x:0,y:0,z:0},size:{x:0,y:0,z:0},name:r.name||"space",shelves:e.map(a=>Ri(a,o)),dimensions:[],items:e.map(a=>Ti(a,o)),interior:{...n},studio:Bt(e,o)}},GS=(e=[],r)=>{let o=Je(e[0],r.category),n=it.recommendations.getInteriorRecommendationForShelf(e[0],o,H.LIFESTYLE);return{position:{x:0,y:0,z:0},size:{x:0,y:0,z:0},name:r.name||"space",shelves:e.map(a=>Ri(a,o)),dimensions:[],items:e.map(a=>Ti(a,o)),interior:{...n},studio:Bt(e,o)}},DS=(e=[],r)=>{let o=Je(e[0],r.category),n="materials"in e[0]?e[0].materials[0]:e[0].material;Ls.setShelfParameters({shelfCategory:o,shelfType:ae(e[0].shelf_type),shelfMaterial:n});let a=Jc(e[0],o),s=it.configurator.defaultConfiguration,m=e.map(l=>Ri(l,o));return{position:{x:0,y:0,z:0},size:{x:0,y:0,z:0},name:r.name||"space",shelves:m,dimensions:zs(e,m),items:e.map(l=>Ti(l,o)),interior:r.interiorSetup?{...r.interiorSetup}:{...s},studio:Bt(e,o),camera:a}},Ti=(e,r)=>{let o=ae(e.shelf_type)==="TYPE_10"||ae(e.shelf_type)==="TYPE_03"||ae(e.shelf_type)==="TYPE_13"||ae(e.shelf_type)==="VENEER_13",n=ae(e.shelf_type)==="TYPE_23"||ae(e.shelf_type)==="TYPE_24"||ae(e.shelf_type)==="TYPE_25"||ae(e.shelf_type)==="VENEER_25";e.category=r;let a=Lt(e),s=o?[]:Go(e,n);return{children:[{position:Os(e,a),size:{x:1,y:1,z:1},name:"items",segmentTag:{sectionId:1},linkedSegments:[],geometries:s}]}},Ri=(e,r)=>{e.dimensions==null&&(e.dimensions=[]);let o=ae(e.shelf_type),n=Lt(e),a=r===w.Sofa?zr(e,{x:1,y:1,z:1}):ct(e),s=Cs(e.width,e.depth);return e.dimensions.push(s),{position:Os(e,n),size:h(a,y.SCALAR),rotation:null,name:"shelf",meta:{...Gs(e,r)},category:r,schema:Qc[o](e),interaction:vs(e,r)}},Qc={TYPE_01:es,VENEER_01:ts,TYPE_02:is,TYPE_03:os,TYPE_13:as,VENEER_13:ls,TYPE_23:hs,TYPE_24:gs,TYPE_25:us,VENEER_25:fs,TYPE_10:xs},Jc=(e,r)=>{if(r!==w.Sofa)return null;let o=Ot(e),n=Mr(o);return Ls.calculateFor(h(n.size,y.SCALAR),h(n.position,y.SCALAR))},Os=(e,r)=>{let o=ae(e.shelf_type),n=e.configurator_params.plinth,a=["TYPE_03","TYPE_13","VENEER_13"];return o==="TYPE_24"?h(j.BASE.ORIGIN.HANGING_AT,y.SCALAR):r==="col"&&!n||r==="row"||a.includes(o)?h(j.BASE.ORIGIN.SHORT_LEG_CORRECTION,y.SCALAR):h(j.BASE.ORIGIN.NO_CORRECTION,y.SCALAR)},Gs=(e,r)=>{let o=ae(e.shelf_type),n=ci(e),a=En(o,n),s=dt(e,r);return{mainMaterial:a==null?void 0:a.name,mainColor:a==null?void 0:a.colors.primary,shelfCategory:r,shelfType:o,layout:s}};export{Ls as CameraDriverApi,vS as InteriorApi,DS as buildSpaceRenderFormat,LS as buildSpaceRenderFormatWithConfiguratorInterior,GS as buildSpaceRenderFormatWithInterior,OS as buildSpaceRenderFormatWithStudioInterior};
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
