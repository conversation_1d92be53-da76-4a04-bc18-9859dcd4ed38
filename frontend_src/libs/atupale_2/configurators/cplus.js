import AtupaleCore from '../core';

class AtupaleCplus extends AtupaleCore {
  constructor() {
    super('configurator');
  }

  setupDataLayer() {
    this.dataLayer = {
      eventParam_0: -1,
      eventParam_1: -1,
      eventParam_2: -1,
      eventParam_3: -1,
      eventParam_4: -1,
      eventParam_5: -1,
      eventParam_6: -1,
      eventParam_7: undefined,
      eventParam_8: undefined,
      eventParam_9: -1,
      eventParam_10: -1,
      eventParam_11: -1,
      eventParam_12: -1,
      eventParam_13: -1,
      eventParam_14: undefined,
      eventParam_15: undefined,
      eventParam_16: -1,
      eventParam_17: -1,
      eventParam_18: -1,
      eventParam_19: -1,
      eventParam_20: -1,
      eventParam_21: -1,
      eventParam_22: -1,
      eventParam_23: -1,
      eventParam_24: -1,
    };
  }

  hitGlobalObjectData({ configuratorState, paramName, payload }) {
    this.configuratorState = configuratorState;
    this.payload = Object.entries(payload);

    if (this.payload.length === 0) {
      // init
      if (this.DEBUG) {
        console.log('init');
      }
      this.dataLayer.eventParam_0 = this.cstmItem.id;
      this.dataLayer.eventParam_5 = this.getBoardId();
      this.dispatchData({ event: 'conf_DnaStart', ...this.dataLayer });
    } else if (this.payload.length === 1) {
      if (
        this.payload[0][0] === 'thumbnail'
                || this.payload[0][0] === 'localEdge'
                || this.payload[0][0] === 'cables'
                || this.payload[0][0] === 'doors_direction'
                || this.payload[0][0] === 'inserts'
      ) {
        // komponent lokalny
        if (this.DEBUG) {
          console.log('local');
        }
        this.hitComponentData(this.payload[0][0]);
        return;
      }
      // komponent globalny
      if (this.DEBUG) {
        console.log('global');
      }
      this.dataLayer.eventParam_1 = -1;
      this.dataLayer.eventParam_4 = -1;
      this.buildDataLayer(this.payload[0][0]);
    } else if (paramName) {
      this.dataLayer.eventParam_1 = -1;
      this.dataLayer.eventParam_4 = -1;
      this.buildDataLayer(paramName);
    }
  }

  hitComponentData(configName) {
    if (!configName) return;
    const activeComponent = this.configuratorState.FPM.components[this.configuratorState.activeComponent];
    this.dataLayer.eventParam_1 = activeComponent.component_id;
    this.dataLayer.eventParam_4 = this.configuratorState.activeComponentIndex ? `1${this.configuratorState.activeComponentIndex + 1}` : -1;
    this.buildDataLayer(configName);
  }

  buildDataLayer(configName) {
    const features = this.buildFeatures();
    this.dataLayer.eventParam_2 = eventAction[configName].name;
    this.dataLayer.eventParam_3 = eventAction[configName].value(this.configuratorState, this);
    // eventParam_5 -  -1
    // eventParam_6 -  -1
    this.dataLayer.eventParam_9 = JSON.stringify(this.configuratorState.FPM.renderGeom.components.map(({ width }) => width));
    this.dataLayer.eventParam_10 = JSON.stringify(this.configuratorState.FPM.renderGeom.components.map(({ component_id }) => component_id));
    // BACK PANEL
    this.dataLayer.eventParam_11 = JSON.stringify(this.configuratorState.FPM.renderGeom.components.map(() => 1));
    this.dataLayer.eventParam_12 = JSON.stringify(this.configuratorState.FPM.renderGeom.components.map(
      ({ bi_info: { doors, doubleDoors } }) => (doors ? (doors === 'left' ? 1 : 2) : (doubleDoors ? 3 : 0)),
    ));
    this.dataLayer.eventParam_13 = JSON.stringify(this.configuratorState.FPM.renderGeom.components.map(({ bi_info: { cables } }) => (cables ? 1 : 0)));
    this.dataLayer.eventParam_16 = JSON.stringify(features).replace(/\\"/g, '"');
    this.dataLayer.eventParam_17 = this.configuratorState.configuration.material;
    this.dataLayer.eventParam_18 = this.configuratorState.dnaDictionary[this.configuratorState.configuration.furnitureStyle].pattern || -1;
    this.dataLayer.eventParam_19 = this.configuratorState.commonPrices.price.priceGrossEur;
    this.dataLayer.eventParam_20 = this.configuratorState.FPM.geom.width;
    const baseHeight = ['plinth', 'longLegs'].includes(this.configuratorState.configuration.feet) ? 108 : 8;
    this.dataLayer.eventParam_21 = JSON.stringify([
      this.configuratorState.FPM.geom.height - 22 - baseHeight, // TODO Verify if this -22 is really needed.
      this.configuratorState.FPM.geom.height - 22,
      baseHeight,
    ]);
    this.dataLayer.eventParam_22 = this.configuratorState.FPM.preset.density;
    this.dataLayer.eventParam_23 = this.configuratorState.FPM.geom.depth;
    this.dataLayer.eventParam_24 = this.configuratorState.FPM.renderGeom.components.length;
    this.dispatchData({ event: 'confEvent', ...this.dataLayer });
  }

  buildFeatures() {
    const isSideboard = this.configuratorState.FPM.preset.additional_parameters.collection_type === 'Sideboard';
    const feetOption = this.configuratorState.ui.configuratorParams.feet
      ? this.configuratorState.ui.configuratorParams.feet.options[1].value
      : null;
    const cablesToggleAvailable = isSideboard;
    const plinthAvailable = isSideboard && feetOption === 'plinth';
    const longLegsAvailable = isSideboard && feetOption === 'longLegs';

    return {
      backs: 'on',
      plinth: plinthAvailable ? 'on' : 'off',
      long_legs: longLegsAvailable ? 'on' : 'off',
      cables: cablesToggleAvailable ? 'on' : 'off',
    };
  }

  async addToCart({ data, ecommerceHitType }) {

  }

  adjustSlider(payload) {
    this.dispatchData({
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventCategory: 'KP',
      eventAction: 'conf_width_adjust',
      eventLabel: payload,
      eventValue: undefined,
      eventNonInteraction: false,
    });
  }

  addToWishlist() {
    this.dispatchData({ event: 'add_to_wishlist' });
  }
}
const eventAction = {
  width: {
    name: 'conf_Width',
    value: ({ FPM: { preset: { width } } }) => width,
  },
  height: {
    name: 'conf_Height',
    value: ({ FPM: { preset: { height } } }) => height,
  },
  furnitureStyle: {
    name: 'conf_dnaChange',
    value: ({ dnaDictionary, configuration: { furnitureStyle } }) => dnaDictionary[furnitureStyle].pattern,
  },
  depth: {
    name: 'conf_Depth',
    value: ({ FPM: { preset: { depth } } }) => depth,
  },
  backPanels: {
    name: 'conf_BackPanels',
    value: ({ FPM: { preset: { backpanels } } }) => backpanels,
  },
  columnCount: {
    name: 'conf_Density',
    value: ({ FPM: { preset: { density } } }) => density,
  },
  legroomArea: {
    name: 'conf_LegroomArea',
    value: ({ FPM: { preset: { legroomArea } } }) => legroomArea,
  },
  feet: {
    name: 'conf_Footing',
    value: () => -1,
  },
  material: {
    name: 'conf_Color',
    value: ({ configuration: { material } }) => material,
  },
  thumbnail: {
    name: 'conf_ChangeComponent',
    value: ({ activeComponent, FPM: { components } }) => components[activeComponent].component_id,
  },
  doors_direction: {
    name: 'conf_DoorFlip',
    value: (state, { payload }) => ({ 1: 'left', 2: 'right' }[payload[0][1]]),
  },
  drawer_flip: {
    name: 'conf_DrawerFlip',
    value: (state, { payload }) => ({ 1: 'right', 2: 'left' }[payload[0][1]]),
  },
  cables: {
    name: 'conf_CableOpening',
    value: (state, { payload }) => (payload[0][1] ? 1 : 0),
  },
  inserts: {
    name: 'conf_Inserts',
    value: (state, { payload }) => (payload[0][1] ? 1 : 0),
  },
  cables_system: {
    name: 'conf_DeskCableSystem',
    value: (state, { payload }) => (payload > 0 ? (payload === 1 ? 'left' : 'right') : 'off'),
  },
  local_edge: {
    name: 'conf_LocalEdge',
    value: ({ activeComponent, FPM: { components } }, { payload }) => {
      const line = Object.keys(payload[0][1])[0];
      const { line_left, line_right } = components[activeComponent];
      if (line === line_left) {
        return 'left';
      } if (line === line_right) {
        return 'right';
      }
    },
  },
  prev: {
    name: 'conf_HistoryPrev',
    value: () => -1,
  },
  next: {
    name: 'conf_HistoryNext',
    value: () => -1,
  },
  reset: {
    name: 'conf_HistoryReset',
    value: () => -1,
  },
  distortion: {
    name: 'conf_Distortion',
    value: ({ FPM: { preset: { distortion } } }) => distortion,
  },
};
export default AtupaleCplus;
