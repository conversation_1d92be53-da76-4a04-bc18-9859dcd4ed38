const atupale_2 = function(configurator_type, cstmItem) {
  let fn;
  switch (configurator_type) {
  case 'cplus':
    fn = require('./configurators/cplus.js');
    break;
  case 'crow':
    fn = require('./configurators/crow.js');
    break;
  case 'cstep':
    fn = require('./configurators/crow.js');
    break;
  case 'cwatwar':
    fn = require('./configurators/cwatwar.js');
    break;
  case 'cwatcol':
    fn = require('./configurators/cwatcol.js');
    break;
  case 'notify':
    fn = require('./modals/notify.js');
    break;
  case 'exitMobileSection':
    fn = require('./modules/exitMobileSection.js');
    break;
  }
  new fn.default(cstmItem);
};
window.atupale_2 = atupale_2;
if (window.PubSub) {
  window.PubSub.publish('getAtupale_2', atupale_2);
}
