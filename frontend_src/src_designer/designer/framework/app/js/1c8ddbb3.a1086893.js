(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["1c8ddbb3"],{"320b":function(e,t,i){"use strict";var n=i("6215");var r=i.n(n);var a=r.a},3687:function(e,t,i){},"48af":function(e,t,i){"use strict";var n=i("f423");var r=i.n(n);var a=r.a},"5dca":function(e,t,i){i("d2c4")("search",1,function(e,t,i){return[function i(n){"use strict";var r=e(this);var a=n==undefined?undefined:n[t];return a!==undefined?a.call(n,r):new RegExp(n)[t](String(r))},i]})},"608f":function(e,t,i){},6215:function(e,t,i){},6250:function(e,t,i){"use strict";var n=i("608f");var r=i.n(n);var a=r.a},"680c":function(e,t){function i(e){var t;if(typeof Symbol==="function"){if(Symbol.asyncIterator){t=e[Symbol.asyncIterator];if(t!=null)return t.call(e)}if(Symbol.iterator){t=e[Symbol.iterator];if(t!=null)return t.call(e)}}throw new TypeError("Object is not async iterable")}e.exports=i},"7c0f":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this;var t=e.$createElement;var i=e._self._c||t;return i("section",{staticClass:"main"},[i("t-relations",{ref:"editor"})],1)};var r=[];var a=i("b263");var s=function(){var e=this;var t=e.$createElement;var i=e._self._c||t;return i("section",{staticClass:"relations-editor-container"},[i("div",{staticClass:"flex"},[i("div",{staticClass:"row"},[i("div",{staticClass:"col",staticStyle:{width:"calc(100vw)",overflow:"hidden",position:"relative"}},[i("div",{staticClass:"_"},[e._m(0)],1)])]),i("div",{staticClass:"row"},[i("div",{staticClass:"col",staticStyle:{width:"calc(100vw - 420px - 40px)"}},[i("div",{staticClass:"row firstRowRelations"},[e.queryResult?i("div",{ref:"seriesNames",staticClass:"col seriesNames"},e._l(e.currentComponentSeries,function(t){return i("div",{staticClass:"collection-name",class:{selectedCollectioName:t.id==e.$route.params.selectedSerie}},[i("div",{staticClass:"name"},[i("router-link",{staticClass:"relLink",attrs:{to:"/relations/"+e.$route.params.selectedCollection+"/"+e.$route.params.selectedTable+"/"+t.id}},[e._v(e._s(t.name))])],1),i("div",{staticClass:"select"},[i("q-checkbox",{attrs:{color:"pink",dark:"dark",dense:"dense",disabled:e.$refs.tableEditor.syncing,label:"Use",val:t.id},on:{input:e.updateTable},model:{value:e.choosenSeriesForTable,callback:function(t){e.choosenSeriesForTable=t},expression:"choosenSeriesForTable"}})],1)])}),0):e._e()]),i("div",{staticClass:"flex row secondRowRelatcions"},[i("div",{staticClass:"col heights"},e._l(e.heights,function(t){return i("div",{staticClass:"series-height"},[e._v(e._s({300:"B",400:"C",500:"D",600:"E",700:"F",800:"G",900:"H",1000:"I"}[t])+" "+e._s(t))])}),0),i("div",{ref:"container",staticClass:"col relations-editor-container-canvas"},[i("div",{ref:"renderer",staticClass:"renderer-relations"})])])]),i("div",{staticClass:"col",staticStyle:{"max-width":"420px"}},[i("q-scroll-area",{staticClass:"scrollRel"},[i("t-card",{attrs:{name:"Editor settings"}},[i("div",{staticClass:"card mid"},[i("div",{staticClass:"_"},[i("div",{staticClass:"card-main"},[i("div",{staticClass:"card-content"},[i("t-presets",{attrs:{options:[{label:"Replace",value:"replace"},{label:"Normal",value:"normal"}],"target-model":e.dropMode,big:"big","section-label":"Drop mode"}}),i("t-presets",{attrs:{options:[{label:"Replace",value:"replace"},{label:"Clone",value:"clone"}],"target-model":e.editMode,big:"big","section-label":"Swap mode"}})],1)])]),i("div",{staticClass:"_"},[i("div",{staticClass:"card-main"},[i("t-section",{attrs:{name:"Extra settings"}},[i("q-list",[i("q-item",[i("q-select",{attrs:{"inverted-light":"inverted-light",color:"grey-7",separator:"separator",options:e.miniops},on:{input:e.selectMiniMode},model:{value:e.mini,callback:function(t){e.mini=t},expression:"mini"}})],1),i("q-item",[i("q-btn",{attrs:{loading:e.batchRunning==2,label:"Render all miniatures",percentage:e.miniaturesDone},nativeOn:{click:function(t){return e.prepareMiniaturesAll(t)}}})],1),i("q-item",[i("q-btn",{attrs:{loading:e.batchRunning==1,label:"Render miniatures for collection",percentage:e.miniaturesDone},nativeOn:{click:function(t){return e.prepareMiniaturesCollection(t)}}})],1)],1)],1)],1)])])]),i("t-table",{ref:"tableEditor",attrs:{series:e.queryResult,tablesList:e.tablesList,tableConfigurations:e.tableConfigurations,colletionID:e.projectFilter,selectedSeries:e.choosenSeriesForTable},on:{loadTables:e.loadTables,loaded:e.dimSeries}}),i("t-series",{attrs:{series:e.currentComponentSeries,colletionID:e.projectFilter},on:{loadTables:e.loadTables,render:e.rerrender}})],1)],1)])])])};var o=[function(){var e=this;var t=e.$createElement;var i=e._self._c||t;return i("t-bar",{ref:"bar",attrs:{menu:"true",box:{width:120,height:90,cols:2,rows:2,gutter:8},actAsNavigation:false,typesToDisplay:"componentRelations"},on:{searchMatch:e.lightup}})}];var l=i("9523");var c=i.n(l);var u=i("359c");var d=i("7341");var h=i("fb2b");var p=i("7621");var f=i("1a9d");var v=i("9af0");var m=i("a34a");var g=i.n(m);var b=i("192a");var w=i("c973");var x=i.n(w);var y=i("3156");var C=i.n(y);var S=i("680c");var T=i.n(S);var k=i("9ec3");var I=i.n(k);var A=i("18a5");var D=i("3b5a");var N=i("9b8e");var R=i("970b");var $=i.n(R);var q=i("5bc3");var E=i.n(q);var M=i("912c");var O=i("b082");var H=i("c098");var L=i.n(H);var F=i("0fe3");var z=i.n(F);var j=1e3;var B={ORIENTATION_VERTICAL:"vertical",ORIENTATION_HORIZONTAL:"horizontal"};var P=true;var W=function(){function e(){var t=this;$()(this,e);this.checked=false;this.bgDrawn=false;this.items=[];this.labels=[];this.sprites=[];this.orderHittest=[];this.getSortArray=[];this.series=[];this.currentState=null;this.cache=[];this.delta={x:0,y:0};this.editMode="normal";this.selectedSeriesNo=[];this.fetching=false;this.throttledDraw=I.a.throttle(this.__draw,1);var i=this.heights=[300,400,500,600,700,800,900,1e3];window.addEventListener("resize",function(e){t.onResize()})}E()(e,[{key:"onResize",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(!this.pixiApp||!this.vue)return;var i=this.getSize(),n=i.width,r=i.height;this.currentWidth=n;this.pixiApp.renderer.resize(n,r);this.pixiApp.view.style["max-width"]="".concat(n,"px");this.backgroundContainer.clear();this.bgDrawn=false;if(t)this.vue.draw()}},{key:"setContext",value:function e(t){var i=this;this.vue=t;this.initializePixi();this.vue.$refs.renderer.appendChild(this.pixiApp.view);this.delta={x:0,y:0};this.vue.$refs.renderer.addEventListener("mousewheel",function(e){var t=L()(e);i.delta.x+=t.pixelY;if(i.delta.x>0)i.delta.x=0;if(Math.abs(i.delta.x)>G.pixiHitests.width-i.currentWidth)i.delta.x=-(G.pixiHitests.width-i.currentWidth);if(G.pixiHitests.width<i.currentWidth)i.delta.x=0;i.throttledDraw()});this.selected={id:this.vue.$route.params.id};this.vue.draw();this.onResize()}},{key:"getXY",value:function e(){var t=this.vue.$el.getBoundingClientRect();return{x:t.top,y:t.left}}},{key:"getSize",value:function e(){var t=this.vue.$el.getBoundingClientRect();return{width:t.width-390,height:t.height};switch(this.orientation){case Editor.ORIENTATION_VERTICAL:break;case Editor.ORIENTATION_HORIZONTAL:break;default:break}}},{key:"initializePixi",value:function e(){if(this.pixiApp)return;var t=this.getSize(),i=t.width,n=t.height;this.pixiApp=new M["Application"](i,n,{backgroundColor:0,resolution:1});var r=this.pixiApp;this.cache=[];this.pixiRoot=new M["Container"];this.pixiHitests=new M["Container"];this.annotationsOverlay=new M["Container"];this.drawingContainer=new M["Graphics"];this.backgroundContainer=new M["Graphics"];this.pixiRoot.addChild(this.backgroundContainer);this.pixiRoot.addChild(this.drawingContainer);this.pixiApp.stage.addChild(this.pixiRoot);this.pixiApp.stage.addChild(this.pixiHitests);this.pixiApp.stage.addChild(this.annotationsOverlay);this.vue.$refs.renderer.appendChild(r.view);this.pixiApp.view.style["max-width"]="".concat(i,"px");this.pixiApp.stop();this.seriesToDim=[];this.vue.draw()}},{key:"traceDropCoordinates",value:function e(t){var i=t.clientX,n=t.clientY;var r=this.vue.$refs.renderer.firstChild.getBoundingClientRect(),a=r.top,s=r.left;i-=s+this.delta.x;n-=a;var o,l;this.orderHittest.map(function(e,t){e.map(function(e,t){if(i>=e.y&&i<=e.y+e.h){o=t;l=Math.ceil(n/e.h)-1}})});return{selectedSeries:o,selectedHeight:l}}},{key:"getElementPosition",value:function e(t){var i=1;var n=100;var r=100;var a=10;var s=1;var o=1;var l=s*o;var c=function e(t){var a=Math.floor(t/l);var s=t-a*l;var c=Math.floor(s/o);var u=s-c*o;var d=Math.round(t/l);return{x:c*n+ +i/2,y:u*r+i/2+a*r*o}};return C()({},c(t),{w:n-i,h:r-i,gutter:i})}},{key:"addItem",value:function e(t,i){var n={id:t,type:i,order:this.items.length+2};this.items.unshift(n);this.addConfiguration(n)}},{key:"drawItem",value:function e(t,i,n,r,a){var s=this;var o=this.getElementPosition(i),l=o.x,c=o.y,u=o.w,d=o.h,h=o.gutter;this.orderHittest[t.id]=this.orderHittest[t.id]||[];this.orderHittest[t.id].push({y:c,h:d});var p=a?a:parseInt(this.vue.$route.params.selectedConfiguration);var f=new M["Graphics"];f.height=d;f.width=u;f.series=t.id;if(n.fake){f.lineStyle(1,16777215,.2).beginFill(0,.8).drawRoundedRect(h,h,u-h*2,d-h*2,5).endFill()}else{f.lineStyle(2,16777215,.2).beginFill(n.inconsequent?255:819,n.fake?.1:1).drawRect(0,0,u,d).endFill()}var v=new M["Graphics"];v.lineStyle(5,65280,.9).drawRect(h*2+5,h*2+5,u-h*4-10,d-h*2-10).endFill();f.addChildAt(v,0);v.alpha=0;v.interactive=false;v.buttonMode=false;f.hitArea=new M["Rectangle"](0,0,u,d);f.position.y=t.order*(u+h)+l;f.position.x=c;f.interactive=true;f.buttonMode=true;f.cursor="pointer";f.data={event:null,serieId:t.id,order:i,componentID:n.componentID};if(!n.fake){var m=z.a.h32("".concat(n.componentID),43981).toString(16);if(this.cache[m]){var g=new M["Sprite"](this.cache[m]);f.addChildAt(g,0)}else{if(this.queryResult&&n.componentID){A["a"].services.miniatures.add({data:I.a.find(this.queryResult.component,{id:n.componentID}).thumbnails_data,id:n.componentID,fetched:true}).then(function(e){var t=new M["BaseTexture"](e.painterState);t._sourceLoaded();var i=new M["Texture"](t);i.defaultAnchor={x:0,y:0};s.cache[m]=i;var n=new M["Sprite"](i);f.addChildAt(n,0);s.pixiApp.render()})}}console.log(t.id);f.on("rightdown",function(e){if(s.fetching)return;f.specialMenuOpened=true;e.data.originalEvent.preventDefault();console.log("wwww",n);s.lightupByID(n.componentID);A["a"].application.contextMenu.show(e,{name:n.compName,setID:n.setId,configID2:n.id,configID:n.componentID,selectedCollection:s.vue.$route.params.selectedCollection},"relations")}).on("mousedown",function(e){if(s.fetching)return;s.onDragStart(n,i,f,e,t)}).on("mouseup",function(e){if(s.fetching)return;s.onDragEnd(n.id,f,n,i,t)}).on("pointerupoutside",function(e){if(s.fetching)return;s.onDragEnd(n.id,f,n,i,t)}).on("mousemove",function(e){if(s.fetching)return;s.onDragMove(n,i,f,e,t)}).on("mouseover",function(e){if(s.fetching)return;if(A["a"].application.dnd.isDraggingHappening){f.alpha=.5;s.pixiApp.render()}}).on("mouseout",function(e){if(s.fetching)return;if(A["a"].application.dnd.isDraggingHappening){f.alpha=1;s.pixiApp.render()}})}else{f.on("mouseover",function(e){if(A["a"].application.dnd.isDraggingHappening){f.clear();f.lineStyle(1,16777215,.2).beginFill(65280,.2).drawRoundedRect(h,h,u-h*2,d-h*2,5).endFill();s.pixiApp.render()}}).on("mouseout",function(e){f.clear();f.lineStyle(1,16777215,.2).beginFill(0,.8).drawRoundedRect(h,h,u-h*2,d-h*2,5).endFill();s.pixiApp.render()})}this.pixiHitests.addChild(f);this.sprites.push(f)}},{key:"lightupByID",value:function e(t){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;console.log(t,i,"lightupByID");var n=false;if(!this.pixiHitests)return;this.pixiHitests.children.map(function(e){console.log("idi",t,e.data.componentID,+e.data.componentID==+t);if(!e||!e.children){}else if(e.children.length>1){if(!t){e.children[1].alpha=0}else if(i?t.indexOf(+e.data.componentID)>-1:+e.data.componentID==+t){e.children[0].alpha=1;e.children[1].alpha=1;n=true}else{e.children[1].alpha=0}}});if(!n&&t)this.lightupByID();this.pixiApp.render()}},{key:"setInconsequent",value:function e(t){this.inconsequentArray=t;this.drawSelectedSeries();this.pixiApp.render()}},{key:"selectSeries",value:function e(t){var i=this;if(!t||t<0)return;var n=function e(t){return I.a.findIndex(i.rawSeries,{id:t})};this.selectedSeriesNo=Array.isArray(t)?t.map(n):[n(t)];this.drawSelectedSeries();this.pixiApp.render()}},{key:"dimSeries",value:function e(t,i){if(P)console.log("dim",t);if(!t||t<0)return;this.seriesToDim=t;this.seriesToDimId=i;this.drawSelectedSeries();this.pixiApp.render()}},{key:"drawSelectedSeries",value:function e(){var t=this.annotationsOverlay;var i=this.selectedSeriesNo;var n=1e3;var r=this.series.length;var a=this.getElementPosition(1),s=a.x,o=a.y,l=a.w,c=a.h,u=a.gutter;var d=new M["Graphics"];d.lineStyle(5,16777215,.9).drawRect(0,0,l-u*2,n).endFill();var h=new M["Graphics"];h.beginFill(0,.6).drawRect(0,0,l,n).endFill();t.removeChildren();i.map(function(e){var i=new M["Sprite"](d.generateTexture());i.position.x=(l+u)*e+u;t.addChild(i)});var p=new Array(40).fill(0);p=p.map(function(e,t){return t});p=I.a.difference(p,this.seriesToDim.concat(i));p.map(function(e){var i=new M["Sprite"](h.generateTexture());i.position.x=e==0?1:(l+u)*e;t.addChild(i)});this.drawInconsequent()}},{key:"drawInconsequent",value:function e(){var t=this.annotationsOverlay;for(var i=0;i<this.seriesToDim.length;i++){for(var n=0;n<this.heights.length;n++){var r=this.seriesToDimId[i];var a=I.a.find(this.inconsequentArray,{series:r});var s=a?a.inconsequent:null;console.log("im22",a,s);if(s&&s.indexOf(this.heights[n])>-1){var o=this.getElementPosition(n),l=o.x,c=o.y,u=o.w,d=o.h,h=o.gutter;var p=new M["Graphics"];p.lineStyle(5,16711680,.9).beginFill(16711680,.2).drawRect(0,0,u-h*2,d).endFill();p.position.x=this.seriesToDim[i]*(u+h)+h;p.position.y=c;t.addChild(p)}}}}},{key:"transferControl",value:function e(){this.dragging=false}},{key:"onDragStart",value:function e(t,i,n,r){n.data.event=r.data;n.dragging=true;n.delta=false;n.startPosition=n.data.event.getLocalPosition(this.pixiApp.stage)}},{key:"liveSortExecute",value:function e(t,i,n,r){var a=null;this.orderHittest[n.id].map(function(e,t){if(i>=e.y&&i<=e.y+e.h+16){a=t}});if(a===null){a=this.orderHittest[n.id].length}var s=null;var o=null;var l=this.sprites.map(function(e){if(e.data.serieId==n.id)return e}).filter(function(e){return e?true:false}).map(function(e,i){if(i!=t){if(t!=a){if(i==a){o=e}else{return e}}else{return e}}else{s=e}}).filter(function(e){return e?true:false});this.directionReverse=t>a?1:0;if(this.editMode=="normal"){if(o)l.splice(t-this.directionReverse,0,o);l.splice(a,0,s)}else if(this.editMode=="replace"){}else if(this.editMode=="clone"){if(o)l.splice(t-this.directionReverse,0,o);l.splice(a,0,s)}if(P)console.log("before diff",t,t-this.directionReverse,a,s,o);this.draggedInitialNo=t-this.directionReverse;this.draggedTargetNo=a;if(this.draggedTargetNo==this.draggedInitialNo){this.draggedInitialNo=t}if(this.editMode=="clone"){}else{}}},{key:"onDragEnd",value:function e(t,i,n,r,a){var s=this;i.alpha=1;i.dragging=false;var o=0;this.changedSerie=[];if(P)console.log("DELTA",i.delta);if(i.delta==false){var l=function(){var e=x()(g.a.mark(function e(t,r){var a,l,c,u,d,h;return g.a.wrap(function e(p){while(1){switch(p.prev=p.next){case 0:p.next=2;return A["a"].api.updateSeriesConfiguration(t,{inconsequent:r});case 2:a=s.getElementPosition(o),l=a.x,c=a.y,u=a.w,d=a.h,h=a.gutter;if(r==false){if(i.tag)i.removeChild(i.tag)}else{}n.inconsequent=r;s.pixiApp.render();s.vue.updateAfterChange(false);case 7:case"end":return p.stop()}}},e)}));return function t(i,n){return e.apply(this,arguments)}}();if(P)console.log(n.inconsequent);var c=this.seriesToDim.indexOf(r);var u=this.seriesToDimId[c];if(u){var d=I.a.find(this.inconsequentArray,{series:u});this.vue.inconsequentToggle(n.height,d);i.delta=false;this.changedSerie.push(a.id)}}else if(i.delta===true){if(P)console.log("wtf",t,i,n,r,a);var h=a.items.map(function(e){return{order:e.order}});var p=this.editMode=="clone";if(p){var f=I.a.findIndex(h,{order:this.draggedInitialNo});var v=I.a.findIndex(h,{order:this.draggedTargetNo});var m=a.items[f];if(P)console.log("before diff","fin",m);if(m.config==-1){m=a.items[v];if(P)console.log("before diff","fin2",m)}a.items.splice(this.draggedTargetNo,1,{id:m.componentID,type:"component",isNew:true,height:this.heights.indexOf(m.height),order:this.draggedTargetNo,config:m.componentID});this.changedSerie.push(a.id)}else{var b=I.a.findIndex(h,{order:this.draggedInitialNo});var w=I.a.findIndex(h,{order:this.draggedTargetNo});var y=a.items[b];if(P)console.log("before diff",b,w,a.items,"fin",y);if(y.config==-1){y=a.items[w];if(P)console.log("before diff","fin2",y)}a.items.splice(this.draggedInitialNo,1,{id:-1,order:this.draggedInitialNo,fake:true});a.items.splice(this.draggedTargetNo,1,{id:y.componentID,type:"component",isNew:true,height:this.heights.indexOf(y.height),order:this.draggedTargetNo,config:y.componentID});this.changedSerie.push(a.id)}this.vue.updateAfterChange(true)}}},{key:"onDragMove",value:function e(t,i,n,r,a){if(n.dragging){this.pixiApp.render();var s=1;if(n.delta){var o=n.data.event.getLocalPosition(this.pixiApp.stage);var l=Math.ceil((o.x-this.delta.x)/s)*s;n.position.x=l-50;this.liveSortExecute(i,l,a,n)}else{var o=n.data.event.getLocalPosition(this.pixiApp.stage);var c=n.startPosition;var u=Math.sqrt(Math.pow(o.y-c.y,2)+Math.pow(o.x-c.x,2));n.delta=false;if(u>s){n.delta=true;this.pixiHitests.removeChild(n);this.pixiHitests.addChildAt(n,this.pixiHitests.children.length)}}}}},{key:"__draw",value:function e(){this.pixiHitests.position.x=this.delta.x;this.annotationsOverlay.position.x=this.delta.x;this.vue.$refs.seriesNames.style.transform="translateX(".concat(this.delta.x,"px)");this.pixiApp.render()}},{key:"_draw",value:function e(t){var i=this;this.labels.map(function(e,t){if(e){i.pixiApp.stage.removeChild(e);i.labels[t]=null;e=null}});this.sprites.map(function(e,t){if(e){e.removeAllListeners();i.pixiHitests.removeChild(e);i.sprites[t]=null;e=null}});this.labels=[];this.sprites=[];this.orderHittest=[];if(!this.bgDrawn){var n=this.getSize(),r=n.width,a=n.height;var s=80;for(var o=0;o<r/s;o++){for(var l=0;l<a/s;l++){this.backgroundContainer.lineStyle(1,35791401,1).drawRect(o*s,l*s,1,1)}}this.bgDrawn=true}var c=0;if(this.series.length>0){var u=Math.round(this.delta.x/20);this.series.map(function(e,n){console.log(1234,e);var r=I.a.orderBy(e.items,["order"],["asc"]).map(function(n,r){console.log("item",n);i.drawItem(e,r,n,n.type,t)})})}this.drawSelectedSeries();this.pixiApp.render()}},{key:"drawText",value:function e(t,i,n,r){return;var a=this.pixiApp;var s=O["a"].create(t);this.labels.push(s);r.addChild(s);s.position.set(10,10)}}]);return e}();var G=new W;var V=function(){var e=this;var t=e.$createElement;var i=e._self._c||t;return i("t-card",{attrs:{name:"Series settings"}},[i("div",{staticClass:"card mid"},[i("div",{staticClass:"card-main"},[i("div",{staticClass:"_"},[i("div",{staticClass:"card-content"},[i("t-select",{attrs:{"target-model":e.selectedSerie,neverEmpty:"neverEmpty",options:e.seriesList,"section-label":"Selected series:","update-model":"update-model"},on:{save:function(t){return e.selectSerie(""+t.value)}}})],1)]),e.selectedSerie?i("div",{staticClass:"_"},[i("div",{staticClass:"card-content"},[i("q-btn",{attrs:{label:"Change name"}},[i("q-popup-edit",{attrs:{title:"Change name",buttons:"buttons"},on:{save:e.saveSerie},model:{value:e.selectedSerieName,callback:function(t){e.selectedSerieName=t},expression:"selectedSerieName"}},[i("q-input",{attrs:{type:"text"},model:{value:e.selectedSerieName,callback:function(t){e.selectedSerieName=t},expression:"selectedSerieName"}})],1)],1),i("q-btn",{attrs:{label:"Remove"},on:{click:e.remove}})],1)]):e._e(),e.selectedSerie?i("div",{staticClass:"_"},[i("div",{staticClass:"card-content"},[e.selectedSerie?i("q-tree",{attrs:{nodes:e.tree,dark:"dark","inverted-light":"inverted-light","node-key":"label","default-expand-all":"default-expand-all"}}):e._e()],1)]):e._e()])])])};var X=[];var Y=i("39fc");var Z={name:"Tylko-Series",props:{series:Array},components:C()({},Y["j"]),data:function e(){return{tree:[],selectedSerie:-1,selectedSerieName:""}},computed:{seriesList:function e(){return this.series.map(function(e){return{label:e.name,value:parseInt(e.id)}})}},watch:{seriesList:function e(){},series:function e(){if(this.series){var t=_.find(this.series,{id:this.selectedSerie});if(t)this.selectedSerieName=t.name;this.tree=t.tree}},$route:function e(t,i){this.selectedSerie=+this.$route.params.selectedSerie;if(this.series){var n=_.find(this.series,{id:this.selectedSerie});if(n)this.selectedSerieName=n.name;this.tree=n.tree}}},mounted:function e(){this.selectedSerie=+this.$route.params.selectedSerie;if(this.series){var t=_.find(this.series,{id:this.selectedSerie});if(t)this.selectedSerieName=t.name}},methods:{saveSerie:function(){var e=x()(g.a.mark(function e(t){var i,n;return g.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:i=A["a"].api.updateSerie(+this.$route.params.selectedSerie,{name:t});r.next=3;return i;case 3:n=r.sent;this.$emit("loadTables");this.$emit("render");case 6:case"end":return r.stop()}}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),remove:function e(){var t=this;this.$q.dialog({title:"Delete",message:"This action will delete serie",ok:"Ok",cancel:"Cancel"}).onOk(x()(g.a.mark(function e(){return g.a.wrap(function e(i){while(1){switch(i.prev=i.next){case 0:i.next=2;return A["a"].api.removeSeries(t.selectedSerie);case 2:t.$emit("loadTables");t.$emit("render");case 4:case"end":return i.stop()}}},e)})))},selectSerie:function e(t){this.$router.push("/relations/".concat(this.$route.params.selectedCollection,"/").concat(this.$route.params.selectedTable,"/").concat(t))},broadcastNewParameters:function e(t){var i=this;A["a"].api.updateComponent(this.comp.id,{additional_params:t}).subscribe(function(){i.$emit("newParameters")})}}};var J=Z;var U=i("48af");var K=i("2877");var Q=Object(K["a"])(J,V,X,false,null,null,null);var ee=Q.exports;var te=function(){var e=this;var t=e.$createElement;var i=e._self._c||t;return i("t-card",{attrs:{name:"Table Settings "+e.selectedTableName}},[i("div",{staticClass:"card"},[i("div",{staticClass:"card-main"},[e.tablesList.length>0?i("div",{staticClass:"_"},[i("div",{staticClass:"card-content"},[i("t-select",{attrs:{"target-model":e.newModel,neverEmpty:"neverEmpty",options:e.tablesList,"section-label":"Selected table:","update-model":"update-model"},on:{save:function(t){return e.selectTable(t.value)}}})],1)]):e._e(),i("div",{staticClass:"_"},[i("div",{staticClass:"card-content"},[e.selectedTable!=-1?i("div",{staticClass:"__"},[i("q-btn",{attrs:{label:"Rename"}}),i("q-popup-edit",{attrs:{title:"Change name",buttons:"buttons"},on:{save:e.saveTable},model:{value:e.selectedTableName,callback:function(t){e.selectedTableName=t},expression:"selectedTableName"}},[i("q-input",{attrs:{type:"text"},model:{value:e.selectedTableName,callback:function(t){e.selectedTableName=t},expression:"selectedTableName"}})],1)],1):e._e(),i("div",{staticClass:"__"},[i("q-btn",{attrs:{label:"Add new"}}),i("q-popup-edit",{attrs:{title:"Add new table",buttons:"buttons"},on:{save:e.createTable},model:{value:e.newTableName,callback:function(t){e.newTableName=t},expression:"newTableName"}},[i("q-input",{attrs:{type:"text"},model:{value:e.newTableName,callback:function(t){e.newTableName=t},expression:"newTableName"}})],1),e.newModel?i("q-btn",{attrs:{label:"Remove"},on:{click:e.remove}}):e._e()],1)])]),e.tablesList.length>0?i("div",{staticClass:"_"},[i("div",{staticClass:"card-main"},[e.newModel?i("q-tree",{attrs:{nodes:e.tree,dense:"dense",dark:"dark","inverted-light":"inverted-light","node-key":"label","default-expand-all":"default-expand-all"}}):e._e()],1)]):e._e(),!(e.tablesList.length>0)?i("div",{staticClass:"_"},[i("div",[i("p",[e._v("No tables exist for the selected colelction")])])]):e._e()])])])};var ie=[];var ne=i("d6b6");var re={name:"Tylko-Table",data:function e(){var t;return t={selectedTableName:"",newTableName:"",collectionTables:[],tablesList:[]},c()(t,"newTableName",""),c()(t,"selectedTable",-1),c()(t,"newModel",-1),c()(t,"syncing",false),c()(t,"tree",[]),t},props:{collectionID:[String,Number],selectedSeries:Array,tableConfigurations:Array,tablesList:Array},components:C()({},Y["j"]),watch:{selectedSeries:function e(){this.selectedTable=this.$route.params.selectedTable},selectedTableName:function e(){},tablesList:function e(){if(!this.selectedTable&&this.tablesList.length>0&&!this.$route.params.selectedTable){this.selectedTable=this.tablesList[0].value;this.selectTable(this.tablesList[0].value)}this.newModel=parseInt(this.$route.params.selectedTable)},newModel:function e(t){var i=this;this.tablesList.map(function(e){if(e.table.id==t){i.selectedTableName=e.label;i.tree=e.table.tree;console.log("tree",e)}});this.$emit("loadTables")}},mounted:function e(){},methods:{selectTable:function e(t){this.selectedTable=t;var i=this.$route.params.selectedSerie;this.$router.push("/relations/".concat(this.$route.params.selectedCollection,"/").concat(t).concat(i?"/"+i:""));this.newModel=parseInt(this.$route.params.selectedTable)},diffSeriesConfigurations:function(){var e=x()(g.a.mark(function e(){return g.a.wrap(function e(t){while(1){switch(t.prev=t.next){case 0:this.selectedSeries;case 1:case"end":return t.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),saveTable:function(){var e=x()(g.a.mark(function e(t){var i,n;return g.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:i=A["a"].api.updateTable(this.newModel,{name:t});r.next=3;return i;case 3:n=r.sent;this.$emit("loadTables");case 5:case"end":return r.stop()}}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),remove:function e(){var t=this;this.$q.dialog({title:"Delete",message:"This action will delete table",ok:"Ok",cancel:"Cancel"}).onOk(x()(g.a.mark(function e(){return g.a.wrap(function e(i){while(1){switch(i.prev=i.next){case 0:i.next=2;return A["a"].api.removeTable(t.newModel);case 2:t.$emit("loadTables");t.$emit("render");t.newModel=-1;case 5:case"end":return i.stop()}}},e)})))},createTable:function(){var e=x()(g.a.mark(function e(t){var i,n;return g.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:i=A["a"].api.createTable({collection:this.$route.params.selectedCollection,name:t});r.next=3;return i;case 3:n=r.sent;this.selectTable(n.id);this.$emit("loadTables");case 6:case"end":return r.stop()}}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),getCurrentTableConfigs:function e(t){var i=[];this.tablesList.map(function(e){if(e.table.id==t){i=e.table.configurations}});return i},updateTable:function(){var e=x()(g.a.mark(function e(t){var i,n,r,a,s,o,l,c,u,d,h,p,f,v,m,b,w,x,y,C,S,k;return g.a.wrap(function e(g){while(1){switch(g.prev=g.next){case 0:this.syncing=true;i=this.newModel;n=this.$route.params.selectedCollection;r=this.getCurrentTableConfigs(i);a=function e(t){return A["a"].api.createTableConfiguration({table:i,serie:t,collection:n})};s=function e(t){return A["a"].api.removeTableConfiguration(t)};o=t;l=r.map(function(e){return e.series});c={remove:_.differenceWith(l,o,_.isEqual),add:_.differenceWith(o,l,_.isEqual)};c.remove=c.remove.map(function(e){return _.find(r,{series:e}).id});u=true;d=false;g.prev=12;p=T()(c.add.map(a));case 14:g.next=16;return p.next();case 16:f=g.sent;u=f.done;g.next=20;return f.value;case 20:v=g.sent;if(u){g.next=26;break}m=v;case 23:u=true;g.next=14;break;case 26:g.next=32;break;case 28:g.prev=28;g.t0=g["catch"](12);d=true;h=g.t0;case 32:g.prev=32;g.prev=33;if(!(!u&&p.return!=null)){g.next=37;break}g.next=37;return p.return();case 37:g.prev=37;if(!d){g.next=40;break}throw h;case 40:return g.finish(37);case 41:return g.finish(32);case 42:if(!s){g.next=75;break}b=true;w=false;g.prev=45;y=T()(c.remove.map(s));case 47:g.next=49;return y.next();case 49:C=g.sent;b=C.done;g.next=53;return C.value;case 53:S=g.sent;if(b){g.next=59;break}k=S;case 56:b=true;g.next=47;break;case 59:g.next=65;break;case 61:g.prev=61;g.t1=g["catch"](45);w=true;x=g.t1;case 65:g.prev=65;g.prev=66;if(!(!b&&y.return!=null)){g.next=70;break}g.next=70;return y.return();case 70:g.prev=70;if(!w){g.next=73;break}throw x;case 73:return g.finish(70);case 74:return g.finish(65);case 75:this.$emit("loadTables");this.$emit("loaded",t);console.log("dddddim",t);case 78:case"end":return g.stop()}}},e,this,[[12,28,32,42],[33,,37,41],[45,61,65,75],[66,,70,74]])}));function t(t){return e.apply(this,arguments)}return t}()}};var ae=re;var se=i("8efb");var oe=Object(K["a"])(ae,te,ie,false,null,null,null);var le=oe.exports;var ce=i("b2ec");var ue={ORIENTATION_VERTICAL:"vertical",ORIENTATION_HORIZONTAL:"horizontal"};var de=i("202d");var he=false;var pe={name:"TylkoEditor",components:C()({"t-series":ee,"t-table":le,"t-bar":ce["a"]},Y["j"]),props:["comp","mesh","meshMode","currentSetup"],data:function e(){return{miniApperance:null,colors:"#ddffaa",editMode:"replace",dropMode:"replace",checked:false,bgDrawn:false,items:[],labels:[],sprites:[],fetching:false,orderHittest:[],queryResult:null,heights:[],choosenSeriesForTable:[],tablesList:[],collectionTables:[],tableConfigurations:[],currentComponentSeries:[],miniaturesDone:0,batchRunning:false,miniops:[{label:"standard",value:"triangles"},{label:'the wire "frame"',value:"line loop"}],mini:A["a"].application.settings.get("ministyle")||"triangles"}},methods:{updateTable:function e(){this.$refs.tableEditor.updateTable(this.choosenSeriesForTable)},lightup:function e(t){G.lightupByID(t,true)},selectMiniMode:function e(t){A["a"].application.settingss.set("ministyle",t)},dimSeries:function(){var e=x()(g.a.mark(function e(t){var i=this;var n,r;return g.a.wrap(function e(a){while(1){switch(a.prev=a.next){case 0:if(he)console.log(123,"dodo",t);n=t;r=n.map(function(e){var t=false;i.currentComponentSeries.map(function(i,n){if(e==i.id){t=n}});return t}).filter(function(e){return e!==false});G.dimSeries(r,t);case 4:case"end":return a.stop()}}},e)}));function t(t){return e.apply(this,arguments)}return t}(),loadSeries:function(){var e=x()(g.a.mark(function e(){var t;return g.a.wrap(function e(i){while(1){switch(i.prev=i.next){case 0:t=A["a"].api.palette.relations(this.$route.params.selectedCollection).componentSeries.thumbs;i.next=3;return t.fetch();case 3:this.collectionTables=i.sent;this.currentComponentSeries=this.collectionTables["component-series"];case 5:case"end":return i.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),loadTables:function(){var e=x()(g.a.mark(function e(){var t=this;var i,n;return g.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:i=A["a"].api.palette.relations(this.$route.params.selectedCollection).componentTable.tableConfigurations.thumbs;r.next=3;return i.fetch();case 3:this.collectionTables=r.sent;this.tablesList=this.collectionTables["component-table"].map(function(e){return{label:e.name,value:e.id,table:e}});n=[];this.tablesList.map(function(e){if(e.table.id==t.$route.params.selectedTable){t.currentTable=e.table;n=e.table.configurations}});this.choosenSeriesForTable=n.map(function(e){return e.series});this.selectSeries();this.selectInconsequent(n);this.$refs.tableEditor.syncing=false;case 11:case"end":return r.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),uuid:function e(){return de()},selectConfiguration:function e(t,i){},prepareMiniaturesAll:function e(){this.prepareMiniatures()},prepareMiniaturesCollection:function e(){this.prepareMiniatures(this.$route.params.selectedCollection)},prepareMiniatures:function(){var e=x()(g.a.mark(function e(t){var i=this;var n,r,a,s,o,l,c,u,d,h,p,f,v,m;return g.a.wrap(function e(b){while(1){switch(b.prev=b.next){case 0:n=[],r=0,a=0;if(t){b.next=9;break}b.next=4;return A["a"].api.palette.collection().fetch();case 4:l=b.sent;s=l.collection;o=s.map(function(e){return A["a"].api.palette.relations(e.id).component.thumbs.fetch()});b.next=13;break;case 9:b.next=11;return A["a"].api.palette.relations(t).component.thumbs.fetch();case 11:o=b.sent;o=[o];case 13:c=true;u=false;b.prev=15;h=T()(o);case 17:b.next=19;return h.next();case 19:p=b.sent;c=p.done;b.next=23;return p.value;case 23:f=b.sent;if(c){b.next=31;break}v=f;n.push(v.component);r+=v.component.length;case 28:c=true;b.next=17;break;case 31:b.next=37;break;case 33:b.prev=33;b.t0=b["catch"](15);u=true;d=b.t0;case 37:b.prev=37;b.prev=38;if(!(!c&&h.return!=null)){b.next=42;break}b.next=42;return h.return();case 42:b.prev=42;if(!u){b.next=45;break}throw d;case 45:return b.finish(42);case 46:return b.finish(37);case 47:n=I.a.flatten(n);m=function e(){var t=0;var s=function e(){if(t<=n.length){var s=n[t];A["a"].api.geo.componentSet({type:"componentRelations",id:s.id,queryForMiniature:true}).forceFetch(true).pipe(function(n){if(he)console.log("123",n);A["a"].services.miniatures.add({data:n,id:s.id,fetched:false}).then(function(){var n=x()(g.a.mark(function n(o){return g.a.wrap(function n(l){while(1){switch(l.prev=l.next){case 0:l.next=2;return A["a"].api.updateComponentThumb(s.id,{thumbnails_data:o.error?[{}]:o.geo});case 2:e();a++;t++;i.miniaturesDone=a/r*100;case 6:case"end":return l.stop()}}},n)}));return function(e){return n.apply(this,arguments)}}()).catch(function(){})},"paletteRelations")}};s()};this.$q.dialog({title:"Confirm",message:"Batch render all ".concat(r," mianitures?"),ok:"Well ok.",cancel:"No"}).onOk(function(){i.batchRunning=t?1:2;m()});case 50:case"end":return b.stop()}}},e,this,[[15,33,37,47],[38,,42,46]])}));function t(t){return e.apply(this,arguments)}return t}(),getHeightFromSetup:function e(){return 200},rerrender:function e(){this.computeItems(true)},computeItems:function(){var e=x()(g.a.mark(function e(){var t,i,n,r,a,s,o,l,c,u,d,h,p,f,v,m,b=arguments;return g.a.wrap(function e(g){while(1){switch(g.prev=g.next){case 0:t=b.length>0&&b[0]!==undefined?b[0]:false;i=b.length>1&&b[1]!==undefined?b[1]:true;n=b.length>2&&b[2]!==undefined?b[2]:true;G.fetching=true;if(i){g.next=7;break}if(t){G.currentState=this.serializeState();G._draw()}return g.abrupt("return");case 7:this.projectFilter=this.$route.params.selectedCollection;r=A["a"].api.palette.relations(this.projectFilter).componentSeries.component.thumbs;g.next=11;return r.fetch();case 11:a=g.sent;this.queryResult=a;this.currentComponentSeries=a["component-series"];G.queryResult=a;s=a["component-series"];if(he)console.log("SERIES",s);this.rawSeriesObject=s;o=this.heights=[300,400,500,600,700,800,900,1e3];l=s;G.rawSeries=l;c=function e(t,i){return{order:t,height:i.height,inconsequent:i.inconsequent,setId:i.component_set,compName:i.component_name,id:i.id,configurationID:i.id,config:i.component,componentID:i.component,type:"component",fake:false}};u=function e(t,i){return{order:t,height:i,name:"space",id:t,config:-1,type:"empty",fake:true}};d=function e(t,i){return t.height==i};l=l.map(function(e,t){console.log("seria",e);e.configurations=I.a.orderBy(e.configurations,["height"],["asc"]);var i=[],n=0,r=0,a=0;for(var s=0;s<o.length;s++){var l=o[s];var d=I.a.find(e.configurations,{height:l});if(he)console.log(n,d,l,(d,l));console.log("cell",d);if(d){i.push(c(a++,d))}else{i.push(u(a++,l))}}return C()({},e,{idserii:e.id,items:i,order:t})});h=[],p=0;for(f=0;f<o.length;f++){v=o[f];h.push(u(p++))}l.push({id:"new",order:2,config:-1,items:h});m=[];l.map(function(e,t){e.items.map(function(i,n){i.order=t;i.inconsequent=i.inconsequent;if(he)console.log("qq",i);if(m[n]){m[n].items.push(i)}else{m[n]={items:[i],id:n,idserii:e.idserii,seriesID:e.id,type:"serie",order:n}}})});G.series=m;if(t){G.currentState=this.serializeState();G._draw()}if(n){this.dimSeries(this.$refs.tableEditor.getCurrentTableConfigs(this.$route.params.selectedTable).map(function(e){return e.series}))}this.selectSeries();G.fetching=false;case 35:case"end":return g.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),reload:function e(){this.$forceUpdate()},saveOrder:function(){var e=x()(g.a.mark(function e(){return g.a.wrap(function e(t){while(1){switch(t.prev=t.next){case 0:case"end":return t.stop()}}},e)}));function t(){return e.apply(this,arguments)}return t}(),addConfiguration:function(){var e=x()(g.a.mark(function e(t){var i,n=arguments;return g.a.wrap(function e(t){while(1){switch(t.prev=t.next){case 0:i=n.length>1&&n[1]!==undefined?n[1]:false;case 1:case"end":return t.stop()}}},e)}));function t(t){return e.apply(this,arguments)}return t}(),build:function e(){this.draw()},executeChanges:function e(t){var i=this;if(he)console.log("diff","Looking for differences after action...");var n=[];var r=this.findDifferences(this.serializeState());if(he)console.log("diff","full",r,r,G.currentState,this.serializeState());var a=function e(t){return t.filter(function(e){return!e.fake})};r.add=r.add.map(a);r.remove=r.remove.map(a);if(he)console.log("diff",r);r.remove.map(function(e,t){e.map(function(e){var t=e.id;var i={type:"REMOVE",config:t};if(he)console.log("diff","Remove cell with ID ".concat(t),e);n.push(i)})});r.add.map(function(e,t){e.map(function(e){var t=e.id;var i=e.height;var r=e.serie;var a=e.isNew;var s={type:"ADD",config:t,targetSerieNo:r};if(a){s=C()({},s,{height:i==300?0:i});s.type="ADDNEW"}if(he)console.log("diff","Add ".concat(a?"NEW ":"","cell with ID ").concat(t," to series ").concat(r),s,e);n.push(s)})});var s=function(){var e=x()(g.a.mark(function e(){var r,a,s,o,l,c,u,d,h,p;return g.a.wrap(function e(f){while(1){switch(f.prev=f.next){case 0:r=[];a=false;s=false;o=0;case 4:if(!(o<n.length)){f.next=37;break}l=n[o];if(!(l.type=="ADD"||l.type=="ADDNEW")){f.next=18;break}c=i.rawSeriesObject[l.targetSerieNo];if(c){f.next=17;break}f.next=11;return A["a"].api.createSeries({collection:i.projectFilter,name:"zzz"});case 11:u=f.sent;d=u.id;c=d;s=true;f.next=18;break;case 17:c=c.id;case 18:h=void 0;f.t0=l.type;f.next=f.t0==="REMOVE"?22:f.t0==="ADD"?27:f.t0==="ADDNEW"?30:33;break;case 22:p=I.a.findIndex(n,{type:"ADD",config:l.config})>-1;if(p){f.next=26;break}f.next=26;return A["a"].api.removeSeriesConfiguration(l.config);case 26:return f.abrupt("break",33);case 27:f.next=29;return A["a"].api.updateSeriesConfiguration(l.config,{parent:c});case 29:return f.abrupt("break",33);case 30:f.next=32;return A["a"].api.createSeriesConfiguration(c,l.config,i.heights[l.height]);case 32:return f.abrupt("break",33);case 33:t();case 34:o++;f.next=4;break;case 37:case"end":return f.stop()}}},e)}));return function t(){return e.apply(this,arguments)}}();s()},selectSerie:function e(t){if(!this.$route.params.selectedCollection)return;this.$router.push("/relations/".concat(this.$route.params.selectedCollection,"/").concat(this.$route.params.selectedTable,"/").concat(t))},selectSeries:function e(){var t=+this.$route.params.selectedSerie;if(t)G.selectSeries(t)},selectInconsequent:function e(t){console.log("cfgs",t);this.currentInq=t;G.setInconsequent(t)},inconsequentToggle:function(){var e=x()(g.a.mark(function e(t,i){var n,r,a,s;return g.a.wrap(function e(o){while(1){switch(o.prev=o.next){case 0:n=I.a.find(this.currentInq,{id:i.id});r=n.inconsequent?n.inconsequent:[];if(r.indexOf(t)>-1){r.splice(r.indexOf(t),1)}else{r.push(t)}this.currentInq=r;a=A["a"].api.updateTableConfiguration(i.id,{inconsequent:r});o.next=7;return a;case 7:s=o.sent;this.$refs.tableEditor.$emit("loadTables");case 9:case"end":return o.stop()}}},e,this)}));function t(t,i){return e.apply(this,arguments)}return t}(),draw:function e(){if(G.series){G.currentState=this.serializeState();G._draw()}},updateAfterChange:function e(t){var i=this;G._draw();if(G.fetching)return;this.executeChanges(function(){i.computeItems(true,t)})},addItemDrop:function e(t,i){var n=t.id,r=t.type;var a=i.selectedSeries,s=i.selectedHeight;var o=I.a.orderBy(G.series[s].items,["order"],["asc"]);if(o[a].fake){o.splice(a,1,{id:n,type:r,isNew:true,height:s,order:a,config:n});G.series[s].items=o;this.updateAfterChange(true)}else if(this.dropMode=="replace"){o.splice(a,1,{id:n,type:r,isNew:true,height:s,order:a,config:n});G.series[s].items=o;this.updateAfterChange(true)}},findDifferences:function e(t,i){var n={add:[],remove:[]};for(var r in G.currentState){n.add.push(I.a.differenceWith(t[r],G.currentState[r],I.a.isEqual));n.remove.push(I.a.differenceWith(G.currentState[r],t[r],I.a.isEqual))}return n},serializeState:function e(){var t=this;var i=[],n=[];var r=[];var a=[];G.series.map(function(e,i){e.items.map(function(e){n.push({id:e.id,height:e.height||t.heights[i],serie:e.order,config:e.config,fake:e.fake,isNew:e.isNew})})});i=I.a.groupBy(n,function(e){return e.serie});if(he)console.log("####",i);return i}},created:function e(){},mounted:function e(){var t=this;var i=function e(){var t=[{type:"back",id:1,color:"#252120"},{type:"doors",id:2,color:"#32453A"},{type:"supports",id:3,color:"#252120"},{type:"drawers",id:4,color:"#802431"},{type:"horizontal",id:5,color:"#A19C8A"},{type:"verticals",id:6,color:"#A19C8A"},{type:"insert",id:7,color:"#FCFF69"},{type:"----",id:8,color:"#00ff00"}];var i=userSettings.get("miniApperance");if(i.data){i.data.map(function(e){var i=Object.keys(e)[0];var n=I.a.findIndex(t,{id:+i});t[n].color=e[i]})}return t};A["a"].application.dnd.addDestinationComponent({type:"compset-editor",instance:this,incomingHandler:function e(i,n){var r=G.traceDropCoordinates(n);t.addItemDrop(i,r)}});this.pixiApp=null;this.pixiRoot=null;this.drawingContainer=null;this.backgroundContainer=null;this.projectFilter=this.$route.params.selectedCollection;G.setContext(this);this.computeItems(true,true,true);this.loadTables();A["a"].application.contextMenu.listenAction({action:"reload",context:"relations"}).subscribe(function(){t.computeItems(true,true,true)});this.miniApperance=i();window.dispatchEvent(new Event("resize"))},watch:{miniApperance:{handler:function e(){var t=this.miniApperance.map(function(e){return c()({},e.id,e.color)});userSettings.setObjectItem("miniApperance","data",t)},deep:true},currentComponentSeries:function e(){},$route:function e(t,i){this.selectSeries();this.dimSeries(this.$refs.tableEditor.getCurrentTableConfigs(this.$route.params.selectedTable).map(function(e){return e.series}))},dropMode:function e(){G.dropMode=this.dropMode},editMode:function e(){G.editMode=this.editMode},items:function e(){},comp:function e(){},currentSetup:function e(){},currentHeight:function e(){}}};var fe=pe;var ve=i("6250");var me=i("320b");var ge=Object(K["a"])(fe,s,o,false,null,null,null);var be=ge.exports;var we={components:{"t-relations":be},mounted:function e(){viewport.fixed(true)},methods:{onResize:function e(t){this.rendererHeight=t.height-230},created:function e(){this.onResize({height:window.innerHeight})}},data:function e(){return{activeCollection:null}}};var xe=we;var ye=Object(K["a"])(xe,n,r,false,null,null,null);var Ce=t["default"]=ye.exports},"8efb":function(e,t,i){"use strict";var n=i("3687");var r=i.n(n);var a=r.a},f423:function(e,t,i){}}]);
//# sourceMappingURL=1c8ddbb3.a1086893.js.map