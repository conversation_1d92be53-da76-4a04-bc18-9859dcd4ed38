{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/modals/new-component-modal.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/modals/new-mesh-modal.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@theme/menu-items.scss", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/navigation/menu/search/TylkoMenuItem.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/navigation/menu/search/TylkoMenuFullscreenSearch.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/navigation/menu/navigation/AppNavigation.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/views/layouts/default.vue"], "names": [], "mappings": "AAkCA,kBACI,UAAY,CAGhB,uBACI,UAAW,CACX,mBAAoB,CAExB,yBACI,WAAY,CACZ,YAAa,CAEjB,6BAII,wBAAyB,CAJ7B,8BAEQ,UAAY,CCdpB,uBACI,UAAW,CACX,mBAAoB,CAExB,yBACI,WAAY,CACZ,YAAa,CAEjB,6BAII,wBAAyB,CAJ7B,8BAEQ,UAAY,CC5CpB,kCACI,oBAAqB,CACrB,UAAY,CAGhB,2BACI,cAAe,CAGjB,6BAsBE,iBAAkB,CAClB,kBAAmB,CACnB,sBAAuB,CACvB,YAAa,CACb,WAxBQ,CAFV,6CAMU,UAAW,CACX,eAAiB,CACjB,SAAU,CACV,WAAY,CACZ,iBAAkB,CAClB,UAAW,CACX,iBAAkB,CAZ5B,sCAiBM,cAAe,CACf,WAAY,CACZ,QAAS,CAnBf,2CA8BM,kBAA2B,CA9BjC,iDAkCQ,aAAc,CAlCtB,mCA2DI,iBAAkB,CAClB,cAAe,CAEf,UA7DM,CADV,qCAyCM,cAAe,CACf,eAA2B,CAC3B,iBAAkB,CAClB,YAAa,CA5CnB,2CAgDM,OAAQ,CACR,iBAAkB,CAClB,UAAW,CACX,YAAa,CACb,sBAAsB,CACtB,YAAa,CACb,SAAU,CACV,eArDI,CAsDJ,eAAgB,CAChB,eAAgB,CAUtB,6BACE,UAAW,CAGb,4BACE,WAAY,CAGd,2BAGE,WAAY,CAGZ,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CARpB,0EAYI,iBAAkB,CAClB,UAKW,CAlBf,mCAsBI,UAAW,CACX,eAAgB,CAvBpB,+CAyBM,oBAAyB,CAK/B,qCACE,UAAY,CACZ,SAAU,CACV,mBAAoB,CACpB,iBAAkB,CAClB,UAAW,CAGb,wBACE,KAAQ,CACR,cAAe,CACf,WAAY,CACZ,UAAW,CACX,UAAW,CACX,YAAa,CCvGjB,4BACI,UAAW,CACX,WAAY,CACZ,iBAAkB,CAHtB,gCAKQ,YAAa,CACb,sBAAuB,CACvB,qBAAsB,CACtB,cAAe,CARvB,oCAWQ,cAAe,CAEnB,oCACI,cAAe,CACf,0BAA+B,CAC/B,SAAU,CACV,OAAQ,CACR,eAAgB,CAChB,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,gBAAiB,CACjB,SAAU,CACV,SAAU,CACV,iBAAkB,CAClB,qDAAyD,CACzD,UAAW,CACX,mCAA0C,CAC1C,iBAAkB,CAhBrB,iDAkBO,cAAe,CAGvB,kCACI,cAAe,CACf,gBAAiB,CACjB,4BAA6B,CAC7B,kBAAmB,CACnB,eAAiB,CACjB,kBAAmB,CAxC3B,qDA4CY,SAAU,CACV,kBAAmB,CCyC/B,oCACI,UAAY,CACZ,eAAiB,CACjB,cAAe,CACf,KAAM,CACN,MAAO,CACP,UAAW,CACX,iBAAkB,CAClB,WAAY,CACZ,YAAa,CACb,gBAAiB,CACjB,SAAU,CACV,iBAAkB,CAZtB,2CAcQ,SAAU,CACV,kBAAmB,CAEvB,2CACI,UAAW,CACX,gBAAiB,CACjB,cAAe,CAEnB,6CACI,YAAa,CAEjB,4CACI,WAAY,CACZ,YAAa,CACb,YAAa,CAHhB,+CAKO,eAAkB,CAG1B,iDACI,YAAa,CACb,eAAgB,CAChB,UAAW,CACX,kBAAmB,CACnB,iBAAkB,CAErB,+CAEO,SAAU,CACV,eAAkB,CAI9B,yEAEI,oBAAsB,CAF1B,oEAEI,oBAAsB,CAF1B,qEAEI,oBAAsB,CAF1B,2DAEI,oBAAsB,CAE1B,4BACI,oBAAqB,CACrB,iBAAkB,CAEtB,wEAEI,kBAAoB,CAExB,6DACI,SAAU,CACV,kCAA2B,CAA3B,0BAA2B,CF5K/B,kCACI,oBAAqB,CACrB,UAAY,CAGhB,2BACI,cAAe,CAGjB,6BAsBE,iBAAkB,CAClB,kBAAmB,CACnB,sBAAuB,CACvB,YAAa,CACb,WAxBQ,CAFV,6CAMU,UAAW,CACX,eAAiB,CACjB,SAAU,CACV,WAAY,CACZ,iBAAkB,CAClB,UAAW,CACX,iBAAkB,CAZ5B,sCAiBM,cAAe,CACf,WAAY,CACZ,QAAS,CAnBf,2CA8BM,kBAA2B,CA9BjC,iDAkCQ,aAAc,CAlCtB,mCA2DI,iBAAkB,CAClB,cAAe,CAEf,UA7DM,CADV,qCAyCM,cAAe,CACf,eAA2B,CAC3B,iBAAkB,CAClB,YAAa,CA5CnB,2CAgDM,OAAQ,CACR,iBAAkB,CAClB,UAAW,CACX,YAAa,CACb,sBAAsB,CACtB,YAAa,CACb,SAAU,CACV,eArDI,CAsDJ,eAAgB,CAChB,eAAgB,CAUtB,6BACE,UAAW,CAGb,4BACE,WAAY,CAGd,2BAGE,WAAY,CAGZ,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CARpB,0EAYI,iBAAkB,CAClB,UAKW,CAlBf,mCAsBI,UAAW,CACX,eAAgB,CAvBpB,+CAyBM,oBAAyB,CAK/B,qCACE,UAAY,CACZ,SAAU,CACV,mBAAoB,CACpB,iBAAkB,CAClB,UAAW,CAGb,wBACE,KAAQ,CACR,cAAe,CACf,WAAY,CACZ,UAAW,CACX,UAAW,CACX,YAAa,CGIjB,+BACI,iBAAkB,CAClB,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,YAAa,CACb,WAAY,CANhB,iCASQ,cAAe,CACf,eAA2B,CAC3B,iBAAkB,CAClB,WAAY,CAIpB,kCACI,oBAAqB,CACrB,UAAY,CAGhB,oCACI,kBAA2B,CAK3B,qCAAgC,CAAhC,6BAAgC,CAAhC,wDAAgC,CAChC,mCAA4B,CAA5B,2BAA4B,CAC5B,cAAe,CACf,KAAQ,CACR,MAAS,CACT,YAAa,CACb,aAAc,CACd,UAAY,CACZ,WAAkB,CAClB,eAAgB,CAChB,WAAY,CAhBhB,6CAmBQ,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,iBAAkB,CAClB,SAAU,CArBlB,oDAyBQ,oBAAqB,CACrB,WAAY,CACZ,YAAa,CACb,iBAAkB,CA5B1B,+EAgCgB,UAAW,CACX,aAAc,CACd,UAAW,CACX,UAAW,CACX,kCAA2B,CAA3B,0BAA2B,CAC3B,wBAAoC,CAKpD,mCACI,YAAa,CACb,kBAA2B,CCxK/B,uBACI,qCAAgC,CAAhC,6BAAgC,CAAhC,wDAAgC,CAChC,+BAA0B,CAA1B,uBAA0B,CAF9B,4BAKQ,kCAA2B,CAA3B,0BAA2B,CALnC,uCASQ,cAAe,CACf,WAAY,CACZ,kBAA2B", "file": "d997cee6.44af216b.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n* {\n    color: white;\n}\n\n.full {\n    width: 100%;\n    padding-bottom: 10px;\n}\n.global {\n    width: 300px;\n    padding: 20px;\n}\n.modal-main {\n    * {\n        color: white;\n    }\n    background-color: #1a1a1a;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.full {\n    width: 100%;\n    padding-bottom: 10px;\n}\n.global {\n    width: 300px;\n    padding: 20px;\n}\n.modal-main {\n    * {\n        color: white;\n    }\n    background-color: #1a1a1a;\n}\n", ".collection-link {\n    text-decoration: none;\n    color: white;\n}\n\n.menu-col {\n    max-width: 44px;\n  }\n  \n  .tool-entry {\n    $w: 50px;\n    $h: 42px;\n\n    &.selected {\n        &:before {\n            content: '';\n            background: white;\n            width: 6px;\n            height: 34px;\n            position: absolute;\n            left: -10px;\n            border-radius: 4px;\n        }\n    }\n\n    &.settings {\n        position: fixed;\n        bottom: 22px;\n        left: 4px;\n    }\n  \n    position: relative;\n    align-items: center;\n    justify-content: center;\n    display: flex;\n    height: $h;\n    &:hover {\n      .icon {\n        i { \n        background: rgb(44, 44, 44);\n        \n        }\n        .action {\n          display: block;\n        }\n      }\n    }\n  \n    .icon {\n      i {\n        font-size: 22px;\n        background: rgb(34, 34, 34);\n        border-radius: 3px;\n        padding: 17px;\n      }\n  \n      .action {\n        top: 7px;\n        position: absolute;\n        width: auto;\n        z-index: 1000;\n        background:transparent;\n        display: none;\n        left: 40px;\n        min-height: $h;\n        text-align: left;\n        min-width: 200px;\n      }\n      text-align: center;\n      cursor: pointer;\n  \n      width: $w;\n      //background: rgb(12, 12, 12);\n    }\n  }\n  \n  .full-width {\n    width: 100%;\n  }\n  \n  .home-icon {\n    margin: 13px;\n  }\n  \n  .menu-bar {\n    position: relative;\n    //box-shadow: inset 0 -7px 20px -4px #000;\n    height: auto;\n    $color: black;\n    $size: 60px;\n    background-color: #0f0f0f;\n    z-index: 100;\n    position: relative;\n\n  \n    .main-ico {\n      position: relative;\n      float: left;\n    }\n  \n    .funtions {\n      position: relative;\n      float: left;\n    }\n  \n    &.compact {\n      width: 100%;\n      min-height: 60px;\n      .full-width {\n        width: initial !important;\n      }\n    }\n  }\n  \n  .fake-container-bar {\n    opacity: 0.3;\n    z-index: 1;\n    pointer-events: none;\n    position: absolute;\n    width: 100%;\n  }\n  \n  .notop {\n    top: 0px;\n    position: fixed;\n    z-index: 100;\n    right: -5px;\n    width: 70px;\n    height: 184px;\n  }", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.menu-item {\n    width: 100%;\n    height: 40px;\n    position: relative;\n    > div {\n        display: flex;\n        justify-content: center;\n        vertical-align: middle;\n        cursor: pointer;\n    }\n    .q-icon {\n        font-size: 28px;\n    }\n    &-wrapper {\n        position: fixed;\n        background: rgba(0, 0, 0, 0.85);\n        left: 44px;\n        top: 5px;\n        min-width: 500px;\n        width: 90vw;\n        height: auto;\n        max-height: 90vh;\n        padding: 8px 12px;\n        z-index: 3;\n        opacity: 0;\n        visibility: hidden;\n        transition: opacity 0.3s ease-in, visibility 0.3s ease-in;\n        color: #fff;\n        border: 1px solid rgba(255, 255, 255, 0.2);\n        border-radius: 6px;\n        .q-tab-label {\n            font-size: 12px;\n        }\n    }\n    &-title {\n        font-size: 20px;\n        line-height: 28px;\n        border-bottom: 1px solid #fff;\n        margin-bottom: 12px;\n        font-weight: bold;\n        padding-bottom: 4px;\n    }\n    &:hover {\n        .menu-item-wrapper {\n            opacity: 1;\n            visibility: visible;\n        }\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.fullscreen-search {\n    color: white;\n    background: black;\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    overflow-y: scroll;\n    height: 100%;\n    padding: 32px;\n    z-index: 10000000;\n    opacity: 0;\n    visibility: hidden;\n    &.opened {\n        opacity: 1;\n        visibility: visible;\n    }\n    &--close {\n        color: #fff;\n        text-align: right;\n        cursor: pointer;\n    }\n    &--results {\n        display: flex;\n    }\n    &--column {\n        flex-grow: 1;\n        flex-basis: 0;\n        padding: 12px;\n        h6 {\n            margin: 0 0 16px 0;\n        }\n    }\n    &--column-item {\n        padding: 12px;\n        background: #fff;\n        color: #000;\n        margin-bottom: 12px;\n        border-radius: 4px;\n    }\n    &--header {\n        h3 {\n            padding: 0;\n            margin: 0 0 24px 0;\n        }\n    }\n}\ninput,\ninput::placeholder {\n    color: #fff !important;\n}\n.list-item {\n    display: inline-block;\n    margin-right: 10px;\n}\n.list-enter-active,\n.list-leave-active {\n    transition: all 0.2s;\n}\n.list-enter, .list-leave-to /* .list-leave-active below version 2.1.8 */ {\n    opacity: 0;\n    transform: translateY(30px);\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\ndiv.icon-home {\n    position: absolute;\n    background: #181818;\n    width: 60px;\n    height: 60px;\n    padding: 13px;\n    z-index: 100;\n\n    i {\n        font-size: 25px;\n        background: rgb(34, 34, 34);\n        border-radius: 3px;\n        padding: 5px;\n    }\n}\n\n.collection-link {\n    text-decoration: none;\n    color: white;\n}\n\n.collections-quick {\n    background: rgb(24, 24, 24);\n    &.show {\n        // transform: translateY(0px);\n    }\n\n    transition: transform 200ms ease;\n    transform: translateY(-60px);\n    position: fixed;\n    top: 0px;\n    left: 0px;\n    z-index: 1000;\n    display: block;\n    color: white;\n    width: calc(100vw);\n    overflow: hidden;\n    height: 60px;\n\n    .content {\n        width: max-content;\n        position: relative;\n        left: 60px;\n    }\n\n    .collection-pan {\n        display: inline-block;\n        height: 60px;\n        padding: 20px;\n        position: relative;\n\n        .selected-collection {\n            &:after {\n                content: '';\n                display: block;\n                width: 100%;\n                height: 3px;\n                transform: translateY(18px);\n                background-color: rgb(233, 233, 233);\n            }\n        }\n    }\n}\n.navigation-panel {\n    height: 100vh;\n    background: rgb(10, 10, 10);\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nsection.tylko-designer {\n    transition: transform 200ms ease;\n    transform: translateY(0px);\n\n    &.pane {\n        transform: translateY(60px);\n    }\n\n    div.menu-column {\n        max-width: 60px;\n        padding: 8px;\n        background: rgb(10, 10, 10);\n    }\n}\n"]}