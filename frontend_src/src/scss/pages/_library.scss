/* My Library */

.library-heading {
    font-size: 36px;
    margin-top: 60px;
    padding-bottom: 30px;
}

.product {
    padding-top: 3/4 * 100%;
    position: relative;
    img {
        position: absolute;
        display: block;
        left: 0;
        top: 0;
        width: 100%;
    }
    .product-info-sign img {
        width: 12%;
        top: 51%;
        left: 45%;
    }
    img.product-info-sign{
        width: 12%;
        top: 51%;
        left: 45%;
    }
    .product-create-new {
        /*background-color: $color-high;
        background: rgba(255,60,0,0.74);*/
        color: $color-high;
        @include font(26, 30);
        opacity: 1;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        text-align: center;
    }
    h2 {
        width: 50%;
        float: left;
        font-size: 20px;
        padding-top: 5px;
        line-height: 16px;
    }
    p {
        position: absolute;
        top: 8px;
        left: 10px;
        padding: 0;
        font-size: 20px;
        label {
            font-size: 14px;
            color: #fff;
            background: $color-confirm;
            height: 20px;
            display: inline-block;
            vertical-align: top;
            border-radius: 20px;
            padding: 0 5px;
        }
        em {
            font-size: 14px;
            display: inline-block;
            margin-right: 5px;
            color: $color-gray;
            span {
                text-decoration: line-through;
            }
        }
    }
    div {
        .button {
            margin-top: 10px;
        }
    }
}




@media screen and (min-width: $size-tablet-portrait + 1px) {


    .no-touch {

        .product {
            margin-bottom: $space / 2;
        }
        .product-info { 
            //background-color: $color-high;
            //background: rgba(255,60,0,0.74);
            //color: $color-bg;
            @include font(26, 30);
            opacity: 1;
            //position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            text-align: left;
            //transition: opacity 0.3s $opacity-easing;
            line-height: 20px;
            .product-info-name {
                color: $color-gray;
                font-size: 20px;
                margin-top: 12px;
                margin-bottom: 7px;
            }
            .product-info-price {
                color: #000;
                font-size: 20px;
                margin-bottom: 25px;
            }
        }

        .product-create-new {
            /*background-color: $color-high;
            background: rgba(255,60,0,0.74);*/
            color: $color-high;
            @include font(26, 30);
            opacity: 1;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            text-align: center;
        }
        .product a:hover {
            //.product-info {
            //    opacity: 1;
            //}
            .product-create-new {
                opacity: 0;
            }
        }
    }

    .touch .product {

        margin-bottom: $space;

        .product-info {
            @include font(22, 30);
            color: $color-gray;
            padding-top: 0.3em;
        }
    }

}

@media screen and (max-width: $size-tablet-portrait) {


    .product-desktop .product-info {
        display: none;
    }

    .product-desktop.product-desktop {
        margin-bottom: 0;
    }

    .product-info {
        @include font(15, 15);
        padding-top: 0.3em;
        .product-info-price {
            color: #000;
            margin-top: 8px;
        }
    }
}

.library-item {
    .product {
        margin-bottom: $gutter;
    }
    margin-bottom: $space;
}

.product-button-delete.product-button-delete {
    width: 50%;
    left: 25%;
    min-width: 50%;
    position: absolute;
    top: 50%;
    margin-top: -17px;
    opacity: 0;
    z-index: -1;
}

.product-drop {
    transition: opacity 0.3s $opacity-easing;
    position: absolute;
    right: 10px;
    top: 8px;
    width: 10px;
    height: 10px;
    padding: 5px;
}

.library-save {
    height: 0;
    .button {
        opacity: 1;
    }
}

.is-edited {
    .product-button-delete.product-button-delete {
        opacity: 1;
        z-index: 1;
    }
    .product-drop {
        opacity: 0;

    }
    .button {
        opacity: 1;
    }
}
.product-grid {
    &.hero-short {
        padding-top: 42% !important;
        @media screen and (min-width: $size-tablet-portrait + 1) {
            padding-top: 17.5625% !important;
            text-align: center;
        }
    }
    &.slider-menu {
        background: #fff;
        border-bottom: 1px solid $color-gray-line;
        position: relative;
        .back {
                position: absolute;
                bottom: -45px;
                left: 20px;
                z-index:9;
                font-size: 12px;
        }
        .js-slider {
            .nav-element {
                float: left;
                padding-left: 20px;
                width: auto;
                a {
                    line-height: 65px;
                }
            }
        }
    }
}

.product-nav {
    border-bottom: 1px solid $color-gray-line;
    height: 65px;
    .product-grid-nav {
        text-align: center;
        position: relative;
        padding: 0;
        .back {
            position: absolute;
            left: 10px;
            top: 22px;
        }
        ul {
            display: inline-block;
            li {
                float: left;
                font-size: 16px;
                margin-right: 30px;
                line-height: 65px;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}
.product-filter  {

    vertical-align: middle;

     @media screen and (min-width: $size-tablet-portrait + 1) {
         margin-left: 16px;
         display: inline-block;
     }
    .form {
        padding-top: 0;
        max-width: 200px !important;
        width: 200px;
        background: #fff;
        margin-top: 15px;
        display: inline-block;
        text-align: left;
         @media screen and (max-width: $size-tablet-portrait + 1) {
             text-align: right;
           float: right;
            text-align: left;
         }
        .form-select {
            background: #fff;
            text-align: left;
        }
    }
}


.grid-sort-by {
    display: inline-block;
    vertical-align: middle;
    margin-right: 25px;
}

.library-sort {

    .library-filter {
        margin-top: 0;
    }

    .form-select-title {
        line-height: 32px;
    }

    @media screen and(max-width: $size-mobile) {
        .grid-sort-by {
            width: 100%;
            margin-bottom: 10px;
            margin-top: 25px;
        }
        .product-filter {
            .form {
                width: 100%;
                max-width: 100% !important;
                margin-top: 0;
            }
        }
    }
    @media screen and(min-width: $size-mobile +1) {
        .product-filter {
            display: inline-block;
        }
    }
}

.product-hero-container {
    position: relative;
    width: 100%;
    > div {
        position: absolute !important;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 100;
        .hero-short {
            display: none;
        }
        .table {
            margin: 0;
        }
    }
}

