.features-section-with-photos {
    @keyframes fade-in {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
        
    @keyframes fade-out {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
    background-color: var(--bg-color);
    &__hidden-wrapper {
        position: absolute;
        top: -2000px;
        left: -2000px;
        opacity: 0;
        .features-section-with-photos-navigation {
            &__copy {
                padding-top: 12px;
                height: auto;
            }
        }
    }
    &-navigation {
        &__header {
            &--show-lg {
                display: none;
                @include desktop-min {
                    display: block;
                }
            }
            &--hide-lg {
                display: block;
                @include desktop-min {
                    display: none;
                } 
            }
        }
        &__wrapper {
            flex-direction: column;
            align-items: start;
            justify-content: center;
            height: 100%;
        }
        &__item {
            cursor: pointer;
            &--active {
                .features-section-with-photos-navigation {
                    &__line {
                        background-color: $ds-offblack-600;
                    }
                    &__text {
                        color: $ds-offblack-600;
                    }
                    &__copy {
                        color: $ds-offblack-600;
                    }
                }
            }
        }
        &__line {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 6px;
            background-color: $ds-grey-900;
            border-radius: $border-radius-xl;
            transition: height $basic-transition;
            &-wrapper {
                position: relative;
            }
        }
        &__copy {
            color: transparent;
            overflow: hidden;
            height: 0;
            transition: color $basic-transition;
            word-break: break-word;
        }
    }

    &-media {
        &__wrapper {
            position: relative;
            width: 100%;
            border-radius: $border-radius-xl;
            overflow: hidden;
            transform: translateZ(0);
            .wistia_responsive_padding {
                pointer-events: none;
            }
        }
        &__navigation {
            width: 335px;
            height: 84px;
            margin: 0 auto;
            position: absolute;
            left: 0;
            right: 0;
            bottom: -200px;
            z-index: 10;
            background-color: rgba(#121213, 0.38);
            background-blend-mode: multiply;
            border-radius: $border-radius-xl;
            &--visible {
                bottom: 0;
            }
        }
        &__type {
            &-button {
                cursor: pointer;
                flex-direction: column;
                width: 85px;
                height: 49px;
                border-radius: 8px;
                border: 2px solid $ds-grey-700;
                box-sizing: border-box;
                &:focus {
                    outline: none;
                }
                &--active {
                    background-color: $ds-white !important;
                    color: $ds-orange !important;
                }
                svg {
                    pointer-events: none;
                }
            }
        }
        &__color {
            &-buttons {
                width: 100%;
            }
            &-button {
                cursor: pointer;
                overflow: hidden;
                width: 28px;
                height: 28px;
                border-radius: 30px;
                box-shadow: 0 0 0 2px $ds-grey-600;
                box-sizing: border-box;
                &:focus {
                    outline: none;
                }
                &--active {
                    box-shadow: 0 0 0 2px $ds-orange;
                }
                &-bg {
                    width: 100%;
                    height: 100%;
                    min-width: 34px;
                    min-height: 34px;
                }
            }
        }
        &__slider {
            width: 100%;
            height: 100%;
            padding: 0 50px;
            position: relative;
            &-line {
                height: 2px;
                border-radius: 2px;
                width: 100%;
                position: relative;
                &-progress {
                    height: 100%;
                    width: 0;
                    border-radius: 2px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    background-color: $ds-orange;
                }
            }
            &-handler {
                cursor: pointer;
                position: absolute;
                top: 25px;
                left: 0;
                margin-left: 41px;
                width: 84px;
                height: 36px;
                border-radius: 30px;
                border: 2px solid $ds-offwhite-600;
                box-sizing: border-box;
                user-select: none;
            }
        }
        &__video {
            &-wrapper {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 100%;
            }
            &-embed {
                position: absolute !important;
                display: block;
                opacity: 0;
                &--visible {
                    opacity: 1;
                }
            }
            opacity: 0;
            visibility: hidden;
            &--visible {
                visibility: visible;
                opacity: 1;
            }
            &--preload {
                transition: none;
            }
        }
        &__image {
            width: 100%;
            opacity: 1;
            visibility: visible;
            transition: none;
            &--hidden {
                visibility: hidden;
                opacity: 0;
            }
        }
        &__picture {
            &-wrapper {
                position: relative;
            }
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            transition: none;
        }
    }

    &.features-section-with-photos--is-mobile {
        .features-section-with-photos {
            &-media {
                &__wrapper {
                    height: auto;
                }
                &__navigation {
                    width: 100%;
                    border-radius: 0;
                }
            }
            &-navigation {
                &__header {
                    br {
                        content: '';
                    }
                }
                &__text {
                    color: $ds-offblack-700;
                }
                &__copy {
                    height: auto;
                    overflow: visible;
                    color: $ds-offblack-600;
                    transition: none;
                } 
            }
        }
    }

    .w-focus-outline {
        box-shadow: none !important;
    }

    .w-video-wrapper {
        video {
            @media only screen and (min-width: $size-desktop-min) and (max-width: $size-desktop-air - 1px) {
                object-fit: cover !important;
            }
        }
    }
    .w-vulcan-v2-button {
        display: none !important;
    }
}
