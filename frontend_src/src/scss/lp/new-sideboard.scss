.sideboard {
    background-color: #A48F8E;
    position: relative;

    @include tablet-min {
        display: flex;
    }

    &__image {
        &--mobile {
            width: 100%;

            @include tablet-min {
                width: 50%;
            }
        }

        &--desktop {
            display: none;
            width: 50%;

            @include tablet-min {
                display: block;
            }
        }
    }

    &__content {
        @include tablet-min {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            right: 0;
            margin: 0 auto;
            max-width: 300px;
            text-align: center;
        }

        @include desktop-air {
            max-width: 410px;
        }

    }

    &__subheading {
        width: 100%;
    }

    &__paragraph {
        @include tablet-min {
            max-width: 223px;
        }

        @include desktop-min {
            max-width: 300px;
        }

        @include desktop-air {
            max-width: 100%;
        }
    }

    .button {
        width: auto;
    }
}
