$BAR_END_HEIGHT: 32px;

.features {
    &__video-container {
        position: relative;

        [data-handle="playPauseLoading"] .w-css-reset-tree {
            button {
                background: none !important;
                width: 100% !important;
                height: 100% !important;
                left: 0 !important;
                top: 0 !important;
                transform: none !important;

                div {
                    display: none !important;
                }

                img {
                    height: 25%;
                }
            }

            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                transition: background-color $short-transition;
                background-color: rgba(255, 255, 255, 0.2);
            }
        }
    }

    &__video-overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        opacity: 0;
        transition: opacity 0.3s;

        &.active {
            opacity: 1;
        }
    }

    &__button-container {
        display: none;

        @include desktop-min {
            display: block;
        }
    }

    &__description {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    &__cta-button {
        opacity: 0;
        transition: opacity .5s;
        white-space: nowrap;
        pointer-events: none;

        &--mobile {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);

            &.active {
                opacity: 1;
                pointer-events: auto;

                @include desktop-min {
                    opacity: 0;
                    pointer-events: none;
                }
            }
        }

        &--desktop.active {
            @include desktop-min {
                opacity: 1;
                pointer-events: auto;
            }
        }
    }

    &__navigation {
        display: flex;
        position: relative;
        background-color: #ECDCDC;
        overflow: scroll;
        -ms-overflow-style: none;
        scrollbar-width: none;


        &::-webkit-scrollbar {
            display: none;
        }

        &::after {
            content: '';
            min-width: 20px;
        }

        @include tablet-min {
            background-color: transparent;
            overflow: visible;
            flex-direction: column;
            min-height: 164px;
        }
    }

    .navigation-item {
        white-space: nowrap;
        position: relative;
        cursor: pointer;
        transition: color $basic-animation;
        color: rgba($ds-offblack-600, 0.25);

        @include tablet-min {
            display: flex;
            color: rgba($ds-offwhite-800, 0.8);
        }

        &__bar {
            width: 4px;
            height: 4px;
            margin-top: 11px;
            margin-bottom: 11px;
            background-color: rgba($ds-offwhite-600, .5);
            overflow: hidden;
            position: relative;
            display: none;

            @include tablet-min {
                display: block;
            }
        }

        &__progress {
            position: absolute;
            height: $BAR_END_HEIGHT;
            width: 100%;
            left: 0;
            top: 0;
            transform: translateY(-$BAR_END_HEIGHT);
            background: $ds-offwhite-600;
        }

        &:hover {
            @include tablet-min {
                color: $ds-offwhite-800;
            }
        }

        &.active-item {
            color: $ds-offblack-600;

            @include tablet-min {
                font-weight: $font-weight-bold;
                color: $ds-offwhite-800;
            }
        }
    }

    &--dark {
        .features__navigation {
            background-color: #4C4341;

            @include tablet-min {
                background-color: unset;
            }
        }

        .navigation-item {
            color: $ds-grey-800;

            @include tablet-min {
                color: $ds-offblack-600;
            }

            &:hover {
                @include tablet-min {
                    color: $ds-offblack-800;
                }
            }

            &.active-item {
                color: $ds-offwhite-600;

                @include tablet-min {
                    color: $ds-offblack-600;
                }
            }
        }

        .navigation-item__progress {
            background: $ds-offblack-600;
        }

        .navigation-item__bar {
            background-color: rgba($ds-offblack-600, .5);
        }
    }

    &__mobile-bar {
        position: relative;
        width: 100%;
        height: 2px;
        background-color: #93463D;

        @include tablet-min {
            display: none;
        }
    }

    &__mobile-progress {
        position: absolute;
        height: 100%;
        left: 0;
        top: 0;
    }
}


