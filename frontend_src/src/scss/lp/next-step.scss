.next-step {
    &__item {
        &:hover {
            .next-step__title {
                color: $ds-orange-900;
            }
            .next-step__image {
                transform: scale(1.1);
            }
        }
    }
    &__picture {
        width: 100%;
        border-radius: $border-radius-xl;
        overflow: hidden;
    }
    &__image {
        @include size(100%, auto);
        object-fit: cover;
        object-position: center;
        transition: transform $basic-transition;
    }
    &__title {
        position: absolute;
        top: 0;
        left: 0;
        transition: color $basic-transition;
    }
    &__carousel {
        &__item {
            width: 294px;

            @include tablet-min {
                width: 288px;
            }
        }
    }
}
