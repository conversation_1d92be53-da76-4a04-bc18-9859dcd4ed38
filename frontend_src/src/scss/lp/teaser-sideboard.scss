.teaser-sideboard {
    display: flex;
    flex-direction: column;

    @include desktop-min {
        flex-direction: row;
    }

    &__checkbox {
        margin-bottom: 88px;

        @include tablet-min {
            margin-bottom: 0;
        }

        &.footer-rodo {
            .fast-checkbox-icon-selected {
                left: 5px;
                top: 2px;

                .noselect {
                    width: 10px;
                }
            }
        }
    }

    &__content {
        position: relative;
        width: calc(100% - 32px);
        z-index: 2;

        @include desktop-min {
            width: calc(100% - 96px);
        }

        @include desktop-air {
            width: calc(100% - 176px);
        }

        .hero-middle__caption {
            transform: translateY(0);
            top: 0;
            position: relative;

            @include desktop-min {
                top: 50%;
                transform: translateY(-50%);
                position: absolute;
            }
        }

        .btn-cta {
            margin: 48px 0 -80px;
            order: 2;

            @include tablet-min {
                margin: 0;
            }
        }

        .parsley-errors-list {
            order: 3;
            width: 100%;

            .parsley-type,
            .parsley-required {
                margin: -40px 0 0;
                padding: 2px 0;

                @include tablet-min {
                    margin: 0;
                }
            }
        }

        .form-group {
            flex-direction: column;
            flex-wrap: wrap;

            @include tablet-min {
                flex-direction: row;
                align-items: center;
            }

            .subscribe-input-field {
                @include size(100%, 46px);
                max-width: 313px;
                border: 0;
                border-radius: $border-radius;
                font-size: 16px;
                padding: 0 16px;

                @include tablet-min {
                    max-width: 268px;
                }

                @include desktop-air {
                    max-width: 379px;
                }

                &.parsley-error {
                    margin-bottom: 40px;

                    @include tablet-min {
                        margin-bottom: 0;
                    }
                }

                &::placeholder {
                    color: $ds-offblack-600;
                }
            }
        }

        .text-orange {
            text-decoration: none;
        }

        .fast-checkbox {
            @include size(20px, 20px);
            background-color: transparent;
            border-color: $ds-grey-900;
            box-sizing: border-box;

            &.selected {
                background-color: transparent;
                border-color: $ds-grey-900;

            }

            &:hover:not(.selected) {
                border: 1px solid $ds-offblack-600;
                background-color: transparent;
            }
        }
    }

    &__title {

        @include tablet-min {
            line-height: 1;
        }

        @include desktop-air {
            line-height: 1.05;
        }

        .break {
            @include tablet-min {
                display: none;
            }

            @include desktop-min {
                display: block;
            }
        }
    }

    &__paragraph {
        max-width: 310px;

        @include tablet-min {
            max-width: 448px;
        }

        @include desktop-min {
            max-width: 377px;
        }

        @include desktop-air {
            max-width: 537px;
        }
    }

    &__image {
        min-height: 197px;
        height: auto;
        width: 100%;

        @include tablet-min {
            min-height: 256px;
        }

        @include desktop-min {
            min-height: 286px;
        }

        @include desktop-air {
            min-height: 418px;
        }

        .hero-middle__picture {
            width: auto;
            margin-left: auto
        }

        @include desktop-min {
            width: 100%;
        }

        .hero-middle__photo {
            object-fit: contain;
            object-position: center right;
        }
    }
}
