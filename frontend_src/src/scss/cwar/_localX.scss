.localX-overlay {
    display: flex;
    width: 48px;
    height: 20px;
    position: relative;
    overflow: hidden;

    svg {
        position: absolute;
    }
}

.single-localX {
    @extend .c-button;
    padding: 0;
    width: 67px;
    margin: auto;

    .localX-overlay {
        box-sizing: content-box;
        padding: 6px 0;
        margin: auto;

        svg {
            rect {
                &.active {
                    fill: $ds-orange-600;
                }

                stroke: $ds-orange;
            }
        }
    }
}

.localX-wrapper {
    .toggle-button,
    .overlay {
        padding: 8.5px 7px;
    }

    .toggle-button {
        &.active {
            .localX-overlay {
                svg {
                    rect {
                        &.active {
                            fill: $ds-orange-600;
                        }

                        stroke: $ds-orange;
                    }
                }
            }
        }
    }

    &__button {
        width: 46px;
        height: 20px;
    }
    
    &__image {
        display: none;
    }
}

.toggle-button {
    .localX-overlay {
        box-sizing: content-box;
        svg {
            rect {
                &.active {
                    fill: $ds-grey-800;
                }
            }
        }
    }
}

