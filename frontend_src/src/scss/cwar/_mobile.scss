#wardrobeConfigurator,
#cwatcolConfigurator {
  .active-component-canvas-wrapper {
    position: relative;
  }

  .canvas-header {
    position: absolute;
    left: 0;
    top: 0;
  }

  .mobile-canvas-global-indicators {
    position: absolute;
    right: 0;
    bottom: 10px;
  }

  .canvas-price {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    @include mobile {
      padding-top: 4px;
      padding-bottom: 4px;
    }
  }

  .mobile-indicators {
    .c-button-round {
      border-color: $ds-white;

      &.active {
        border-color: $ds-grey-900 !important;
      }
    }
  }

  .controls-mobile {
    .tylko-tabs-wrapper {
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 5px;
    }
  }

  .active-component-content-wrapper {
    .tylko-tabs-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  @include mobile {
    .thumbs-wardrobe-padding {
      width: calc(100% + 47px);
      margin-left: -10px;

      .tylko-tabs-container {
        //padding-left: 16px;
        margin-top: -10px;
      }
    }
    .thumbs {
      .tylko-tabs-wrapper {
        display: block;
      }

      .thumbs-wrapper:first-child {
        padding-left: 26px !important;
      }
    }
  }

  .additional-tab-item {
    transition: all 0.5s, opacity 0.1s linear 0.4s;
    display: inline-block;
  }

  .additional-tab-enter,
  .additional-tab-leave-to {
    opacity: 0;
  }

  .additional-tab-leave-to {
    transition: all 0.5s, opacity 0.1s linear;
  }

  .additional-tab-leave-active {
    position: absolute;
  }

  .localX-mobile-container {
    .circle {
      width: 4px;
      height: 4px;
      background-color: $ds-grey-900;
      border-radius: 50%;
      display: inline-block;
      margin-bottom: 3px;
    }
  }

  .prev-component,
  .next-component {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }

  .prev-component {
    left: 0;
  }

  .next-component {
    right: 0;
  }

  .interior-section-info {
    min-height: 40px;
  }
}
