.wrapper-loader {
    height: 160px;
    width: 160px;
    display: inline-flex;
    align-items: flex-end;
    justify-content: center;
}

#pdp-cplus {
    background: $ds-white;
    min-height: calc(100vw + 250px);
    position: relative;
    @include desktop-min {
        background: #f0f0f0;
        min-height: auto
    }

    #app {
        opacity: 0;
        transition: opacity .3s ease-out 1.5s;

        &.visible {
            opacity: 1;
        }
    }

    .spinner {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        transition: opacity .3s ease-out 1.5s;
        opacity: 1;

        &.hidden {
            opacity: 0;
        }
    }

    .spinner > div {
        width: 18px;
        height: 18px;
        background-color: #ff3c00;

        border-radius: 100%;
        display: inline-block;
        -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
        animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    }

    .spinner .bounce1 {
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
    }

    .spinner .bounce2 {
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
    }

    @-webkit-keyframes sk-bouncedelay {
        0%, 80%, 100% {
            -webkit-transform: scale(0)
        }
        40% {
            -webkit-transform: scale(1.0)
        }
    }

    @keyframes sk-bouncedelay {
        0%, 80%, 100% {
            -webkit-transform: scale(0);
            transform: scale(0);
        }
        40% {
            -webkit-transform: scale(1.0);
            transform: scale(1.0);
        }
    }

}

.noselect {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Old versions of Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none;
    /* Non-prefixed version, currently
                                     supported by Chrome, Opera and Firefox */
}

.price-wrapper-mobile {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include mobile {
      padding-top: 4px;
      padding-bottom: 4px;
    }
}

.fade-leave-active {
    transition: opacity .1s ease-out;
}

.fade-enter-active {
    transition: opacity .1s ease-in;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

.fade-slow-leave-active {
    transition: opacity .2s ease-out;
}
.fade-slow-enter-active {
    transition: opacity .2s ease-in;
}

.fade-slow-enter, .fade-slow-leave-to {
    opacity: 0;
}

.fade-carousel-leave-active {
    transition: opacity .5s ease-out;
}
.fade-carousel-enter-active {
    transition: opacity .1s ease-in;
}

.fade-carousel-enter, .fade-carousel-leave-to {
    opacity: 0;
}


$configurator-width-md: calc(100% - (393px + 16px - 32px));
$configurator-width-xl: calc(100% - (393px + 32px - 32px));
$configurator-width-2xl: calc(100% - (420px + 74px - 32px));
$configurator-width-3xl: calc(100% - (420px + 74px - 32px));


#pdp-cplus {
    @include desktop-min {
        min-height: calc(((100vw - (393px + 16px - 32px)) * 0.74) - 10px);
    }
    @include basic-desktop-min {
        min-height: calc(((100vw - (393px + 32px - 32px)) * 0.58) - 9px);
    }
    @include desktop-air {
        min-height: calc(((100vw - (420px + 74px - 32px)) * 0.63) - 10px);
    }
    @include desktop-xl {
        min-height: calc(((100vw - (420px + 74px - 32px)) * 0.56) - 10px);
    }
}

.configurator {
    @include desktop-min {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
    }

    &.cplus-loader {
        position: absolute !important;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        align-items: stretch;
        flex-direction: row;
        justify-content: flex-start;
        transition: opacity $short-transition;

        .cplus-loader-copy {
            margin-top: -18px;
            text-align: center;
        }

        @include mobile {
            display: block;
            z-index: 1;
            margin-top: -46px;
            .cplus-loader-copy {
                margin-top: -11px
            }
            .skeleton-wrapper {
                margin-left: 0;

                img {
                    width: 100%;

                }
            }
            .cplus-loader-container {
                margin-top: 43px;
                height: 100vw;
            }
        }
    }


    .cplus-loader-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        @include desktop-min {
            width: calc(100% - (393px + 16px - 32px));
            height: calc(((100vw - (393px + 16px - 32px)) * 0.74) - 10px);
        }
        @include basic-desktop-min {
            width: calc(100% - (393px + 32px - 32px));
            height: calc(((100vw - (393px + 32px - 32px)) * 0.58) - 9px);
        }
        @include desktop-air {
            width: calc(100% - (420px + 74px - 32px));
            height: calc(((100vw - (420px + 74px - 32px)) * 0.63) - 10px);
        }
        @include desktop-xl {
            width: calc(100% - (420px + 74px - 32px));
            height: calc(((100vw - (420px + 74px - 32px)) * 0.56) - 10px);
        }
    }

    .skeleton-wrapper {
        margin-left: -32px;

        @include desktop-min {
            width: 393px;
        }
        @include desktop-air {
            width: 420px;
        }
    }

    .skeleton {


    }

    @include desktop-min {
        position: relative;
    }

    .configurator-wrapper {
        position: relative;
        @include desktop-min {
            margin-top: 0;
            width: $configurator-width-md;
        }
        @include basic-desktop-min {
            width: $configurator-width-xl;
        }
        @include desktop-air {
            width: $configurator-width-2xl;
        }
        @include desktop-xl {
            width: $configurator-width-3xl;
        }


        canvas {
            display: block;
            width: 100%;
            max-width: 100%;
        }

        .interaction-layer {
            position: absolute;
            top: 0;
            left: 0;
        }

        @include desktop-min {
            .mask {
                pointer-events: none;
                position: absolute;
                display: block;
                z-index: 1;

                &.mask-right {
                    right: 0;
                    top: 0;
                    width: 10%;
                    height: 100%;
                    background: linear-gradient(to left, rgba(240, 240, 240, 1) 0%, rgba(240, 240, 240, 0) 100%);
                }
            }
        }
    }

    .desktop-controls-wrapper {
        position: relative;
        margin-left: -32px;
        z-index: 1;
        @include desktop-min {
            width: 393px;
        }
        @include basic-desktop-min {
            width: 393px;
        }
        @include desktop-air {
            width: 420px;
        }
        @include desktop-xl {
            width: 420px;
        }

        .tooltip-wrapper {
            position: absolute;
            right: calc(100% - -8px);
            top: 90px !important;
            display: flex;
            flex-direction: column;
            &--no-outline {
                outline: none;
            }
        }

    }

    .configurator-modal {
        canvas {
            max-width: 100%;
            max-height: 100vw;
        }
    }

    .cplus-divider {
        width: 100%;
        border-top: 1px solid $ds-grey-600;
    }

    .pdp-2018-tooltip-wrapper {
        .pdp-2018-tooltip {
            margin-left: -80px;
            border: 2px solid $ds-grey-800;
            &.depth {
                margin-left: 0;
            }
        }
    }
    .column-width {
        margin-left: -110px !important;
    }
}

.configurator-collapse {
    &.show {
        .collapse-trigger {
            svg {
                transform: rotate(180deg);
            }
        }
    }

    .collapse-trigger {
        svg {
            transition: transform 0.2s ease;
        }
    }
}
#wardrobeConfigurator {
    .thumbs-wardrobe-padding {
        min-height: 126px;
    }
}
#wardrobeConfigurator, #cwatcolConfigurator {
    .thumbs-wardrobe-padding {
        position: relative;
        .prev-component,
        .next-component {
            top: 50%;
            transform: translateY(-50%);
            margin: 0;
            position: absolute;
        }
    }
}
.custom-slider-column {
    flex-basis: 70%;
    max-width: 70%;
}

