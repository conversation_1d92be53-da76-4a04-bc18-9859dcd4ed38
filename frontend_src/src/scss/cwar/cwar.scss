@import "../utils/_variables.scss";
@import "../utils/_mixins.scss";
@import "../common-design-system/_base.scss";
@import "../common-design-system/_typography.scss";
@import "../configurator/_configurator-loader.scss";
@import "../configurator/_tylko-snackbar.scss";
@import "../configurator/_slider.scss";
@import "../configurator/_tylko-snackbar.scss";
@import "../configurator/_slider-mock";
@import "../configurator/tylko-link";
@import "../configurator/_tylko-payment-details-button.scss";
@import "../configurator/_adjust-buttons.scss";
@import "../configurator/_colors-wrapper.scss";

//configurator-wardrobe
@import "_configurator.scss";
@import "../cplus/_buttons.scss";
@import "../cplus/_slider.scss";
@import "../cplus/_toggle.scss";
@import "../cplus/_toggle-checkbox.scss";
@import "../cplus/_stepper.scss";
@import "../cplus/_local-edge.scss";
@import "../cplus/_active-component.scss";
@import "../configurator/_tooltip.scss";
@import "../pdp/_pdp-elevator-pitch.scss";
@import "./_panel-toggle.scss";
@import "./_component-switcher.scss";
@import "./_height-slider.scss";
@import "./_dna-switch.scss";
@import "./_mobile.scss";
@import "./_localX.scss";
@import "../configurator/_tylko-collpase.scss";
@import "./_controls-desktop.scss";
@import "./_wardrobe-carousel.scss";
@import "./_active-component-mobile.scss";
@import "./_additional-elements.scss";
@import "../configurator/_thumbnails-gallery.scss";
@import "../crow/mobile-dimensions.scss";
@import "../configurator/column-stepper";

@import "./_cvert.scss";
