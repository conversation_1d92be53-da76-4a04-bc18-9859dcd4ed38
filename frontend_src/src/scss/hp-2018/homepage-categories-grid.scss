.homepage-categories-grid {
    &__product {
        &-wrapper {
            overflow: hidden;
            border-radius: $border-radius-xl;
            // it fixes composition of overflow hidden and border radius on safari
            clip-path: content-box;
        }
        &-link:hover {
            .homepage-categories-grid {
                &__image {
                    transform: scale(1.03);
                    transition: transform $basic-transition;
                }
                &__product-title {
                    color: $ds-orange-900;
                    transition: color $basic-transition;
                }
            }
        }
    }
    &__image {
        width: 100%;
        display: block;
        // hackish trick to remove border on the edges of the new label
        transform: translateZ(0);
        &--has-new-label {
            border-top-left-radius: 40px;
        }
    }
}
