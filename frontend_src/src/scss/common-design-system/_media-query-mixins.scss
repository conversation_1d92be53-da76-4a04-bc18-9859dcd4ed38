//extra media
$size-desktop-xl: 1680px;

@mixin media-query-prefix($prefix: $media-names, $sizes: $media-sizes) {
    @for $i from 1 through length($prefix) {
        @media only screen and (min-width: #{nth($sizes, $i)}) {
            .#{nth($prefix, $i)} {
                @content
            }
        }
    }
}

@mixin mobile-middle {
  @media only screen and (min-width: #{$size-mobile-middle}) {
    @content;
  }
}

@mixin tablet-min {
  @media only screen and (min-width: #{$size-tablet-min}) {
    @content;
  }
}

@mixin desktop-min {
  @media only screen and (min-width: #{$size-desktop-min}) {
    @content;
  }
}

@mixin basic-desktop-min {
  @media only screen and (min-width: #{$size-basic-desktop}) {
    @content;
  }
}

@mixin desktop-air {
  @media only screen and (min-width: #{$size-desktop-air}) {
    @content;
  }
}

@mixin desktop-xl {
  @media only screen and (min-width: #{$size-desktop-xl}) {
    @content;
  }
}

@mixin desktop-xl2 {
  @media only screen and (min-width: #{$size-desktop-xl2}) {
    @content;
  }
}
