/*

    Z-index levels:

    -10
        - util class for edge cases and hidden elements
    10
        - tooltip clouds
        - snackbars
        - magnifying glass tooltips
    20
        - sidebar cart
        - ribbons
        - navbars
    30
        - overlays behind modals
    40
        - notifications
    50
        - "please rotate your device" popup, popups

    -important flag should be used ONLY for overwriting inline styles
    set by third party packages, plugins etc.
*/

@for $i from 1 through 10 {
  .z-#{$i * 10} {
    z-index: $i * 10;
  }
  .z-#{$i * 10}-important {
    z-index: $i * 10 !important;
  }
}

@include media-query-prefix {
  @for $i from 1 through 10 {
    &\:z-#{$i * 10} {
      z-index: $i * 10;
    }
    &\:z-#{$i * 10}-important {
      z-index: $i * 10 !important;
    }
  }
}

.-z-10 {
  z-index: -10;
}

.-z-10-important {
  z-index: -10 !important;
}
