@each $size in $size-values {
  .mt-#{$size} {
    margin-top: #{$size}px;
  }
  .mr-#{$size} {
    margin-right: #{$size}px;
  }
  .mb-#{$size} {
    margin-bottom: #{$size}px;
  }
  .ml-#{$size} {
    margin-left: #{$size}px;
  }
  .mx-#{$size} {
    margin-left: #{$size}px;
    margin-right: #{$size}px;
  }
  .-mx-#{$size} {
    margin-left: #{$size * -1}px;
    margin-right: #{$size * -1}px;
  }
  .my-#{$size} {
    margin-top: #{$size}px;
    margin-bottom: #{$size}px;
  }
  .m-#{$size} {
    margin: #{$size}px;
  }

  .pt-#{$size} {
    padding-top: #{$size}px;
  }
  .pr-#{$size} {
    padding-right: #{$size}px;
  }
  .pb-#{$size} {
    padding-bottom: #{$size}px;
  }
  .pl-#{$size} {
    padding-left: #{$size}px;
  }
  .px-#{$size} {
    padding-left: #{$size}px;
    padding-right: #{$size}px;
  }
  .py-#{$size} {
    padding-top: #{$size}px;
    padding-bottom: #{$size}px;
  }
  .p-#{$size} {
    padding: #{$size}px;
  }
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-auto {
  margin-right: auto;
}

@include media-query-prefix {
  &\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
  &\:ml-auto {
    margin-left: auto;
  }
  &\:mr-auto {
    margin-right: auto;
  }
  @each $size in $size-values {
    &\:mt-#{$size} {
      margin-top: #{$size}px;
    }
    &\:mr-#{$size} {
      margin-right: #{$size}px;
    }
    &\:mb-#{$size} {
      margin-bottom: #{$size}px;
    }
    &\:ml-#{$size} {
      margin-left: #{$size}px;
    }
    &\:mx-#{$size} {
      margin-left: #{$size}px;
      margin-right: #{$size}px;
    }
    &\:my-#{$size} {
      margin-top: #{$size}px;
      margin-bottom: #{$size}px;
    }
    &\:m-#{$size} {
      margin: #{$size}px;
    }

    &\:pt-#{$size} {
      padding-top: #{$size}px;
    }
    &\:pr-#{$size} {
      padding-right: #{$size}px;
    }
    &\:pb-#{$size} {
      padding-bottom: #{$size}px;
    }
    &\:pl-#{$size} {
      padding-left: #{$size}px;
    }
    &\:px-#{$size} {
      padding-left: #{$size}px;
      padding-right: #{$size}px;
    }
    &\:py-#{$size} {
      padding-top: #{$size}px;
      padding-bottom: #{$size}px;
    }
    &\:p-#{$size} {
      padding: #{$size}px;
    }
  }
}
