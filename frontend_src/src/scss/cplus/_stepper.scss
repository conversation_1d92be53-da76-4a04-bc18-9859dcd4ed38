.cplus-stepper,
.slider-controls,
.configurator-wrapper {
    .minus-button,
    .plus-button {
        cursor: pointer;
        position: relative;
        outline: none;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid $ds-grey-800;
        transition: background-color $short-transition,
                    border-color $short-transition;
        @include desktop-min {
            &:hover,
            &.extended {
                border-color: $ds-orange-900;
                &:before,
                &:after {
                    background-color: $ds-orange;
                }
                svg {
                    path {
                        fill: $ds-orange;
                    }
                }

            }
        }
        &:active {
            border-color: $ds-orange;
            &:before,
            &:after {
                background-color: $ds-orange;
            }
            svg {
                path {
                    fill: $ds-orange;
                }
            }
        }
        &.disabled {
            background-color: transparent;
            border-color: $ds-grey-600;
            cursor: default;
            &:before,
            &:after {
                background-color: $ds-grey-800 !important;
            }
            svg {
                path {
                    fill: $ds-grey-800 !important;
                }
            }
        }
        &:before {
            transition: background-color $short-transition;
            position: absolute;
            content: '';
            background-color: $ds-offblack-600;
            width: 14px;
            height: 2px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        svg {
            path {
                transition: fill $short-transition;
                fill: $ds-offblack-600;
            }
        }
    }

    .plus-button {
        &:after {
            transition: background-color $short-transition;
            position: absolute;
            content: '';
            background-color: $ds-offblack-600;
            width: 2px;
            height: 14px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
    &.custom-images {
        button {
            &:before,
            &:after {
                display: none;
            }
            &:last-of-type {
                svg {
                    transform: rotate(180deg);
                }
            }
        }
    }
}

.cplus-stepper {
    border-radius: 30px;
    display: inline-flex;
    box-sizing: border-box;
    align-items: center;

    .step {
        text-align: center;
        width: 50px;
    }
}
.cplus-stepper,
.slider-controls {
    .minus-button,
    .plus-button {
        span {
            position: absolute;
            top: -11px;
            width: 46px;
            height: 52px;
        }
    }
    .plus-button {
        span {
            left: -4px;
        }
    }
    .minus-button {
        span {
            left: -9px;
        }
    }

}
