#pdp-cplus {
    .configurator-header {
        position: fixed;
        z-index: 2003;
        top: 0;
        height: 50px;
        box-sizing: border-box;
        background-color: $ds-white;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
            color: $ds-black;
        }
        .crossed-out-price-container {
          flex-direction: column;
          .cplus-crossed-out-price,
          .price-with-discount-wrapper {
            width: 100%!important;
            margin-top: -5px
          }
          .cplus-crossed-out-price {
            flex-direction: row;
            justify-content: end;
          }
          .crossed-out-price-copy, {
            display: none;
          }
          .price-with-discount {
            font-size: 16px!important;
          }
        }
        .promo-code,
        .promo-code-icon {
          display: none;
        }
        .price-coupon {
          font-size: 16px;
        }
        .promo-text {
          font-size: 10px;
        }
        .regular-price-text {
          font-size: 12px;
          color: #6F7173;
        }
        .regular-price---small {
          color: #000000;
        }
        .price-container {
          justify-content: end;
          padding-left: 5px;
        }
        .price-coupon__container {
          align-items: end;
        }
        .regular-price-text {
          margin-top: -5px
        }
    }

    .configurator-modal {
        position: fixed;
        overflow-y: scroll;
        box-sizing: border-box;
        max-height: 100%;
        background-color: white;
        width: 100%;
        z-index: 2002;
        padding-top: 60px;
        top: 0;
    }

    .active-component-wrapper {
        padding: 0 !important;
        padding-top: 16px !important;
        background-color: white;
        border-radius: 0;
        position: relative;
        &:before {
            height: 0;
            width: 32px;
            border: 1px solid $ds-grey-800;
            border-radius: 1.5px;
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
        }
    }

    .controls-wrapper {
        position: relative;
        &:before {

        }
    }

    .canvas-controls {
        position: relative;
        .control-btn {
            position: absolute;
            top: 8px;
            z-index: 1;
            &.prev-component {
                left: 16px;
            }

            &.next-component {
                right: 16px;
            }
        }
    }

    .back-button {
        background-color: white;
        outline: none;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .configurator-articles {
        .article-title {
            color: $ds-black;
        }

        .article-image {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
        }

        .article-text {
            color: $ds-offblack-600;
        }
    }

    .thumbs {
        .thumbs-wrapper {
            box-sizing: content-box;
            &:first-child {
                padding-left: 16px !important;
            }
            &:last-child {
                padding-right: 16px;
            }
            .thumbnail-icon--wardrobe {
                width: 34px;
                height: 91px;
            }
        }

        .container,
        .mini,
        img {
            width: 68px;
            height: 68px;
        }
    }
    .desktop-active-component {
        border: 2px solid $ds-grey-800;
        position: absolute;
        top: 0;
        width: auto;
        z-index: 4;
        transform: translateX(-50%);
        margin-top: 29px;
        padding: 16px 0 !important;
        .doors-direction {
            max-width: 384px;
        }
        .desktop-cell {
            justify-content: flex-start;
        }
    }
}
