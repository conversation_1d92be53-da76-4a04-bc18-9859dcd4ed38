.mobile-indicator-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    touch-action: none;
    pointer-events: none;

    .relative {
        position: relative;
        width: 26px;
        height: 26px;
        margin-top: -13px;
        margin-left: -13px;
    }

    .pulsar {
        border: 0;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: block;
        background-color: $ds-grey-600;
        position: relative;
        opacity: 0.85;
        &__img {
            position: relative;
            width: 36px;
            height: 36px;
        }
        &:after {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 1px solid $ds-orange;
            animation: borderPulse 1.5s infinite ease-out;            
        }
    }

    @keyframes borderPulse {
        0% {
            width: 18px;
            height: 18px;
            opacity: 0;
        }
        20% {
            opacity: 1;
        }
        70% {
            opacity: 0;
        }
        100% {
            width: 44px;
            height: 44px;
            opacity: 0;
        }
    }
}