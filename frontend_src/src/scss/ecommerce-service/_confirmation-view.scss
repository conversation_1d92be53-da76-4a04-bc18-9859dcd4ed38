.confirmation-page {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    &__overlay {
        position: absolute;
        top:0;
        left:0;
        width:100%;
        height:100%;
        z-index: 9;
        transition: opacity $basic-transition;
        &__text-wrapper {
            position: absolute;
            top: 25%;
            box-sizing: border-box;
            width: 100%;
        }
        &--hidden {
            opacity: 0;
            visibility: hidden;
        }
        @include desktop-min {
            &__text-wrapper {
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
    &__summary {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow-y: scroll;
        &__graphic {
            overflow: hidden;
            width: 100%;
            height: 216px;
            &__img {
                height: 100%;
                object-fit: cover;
            }
        }
        &__content {
            width: 100%;
            &__header,
            &__body {
                text-align: center;
            }
        }
        .clipboard {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            .input-container {
                position: relative;
                width: 100%;
                border-radius: $border-radius;
                overflow: hidden;
                .clipboard__input {
                    height: 50px;
                    border: 1px solid $ds-grey-800;
                    border-radius: $border-radius;
                    width: 100%;
                    outline: none;
                    box-sizing: border-box;
                }
                &:after {
                    content: '';
                    position: absolute;
                    right: 1px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 100px;
                    height: calc(100% - 2px);
                    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
                    border-radius: $border-radius;
                }
            }
            &__button {
                position: relative;
                width: 30px;
                height: 100%;
                outline: none;
                cursor: pointer;
                svg path {
                    fill: $ds-offblack-600;
                    transition: opacity $basic-transition;
                }
                &__toast {
                    background-color: $ds-white;
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    white-space: nowrap;
                    border: 2px solid $ds-grey-600;
                    border-radius: $border-radius;
                    top: -45px;
                    opacity: 0;
                    transition: opacity $basic-transition;
                    display: flex;
                    svg path {
                        fill: none;
                    }
                }
                &.disabled {
                    pointer-events: none;
                    svg {
                        opacity: 0.3;
                    }
                }
                &:hover {
                    svg path {
                        fill: $ds-orange;
                    }
                    .clipboard__button__toast {
                        opacity: 1;
                        svg path {
                            fill: none;
                        }
                    }
                }
            }
        }
        .social-media {
            width: 100%;
            &__facebook,
            &__twitter {
                border:2px solid $ds-grey-600;
                border-radius: 30px;
                height:44px;
                width: 50%;
                cursor: pointer;
                outline: none;
                svg {
                    transition: opacity $basic-transition;
                }
                &:hover {
                    background-color: $ds-offwhite-600;
                }
                &.disabled {
                    pointer-events: none;
                    color: $ds-grey-800;
                    svg {
                        opacity: 0.3;
                    }
                }
            }
        }
    }
    @include desktop-min {
        &__summary {
            flex-direction: row;
            overflow: hidden;
            &__graphic {
                width: 50%;
                height: 100%;
            }
            &__content {
                width: 50%;
                &__header,
                &__body {
                    text-align: left;
                }
            }
        }
    }
}
