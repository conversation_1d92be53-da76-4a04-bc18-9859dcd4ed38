@import "../utils/_variables.scss";
@import "../common-design-system/_variables.scss";
@import "../common-design-system/_media-query-mixins.scss";
@import "../utils/_mixins.scss";

@import './_email-subscription-view.scss';
@import './_confirmation-view.scss';
@import './_wishlist-indicator.scss';
@import "../utils/_colors.scss";

.wishlist-modal-indicator__modal,
.save-my-design,
.confirmation-page,
.notifyMeModal__modal,
.ty-modal {
  @import "../yoda-forms/form-index.scss";
  @import './_ty-modal.scss';
  @import "../utils/_helpers.scss";
  @import "../common-design-system/legacy-ds-for-configurators/_distances.scss";
  @import "../common-design-system/_z-index.scss";
  @import "../components/_rodo-checkbox.scss";

  textarea, input {
    -webkit-appearance: none;
    border-radius: 0;
  }

  a, area, button, iframe, [tabindex].focus-visible, [tabindex]:focus-visible {
    outline: none !important;
    box-shadow: none;
  }

  .middle-xs {
    align-items: center;
  }
}

.notifyMeModal__modal {
  @import '../notify-me/_waiting-list.scss';
  @import '../notify-me/_notify-me.scss';
  @import '../notify-me/_expiration.scss';
}

.wishlist-modal-indicator__modal {
  @media screen and (min-width: 1024px) {
    .v--modal-overlay .v--modal-box {
      overflow-y: hidden;
    }
  }
}

.save-my-design__screenshot__img {
  max-width: none;
}

.v--modal-overlay {
  background: rgba(0, 0, 0, 0.7) !important;
}

.cta-modal-wrapper {
  .cta-modal__point {
    position: relative;
    display: flex;

    .svg-column {
      position: relative;
      width: 58px;

      svg {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 24px;
      }

      .img-uk {
        height: 28px;
        width: 28px;
      }
    }

    .content-column {
      width: 375px;
    }

    .svg-column,
    .content-column {
      display: inline-block;
    }
  }

  .cta-button {
    width: 190px;
    margin: 0 auto;
  }

  .mobile-scroll-area {
    height: calc(100vh - 470px);
    overflow-y: scroll;
    @media screen and (min-width: 1024px) {
      height: auto;
      overflow-y: visible;
    }
  }

  .desktop-scroll-area {
    height: auto;
    overflow-y: visible;
    @media (min-width: 1024px) {
      height: 450px;
      overflow-y: scroll;
    }
  }
}