.configure-badge {
    top: 0;
    right: 0;
    z-index: 1;
    border: 1px solid $ds-white;
    border-radius: 100px;
    height: 38px;
    box-sizing: border-box;
    transition: background-color $basic-transition;

    &__icon {
        @include size(20px, 20px);
        transition: color $basic-transition;
    }

    &__text {
        overflow: hidden;
        max-width: 0;
        transition: max-width $basic-transition, color $basic-transition;
    }
}

.configure-badge-hover {
    @include basic-desktop-min {
        &:hover {
            .configure-badge {
                background-color: $ds-white;

                &__icon {
                    color: $ds-orange;
                }

                &__text {
                    max-width: 175px;
                    margin-left: 8px;
                    margin-right: 4px;
                    color: $ds-orange;
                }
            }
        }
    }
}
