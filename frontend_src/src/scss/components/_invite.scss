section.invite {
  padding-top: $space*2;
  padding-bottom: $space;
}

#invite-form {
  padding-bottom: 127px;
}

.invite-login {
  position: absolute;
  top: 0px;
  z-index: 10;
  bottom: 0;
  padding: 60px 0px 80px;
  width: 100%;

  .form-label, label {
    color: #fff !important;

    a {
      color: inherit;
    }
  }

  input {

    color: #fff;

    &::-webkit-input-placeholder {
      color: #eee;
    }

    &:-moz-placeholder {
      /* Firefox 18- */
      color: #eee;
    }

    &::-moz-placeholder {
      /* Firefox 19+ */
      color: #eee;
    }

    &:-ms-input-placeholder {
      /* Firefox 19+ */
      color: #eee;
    }
  }

  .form input, .form input:focus, .form .is-focused.is-focused input, .checkbox, .radio,.form .is-focused.is-focused .checkbox  {
    border-color: #fff;
    background-color: transparent;
  }

  .form .is-focused.is-focused .checkbox .icon {
    fill: #fff;
  }
}

.invite-text {
  text-align: center;
  margin-bottom: 35px;
}

@media screen and (min-width: $size-tablet-portrait + 1px) {
  .invite-text {
    padding: 0 $space;
  }
}