.storage-button {
  position: absolute;
  height: 36px;
  background-color: white;
  border: 2px solid $ds-grey-800;

  &:not(.extended) {
    border-color: white;
  }

  &__top,
  &__bottom {
    transform: translate(-50%, -50%);
  }

  &__top {
    margin-top: -15px;
  }

  &__bottom {
    margin-top: 15px;
  }

  &.disabled {
    background-image: url('/r_static/basic-pdp/storage-button-info.svg');
    background-repeat: no-repeat;
    background-size: 20px;
    background-position: 6px;
  }

  .tylko-extend-btn {
    transition: color $short-transition;
  }

  &:hover,
  &:active {
    .tylko-extend-btn {
      color: $ds-grey-900;
    }
  }

  &.plus-button {
    &:hover,
    &:active {
      &:after,
      &:before {
        background-color: $ds-grey-900;
      }
    }

    &:active {
      border-color: $ds-grey-700;
    }
  }
}