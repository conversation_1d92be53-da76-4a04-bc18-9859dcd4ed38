/* Global typography settings & Webfonts */

a {
    color: $color-text;
    text-decoration: none;
}

p {
    color: $color-text;
    @include font(15, 19);
    a {
      text-decoration: underline;
    }
}

em {
  color: $color-high;
}

* + p {
    padding-top: 5/4 * 1em;
}


// heading styles

.heading-0 {
  @include font(64, 68);
  letter-spacing: -.5px;
}

.heading-1 {
  @include font(50, 54);
  letter-spacing: -.5px;
  color: $t-grey-1300;
}

.heading-15 {
  @include font(38, 42);
  letter-spacing: -.5px;
  color: $t-grey-1300;
}

.heading-2 {
  @include font(26, 30);
  letter-spacing: -.5px;
  color: $t-grey-1300;
}

.heading-2-alt {
  @include font(26, 30);
  letter-spacing: -.5px;
  color: $t-grey-1300;
}

.heading-3, .heading-3 p {
  @include font(17, 23);
}

.heading-4 {
  color: $color-gray;
  @include font(15, 19);
}

* + {
  .button {
    margin-top: $space - 4px;
  }
}


* + {
  .heading-1, .heading-2, .heading-2-alt, .heading-3, .heading-4 {
    padding-top: $space - 4px;
  }
}

.heading-1, .heading-2, .heading-3, .heading-4, .heading-2-alt {
  &.is-high {
    color: $color-high;
  }
  &.is-gray {
    color: $color-gray;
  }
}


// font modifiers

.font-black {
  color: $color-black;
}

.font-bold {
  font-weight: bold;
}

.font-red {
  color: $color-high !important;
}

.font-white {
  color: $color-bg;
}

.font-gray {
  color: $color-text !important;
}

// lists

.list {
  color: $color-gray;
  li {
    padding-left: 1em;
    position: relative;
    &:before {
      position: absolute;
      content: "\2013";
      top: 0;
      left: 0;
    }
  }
}

// donwloads

.download {
  text-decoration: underline;
  @include inline-block;
  padding-top: 0.75em;

  & + .download {
    margin-left: 0.5em;
  }
}



@media screen and (max-width: $size-tablet-portrait ) {

  .mobile-font-black.mobile-font-black.mobile-font-black.mobile-font-black {
    color: $color-black;
  }

  .heading-0 {
    @include font(30, 34);
  }

  .heading-1 {
    @include font(22, 25);
  }

  .heading-2 {
    @include font(22, 26);
  }


  .heading-3, .heading-3 p, .heading-4, .heading-2-alt {
    @include font(13, 16);
  }

  .mobile-hidden {
    & + p {
      padding-top: 0;
    }
  }


  .list,
  p {
    @include font(14, 20);
  }
}
