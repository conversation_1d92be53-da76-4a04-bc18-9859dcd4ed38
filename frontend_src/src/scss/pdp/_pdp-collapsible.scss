@import "_pdp-shared.scss";

&.collapsible {
  background-color: $color-gray-background;
  width: 100%;
  box-sizing: border-box;

  .wrapper {
    min-height: auto;
    background-color: $color-gray-background;
  }

  ul.values {
    margin-bottom: 0;

    li {
      display: inline-block;
      width: 50%;
      margin-bottom: 16px;

      p {
        padding-bottom: 0 !important;

        &:first-of-type {
          padding-bottom: 2px !important;
        }
      }
    }
  }

  label {
    color: $color-black;
    margin-bottom: 0;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
  }

  p {
    width: 100%;
    padding-bottom: $ts-m
  }

  .tab {
    position: relative;
    width: 100%;
    color: #fff;
    overflow: hidden;

    .content {
      max-height: 0;
      overflow: hidden;
      transition: max-height .55s ease-in-out;
      background-color: $color-gray-background;

      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 5;
        opacity: 1;
        transition: opacity .55s ease-in-out;
        background: linear-gradient(0deg, #f0f0f0 22%, rgba(240, 240, 240, 0) 100%);
      }

      &-text {
        z-index: 2002;
        position: relative;
      }
    }

    &:last-of-type {
      label {
        margin-bottom: 0;
      }
    }
  }

  input {
    position: absolute;
    opacity: 0;
    z-index: -1;
  }

  label {
    display: block;
    cursor: pointer;
  }

  &.faq {
    .th-2-t,
    .th-3-m,
    .th-4-3,
    .normal-14 {
      width: calc(90% - 36px);
    }

    @media screen and (max-width: $size-desktop-min - 1px) {
      label::after,
      label::before,
      input[type=checkbox] + label::after,
      input[type=checkbox] + label::before,
      input[type=checkbox]:checked + label::after {
        top: 11px;
      }
    }
  }

  .th-5 {
    padding-bottom: $ts-s;
    @media screen and (min-width: $size-desktop-min) {
      padding-bottom: $ts-m;
    }
  }

  input:checked ~ .content {
    max-height: 100em;
    z-index: 5;

    &:after {
      opacity: 0;
      visibility: hidden;
      z-index: -1;
    }
  }

  input[type=checkbox] + label::after, input[type=checkbox] + label::before {
    content: '';
    position: absolute;
    width: 14px;
    height: 2px;
    display: block;
    background: $ds-grey-900;
    top: 42px;
    right: 5px;
    transition: all .35s;
    z-index: 5;

    @include desktop-min {
      width: 20px;
    }
  }

  input[type=checkbox] + label::after {
    transform: rotate(90deg);
  }

  input[type=checkbox] + label::before {
    transform: rotate(0deg);
  }

  input[type=checkbox]:checked + label::after {
    transform: rotate(180deg);
  }

  input[type=checkbox]:checked + label::before {
    transform: rotate(90deg);
    opacity: 0;
  }

  div.content {
    margin: 0 !important;
  }

  &.product-details {
    .plus-messina:after,
    .plus-messina:before {
    }

    .content {
      p {
        padding: 0 0 $ts-s 0;
      }

      .item-heading {
        padding: 0;
      }

      .th-5 {
        padding-bottom: $ts-xs;
        @media screen and (min-width: $size-desktop-min) {
          padding-bottom: $ts-m;
        }

      }
    }

    @media screen and (min-width: $size-desktop-min) {
      .width {
        order: 1;
      }
      .height {
        order: 2;
      }
      .depth {
        order: 3;
      }
      .features {
        order: 4;
      }
      .max-load {
        order: 5;
        display: block
      }
      .comp-load {
        order: 6;
      }
      .seams {
        order: 7;
      }
    }

    .tab-content-flex {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .tab-content-item {
      &.first-item-fix, .first-item-fix {
        ul {
          li {
            &:first-child {
              padding-left: 0;

              &:before {
                background-image: none !important;
              }
            }
          }
        }
      }

      ul,
      .tab-content-list-messina {
        padding-bottom: $ts-m;

        li {
          position: relative;
          padding-left: 18px;
          @include pdp-2018-tp-default($color-gray);

          &:before {
            position: absolute;
            top: 5px;
            @media screen and (min-width: $size-desktop-min) {
              top: 6px;
            }
            left: 0;
            content: '';
            background: url('/r_static/basic-pdp/ic_arrow.svg') no-repeat;
            height: 10px;
            width: 10px;
          }
        }
      }
    }

    .product-details-sublist {
      > li {
        display: block;
        width: 100%;

        &:not(:first-child) {
          margin-top: 31px;
        }
      }
    }

    .pdp-2018-assembly-gif {
      width: 100%;
      border-radius: 6px;
      border: 1px solid $color-gray-border;
      margin-bottom: $ts-s;
      max-width: 99%;
      @include desktop-min {
        margin-bottom: $ts-m;
        max-width: 300px;
      }

      &--single {
        max-width: 50%;
        @include desktop-min {
          max-width: 164px;
        }
      }

      &.bordered {
        border: 1px solid $color-gray-form;
        border-radius: $radius;
      }
    }

    .red-text {
      @include pdp-2018-th-5($color-error);
    }

    a {
      z-index: 2001;
      text-decoration: none;
      position: relative;
    }

    .arrow-right,
    .arrow-right-service {
      position: relative;
      padding-right: 12px;
      white-space: nowrap;

      &:after {
        position: absolute;
        top: 50%;
        right: 1px;
        transform: translateY(-50%);
        content: '';
        background-image: url('/r_static/basic-pdp/right.svg');
        height: 10px;
        width: 5px;
      }
    }
  }

  .animation-wrapper {
    position: relative;
    width: 100%;
    max-width: 302px;
    height: auto;
    border-radius: $border-radius;
    border: 1px solid $ds-grey-700;

    &__play {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 64px;
      height: 64px;
      border-radius: 50%;
      border: solid 3px $ds-white;
      background: rgba(124, 125, 129, 0.5);
      transform: translate(-50%, -50%);
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: opacity $animation-time $animation-easing;
      outline: 0;

      &--hidden {
        opacity: 0;
        visibility: hidden;
      }
    }
  }
}

@media screen and (min-width: $size-desktop-min) {

  &.collapsible {
    .th-2-t,
    .th-2-d,
    .th-4-3 {
      padding: 32px 0;
    }

    p {
      padding-bottom: 0;
    }

    label {

    }

    ul.values {
      li {
        display: inline-block;
        margin-bottom: 16px;

        p {
          padding-bottom: 0 !important;

          &:first-of-type {
            padding-bottom: 2px !important;
          }
        }
      }
    }

    .content > p {
      float: left;
      display: inline-block;
    }
  }
}
