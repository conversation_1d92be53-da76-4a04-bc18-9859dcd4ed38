@import "_pdp-shared.scss";

&.arranged-grid {
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-color: #fff;
    //background-position: center calc(100% + 1700px);
    @media screen and (max-width: $size-desktop-min - 1px) {
        background-image: url(/r_static/basic-pdp/bg-shapes/PDP_mobile_background_assembly.svg);
        background-position: center bottom;
    }
    @media screen and (min-width: $size-desktop-min) {
        padding: $tsp-mobile;
        background-image: url(/r_static/basic-pdp/bg-shapes/PDP_1280_background_assembly.svg)
    }

    h1 {
        color: $color-black;
        @media screen and (min-width: $size-desktop-min) {
            padding-bottom: 56px;
        }
    }

    .video-wrapper {
        @include video-rounded-wrapper();
        position: relative;
        z-index: 10;
    }

    .caption {
        p {
            padding-bottom: 40px;
        }
    }
    .broken-grid {
        position: relative;
        z-index: 10;
        .row {
            &:last-of-type {
                margin-bottom: 0;
            }
            h2 {
                @include no-bottom-space();
            }
            img {
                @include img-responsive(6px);
            }
            @media screen and (min-width: $size-desktop-min) {
                .img {
                    margin-bottom: $native-margin-desktop;
                }
            }

        }
    }
    @media screen and (min-width: $size-desktop-min) {
        padding: $tsp-desktop;
    }
}

&.animated-background {
    position: relative;
    //background: $color-pink;
    overflow: hidden;
    .container-cstm {
        z-index: 5
    }
    > .bg-shape {
        position: absolute;
        top: 0;
        left: 0;
        @media screen and (max-width: $size-desktop-min - 1px) {
            transform: translateX(-50%);
            left: 50%;
            &.bg-shape-2 {
                width: 600px;
                top: -693px;
                margin-left: -84px;
            }
            &.bg-shape-3 {
                top: -171px;
                margin-left: 109px;
            }
            &.bg-shape-4 {
                top: auto;
                bottom: -100px;
                width: 464px;
                margin-left: 10px;
            }
            &.bg-shape-5 {
                display: none;
            }
            &.bg-shape-6 {
                top: auto;
                bottom: 150px;
                margin-left: 718px;
            }
        }
        //transition: transform .2s cubic-bezier(.17,.67,.83,.67);
        @media screen and (min-width: $size-desktop-min) {
            &.bg-shape-2 {
                top: -1500px;
                left: -500px;
                z-index: 1;

                //animation: animRotateScale 25s infinite alternate linear;
            }
            &.bg-shape-3 {
                top: -1500px;
                left: 250px;
                z-index: 1;
                //animation: animRotateScale 25s infinite alternate linear;
            }

            &.bg-shape-4 {
                top: auto;
                bottom: -400px;
                left: auto;
                right: -28em;
                z-index: 1;
            }
            &.bg-shape-6 {
                top: 150px;
                left: auto;
                right: -22em;
                //transform: scaleY(0.9);
            }
            &.bg-shape-5 {
                //width: 100%;
                top: 200px;
                left: auto;
                right: -50em;
                //transform: scaleY(0.9);
                z-index: 3;
            }
        }
    }
    .animated-background::before {
        position: fixed;
        content: '';
    }
    .bg-shape::before {
        position: fixed;
        content: '';
    }
    .video-overlay {
        position: absolute;
        width: 100%;
        height: 100%;
        transition: opacity .3s ease;
        z-index: 11;
        cursor: pointer;

    }
    .video-overlay-play {
        cursor: pointer;
        display: block;
        border-radius: 50%;
        text-align: center;
        border: 2px solid #fff;
        background-color: rgba(124, 125, 129, 0.5);
        font-size: 20px;
        height: 64px;
        width: 64px;
        line-height: 64px;
        opacity: 0;
        @media screen and (min-width: $size-desktop-min) {
            font-size: 24px;
            height: 96px;
            width: 96px;
            line-height: 96px;
        }
        color: $color-bg;
        left: 50%;
        position: relative;
        top: 50%;
        transition: opacity .3s ease;
        transform: translate(-50%, -50%);
    }
    .video-overlay-play:hover {
        opacity: .5;
    }
}

@keyframes animRotateScale {
    0% {
        transform: rotate(-2deg) scale(1, 1);
    }
    100% {
        transform: rotate(2deg) scale(1, 1.2);;
    }
}

@keyframes animRotate {
    0% {
        transform: rotate(-10deg) scale(1, 1);
    }
    100% {
        transform: rotate(10deg) scale(1.1, 1.1);
    }
}

.type-01-img {
    width:100%;
}

&.assembly-type01 {
    background-image: url('/r_static/pdp/product-details/bg_02.jpg');
    background-size:100% 100%;
    background-repeat: no-repeat;
}
