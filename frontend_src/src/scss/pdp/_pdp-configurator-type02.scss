@import "_pdp-shared.scss";

#pdp-emily {
    h1.th-3 {
        @include pdp-2018-th-3();
        position: absolute;
        top: 32px;
        z-index: 2;
        @media screen and (min-width: $size-desktop) {
            top: 64px;
        }
    }
}

.usps.usps-taxes {
    .usps-taxes__icon {
        height: 28px;
        width: 28px;
    }
    .row {
        padding-top: $ts-s;
        justify-content: space-between;
        .tp-small {
            margin-left: $ts-xs / 2;
            color: $color-gray;
        }
        img {
            display: inline;
        }
    }
}

