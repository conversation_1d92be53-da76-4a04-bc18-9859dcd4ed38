.toggle_checkbox_container {
    .toggle_checkbox {
        display:inline-block;
        position:relative;
        width:14px;
        height:14px;
        margin-right:11px;
        outline:none;
        top:2px;
        cursor:pointer;
        transition:transform 300ms ease-out;
        transform:rotate(225deg);
        & + label { cursor:pointer; }
        &:after,
        &:before {
            content:'';
            position:absolute;
            width:100%;
            height:3px;
            background-color:$color-black;
            top:50%;
            border-radius:16px;
        }
        &:after { transform:translateY(-50%); }
        &:before { transform:translateY(-50%) rotate(-90deg); }
    }
    .margin-on-checked {
        margin-bottom: 32px;
    }
    .yoda-input {
        overflow:hidden;
        height:64px;
        transition:all 300ms ease-out;
        margin-top:16px;
        opacity: 1;
    }
    .toggle_checkbox:checked {
        & ~ .yoda-input {
            height:0;
            opacity: 0;
        }
        & ~ .margin-on-checked {
            margin-bottom: 0;
        }
        transform:rotate(90deg);
    }
}