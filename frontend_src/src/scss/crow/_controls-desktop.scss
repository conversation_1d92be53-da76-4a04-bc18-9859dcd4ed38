.controls-desktop {
    border-radius: 0 0 $border-radius-xl $border-radius-xl;
    &--nowrap {
        white-space: nowrap;
    }
}

.price-wrapper {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    column-gap: 8px;

    &__info-container {
        max-width: 160px;
    }

    &__list {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
    }
}

.shopping-buttons-wrapper {
    flex-wrap: wrap;
    display: flex;
}

.dna-switcher__desktop {    
    @include desktop-xl2 {
        width: calc(100% + 24px)!important;
      }
}

.tooltip-wrapper {
    top: 140px !important;
    display: flex;
    flex-direction: column;
    @include desktop-xl2 {
        top: 144px !important;
    }
    &--no-outline {
        outline: none;
    }
}