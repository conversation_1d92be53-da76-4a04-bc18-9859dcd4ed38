const Site = window.Site || {};

Site.videoWistia = (function() {
    function initialize() {
        const idWistia = 'z0g022ehd7';
        // eslint-disable-next-line no-underscore-dangle
        window._wq = window._wq || [];
        _wq.push({
            id: idWistia,
            onReady(video4) {
                const activeTimeLine = document.querySelector('.perfect-fit .timeline--active');
                const transitionValue = 'clip-path .35s linear';

                video4.bind('end', () => {
                    activeTimeLine.style.clipPath = 'inset(0 0 0 0)';
                    activeTimeLine.style.webkitClipPath = 'inset(0 0 0 0)';
                    setTimeout(() => {
                        activeTimeLine.style.transition = 'none';
                        video4.time(0);
                        video4.play();
                    }, 350);
                    // dirty hack to reset timeline without transition
                    setTimeout(() => {
                        activeTimeLine.style.transition = transitionValue;
                    }, 450);
                });

                video4.bind('timechange', e => {
                    activeTimeLine.style.clipPath = `inset(0 calc(100% - ${e / 15 * 100}%) 0px 0)`;
                    activeTimeLine.style.webkitClipPath = `inset(0 calc(100% - ${e / 15 * 100}%) 0px 0)`;
                });

                ScrollOut({
                    targets: '.perfect-fit .video-wrapper',
                    onShown() {
                        video4.play();
                    },
                    onHidden() {
                        video4.pause();
                    },
                });
            },
        });
    }

    return {
        initialize,
    };
}(jQuery));
