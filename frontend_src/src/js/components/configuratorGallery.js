const Site = window.Site || {};

Site.configuratorGallery = (function() {
    function initialize() {
        const carousel = document.querySelector('.configurator-gallery__carousel');
        const configuratorCardDouble = [...document.querySelectorAll('.configurator-gallery__card--double')];
        const configuratorCardSlimMobile = [...document.querySelectorAll('.configurator-gallery__card-single--slim-mobile')];
        const basic_url = '/r_static/configurator_gallery';

        const shelfType = document.querySelector('.configurator-gallery').dataset.type;
        const imgs = [...document.querySelectorAll('.configurator-gallery__card-single')];
        const deviceType = window.matchMedia('(min-width: 768px)').matches ? 'desktop' : 'mobile';


        function beginSlotSwap(item, url) {
            const imageWebp = [...item.querySelectorAll('.configurator-gallery__card-webp')];
            const imageJpg = [...item.querySelectorAll('.configurator-gallery__card-jpg')];
            const imgElement = item.querySelector('.configurator-gallery__card-image');

            if (deviceType === 'mobile') {
                imageWebp[0].srcset = `${url}.webp 1x, ${url}_2x.webp 2x`;
                imageJpg[0].srcset = `${url}.jpg 1x, ${url}_2x.jpg 2x`;
            } else {
                imageWebp[1].srcset = `${url}.webp 1x, ${url}_2x.webp 2x`;
                imageJpg[1].srcset = `${url}.jpg 1x, ${url}_2x.jpg 2x`;
            }
            imgElement.src = `${url}.jpg`;
        }

        const setupCarousel = function() {
            const flickity = new window.Flickity(carousel, {
                contain: true,
                pageDots: false,
                on: {
                    ready: () => {
                        window.dispatchEvent(new Event('resize'));
                    },
                    settle() {
                        window.Site.utils.manageCarouselButtons(carousel, '.configurator-gallery__card');
                    },
                },
            });
        };

        setupCarousel();
        window.addEventListener('resize', _.throttle(setupCarousel, 100));

        PubSub.subscribe('changeColorDna', (tag, { materialName, dnaName }) => {
            const patternName = ['01pSideboard', '01vSideboard', '02Sideboard'].includes(shelfType) && dnaName !== undefined ? `/${dnaName.toLowerCase()}` : '';

            imgs.forEach((item, index) => {
                const { photoType } = item.dataset;
                const imgIndex = String(index + 1).padStart(2, '0');
                beginSlotSwap(item, `${basic_url}/type${shelfType}${patternName}/${deviceType}/${photoType}/${materialName}/${imgIndex}`);
            });
        });

        if (configuratorCardDouble.length || configuratorCardSlimMobile.length) {
            window.addEventListener('resize', _.throttle(() => {
                const configuratorCardHeight = document.querySelector('.configurator-gallery__card-single').getBoundingClientRect().height;

                configuratorCardDouble.forEach(item => {
                    item.style.height = `${configuratorCardHeight}px`;
                });
                configuratorCardSlimMobile.forEach(item => {
                    item.style.height = `${configuratorCardHeight}px`;
                });
            }, 100));
        }
    }

    return {
        initialize,
    };
}(jQuery));
