/* Tracking component for fb, ga and rest */

const Site = window.Site || {};

window.trackCustomizationSent = false;

Site.Track = (function($) {
  const ecommerceData = {};
  const localData = {};

  const initialize = function() {
    trackPromoLandingPage();
    trackMatteBlackLandingPage();
    trackHomepage();
    trackSamples();

    $('.ecommerce-gtm-data').each((i, el) => {
      const element = $(el);
      if (!('impressions' in ecommerceData)) {
        ecommerceData.impressions = [];
      }
      ecommerceData.impressions.push(element.data('ecommerce-data'));
      element.parent().on('click', () => {
        trackProductClick(element.data('ecommerce-data'), element.data('ecommerce-list'));
      });
    });

    $('.tracking-container').each((i, el) => {
      const $el = $(el);

      if ($el.data('track-event') in Site.Track) {
        Site.Track[$el.data('track-event')]($el.data('track-data'));
        $el.removeAttr($el.data('track-event'));
      } else {
        // eslint-disable-next-line no-console
        console.warn(`tracking: ${$el.data('track-event')} not found;`);
      }
    });
  };

  const shelfTypes = [
    'type01',
    'type02',
    'type01v',
    'type03',
  ];

  const makeDataLayerPush = function(data) {
    if (typeof dataLayer !== 'undefined') {
      const mustHaveKeys = ['eventAction', 'eventLabel', 'eventValue'];
      mustHaveKeys.forEach(key => {
        if (('event' in data) && !(key in data)) {
          data[key] = undefined;
        }
      });
      window.dataLayer.push(data);

      if (window.is_webview) {
        try {
          webkit.messageHandlers.atupaleCallback.postMessage({ event: data.event });
        } catch (err) {
          // eslint-disable-next-line no-console
          console.log('error', err);
        }
      }
    }
    // console.log('dt: ', data); // eslint-disable-line no-console
  };

  const trackMegaMenuNavigation = function(data) {
    makeDataLayerPush({
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: 'megamenu',
      eventAction: 'features-filters',
      eventNonInteraction: false,
      ...data,
    });
  };

  const trackMegaMenuColor = function(data) {
    makeDataLayerPush({
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: 'megamenu',
      eventAction: 'color-filters',
      eventNonInteraction: false,
      ...data,
    });
  };

  const sendDataLayer = function(event, additional) {
    if (window.loadPresetFlag) {
      return;
    }
    if (typeof dataLayer !== 'undefined') {
      let eventObject = { event };
      if (typeof additional !== 'undefined') {
        eventObject = Object.assign(eventObject, additional);
      }
      Site.Track.makeDataLayerPush(eventObject);
    }
  };

  const sendFbq = function(event, uid, data) {
    // eslint-disable-next-line no-undef
    fbq(event, uid, data);
  };

  const trackViewedContent = function(data) {
    if (!$.isEmptyObject(ecommerceData)) { // add other jetties displayed on current page
      data.ecommerce = Object.assign({}, data.ecommerce, ecommerceData);
    }
    Site.Track.makeDataLayerPush(data);
  };

  const trackCheckoutStep = function(data) {
    data.ecommerce = Object.assign({}, data.ecommerce, ecommerceData);
    Object.defineProperty(data.ecommerce.checkout, 'products',
      Object.getOwnPropertyDescriptor(data.ecommerce, 'impressions'));
    delete data.ecommerce.impressions;
    Site.Track.makeDataLayerPush(data);
  };

  const trackTransaction = function(data) {
    data.ecommerce = Object.assign({}, data.ecommerce, ecommerceData);
    Object.defineProperty(data.ecommerce.purchase, 'products',
      Object.getOwnPropertyDescriptor(data.ecommerce, 'impressions'));
    delete data.ecommerce.impressions;
    Site.Track.makeDataLayerPush(data);
  };

  const trackAddToCart = function(data) {
    /* should be called as a callback after adding to cart (with returned data) */
    console.warn('trackAddToCart: we no longer track event: { ecommerce addToCart } -  removed from code');
  };

  const trackRemoveFromCart = function(data) {
    /* should be called as a callback after removing from cart (returned JettySerializer data) */
    Site.Track.makeDataLayerPush({
      event: 'ecommerce removeFromCart',
      ecommerce: {
        remove: {

          products: [data],
        },
      },
    });
  };

  const trackPaymentMethodSelection = function(paymentMethod) {
    if (typeof paymentMethod === 'undefined') {
      if (typeof localData.lastPaymentMethod === 'undefined') {
        paymentMethod = 'credit_card';
      } else {
        return;
      }
    }
    localData.lastPaymentMethod = paymentMethod;
    Site.Track.makeDataLayerPush({
      event: 'ecommerce checkoutOption',
      optionType: 'paymentMethod',
      ecommerce: {
        checkout_option: {
          actionField: {
            step: 2,
            option: `paymentMethod: ${paymentMethod}`,
          },
        },
      },
    });
  };

  const trackProductClick = function(productData, listName) {
    Site.Track.makeDataLayerPush({
      event: 'ecommerce productClick',
      ecommerce: {
        click: {
          actionField: {
            list: listName,
          },
          products: [
            productData,
          ],
        },
      },
    });
  };

  const trackChangedRows = function() {
    makeDataLayerPush({ event: 'confirm-row-height' });
  };

  const trackCustomization = function() {
    if (!window.trackCustomizationSent) {
      window.dataLayer.push({ event: 'customization' });
      window.trackCustomizationSent = true;
    }
  };

  const trackAddToWishlist = function() {
    Site.Track.makeDataLayerPush({
      event: 'add_to_wishlist',
    });
  };

  const trackShareItem = function() {
    if (typeof dataLayer !== 'undefined') {
      window.dataLayer.push({
        event: 'add_to_wishlist',
      }, {
        event: 'userInteraction',
        eventType: 'NOEEC',
        eventCategory: 'KP',
        eventAction: 'share',
        eventLabel: 'modal open',
        eventNonInteraction: false,
      });
    }
  };

  const trackInitiateCheckout = function() {
    Site.Track.makeDataLayerPush({ event: 'initiate_checkout' });
  };

  const trackPurchase = function() {
    if (window.isLocalStorage !== false && window.localStorage) {
      window.localStorage.removeItem('conf_data');
    }
    Site.Track.makeDataLayerPush({ event: 'purchase' });
  };

  const trackLead = function(sourceName) {

  };

  const trackCompleteRegistration = function() {
    Site.Track.makeDataLayerPush({ event: 'registration', eventAction: 'success' });
  };

  const trackLogin = function() {
    Site.Track.makeDataLayerPush({ event: 'log in', eventCategory: 'log in' });
  };

  const trackSendMeLink = function() {
    Site.Track.makeDataLayerPush({ event: 'send_me_link' });
  };

  const trackAppDownload = function(type) {
    Site.Track.makeDataLayerPush({
      event: 'get_app',
      eventAction: type,
    });
  };

  const trackSendInvite = function() {
    Site.Track.makeDataLayerPush({ event: 'send_invite' });
  };

  const trackSendContextCard = function() {
    Site.Track.makeDataLayerPush({ event: 'context_card' });
  };

  const assemblyECommerceFormat = function(data) {
    return {
      name: 'Paid Assembly',
      id: 1,
      price: data.price,
      category: 'service',
      quantity: 1,
    };
  };

  const sampleBoxECommerceFormat = function(data) {
    return {
      name: 'Samplebox',
      id: data.id,
      price: data.price,
      box_variant: data.box_variant,
      variant: `samplebox_${data.box_variant}`,
      samples: data.samples,
      category: 'samples',
      quantity: 1,
    };
  };

  const jettyECommerce = function(data) {
    if (!(data && window.ivy)) {
      return {};
    }
    /* used only on configurator page */
    if (!$.isEmptyObject(data.currency_code)) {
      ecommerceData.currencyCode = 'EUR';
    }
    const deliveryTimeReg = /(\d+) weeks/;
    const results = window.deliveryDate || deliveryTimeReg.exec($('.delivery_time_text').html());
    let deliveryTime = '';
    if (results) {
      // eslint-disable-next-line prefer-destructuring
      deliveryTime = results[1];
    }
    return {
      name: data.id,
      id: data.id,
      price: `${data.price}`,
      localCurrency: data.currency_code,
      localProductPrice: data.region_price,
      brand: data.pattern_name,
      category: data.category,
      variant: prepareVariantDescription(
        data.shelf_type,
        data.color_name,
        data.configurator_type,
        data.digital_product_version,
        data.physical_product_version,
      ),
      position: data.size_txt,
      imgPath: window.location.origin + data.preview,
      cart_item_status_cardboard: data.cardboards_number,
      cart_item_status_width: window.ivy.getWidth() / 10,
      cart_item_status_height: window.ivy.getHeight() / 10,
      cart_item_status_density: window.ivy.getProperty(),
      cart_item_status_depth: window.ivy.getDepth(),
      cart_item_status_material: data.color_name,
      cart_item_status_doors: window.ivy.data.doors.length || 0,
      cart_item_status_drawers: window.ivy.data.drawers.length || 0,
      timeToDelivery: deliveryTime,
      quantity: 1,
      dimension15: data.base_preset,
    };
  };

  const trackTelephoneClick = function() {
    Site.Track.makeDataLayerPush({ event: 'telephone' });
  };

  const trackRegionClick = function(regionName) {
    Site.Track.makeDataLayerPush({
      event: 'menu_regions',
      eventAction: 'country',
      eventLabel: regionName,
    });
  };

  const trackLanguageClick = function(language) {
    Site.Track.makeDataLayerPush({
      event: 'menu_regions',
      eventAction: 'language',
      eventLabel: language,
    });
  };

  const trackContact = function(contactSubject) {
    Site.Track.makeDataLayerPush({
      event: 'contact_form',
      eventAction: contactSubject,
    });
  };

  const trackFAQ = function(sectionName) {
    Site.Track.makeDataLayerPush({
      event: 'faq_menu',
      eventAction: sectionName,
    });
  };

  const trackReview = function(data) {
    Site.Track.makeDataLayerPush({
      event: 'add_review',
      eventAction: data.purpose,
      eventLabel: data.photo,
      eventValue: data.rating,
      eventCallback: data.callback,
    });
  };

  const trackCartPreview = function() {
    Site.Track.makeDataLayerPush({
      event: 'cart_preview',
    });
  };

  const trackSharing = function(sourceName, optionName) {
    Site.Track.makeDataLayerPush({
      event: sourceName,
      eventAction: 'shared',
      eventLabel: optionName,
    });
  };

  const sendMarketingAgree = function() {
    Site.Track.makeDataLayerPush({
      event: 'marketing_grant_1',
      marketing_grant: '1',
    });
  };

  const prepareVariantDescription = function(actualShelfType, material, configurator_type, digital_product_version, physical_product_version, instagrid) {
    const combined_variant = [];
    combined_variant.push(material);
    combined_variant.push(shelfTypes[actualShelfType]);
    combined_variant.push(configurator_type);
    combined_variant.push(digital_product_version);
    combined_variant.push(physical_product_version);
    if (instagrid) {
      combined_variant.push('image');
    }
    return combined_variant.join('|');
  };

  const trackCategoryMiniGrid = function(category) {

  };

  const getInstafeedItems = e => {
    const { target } = e;
    if (target && target.dataset && target.dataset.name) {
      onInstaGridClick(target.dataset.name);
    } else {
      const item = target.closest('.instafeed-main-carousel__link');
      if (item && item.dataset && item.dataset.name) {
        onInstaGridClick(item.dataset.name);
      }
    }
  };

  // PROMO PAGE
  const onCopyPromoCodeButtonClick = function() {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: window.full_category_name,
      eventAction: 'cta',
      eventLabel: 'copy-promo',
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onGetStartedButtonClick = function() {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: window.full_category_name,
      eventAction: 'cta',
      eventLabel: 'get-started',
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onExploreMoreButtonClick = function() {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: window.full_category_name,
      eventAction: 'cta',
      eventLabel: 'get-started',
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onShopTheSaleButtonClick = function() {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: window.full_category_name,
      eventAction: 'cta',
      eventLabel: 'shop-sale',
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onInstaGridClick = function(instagramName) {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: window.full_category_name,
      eventAction: 'insta-grid',
      eventLabel: instagramName,
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onProductGridClick = function(item, index) {
    const {
      id, furniture_category, configurator_type, digital_product_version, additional_dict,
    } = JSON.parse(item);
    const action = {
      event: 'select_item',
      ecommerce: {
        items: [{
          item_id: id,
          item_brand: additional_dict.brand,
          item_category: furniture_category,
          item_category2: `${configurator_type}_${digital_product_version}`,
          item_category3: undefined,
          item_category4: undefined,
          item_variant: `${additional_dict.variant}`,
          item_list_name: window.full_category_name,
          item_list_id: 'promo',
          index,
          quantity: 1,
          price: additional_dict.price,
        }],
      },
    };
    window.dataLayer.push(action);
  };

  const trackElement = ({ selector, fn, listener = 'click' }) => {
    const el = document.querySelector(selector);
    if (el) {
      el.addEventListener(listener, fn);
    }
  };

  const trackHpSliderImp = () => {
    const activeSlides = [...document.querySelectorAll('.inspire__board-carousel_swiper .swiper-slide-visible .board-card')];
    Site.Track.impressionTracking(activeSlides, 'hp-top-picks-carousel');
  };

  const trackPromoLandingPage = () => {
    if (!['promo-lp', 'no-promo-lp'].includes(window.full_category_name)) {
      return;
    }
    trackElement({ selector: '#copyPromo', fn: onCopyPromoCodeButtonClick });
    trackElement({ selector: '.txt-image__link', fn: onGetStartedButtonClick });
    trackElement({ selector: '.inspire__explore-button', fn: onExploreMoreButtonClick });
    trackElement({ selector: '.txt-button__button', fn: onShopTheSaleButtonClick });
    trackElement({ selector: '.instafeed-main-carousel', fn: getInstafeedItems });

    const getInspireItems = e => {
      const { target } = e;
      if (target.classList.contains('board-card')) {
        onProductGridClick(target.dataset.item, Number(target.dataset.index));
      } else {
        const item = target.closest('.board-card');
        if (item && item.dataset && item.dataset.item) {
          onProductGridClick(item.dataset.item, Number(item.dataset.index));
        }
      }
    };
    trackElement({ selector: '.inspire__board-carousel', fn: getInspireItems });
  };

  // MATTE BLACK
  const trackExploreGalleryClick = index => {
    const action = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: 'category-lp',
      eventAction: 'dna',
      eventLabel: index,
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(action);
  };

  const onButtonClick = (action, label, category) => function() {
    action = action || 'cta';
    category = category || window.full_category_name;

    const data = {
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      eventValue: undefined,
      eventNonInteraction: false,
    };
    window.dataLayer.push(data);
  };

  const impressionTracking = (impressions, list, contentful) => {
    let dataForGtm = null;

    if (contentful) {
      dataForGtm = impressions.map(i => {
        const itemData = i.dataset.item.replaceAll(/'/g, '"');

        return JSON.parse(itemData);
      });
    } else {
      dataForGtm = impressions.map(i => JSON.parse(i.dataset.item));
    }

    const itemPositions = impressions.map(i => i.dataset.index);
    const impressionsGtm = [];

    impressions.forEach((el, index) => {
      impressionsGtm.push({
        name: dataForGtm[index].id,
        id: dataForGtm[index].id,
        price: dataForGtm[index].additional_dict.price,
        brand: dataForGtm[index].additional_dict.brand,
        category: dataForGtm[index].furniture_category,
        variant: `${dataForGtm[index].additional_dict.material}|${shelfTypes[dataForGtm[index].shelf_type]}|${dataForGtm[index].configurator_type}|${dataForGtm[index].additional_dict.ppv}|${dataForGtm[index].digital_product_version}`,
        list,
        position: itemPositions[index],
      });
    });
  };

  const onProductClick = (product, list) => function() {
    const {
      id, furniture_category, configurator_type, digital_product_version, shelf_type, additional_dict: {
        price, brand, ppv, material,
      },
    } = JSON.parse(product.dataset.item);
    const { index } = product.dataset;

    const data = {
      event: 'productClick',
      ecommerce: {
        click: {
          actionField: { list },
          products: [{
            name: id,
            id,
            price,
            brand,
            category: furniture_category,
            variant: `${material}|${shelfTypes[shelf_type]}|${configurator_type}|${ppv}|${digital_product_version}`,
            list,
            position: index,
          }],
        },
        price,
        brand,
        category: furniture_category,
        variant: `${material}|${shelfTypes[shelf_type]}|${configurator_type}|${ppv}|${digital_product_version}`,
        list,
        position: index,
      },
      eventTimeout: 2000,
    };
    window.dataLayer.push(data);
  };

  const trackMatteBlackSliderImp = () => {
    const activeSlides = [...document.querySelectorAll('.swiper-container--inspire .swiper-slide-visible .board-card')];
    Site.Track.impressionTracking(activeSlides, 'lp-matte-black-slider');
  };

  const trackMatteBlackGridImp = () => {
    const activeSlides = [...document.querySelectorAll('.explore-gallery-board-carousel .is-selected .board-card')];
    Site.Track.impressionTracking(activeSlides, 'lp-matte-black-grid');
  };

  const trackMatteBlackLandingPage = () => {
    if (window.full_category_name !== 'matte-black-lp') {
      return;
    }

    // Product clicks
    const productsGrid = document.querySelectorAll('.explore-gallery .board-card');
    const productsSlider = document.querySelectorAll('.inspire .board-card');
    productsGrid.forEach(item => {
      item.addEventListener('click', onProductClick(item, 'lp-matte-black-grid'));
    });
    productsSlider.forEach(item => {
      item.addEventListener('click', onProductClick(item, 'lp-matte-black-slider'));
    });

    // Impressions
    Site.utils.isInViewport('.inspire__board-carousel_swiper', trackMatteBlackSliderImp);
    Site.utils.isInViewport('.explore-gallery-board-carousel', trackMatteBlackGridImp);

    // Button clicks
    trackElement({ selector: '.board-card', fn: onProductClick });
    trackElement({ selector: '.instafeed-main-carousel', fn: getInstafeedItems });
    trackElement({ selector: '.btn-cta--hero', fn: onButtonClick('discover more') });
    trackElement({ selector: '.img-fullwidth-textcenter .btn-cta', fn: onButtonClick('configure yours') });
    trackElement({ selector: '#matte_black_minimalist', fn: onButtonClick('configure yours') });
    trackElement({ selector: '#matte_black_storage', fn: onButtonClick('configure yours') });
    trackElement({ selector: '.next-step__item:nth-child(1) a', fn: onButtonClick('colors', 'cant_decide') });
    trackElement({ selector: '.next-step__item:nth-child(2) a', fn: onButtonClick('sample', 'cant_decide') });
    trackElement({ selector: '.next-step__item:nth-child(3) a', fn: onButtonClick('types', 'cant_decide') });
  };

  // Samples
  const trackSamples = () => {
    const body = document.querySelector('body');
    if (!body.classList.contains('material-samples')) {
      return;
    }

    // Buttons
    trackElement({ selector: '.samples_02', fn: onButtonClick('cta', 'type01_type02_set', 'samples-lp') });
    trackElement({ selector: '.samples_03', fn: onButtonClick('cta', 'type03_set', 'samples-lp') });

    // Product clicks
    const productsSlider = document.querySelectorAll('.single-shelf__cell .single-shelf__card');
    productsSlider.forEach(item => {
      item.setAttribute('data-item', item.dataset.item.replaceAll(/'/g, '"'));
      item.addEventListener('click', onProductClick(item, 'lp-samples'));
    });

    // Impressions
    Site.utils.isInViewport('.single-shelf__carousel', trackSamplesSliderImp);
  };

  const trackSamplesSliderImp = () => {
    const activeSlides = [...document.querySelectorAll('.flickity-slider .is-selected .single-shelf__card')];
    Site.Track.impressionTracking(activeSlides, 'lp-samples', true);
  };

  // HOMEPAGE
  const trackHomepageSliderImp = () => {
    const activeSlides = [...document.querySelectorAll('.inspire__homepage-carousel .swiper-slide-visible .board-card')];
    Site.Track.impressionTracking(activeSlides, 'hp-top-picks-carousel');
  };

  const trackHomepage = () => {
    const body = document.querySelector('body');
    if (!body.classList.contains('homepage')) {
      return;
    }

    // Swiper carousel clicks
    const topPicks = document.querySelectorAll('.inspire .board-card');
    if (topPicks.length > 0) {
      topPicks.forEach(item => {
        item.addEventListener('click', onProductClick(item, 'hp-top-picks-carousel'));
      });
    }

    // Impressions
    Site.utils.isInViewport('.inspire__board-carousel_swiper', trackHpSliderImp);

    // Button clicks
    trackElement({ selector: '.inspire .link', fn: onButtonClick('carousel_top_picks', 'view_all', 'hp') });
  };

  // SPACES MINIGRID
  const trackSpacesMiniGridImp = () => {
    const activeSlides = [...document.querySelectorAll('.spaces-grid__main-carousel .swiper-slide-visible .swiper-slide-visible .board-card')];
    Site.Track.impressionTracking(activeSlides, 'hp-spaces-minigrid');
  };

  const trackSpacesMiniGrid = () => {
    // Impressions
    Site.utils.isInViewport('.spaces-grid__board-carousel', trackSpacesMiniGridImp);

    // Product clicks
    const minigridProducts = document.querySelectorAll('.spaces-grid .board-card');
    minigridProducts.forEach(item => {
      item.addEventListener('click', onProductClick(item, 'hp-spaces-minigrid'));
    });

    // Button clicks
    trackElement({ selector: '.spaces-grid .btn-cta', fn: onButtonClick('spaces-minigrid', 'see all products', 'hp') });
    trackElement({ selector: '.spaces-grid .common-slider__cell[data-index="0"]', fn: onButtonClick('spaces-minigrid', 'living room', 'hp') });
    trackElement({ selector: '.spaces-grid .common-slider__cell[data-index="1"]', fn: onButtonClick('spaces-minigrid', 'hallway', 'hp') });
    trackElement({ selector: '.spaces-grid .common-slider__cell[data-index="2"]', fn: onButtonClick('spaces-minigrid', 'office', 'hp') });
    trackElement({ selector: '.spaces-grid .common-slider__cell[data-index="3"]', fn: onButtonClick('spaces-minigrid', 'bedroom', 'hp') });
  };

  return {
    initialize,
    makeDataLayerPush,
    trackViewedContent,
    trackCheckoutStep,
    trackTransaction,
    trackAddToCart,
    trackShareItem,
    trackRemoveFromCart,
    trackCustomization,
    trackAddToWishlist,
    trackInitiateCheckout,
    trackPaymentMethodSelection,
    trackPurchase,
    trackLead,
    trackCompleteRegistration,
    trackLogin,
    trackSendMeLink,
    trackSendInvite,
    trackSendContextCard,
    sendDataLayer,
    sendMarketingAgree,
    sendFbq,
    trackTelephoneClick,
    trackRegionClick,
    trackLanguageClick,
    trackContact,
    trackFAQ,
    trackAppDownload,
    trackReview,
    trackCartPreview,
    trackSharing,
    trackChangedRows,
    jettyECommerce,
    assemblyECommerceFormat,
    sampleBoxECommerceFormat,
    prepareVariantDescription,
    trackCategoryMiniGrid,
    trackExploreGalleryClick,
    impressionTracking,
    trackMatteBlackSliderImp,
    trackMatteBlackGridImp,
    trackHomepageSliderImp,
    trackSamplesSliderImp,
    trackSpacesMiniGridImp,
    trackSpacesMiniGrid,
    trackMegaMenuNavigation,
    trackMegaMenuColor,
  };
}(jQuery));
