
const Site = window.Site || {};

Site.Tooltip = (function($) {
    function initialize() {}

    function handleMouseenter() {
        const $this = $(this);
        $this.find('.pdp-2018-tooltip').css('display', 'block');
        setTimeout(() => {
            $this.find('.pdp-2018-tooltip').addClass('active');
        }, 50);
    }

    function handleMouseleave() {
        const $this = $(this);
        $this.find('.pdp-2018-tooltip').removeClass('active');
        setTimeout(() => {
            $this.find('.pdp-2018-tooltip').css('display', '');
        }, 400);
    }

    $('[data-tooltip-trigger]').on('mouseenter', function() {
        handleMouseenter.call(this);
    });

    $('[data-tooltip-trigger]').on('mouseleave', function() {
        handleMouseleave.call(this);
    });

    function sideBarCartInit(sidebarCart) {
        sidebarCart.on('mouseenter', '[data-tooltip-trigger]', function() {
            handleMouseenter.call(this);
        });
        sidebarCart.on('mouseleave', '[data-tooltip-trigger]', function() {
            handleMouseleave.call(this);
        });
    }

    return {
        sideBarCartInit,
        initialize,
    };
}(jQuery));
