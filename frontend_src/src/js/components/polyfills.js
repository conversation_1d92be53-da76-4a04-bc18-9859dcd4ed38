/* eslint-disable */
const Site = window.Site || {};

// Add polyfills for IE9
if ('NodeList' in window && !NodeList.prototype.forEach) {
    NodeList.prototype.forEach = function(callback, thisArg) {
        thisArg = thisArg || window;
        for (var i = 0; i < this.length; i++) {
            callback.call(thisArg, this[i], i, this);
        }
    };
}

(function($) {
    Site.Polyfills = {
        svg() {
            if (Modernizr.svg) return;
            var src;
            $('img[src$=".svg"]').each(function() {
                src = $(this).attr('src');
                $(this).attr('src', src.replace('.svg', '.png'));
            });
        },
        addClass(el, className) {
            if (el.classList) {
                el.classList.add(className);
            } else {
                el.className += ' ' + className;
            }
        },
        removeClass(el, className) {
            if (el.classList) {
                el.classList.remove(className);
            } else {
                el.className = el.className.replace(new RegExp('(^|\\b)' + className.split(' ').join('|') + '(\\b|$)', 'gi'), ' ')
            }
        },
        transform(el, transform) {
            el.style.webkitTransform = transform;
            el.style.MozTransform = transform;
            el.style.msTransform = transform;
            el.style.transform = transform;
        },

        loadSVGSprite() {
            // laods the svg sprite, so it can be used throughout the page
            var spritePath = (window.location.host === 'localhost:9000') ? '/dist/images/svg.svg' : '/r_static/dist/images/svg.svg';

            $.ajax({
                url: spritePath,
                global: false,
                type: 'GET',
                dataType: 'html',
                async: true
            })
                .done(function(data) {
                    window.$body.append('<div class="svg-sprite">' + data + '</div>');
                })
                .fail(function() {
                    alert('Icons not loaded.');
                });
        },
        autoInit() {
            Site.Polyfills.loadSVGSprite();
        },
        onPageLoad() {
            Site.Polyfills.svg();
        },
    };
}(jQuery));
