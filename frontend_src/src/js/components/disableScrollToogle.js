const Site = window.Site || {};

Site.disableScrollToogle = (function() {
    function initialize() {
        window.addEventListener('scroll', () => {
            document.documentElement.style.setProperty('--scroll-y', `${window.scrollY}px`);
        });
    }

    function disableScroll() {
        const scrollY = document.documentElement.style.getPropertyValue('--scroll-y');
        const { body } = document;
        body.style.position = 'fixed';
        body.style.width = '100%';
        body.style.top = `-${scrollY}`;
        body.style.scrollBehavior = '';
    }

    function enableScroll() {
        const { body } = document;
        const scrollY = body.style.top;
        body.style.position = '';
        body.style.width = '';
        $('html, body').animate({
            scrollTop: parseInt(scrollY || '0', 10) * -1,
        }, 0, () => {
            body.style.scrollBehavior = 'smooth';
        });
    }

    return {
        initialize,
        disableScroll,
        enableScroll,
    };
}(jQuery));
