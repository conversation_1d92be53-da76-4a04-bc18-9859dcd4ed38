const Site = window.Site || {};

Site.pdpExploreMore = (function($) {
    function initialize() {
        const $carousel = $('.pdp-explore-more__carousel');
        const isMobile = window.matchMedia('(max-width: 767px)').matches;
        const exploreMoreCarousel = document.querySelector('.pdp-explore-more__carousel');
        const carouselItems = [...document.querySelectorAll('.pdp-explore-more__cell')];

        if (document.querySelector('.hero-newsletter')) {
            carouselItems.forEach(item => {
                const category = item.getAttribute('data-track-category');
                item.addEventListener('click', () => {
                    Site.Track.trackCategoryMiniGrid(category);
                });
            });
        }

        $carousel.flickity({
            contain: true,
            imagesLoaded: true,
            prevNextButtons: !isMobile,
            cellAlign: isMobile ? 'center' : 'left',
            pageDots: false,
            on: {
                settle() {
                    window.Site.utils.manageCarouselButtons(exploreMoreCarousel);
                },
            },
        });
    }

    return {
        initialize,
    };
}(jQuery));
