const Site = window.Site || {};

/**
 *
 * Popup
 *
 * data-script="Popup"
 *
 * optional template classes
 * js-popup-close - provide custom close trigger
 *
 * django: rendered with `popup `inclusion tag
 *
 * opens modal or regular popup
 *
 *
 * Popup Trigger
 *
 * data-script="PopupTrigger"
 * data-target-popup"XPath selector" – ".popup-subscribe"
 *
 * a button/link that triggers popup open on click
 * <button data-script="PopupTrigger" data-target-popup=".popup-subscribe" style="position: relative;z-index: 12030123012;">Open popup</button>
 */

Site.Popup = (function($) {
    const transitionEndEvents = 'webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend';

    function initialize() {
        $('.popup').each(function() {
            setupPopup.call($(this));
        });
    }

    function setupPopup() {
        this.isModal = !!this.data('popup-modal');
        this.isExitTrap = !!this.data('popup-exit');
        this.isAutoOpen = !!this.data('popup-auto');
        this.delay = parseInt(this.data('popup-delay'), 10) || 0;
        this.dataLayerEventOnOpen = this.data('trackdatalayer');
        this.dataLayerAdditional = this.data('trackdatalayeradditional');
        this.onCloseDataLayer = this.data('onclosedatalayer');

        this.cookiePrefix = `${this[0].className.replace(' ', '-')}1`; // dont reuse popup names please
        this.cookie = this.data('cookie');
        this.cookieRepeat = parseInt(this.data('cookie-repeat'), 10);
        this.cookieInterval = parseFloat(this.data('cookie-interval'));
        this.mobileScroll = this.data('mobile-scroll');
        this.on('force-open', forcePopupOpen.bind(this));
        this.isCartHandler = !!this.data('popup-cart');


        this.on('open', popupOpen.bind(this));
        this.on('close', popupClose.bind(this));

        if (this.isAutoOpen) {
            if (this.delay > 0) {
                setTimeout(() => {
                    popupOpen.call(this);
                }, this.delay);
            } else {
                popupOpen.call(this);
            }
        }

        if (this.isCartHandler) {
            cartHandler.call(this);
        }

        if (this.isExitTrap) {
            const onWindowLeave = function(ev) {
                const isScrolledDown = ($(window).scrollTop() !== 0);
                if (ev.clientY <= 0 && isScrolledDown) {
                    if (this.delay > 0) {
                        setTimeout(() => {
                            popupOpen.call(this);
                        }, this.delay);
                    } else {
                        popupOpen.call(this);
                    }
                    window.removeEventListener('mouseout', onWindowLeave, true);
                }
            }.bind(this);

            window.addEventListener('mouseout', onWindowLeave, true);
        }
        if (this.mobileScroll) {
            mobileScrollHandler.call(this);
        }
    }
    function cartHandler() {
        if ($(window).innerWidth() < 1009) return;
        const that = this;
        const observer = new MutationObserver((() => {
            $(that).toggleClass('handle-cart', $('html').hasClass('sa-cart-in-view'));
        }));

        const html = document.querySelector('html');
        observer.observe(html, {
            attributes: true,
            attributeFilter: ['class'],
        });
    }
    function forcePopupOpen() {
        Site.Cookies.deleteCookie(`${this.cookiePrefix}-done`);
        popupOpen.call(this, this.dataLayerAdditional);
    }

    function popupOpen() {
        if (this.cookie) {
            if (Site.Cookies.hasCookie(`${this.cookiePrefix}-done`)) {
                return;
            }
            Site.Cookies.setCookie(`${this.cookiePrefix}-done`, 'true', this.cookie === 'session' ? null : this.cookie === 'long' ? 365 : parseInt(this.cookie, 10));
        }

        if (this.cookieRepeat) {
            if (!Site.Cookies.hasCookie(`${this.cookiePrefix}-repeat`)) {
                Site.Cookies.setCookie(`${this.cookiePrefix}-repeat`, --this.cookieRepeat, 365 * 10);
                Site.Cookies.setCookie(`${this.cookiePrefix}-interval`, 'true', this.cookieInterval);
            } else if (!Site.Cookies.hasCookie(`${this.cookiePrefix}-interval`)) {
                let repeatsLeft = parseInt(Site.Cookies.getCookie(`${this.cookiePrefix}-repeat`), 10);

                if (repeatsLeft > 0) {
                    Site.Cookies.deleteCookie(`${this.cookiePrefix}-repeat`);
                    Site.Cookies.setCookie(`${this.cookiePrefix}-repeat`, --repeatsLeft, 365 * 10);
                    Site.Cookies.setCookie(`${this.cookiePrefix}-interval`, 'true', this.cookieInterval);
                } else {
                    return;
                }
            } else {
                return;
            }
        }
        this.removeClass('hide');
        this.addClass('pre-show touch-none');

        this.off(transitionEndEvents);

        if (this.dataLayerAdditional && this.dataLayerEventOnOpen) {
            Site.Track.sendDataLayer(this.dataLayerEventOnOpen, { eventCategory: 'popup', eventAction: 'open' });
        } else if (this.dataLayerEventOnOpen) Site.Track.sendDataLayer(this.dataLayerEventOnOpen, { eventCategory: 'popup', eventAction: 'open' });

        setTimeout(() => {
            this.addClass('show');
            if (this.mobileScroll) {
                mobileScrollHandler.call(this);
            }
            if (this.isCartHandler) {
                cartHandler.call(this);
            }
        }, 100);

        this.find('.popup-close').on('click', popupClose.bind(this));
        if (!this.isModal) this.on('click', onWrapperClicked.bind(this));
    }

    function popupClose() {
        if (this.isClosing) return;

        this.isClosing = true;

        if (this.onCloseDataLayer) {
            Site.Track.sendDataLayer(this.dataLayerEventOnOpen, { eventCategory: 'popup', eventAction: 'close' });
        }

        this.addClass('hide');

        this.one(transitionEndEvents, () => {
            this.removeClass('pre-show show hide touch-none');
            this.isClosing = false;
        });
        if ($(this).hasClass('popup-promocode')) {
            $('.luke-sidebar #promo').css('border-color', '').val('').focus();
            $('.luke-promocode-entry .sa-cart-promocode-input').css('border-color', '').val('').focus();
        }
    }

    function onWrapperClicked(ev) {
        if (ev.target === this[0]) {
            popupClose.call(this);
        }
    }

    function mobileScrollHandler() {
        if ($(window).innerWidth() >= 1009) return;
        const contentWrapper = $(this).find('.luke-popup-content');
        const content = contentWrapper.find('.scroll-area');
        const contentHeight = content.height();
        const ctaHeight = $(this).find('.luke-popup-cta').length ? $(this).find('.luke-popup-cta').height() : 0;
        const wrapperHeight = $(this).find('.popup-container-body').height();
        const computedHeight = wrapperHeight - ctaHeight - 106;

        if (contentHeight + ctaHeight + 86 >= wrapperHeight && contentWrapper.parents('.popup-luke').hasClass('full-height')) {
            content.addClass('scroll').css({
                height: computedHeight + 70 + 28 - $(this).find('.luke-popup-title').height(),
                'overflow-y': 'scroll',
            });
            content.scroll(e => {
                setTimeout(() => {
                    if ($(e.currentTarget).scrollTop() > 20) {
                        contentWrapper.addClass('display-block-top');
                        setTimeout(() => {
                            contentWrapper.addClass('shadow-top');
                        }, 50);
                    } else {
                        contentWrapper.removeClass('shadow-top');
                        setTimeout(() => {
                            contentWrapper.removeClass('display-block-top');
                        }, 200);
                    }

                    if ($(e.currentTarget).scrollTop() === ($(e.currentTarget)[0].scrollHeight) - computedHeight) {
                        contentWrapper.removeClass('shadow-bottom');
                        setTimeout(() => {
                            contentWrapper.removeClass('display-block-bottom');
                        }, 200);
                    } else {
                        contentWrapper.addClass('display-block-bottom');
                        setTimeout(() => {
                            contentWrapper.addClass('shadow-bottom');
                        }, 50);
                    }
                }, 50);
            });
        }
        if (!contentWrapper.parents('.popup-luke').hasClass('full-height')) {
            contentWrapper.parents('.popup-content').css('height', contentWrapper.height() + ctaHeight + 64);
        }
    }
    return {
        initialize,
    };
}(jQuery));


Site.PopupTrigger = (function($) {
    function initialize() {
        $('[data-script="PopupTrigger"]').each(function() {
            $(this).on('click', ev => {
                ev.preventDefault();
                $($(this).data('target-popup')).trigger('open');
            });
        });
    }

    return {
        initialize,
    };
}(jQuery));
