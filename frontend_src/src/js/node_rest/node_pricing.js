var express = require("express");
var bodyParser = require("body-parser");
var app = express();

var patterns = [
    // brick
    require('../../../src_webgl/ivy/dna/brick-241115'),
    //require('./dna/brick-bogdan.js'),
    // gradient
    require('../../../src_webgl/ivy/dna/gradient-241115'),

    // chaos tbd
    require('../../../src_webgl/ivy/dna/chaos-241115'),
    //require('./dna/chaos-rex'),
    // grid
    require('../../../src_webgl/ivy/dna/grid-241115')
];

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

var router = express.Router();
router.get('/', function(req, res) {
    res.json({ message: 'hooray! welcome to our api!' });
    console.log(patterns[0].generateWalls(0, 100, [238,238,238,238,238,238,238,238,], [178,238,468], 320, 0, 100));
});
app.use('/', router);

var server = app.listen(3000, function () {
    console.log("Listening on port %s...", server.address().port);
});
