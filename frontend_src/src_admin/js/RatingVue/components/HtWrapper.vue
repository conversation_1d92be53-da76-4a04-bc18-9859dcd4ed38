<template>
    <div>

        <div class="ht-container-main">

            <HtHeader
                    v-bind:currentView="currentView"
                    v-bind:currentShelf="currentShelf"
                    v-bind:currentShelfIndexOneBased="currentShelfIndexOneBased"
                    v-bind:currentCollectionShelfIndex="currentCollectionShelfIndex"
                    v-bind:numberOfShelves="numberOfShelves"
                    v-bind:revertedOnce="revertedOnce"/>

            <transition name="slide-fade">

                <HtNotifications
                        v-if="notificationsVisible"/>

            </transition>


            <transition name="fade" mode="out-in">

                <HtLogin
                        v-if="currentView === 'login'"
                        v-bind:userEmail="userEmail"
                        v-bind:isUserRegistered="isUserRegistered"
                        v-bind:currentView="currentView"
                        v-on:input="userEmail = $event"/>


                <HtAuthenticated
                        v-if="currentView === 'authenticated'"
                        v-bind:isUserRegistered="isUserRegistered"/>


                <HtShelf
                        v-else-if="currentView === 'shelf'"
                        v-bind:currentShelf="currentShelf"
                        v-bind:currentRating="currentRating"
                        v-bind:nextShelfTemporary="nextShelfTemporary"
                        v-bind:nextShelfFadesIn="nextShelfFadesIn"
                        v-bind:currentRatingClass="currentRatingClass"
                        v-bind:ratingIsBeingSent="ratingIsBeingSent"
                        v-bind:ratings="ratings"/>

                <HtStats
                        v-else-if="currentView === 'stats'"
                        v-bind:ratings="ratings"
                        v-bind:statsViewCount="statsViewCount"
                        v-bind:collectionIsEmpty="collectionIsEmpty" />

                <HtEmpty
                        v-else-if="currentView === 'empty'" />

            </transition>


            <transition name="fade">

                <HtFooter
                        v-bind:currentView="currentView"/>

            </transition>


        </div>

        <HtButtonReset
                v-if="currentView !== 'login' && currentView !== 'authenticated' && currentView !== 'empty'"
                v-bind:class="'text-center m-b-lg hidden-md hidden-lg hidden-xl m-b-xxl-mobile'"/>

    </div>

</template>

<script>
    import HtHeader from './HtHeader.vue'
    import HtLogin from './HtLogin.vue'
    import HtAuthenticated from './HtAuthenticated.vue'
    import HtShelf from './HtShelf.vue'
    import HtStats from './HtStats.vue'
    import HtEmpty from './HtEmpty.vue'
    import HtRatings from './HtRatings.vue'
    import HtButtonLoad from './HtButtonLoad.vue'
    import HtKeyboardShortcut from './HtKeyboardShortcut.vue'
    import HtButtonReset from './HtButtonReset.vue'
    import HtFooter from './HtFooter.vue'
    import HtNotifications from './HtNotifications.vue'
    import {EventBus} from '../utils/EventBus'
    import axios from 'axios';


    export default {
        components: {
            HtHeader,
            HtLogin,
            HtAuthenticated,
            HtShelf,
            HtStats,
            HtButtonReset,
            HtKeyboardShortcut,
            HtEmpty,
            HtFooter,
            HtNotifications
        },

        data: function () {
            return {
                currentView: 'login',
                userEmail: null,
                ratingToolUserID: null,
                currentShelfIndex: 0,
                currentCollectionShelfIndex: 0,
                currentCollectionLength: 0,
                currentRating: 0,
                ratingIsBeingSent: false,
                nextShelfTemporary: null,
                nextShelfFadesIn: false,
                revertedOnce: false,
                notificationsVisible: false,
                statsViewCount: 0,
                collection: [],
                collectionIsEmpty: false,
                delayBeforeNextShelf: 1000,
                keyboardKeys: {
                    'a': 65,
                    's': 83,
                    'k': 75,
                    'l': 76,
                    'z': 90,
                    'enter': 13
                },


                collection: [],

                ratings: [

                    {
                        value: 1,
                        count: 0,
                        keyboardLetter: 'l'
                    },

                    {
                        value: 2,
                        count: 0,
                        keyboardLetter: 'k'

                    },

                    {
                        value: 3,
                        count: 0,
                        keyboardLetter: 's'

                    },

                    {
                        value: 4,
                        count: 0,
                        keyboardLetter: 'a'
                    },

                ]
            }
        },

        computed: {

            isUserRegistered: function () {
                return !!this.ratingToolUserID;
            },

            currentShelfIndexOneBased: function () {
                return this.currentShelfIndex + 1
            },

            currentRatingClass: function () {
                const classname = this.getRatingClass(this.currentRating)
                return classname;
            },

            numberOfShelves: function () {
                return this.collection.length
            },

            currentShelf: function () {
                return this.collection[this.currentShelfIndex]
            },

            previousShelf: function() {
              return this.collection[this.currentShelfIndex - 1];
            },

            isCurrentCollectionCompleted: function () {
                if (this.currentShelfIndex > this.numberOfShelves - 1) {
                    console.log('List completed.');
                    return true;
                }
                return false;
            },

            isCurrentShelfFirstInCurrentCollection: function() {
                const result = this.currentCollectionShelfIndex === 0 ? true : false;
                return result;
            },

            isCurrentShelfLastInCollection: function () {
                if (this.currentShelfIndex === this.numberOfShelves - 1) {
                    console.log('Last in collection.');
                    return true;
                }
                return false;
            },

        },


        created: function () {

            this.ratingToolUserID = window.ratingToolUserID;
            if ( !!this.ratingToolUserID ) {
                this.currentView = 'authenticated';
            }

            window.addEventListener('keyup', this.handleKeyPress)


            EventBus.$on('ratedShelf', this.handleRatingClick);
            EventBus.$on('goBack', this.handleGoBack);
            EventBus.$on('loadMore', this.handleLoadMoreClick);
            EventBus.$on('login', this.handleLoginButton);
            EventBus.$on('logout', this.handleReset);
            EventBus.$on('reset', this.handleReset);
            EventBus.$on('testEmit', (payload) => console.log(payload));
        },

        methods: {

            handleKeyPress: function (event) {

                const keyPressed = event.which;
                const keys = this.keyboardKeys;
                //console.log(keyPressed);

                if (this.currentView === 'shelf') {

                    switch (keyPressed) {
                        case keys.a:
                            EventBus.$emit('ratedShelf', {rating: 4});
                            break;
                        case keys.s:
                            EventBus.$emit('ratedShelf', {rating: 3});
                            break;
                        case keys.k:
                            EventBus.$emit('ratedShelf', {rating: 2});
                            break;
                        case keys.l:
                            EventBus.$emit('ratedShelf', {rating: 1});
                            break;
                        case keys.z:
                            EventBus.$emit('goBack');
                            break;
                    }

                } else if (this.currentView === 'stats') {

                    if (keyPressed == keys.enter) {
                        EventBus.$emit('loadMore');
                    }

                } else if (this.currentView === 'authenticated') {

                    if (keyPressed == keys.enter) {
                        EventBus.$emit('loadMore');
                    }
                }

            },

            updateCollection: function (callback) {

                const APIurl = '/api/v1/rating_tool/furniture/';
                axios.get(APIurl)

                    .then(res => {
                        const newCollection = res.data;
                        const collectionLength = newCollection.length;
                        console.log( 'Collection updated - ', newCollection );
                        this.currentCollectionShelfIndex = 0;
                        if ( !collectionLength ) {
                            this.handleCollectionIsEmpty();
                            return console.log('Collection has finished.');
                        }
                        this.currentCollectionLength = collectionLength;
                        this.collection = this.collection.concat(newCollection);
                        //console.log('Collection updated.');
                        this.setNextShelfTemporary();
                        this.currentView = 'shelf';
                        typeof callback !== 'undefined' && callback();
                    })
                    .catch(err => {
                        console.log(err);
                        this.handleNotificationsDisplay();
                    });
            },

            sendRatingToApi: function (shelfId, rating, options) {

                const APIurl = '/api/v1/rating_tool/rating/';

                axios.post(APIurl, {
                    tool_user_id: this.ratingToolUserID,
                    jetty: shelfId,
                    rating: rating
                })
                    .then(res => {

                        if ( options === 'logoutOnSuccessfulRatingSend' ) {
                            this.handleLogout();
                            return;
                        }

                        this.handleSuccessfulRatingSend(rating, options);

                    })
                    .catch(err => {
                        console.log(err);
                        this.ratingIsBeingSent = false;
                        this.handleNotificationsDisplay();
                    });
            },

            handleRatingClick: function (payload) {

                if (this.ratingIsBeingSent) {
                    console.log('Rating is being sent.');
                    return false;
                }

                if (this.isCurrentCollectionCompleted) {
                    console.log('List has been completed.');
                    return false;
                }

                if (this.currentView !== 'shelf') {
                    console.log('Press "Load More" to rate again.');
                    return false;
                }

                this.setShelfRating(payload.rating, this.currentShelf);

                this.ratingIsBeingSent = true;
                this.currentRating = payload.rating;

                //const shelfToBeSent = this.isCurrentShelfLastInCollection ? this.currentShelf : this.previousShelf;

                this.preloadNextImage();

                setTimeout(() => {

                    if ( ( this.currentCollectionShelfIndex === 0 && this.currentCollectionLength !== 1 ) || this.revertedOnce ) {
                        this.proceedToNextShelf();
                        return;
                    }

                    if ( this.currentCollectionLength !== 1 ) {
                        this.sendRatingToApi(this.previousShelf.shelfId, this.previousShelf.rating);
                    }

                    if ( this.currentCollectionLength === 1 ) {
                        this.sendRatingToApi(this.currentShelf.shelfId, this.currentRating);
                    }

                    if ( this.isCurrentShelfLastInCollection && this.currentCollectionLength !== 1 ) {
                        this.sendRatingToApi(this.currentShelf.shelfId, this.currentRating, 'skipIndexIncrementation');
                    }

                }, this.delayBeforeNextShelf)

                setTimeout(() => {
                    //console.log('--fadesIn');
                    this.nextShelfFadesIn = true;

                }, this.delayBeforeNextShelf * 3 / 4)


            },

            handleSuccessfulRatingSend: function (rating, options) {
                this.currentRating = rating;

                //this.updateRatingsCount(rating);
                console.log('Sending completed.');


                if (this.isCurrentShelfLastInCollection) {
                    this.updateRatingsCountBatch();
                    this.statsViewCount += 1;
                    this.currentView = 'stats';
                }

                    this.proceedToNextShelf(options);


            },

            handleLoadMoreClick: function () {
                this.updateCollection();
            },

            handleLoginButton: function (payload) {
               this.ratingToolUserID = payload.tool_user_id;

                if (!this.userEmail) return console.log('Type in your email.');
                this.updateCollection(() => {
                    this.preloadNextImage();
                    this.currentView = 'shelf';
                });
            },

            handleReset: function () {
                const confirmed = confirm('Are you sure?');
                if (!confirmed) return;

                if ( this.currentView === 'shelf' && this.currentCollectionShelfIndex > 0) {
                    this.sendRatingToApi( this.previousShelf.shelfId, this.previousShelf.rating, 'logoutOnSuccessfulRatingSend' );
                    return;
                }

                this.handleLogout();
            },

            handleGoBack: function () {
                if (this.revertedOnce || this.currentShelfIndex <= 0) return;

                this.revertedOnce = true;
                this.currentShelfIndex -= 1;
                this.currentCollectionShelfIndex -= 1;
                setTimeout(this.setNextShelfTemporary, 300);
                console.log('Reverted once.');
            },

            handleNotificationsDisplay() {
                if (this.notificationsVisible) return;
                this.notificationsVisible = true;
                window.notificationsTimeout = setTimeout(() => {
                    this.notificationsVisible = false;
                }, 2000);
            },

            handleCollectionIsEmpty() {
                this.currentView = 'empty';
            },

            getRatingClass: function (rating) {
                const classname = `ht-rating-${ rating }`;
                return classname;
            },

            setNextShelfTemporary() {
                this.nextShelfTemporary = this.collection[this.currentShelfIndex + 1]
            },

            resetApp: function () {
                window.preloadedImages = [];
                this.collection = [];
                this.currentShelfIndex = 0;
                this.currentCollectionShelfIndex = 0;
                this.currentRating = 0;
                this.statsViewCount = 0;
                //this.currentView = this.isUserRegistered ? 'authenticated' : 'login';
            },

            proceedToNextShelf: function (options) {
                this.nextShelfFadesIn = false;
                this.ratingIsBeingSent = false;
                this.revertedOnce = false;
                this.currentRating = null;
                if (options !== 'skipIndexIncrementation') {
                    this.currentShelfIndex += 1;
                    this.currentCollectionShelfIndex += 1;
                } else {
                    //console.log('options activated');
                }

                setTimeout(this.setNextShelfTemporary, 300);
            },

            updateRatingsCount(rating) {

                    const selectedRating = this.ratings[rating - 1];
                    selectedRating.count += 1;

            },

            updateRatingsCountBatch() {

                this.clearRatingsCount();

                this.collection.forEach( shelf => {
                    if ( typeof shelf.rating !== 'undefined' ) {
                        this.ratings[ shelf.rating - 1 ].count += 1;
                    }
                 } );
            },

            clearRatingsCount() {
                this.ratings.forEach( rating => {
                   rating.count = 0;
                });
            },

            getUserRating: function (e) {
                const el = e.target;
                const value = el.getAttribute('data-value');
                return value;
            },

            setShelfRating: function (rating, currentShelf) {
                currentShelf.rating = rating;

            },

            preloadNextImage() {
                const nextShelf = this.collection[this.currentShelfIndex + 2];
                if (typeof nextShelf === 'undefined') return false;
                window.preloadedImages = window.preloadedImages || [];
                const newImage = new Image();
                newImage.src = nextShelf.src;
                window.preloadedImages.push(newImage)
            },

            handleLogout: function() {
                const logoutUrl = '/api/v1/rating_tool/user/logout/';
                axios.post( logoutUrl, { tool_user_id: this.ratingToolUserID } )
                    .then( result => {
                        delete window.ratingToolUserID;
                        this.ratingToolUserID = null;
                        this.currentView = 'login';
                        this.resetApp();
                    });
            },

            testAlert: function () {
                alert('test');
            }

        },

        beforeDestroy() {

            typeof window.notificationsTimeout && clearTimeout(window.notificationsTimeout);
            window.removeEventListener('keyup', this.handleKeyPress);
        }

    }

</script>
