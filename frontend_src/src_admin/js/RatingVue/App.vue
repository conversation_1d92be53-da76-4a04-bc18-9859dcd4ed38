<template>
  <div id="app">

    <transition name="fade" mode="out-in">

    <HtWrapper/>

    </transition>
  </div>
</template>

<script>

  import HtWrapper from './components/HtWrapper.vue';

  export default {
    components: {
      HtWrapper
    }
  }

</script>

<style lang="scss">


  @import 'scss/variables';
  @import 'scss/main';

  #app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    font-weight: 300;
  }
</style>
