 <template>
  <div class="fd-cell-technical">
		<h4>{{ item.pattern }}<ColorLabel v-bind:color="item.color"/></h4>
		{{ item.width}}cm x {{ item.height }}cm x 32cm <br>
		{{ item.weight.toFixed(2) }}kg <br>
    <div class="fd-cell-technical_features">
      <FeatureTag v-if="item.drawers" v-bind:label="'Drawers'" />
      <FeatureTag v-if="item.doors" v-bind:label="'Doors'" />
      <FeatureTag v-if="item.extended" v-bind:label="'Extended'" />
      <FeatureTag v-if="item.custom" v-bind:label="'Custom'" />
    </div>
  </div>
</template>
<script>
import FeatureTag from "./UI/FeatureTag";
import ColorLabel from "./UI/ColorLabel";
export default {
  props: ["item"],
  components: {
    FeatureTag,
    ColorLabel
  },
  computed: {
    pattern() {
      return this.item.pattern;
    }
  }
};
</script>
<style lang="scss" scoped>
@import "../scss/variables";

.fd-cell-technical {
  flex: 1 1 auto;
  align-self: flex-start;
  h4 {
    line-height: 28px;
    margin-top: 0;
    text-transform: capitalize;
  }
  .fd-cell-technical_features {
    margin-top: 10px;
  }
  .fd-table-card & {
    h4 {
      line-height: 1.25em;
      margin-bottom: 0;
    }
  }
}
</style>;

