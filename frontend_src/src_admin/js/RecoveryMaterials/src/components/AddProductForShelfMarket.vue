<template>
  <section>
    <div class="text-h6">
      Dodaj produkt na Shelf Market
    </div>
    <ProductInfo
      v-bind:set-searched-product-id="setSearchedProductId"
    />
    <div
      v-if="searchedProductId"
      class="column"
    >
      <q-select
        v-model="qualityFactor"
        v-bind:options="qualityFactorOptions"
        label="Współczynnik jakościowy"
      />
      <q-btn
        v-bind:disable="!qualityFactor"
        class="q-mt-lg q-mx-auto bg-indigo-9 text-white"
        label="Dodaj"
        v-on:click="addProductToShelfMarket"
      />
    </div>
  </section>
</template>
<script>
import ProductInfo from './ProductInfo.vue';

export default {
  name: 'AddProductForShelfMarket',
  components: {
    ProductInfo,
  },
  props: {
    closeModal: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      searchedProductId: null,
      qualityFactor: null,
      qualityFactorOptions: [
        0.5, 0.8, 0.9, 1, 1.1, 1.2,
      ],
    };
  },
  methods: {
    addProductToShelfMarket() {
      this.$axios({
        method: 'POST',
        url: '/api/v1/product_material_recovery/shelf_market/',
        data: {
          product: this.searchedProductId,
          quality_factor: this.qualityFactor,
        },
      })
        .then(() => {
          this.$q.notify({ message: 'Pomyślnie dodano produkt', type: 'positive' });
          this.closeModal();
        })
        .catch(e => {
          if (e.response.status === 400) {
            const responseMessage = e.response.data.non_field_errors && e.response.data.non_field_errors.join(', ');
            this.$q.notify({ message: responseMessage || 'Wystąpił błąd', type: 'negative' });
          } else {
            this.$q.notify({ message: `Wystąpił błąd - ${e.message}`, type: 'negative' });
          }
        });
    },
    setSearchedProductId(productId) {
      this.searchedProductId = productId;
    },
  },
};
</script>
