var React = require('react');

var PendingPool = {};
var ReadyPool = {};

var ImageLoader = React.createClass({
  propTypes: {
    src: React.PropTypes.string.isRequired,
  },

  getInitialState() {
    return {
      ready: false,
    };
  },

  componentWillMount() {
    this._load(this.props.src);
  },

  componentWillReceiveProps(nextProps) {
    if (nextProps.src !== this.props.src) {
      this.setState({src: null});
      this._load(nextProps.src);
    }
  },

  render() {
    var style = this.state.src ?
      { backgroundImage : 'url(' + this.state.src + ')'} :
      undefined;

    return <div className="previewImage" style={style} />;
  },

  _load(/*string*/ src) {
    if (ReadyPool[src]) {
      this.setState({src: src});
      return;
    }

    if (PendingPool[src]) {
      PendingPool[src].push(this._onLoad);
      return;
    }

    PendingPool[src] = [this._onLoad];

    var img = new Image();
    img.onload = () => {
      PendingPool[src].forEach(/*function*/ callback => {
        callback(src);
      });
      delete PendingPool[src];
      img.onload = null;
      src = undefined;
    };
    img.src = src;
  },

  _onLoad(/*string*/ src) {
    ReadyPool[src] = true;
    if (this.isMounted() && src === this.props.src) {
      this.setState({
        src: src,
      });
    }
  },
});

var ImageListLoader = React.createClass({

  getInitialState() {
    return {
      ready: false,
    };
  },

  componentWillMount() {
    //this._load(this.props.src);
  },

  componentWillReceiveProps(nextProps) {
    return;
    if (nextProps.src !== this.props.src) {
      this.setState({src: null});
      this._load(nextProps.src);
    }
  },

  render() {
    /*console.log('ok, redner image list', resp);*/
    return (<div className="nope"></div>);
    /*
    let src_list = this.state.src ? this.state.src.split(';') : [];
    let resp = [];
    src_list.forEach((src_list,i)=>{
      resp.push(<div key={i} className="previewImage" style={this.state.src ? {backgroundImage : 'url(' + src_list + ')'} : "" } ></div>);
    });
    console.log('ok, redner image list', resp);
    if (resp.length == 0){
      return <div className="nope"></div>;
    }
    else {
      return (
          <div className="imageList">{resp.map(x=>x)}</div>
      )
    }*/
  },

  _load(src_list) {
    let src_list_array = src_list.split(';');
    for (let i=0; i < src_list_array.length; i++){
      if (ReadyPool[src]) {
        this.setState({src: src});
        return;
      }

      if (PendingPool[src]) {
        PendingPool[src].push(this._onLoad);
        return;
      }

      PendingPool[src] = [this._onLoad];

      var img = new Image();
      img.onload = () => {
        PendingPool[src].forEach(/*function*/ callback => {
          callback(src);
        });
        delete PendingPool[src];
        img.onload = null;
        src = undefined;
      };
      img.src = src;
    }
  },

    _onLoad(/*string*/ src) {
      ReadyPool[src] = true;
      if (this.isMounted() && src === this.props.src) {
        this.setState({
          src: src,
        });
      }
    },
});

module.exports = ImageLoader;