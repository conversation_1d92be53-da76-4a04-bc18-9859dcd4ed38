<template>
  <div id="app">
    <transition name="fade">
      <FdWrapper v-bind:gridMode="true"/>
    </transition>
  </div>
</template>

<script>
import FdWrapper from "../Feeds/components/FdWrapper";

export default {
  components: {
    FdWrapper
  }
};
</script>

<style lang="scss">
@import "../Feeds/scss/variables";
@import "../Feeds/scss/main";

#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  font-weight: 300;
  background-color: $color-bg;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 2s;
  top: 0;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>;
