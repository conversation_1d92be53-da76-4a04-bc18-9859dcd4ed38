<template>
  <div class="q-pa-md">
    <q-table
      class="sticky-header-table"
      v-bind:title="title"
      v-bind:row-key="rowKey"
      selection="multiple"
      v-bind="{
        data: tableData,
        columns,
        loading,
        rowsPerPageOptions: [50, 100, 250, 500, 1000],
      }"
      v-bind:selected.sync="selected"
      v-bind:pagination.sync="pagination"
      loading-label="Pobieranie batchy..."
      rows-per-page-label="Ilość na stronie:"
      flat
      bordered
      separator="cell"
      v-on:request="reloadTable"
      v-on:update:selected="updateSelected"
    >
      <template
        v-for="(_, slot) of $scopedSlots"
        #[slot]="scope"
      >
        <slot
          v-bind:name="slot"
          v-bind="scope"
        />
      </template>
      <template v-slot:loading>
        <q-inner-loading
          showing
          color="primary"
        />
      </template>
    </q-table>
  </div>
</template>
<script>
export default {
  name: 'BaseTable',
  props: {
    apiUrl: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      required: true,
    },
    responseKey: {
      type: String,
      required: true,
    },
    updateLoading: {
      type: Function,
      required: true,
    },
    updateSelected: {
      type: Function,
      required: true,
    },
    filters: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pagination: {
        sortBy: 'desc',
        descending: false,
        page: 1,
        rowsPerPage: 100,
        rowsNumber: 10,
      },
      loading: false,
      selected: [],
      tableData: [],
    };
  },
  computed: {
    tableFilters() {
      return this.filters;
    },
  },
  watch: {
    loading() {
      this.updateLoading(this.loading);
    },
  },
  created() {
    this.getData(this.pagination);
  },
  methods: {
    reloadTable(props) {
      this.getData(props.pagination);
      const {
        page, rowsPerPage, sortBy, descending,
      } = props.pagination;
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
    },
    resetSelected() {
      this.selected = [];
      this.updateSelected([]);
    },
    getData(passedPagination, additionalFilters) {
      const pagination = passedPagination || this.pagination;
      const filters = {
        ...this.filters,
        ...additionalFilters || {},
      };

      this.loading = true;
      this.$axios.get(
        this.apiUrl,
        {
          params: {
            page_number: pagination.page,
            page_size: pagination.rowsPerPage,
            ...filters,
          },
        },
      )
        .then(resp => {
          this.tableData = resp.data[this.responseKey];
          this.pagination.rowsNumber = resp.data.count;
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="sass">
  td.break-lines
    white-space: pre-wrap

  .sticky-header-table
    .q-table__middle
      max-height: 70vh

    .q-table__top,
    .q-table__bottom,
    thead tr:first-child th
      background-color: #efebff

    thead tr th
      position: sticky
      z-index: 1

    thead tr:first-child th
      top: 0

    &.q-table--loading thead tr:last-child th
      z-index: 0

  td.items-column
    max-width: 200px
    white-space: normal

  td.next-cells-with-warning, td.next-cells-with-warning ~ td
    background-color: tomato
  td.single-cell-with-warning
    background-color: tomato
</style>
