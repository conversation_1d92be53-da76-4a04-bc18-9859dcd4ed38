<template>
  <q-page class="q-pa-lg">
    <div class="q-pa-md">
      <div class="q-pa-md">
        <div class="row">
          <q-input
            v-model="batchIds"
            class="q-ml-md q-pa-sm col-1"
            label="Batch ID"
            filled
            v-on:keyup.enter="filterData"
          />
          <q-input
            v-model="items"
            class="q-ml-md q-pa-sm col-1"
            label="Szafki"
            filled
            v-on:keyup.enter="filterData"
          />
          <q-select
            v-model="batchType"
            class="q-pa-sm col-1"
            clearable
            label="Typ batchy"
            v-bind:options="batchTypeOptions"
            filled
          />
          <q-select
            v-model="batchStatus"
            class="q-pa-sm col-1"
            clearable
            label="Status"
            v-bind:options="batchStatusOptions"
            filled
          />
          <q-input
            v-model="materialDescription"
            class="q-pa-sm col-1"
            label="Kolor"
            type="searchActive"
            filled
            v-on:keyup.enter="filterData"
          />
          <q-checkbox
            v-model="isDelayed"
            class="q-pa-sm"
            label="opóźnienia"
            filled
          />
          <div class="q-pa-sm">
            <q-btn
              class="q-pa-sm"
              color="primary"
              label="Filtruj"
              v-on:click="filterData"
            />
          </div>
        </div>
        <BaseTable
          ref="tableRef"
          v-bind:title="complaintList ? 'Reklamacje' : 'Batche'"
          v-bind="{
            columns,
            loading,
            filters,
            updateLoading,
            updateSelected,
            apiUrl,
          }"
          row-key="batch_id"
          response-key="batches"
        >
          <template v-slot:body-cell-actions_needed="props">
            <q-td
              v-bind="{
                props,
                class: {
                  'next-cells-with-warning': ['recalculate files', 'fix errors'].includes(props.value)
                }
              }"
            >
              {{ props.value }}
            </q-td>
          </template>
          <template v-slot:body-cell-delayed_items="props">
            <q-td
              v-bind="{
                props,
                class: {
                  'single-cell-with-warning': props.value.length !== 0
                }
              }"
            >
              {{ props.value }}
            </q-td>
          </template>

          <template v-slot:top>
            <q-btn
              flat
              dense
              color="primary"
              v-bind="{ disable: loading || selected.length === 0 }"
              label="Pobierz zużycia - xml"
              v-on:click="getUsageForSelected"
            />
            <q-btn
              class="on-right"
              flat
              dense
              color="primary"
              v-bind="{ disable: loading || selected.length === 0 }"
              label="Pobierz pliki na kartoniarke"
              v-on:click="getFilesForCardboard"
            />
            <q-btn
              class="on-right"
              flat
              dense
              color="primary"
              v-bind="{ disable: loading || selected.length === 0 }"
              label="Pobierz programy CNC"
              v-on:click="getCncPrograms"
            />
            <q-btn
              class="on-right"
              flat
              dense
              color="primary"
              v-bind="{ disable: loading || selected.length === 0 }"
              label="Pobierz pliki pakowania"
              v-on:click="getPackagingFiles"
            />
            <q-btn
              class="on-right"
              flat
              dense
              color="primary"
              v-bind="{ disable: loading || selected.length === 0 }"
              label="Pobierz pliki produkcyjne"
              v-on:click="getProductionFiles"
            />
          </template>
          <template v-slot:body-cell-actions="props">
            <q-td v-bind="{ props }">
              <div class="flex column items-end">
                <div class="flex flex-center">
                  <a
                    class="q-pa-xs"
                    v-bind="{ href:`/pages/producer_batch/${props.row.batch_id}` }"
                  >
                    <q-btn
                      color="primary"
                      label="Szczegóły"
                      class="q-mr-lg"
                    />
                  </a>
                </div>
                <div class="flex flex-center">
                  <a
                    class="q-pa-xs"
                    v-bind="{ href:`/pages/api/production_files/?batch_ids=${props.row.batch_id}` }"
                  >
                    <q-btn
                      color="primary"
                      label="Pobierz pliki produkcyjne"
                    />
                  </a>
                  <FileStatusCircle v-bind:status="props.row.production_files_status" />
                </div>
                <div class="flex flex-center">
                  <a
                    class="q-pa-xs"
                    v-bind="{ href:`/pages/api/packaging_files/?batch_ids=${props.row.batch_id}` }"
                  >
                    <q-btn
                      color="primary"
                      label="Pobierz pliki od pakowania"
                    />
                  </a>
                  <FileStatusCircle v-bind:status="props.row.packaging_files_status" />
                </div>
                <div class="flex flex-center">
                  <a
                    class="q-pa-xs"
                    v-bind="{ href:`/pages/api/cnc_connections/?batch_ids=${props.row.batch_id}` }"
                  >
                    <q-btn
                      color="primary"
                      label="Pobierz programy CNC"
                    />
                  </a>
                  <FileStatusCircle v-bind:status="props.row.cnc_files_status" />
                </div>
              </div>
            </q-td>
          </template>
        </BaseTable>
      </div>
    </div>
  </q-page>
</template>

<script>
import FileStatusCircle from '../components/FileStatusCircle.vue';
import BaseTable from '../components/BaseTable.vue';
import { downloadFile, getProductionProgressInfo } from '../utils/helpers.js';
import { BATCH_TYPE, BATCH_STATUS } from '../utils/consts.js';

export default {
  name: 'BatchesView',
  components: { FileStatusCircle, BaseTable },
  props: {
    complaintList: Boolean,
    isHistory: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      apiUrl: '/pages/api/producer_batches_rest/',
      loading: false,
      selected: [],
      batchType: '',
      isDelayed: false,
      items: '',
      batchIds: '',
      batchTypeOptions: [
        { label: 'NOT_SET', value: BATCH_TYPE.NOT_SET },
        { label: 'STANDARD', value: BATCH_TYPE.STANDARD },
        { label: 'EXTENDED', value: BATCH_TYPE.EXTENDED },
        { label: 'CUSTOM', value: BATCH_TYPE.CUSTOM },
      ],
      batchStatus: '',
      batchStatusOptions: [
        { label: 'Aborted', value: BATCH_STATUS.ABORTED },
        { label: 'Waiting for nesting', value: BATCH_STATUS.NEW },
        { label: 'In production', value: BATCH_STATUS.IN_PRODUCTION },
        { label: 'Sent to customer', value: BATCH_STATUS.SENT_TO_CUSTOMER },
      ],
      materialDescription: '',
    };
  },
  computed: {
    filters() {
      return {
        batch_ids: this.batchIds,
        items: this.items,
        status: this.batchStatus && this.batchStatus.value,
        batch_type: this.batchType && this.batchType.value,
        material_description: this.materialDescription,
        complaints_list: this.complaintList,
        is_history: this.isHistory,
        is_delayed: this.isDelayed,
      };
    },
    columns() {
      return [
        {
          name: 'actions_needed', align: 'center', label: 'File alert!', field: row => row.actions_needed,
        },
        {
          name: 'name',
          required: true,
          label: 'Batch',
          align: 'left',
          field: 'batch_id',
        },
        ...this.isHistory ? [{
          name: 'days',
          align: 'center',
          label: 'Dni realizacji',
          // eslint-disable-next-line camelcase
          field: ({ days_of_production }) => days_of_production,
        }] : [{
          name: 'production_progress',
          align: 'center',
          label: 'Stopień wykonania',
          // eslint-disable-next-line camelcase
          field: ({ completed_products }) => getProductionProgressInfo(completed_products),
        },
        ],
        {
          name: 'batch_status', align: 'center', label: 'Status', field: row => row.batch_status,
        },
        ...this.complaintList
          ? []
          : [{
            name: 'batch_type', align: 'center', label: 'Typ', field: row => `${row.batch_type}\n${row.is_sidebohr ? 'S+' : ''}`, classes: 'break-lines',
          }],
        {
          name: 'area', align: 'center', label: 'Płyta [m^2]', field: row => row.area.toFixed(2),
        },
        {
          name: 'banding_length', align: 'center', label: 'Obrzeże [mb]', field: row => row.banding_length,
        },
        {
          name: 'material_description', align: 'center', label: 'Kolor', field: row => row.material_description,
        },
        {
          name: 'created_at', label: 'Dodano', field: 'created_at',
        },
        {
          name: 'items', label: 'ID produktów', field: row => row.items.join(', '), classes: 'items-column',
        },
        {
          name: 'priorities',
          label: 'Priorytety',
          field: row => Object.entries(row.priorities).map(([key, value]) => `${key}: ${value}`).join('\n'),
          classes: 'break-lines',
        },
        {
          name: 'element_order_ids', label: 'Zamówienie', field: row => row.element_order_ids.join(', '),
        },
        {
          name: 'delayed_items', label: 'Opóźnienia', field: row => row.delayed_items.join(', '), classes: 'items-column',
        },
        { name: 'actions', label: 'Akcje', style: 'min-width: 300px' },
      ];
    },
  },
  watch: {
    complaintList() {
      this.$refs.tableRef.getData(null, { is_history: this.isHistory, complaints_list: this.complaintList });
    },
    isHistory() {
      this.$refs.tableRef.getData(null, { is_history: this.isHistory, complaints_list: this.complaintList });
    },
  },
  methods: {
    updateSelected(selected) {
      this.selected = selected;
    },
    updateLoading(loading) {
      this.loading = loading;
    },
    filterData() {
      this.$refs.tableRef.getData();
    },
    getApiUrlWithBatchesIds(endpointName) {
      return `/pages/api/${endpointName}/?batch_ids=${this.selected.map(x => x.batch_id)}`;
    },
    downloadFileFromApi(apiUrl) {
      this.$axios({
        method: 'GET',
        url: apiUrl,
        responseType: 'blob',
      }).then(response => {
        downloadFile(response);
        this.filterData();
      })
        .catch(error => this.$q.notify({ message: error.message, type: 'negative' }));
    },
    getUsageForSelected() {
      this.$axios({
        method: 'POST',
        url: '/pages/api/producer_actions_rest/',
        data: {
          action: 'getUsageXml',
          batches: this.selected.map(x => x.batch_id),
        },
        responseType: 'blob',
      }).then(response => {
        downloadFile(response);
      })
        .catch(error => this.$q.notify({ message: error.message, type: 'negative' }));
    },
    getFilesForCardboard() {
      this.$axios({
        method: 'POST',
        url: '/pages/api/producer_actions_rest/',
        data: {
          action: 'getCardboardFile',
          batches: this.selected.map(x => x.batch_id),
        },
        responseType: 'blob',
      }).then(response => {
        downloadFile(response);
      })
        .catch(error => this.$q.notify({ message: error.message, type: 'negative' }));
    },
    getCncPrograms() {
      this.downloadFileFromApi(this.getApiUrlWithBatchesIds('cnc_connections'));
    },
    getPackagingFiles() {
      this.downloadFileFromApi(this.getApiUrlWithBatchesIds('packaging_files'));
    },
    getProductionFiles() {
      this.downloadFileFromApi(this.getApiUrlWithBatchesIds('production_files'));
    },
  },
};
</script>
