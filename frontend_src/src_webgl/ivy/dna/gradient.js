
module.exports =         {
            generateWalls: function(parameter, shelfWidth, rowsNum, rowsH, shelfDepth){
              /////////////////// INPUTS //////////////////////////////
                  // var shelftDepth = glebokosc polki
                  // var shelfWidth = szerokosc polki
                  // var rowsNum = ilosc rzedow
                  // var rowsH = wykosci rzedow
                  // var parameter = parametr odkształcenia (0-100)

                  var Point3d = Vector3d = THREE.Vector3;

                  // Zmienne pomocnicze
                  var ptLoc = new Point3d(-shelfWidth / 2, 0, 0);
                  var i = 0;

                  shelfWidth = parseInt(shelfWidth * 0.1) * 10;

                  // ROW ABSOLUTE - wysokosci bezwzgledne rzedow
                  var rowAbsolute = [];

                  rowAbsolute.push(0);

                  for (i = 0; i < rowsH.length; i++)
                  {
                    rowAbsolute.push(rowAbsolute[i] + rowsH[i]);
                  }

                  ///////////////////////////////// SNAPPING /////////////////////////////
                  // SNAPPING - parametr
                  var snapParamSide = 5;
                  var snapParamMiddle = 5;

                  // gradient property
                  var parameterSnapped = parameter;

                  // snapping parametru dla strategicznych wartosci
                  // dla 0
                  if (parameter < (0 + snapParamSide))
                  {
                    parameterSnapped = 0;
                  }
                  // dla 50
                  if ((parameter > (50 - snapParamMiddle)) && (parameter < (50 + snapParamMiddle)))
                  {
                    parameterSnapped = 50;
                  }
                  // dla 100
                  if (parameter > (100 - snapParamSide))
                  {
                    parameterSnapped = 100;
                  }

                  //////////////////////////////// HORIZONTALS /////////////////////////////
                  var ptHorizontalsAll = [];

                  // HORIZONTALS - lewa strona
                  // pierwszy punkt, dolny rzad lewa strona, jako (-shelfWidth/2,0,0)
                  // ptHorizontalLeft.push(ptLoc); // TODO niepotrzebne???
                  // pozostale rzedy lewa strona

                  for (i = 0; i <= rowsNum; i++) {
                    ptHorizontalsAll.push(new Point3d(-shelfWidth / 2, rowAbsolute[i], 0)); // left
                    ptHorizontalsAll.push(new Point3d(shelfWidth / 2, rowAbsolute[i], 0)); // right
                  }

                  // USTALANIE ZMIENNYCH
                  // szerokosc szafki w prawo
                  var maxX = shelfWidth / 2;

                  // punkt zero
                  var minX = shelfWidth / 2 * (-1);

                  // szerokosc plecow
                  var gradientSupportsWidth = 125;

                  // szerokosc materialu
                  var mat = 18;
                  var matHalf = mat / 2;

                  // min support spacing
                  var minSupportsSpacing = gradientSupportsWidth + 125 + mat;

                  // supports - shift to front [cm]
                  var supportsShift = 2;


                  //ZMIENNE POMOCNICZE

                  var pos = 0;
                  var leftDiv;
                  var rightDiv;
                  var minDist = 0;

                  // OUTPUT LISTS
                  // VERTICALS & SUPPORTS

                  var locationPoint = new Point3d();

                  var ptVerticalsAll = [];

                  var ptSupportsBottom = [];
                  var ptSupportsTop = [];
                  var ptSupportsAll = [];

                  // gradient
                  var rowRelative = [];
                  for ( i = 0; i < rowsH.length; i++)
                  {
                    rowRelative.push(rowsH[i]);
                  }


                  // OPISY POSZCZEGOLNYCH SYTUACJI DIV
                  var div0 = [];
                  var div1 = [0.407843];
                  var div2 = [0.229412, 0.5625];
                  var div3 = [0.148784, 0.364807, 0.648546];
                  var div4 = [0.095296, 0.233659, 0.415395, 0.640501];


                  // BREAKPOINTS
                  if(maxX - minX < 1100 - mat){ // breakpoint 0
                    minDist = 102;

                    if((parameterSnapped * 0.01) * (maxX - minX) < (minSupportsSpacing + minDist / 2)){
                      leftDiv = div0;
                      rightDiv = div1;

                    } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2)){
                      leftDiv = div0;
                      rightDiv = div0;

                    } else {
                      leftDiv = div1;
                      rightDiv = div0;
                    }

                  } else if(maxX - minX < 1450 - mat){ // breakpoint 1
                    minDist = 102;

                    if((parameterSnapped * 0.01) * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div0;
                      rightDiv = div2;

                    } else if(parameterSnapped * 0.01 < 0.44){
                      leftDiv = div0;
                      rightDiv = div1;

                    } else if(parameterSnapped * 0.01 < 0.56){
                      leftDiv = div1;
                      rightDiv = div1;

                    } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div1;
                      rightDiv = div0;

                    } else {
                      leftDiv = div2;
                      rightDiv = div0;
                    }

                  } else if (maxX - minX < 2200 - mat){//breakpoint 2
                    minDist = 126;

                    if(parameterSnapped * 0.01 * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div0;
                      rightDiv = div3;

                    } else if(parameterSnapped * 0.01 < 0.4){
                      leftDiv = div0;
                      rightDiv = div2;

                    } else if(parameterSnapped * 0.01 < 0.6){
                      leftDiv = div1;
                      rightDiv = div1;

                    } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div2;
                      rightDiv = div0;

                    } else {
                      leftDiv = div3;
                      rightDiv = div0;

                    }

                  } else { // breakpoint 3
                    minDist = 150;

                    if(parameterSnapped * 0.01 * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div0;
                      rightDiv = div4;

                    } else if(parameterSnapped * 0.01 < 0.25){
                      leftDiv = div0;
                      rightDiv = div3;

                    } else if(parameterSnapped * 0.01 < 0.42){
                      leftDiv = div1;
                      rightDiv = div3;

                    } else if(parameterSnapped * 0.01 < 0.58){
                      leftDiv = div2;
                      rightDiv = div2;

                    }  else if(parameterSnapped * 0.01 < 0.75){
                      leftDiv = div3;
                      rightDiv = div1;

                    } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
                      leftDiv = div3;
                      rightDiv = div0;

                    } else {
                      leftDiv = div4;
                      rightDiv = div0;
                    }
                  }

                  // minimalna szerokosc polki (minimal distance to place a support)
                  var minDistToPlaceSupports = parseInt(minDist * 1.4 + mat);

                  // POSITION OF THE MIDPOINT
                  //find the position of midpoint (w milimetrach)
                  pos = parseInt(minX + ((parameterSnapped * 0.01) * (maxX - minX)));

                  //avoid moving the midpoint too close to a border.
                  if(pos < minX + (minSupportsSpacing + minDist / 2)) {
                    pos = minX + minDist / 2 + matHalf;
                  }
                  else if (pos > maxX - (minSupportsSpacing + minDist / 2)) {
                    pos = maxX - (minDist / 2) - matHalf;
                  }


                  // points - verticals
                  //iterate over every row independently

                  var currentBottomVert;
                  var prevBottomVert;
                  var prevTopVert;

                  for( i = 0; i < rowsNum; i++)
                  {
                    ///////////////////////// VERTICALS /////////////////////////////
                    //
                    // points - verticals  - side left
                    if(pos - minDist / 2 > minX + minDist)
                    {
                      ptVerticalsAll.push(new Point3d(minX + matHalf, rowAbsolute[i] + matHalf, 0));
                      ptVerticalsAll.push(new Point3d(minX + matHalf, rowAbsolute[i + 1] - matHalf, 0));
                    }

                    //points - verticals  - left side from the middle point
                    for(var d = leftDiv.length - 1; d >= 0; d--)
                    {
                      // ptLeftA[i, d] = new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i] + matHalf, 0);
                      // ptLeftB[i, d] = new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i + 1] - matHalf, 0);
                      ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i] + matHalf, 0)); // push bottom
                      ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i + 1] - matHalf, 0)); // push top point

                      ///////////////////////// SUPPORTS /////////////////////////////
                      // supports on the left side
                      if((d == 1 && leftDiv.length >= 2) || (d == 3 && leftDiv.length >= 4)) //create a support every second wall
                      {
                        currentBottomVert = ptVerticalsAll.length - 2;
                        prevBottomVert = currentBottomVert - 2;
                        prevTopVert = ptVerticalsAll.length - 3;

                        // points for supports - left side from the middle point
                        if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports) //if there is enough space between walls
                        {
                          ptSupportsAll.push(new Point3d ((ptVerticalsAll[prevBottomVert].x - matHalf), ptVerticalsAll[prevBottomVert].y, supportsShift));
                          ptSupportsAll.push(new Point3d (ptVerticalsAll[prevTopVert].x - matHalf - gradientSupportsWidth, ptVerticalsAll[prevTopVert].y, supportsShift));
                        }
                      }
                    }

                    // points - verticals  - middle
                    ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i] + matHalf, 0));
                    ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i + 1] - matHalf, 0));
                    ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i] + matHalf, 0));
                    ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i + 1] - matHalf, 0));

                    //points - verticals  - right side from the middle point
                    for(var d = 0; d < rightDiv.length; d++)
                    {
                      // ptRightA[i, d] = new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i] + matHalf, 0);
                      // ptRightB[i, d] = new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i + 1] - matHalf, 0);
                      ptVerticalsAll.push(new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i] + matHalf, 0)); // push bottom point
                      ptVerticalsAll.push(new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i + 1] - matHalf, 0)); // push top point

                      ///////////////////////// SUPPORTS /////////////////////////////
                      // supports on the right side
                      if((d == 1 && rightDiv.length >= 2) || (d == 3 && rightDiv.length >= 4)) //create a support every second wall
                      {
                        currentBottomVert = ptVerticalsAll.length - 2;
                        prevBottomVert = currentBottomVert - 2;
                        prevTopVert = ptVerticalsAll.length - 3;

                        // points for supports - right side from the middle point
                        if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports) //if there is enough space between walls
                        {
                          ptSupportsAll.push(new Point3d ((ptVerticalsAll[prevBottomVert].x + matHalf), ptVerticalsAll[prevBottomVert].y, supportsShift));
                          ptSupportsAll.push(new Point3d (ptVerticalsAll[prevTopVert].x + matHalf + gradientSupportsWidth, ptVerticalsAll[prevTopVert].y, supportsShift));
                        }
                      }
                    }

                    // points - verticals  - side right
                    if(maxX - pos > minDist)
                    {
                      ptVerticalsAll.push(new Point3d(maxX - matHalf, rowAbsolute[i] + matHalf, 0));
                      ptVerticalsAll.push(new Point3d(maxX - matHalf, rowAbsolute[i + 1] - matHalf, 0));
                    }

                    ///////////////////////// SUPPORTS /////////////////////////////
                    // supporst on edges EDGES
                    // points for supports - left side edge
                    if(pos > minX + (minDist * 2 + mat) && (parameterSnapped * 0.01 > 0.49 || maxX - minX >= 90))
                    {
                      ptSupportsAll.push(new Point3d (minX + mat, rowAbsolute[i] + matHalf, supportsShift));
                      ptSupportsAll.push(new Point3d (minX + mat + gradientSupportsWidth, rowAbsolute[i + 1] - matHalf, supportsShift));
                    }

                    // points for supports - right side edge
                    if(pos < maxX - (minDist * 2 + mat) && (parameterSnapped * 0.01 < 0.51 || maxX - minX >= 90))
                    {
                      ptSupportsAll.push(new Point3d (maxX - mat - gradientSupportsWidth, rowAbsolute[i] + matHalf, supportsShift));
                      ptSupportsAll.push(new Point3d (maxX - mat, rowAbsolute[i + 1] - matHalf, supportsShift));
                    }
                  }

                  ////////////////////// SHADOWS ////////////////////////////
                  var ptVerticalsBottom0 = []; // spody verticals na poziomie 0
                  var shadowLocation = []; // srodki cieni
                  var shadowSize = []; // wielkosc cieni

                  // VERTICALS - bottom - pierwszy rzad [0]
                  for ( i = 0; i < ptVerticalsAll.length; i++)
                  {
                    if (ptVerticalsAll[i].y == mat / 2) {
                      ptVerticalsBottom0.push(ptVerticalsAll[i]);
                    }
                  }
                  // SHADOWS - location & size
                  for ( var n = 0; n < rowsNum; n++)
                  {
                    for ( i = 0; i < ptVerticalsBottom0.length - 1; i++)
                    {
                      shadowLocation.push(new Vector3d(ptVerticalsBottom0[i].x + (ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x) / 2, rowAbsolute[n] + (rowAbsolute[n + 1] - rowAbsolute[n]) / 2, shelfDepth / 2));
                      shadowSize.push(new Vector3d(ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x - mat, rowAbsolute[n + 1] - rowAbsolute[n] - mat, shelfDepth));
                    }
                  }

                  //////////////////// OUTPUTS //////////////////////////////
                  // var ptHorizontalsAll - weaved ends of horizontals (left[0], right[0], left[1], right[1], ...)
                  // var ptVerticalsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)
                  // var ptSupportsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)
                  // var shadowLocation - location of shadows boxes
                  // var shadowSize - sizes of shadows boxes

                return {


                  //shadows
                  shadowCenters: shadowLocation,
                  shadowSizes: shadowSize,
                  ptVerticalsAll: ptVerticalsAll,
                  ptHorizontalsAll: ptHorizontalsAll,
                  ptSupportsAll: ptSupportsAll
                }

      }

}