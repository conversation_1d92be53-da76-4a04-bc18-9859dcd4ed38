const measurementSettings = {
    width: {
        color: 0xFFFFFF,
        offsetX: 0,
        offsetY: 1,
        offsetZ: 0.5,
    },
    height: {
        color: 0x4A4A4A,
        offsetX: 1.9,
        offsetY: 0,
        offsetZ: 0.5,
    },
    depth: {
        color: 0x4A4A4A,
        offsetX: 0,
        offsetY: 0,
        offsetZ: 0,
    },
};

const dimPillsSettings = {
    cplus: {
        ...measurementSettings,
        pillSizeParams: {
            shelfSizeThresholdMin: 700,
            shelfSizeThresholdMax: 4500,
            pillSizeMin: 20,
            pillSizeMax: 36,
        },
    },
    crow: {
        ...measurementSettings,
        pillSizeParams: {
            shelfSizeThresholdMin: 700,
            shelfSizeThresholdMax: 4500,
            pillSizeMin: 26,
            pillSizeMax: 36,
        },
    },
    wardrobe: {
        ...measurementSettings,
        pillSizeParams: {
            shelfSizeThresholdMin: 700,
            shelfSizeThresholdMax: 6000,
            pillSizeMin: 30,
            pillSizeMax: 46,
        },
    },
};

export {
    dimPillsSettings,
};
