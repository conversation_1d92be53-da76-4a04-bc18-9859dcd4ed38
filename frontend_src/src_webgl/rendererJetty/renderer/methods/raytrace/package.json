{"name": "shadow-raytrace", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "gulp build-shaders && rollup -c rollup.config.js", "test": ""}, "author": "", "license": "ISC", "dependencies": {"babel-core": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2015-rollup": "^3.0.0", "babel-preset-react": "^6.24.1", "babelify": "^8.0.0", "bl": "^1.2.2", "brfs": "^1.6.1", "browserify": "^16.2.0", "dat.gui": "^0.7.1", "gl": "^4.0.4", "gl-mat4": "^1.1.4", "glsl-combine-chamfer": "^1.0.0", "glsl-combine-smooth": "^1.0.0", "glsl-deparser": "^1.0.0", "glsl-look-at": "^1.0.0", "glsl-min-stream": "^1.0.0", "glsl-noise": "0.0.0", "glsl-parser": "^2.0.0", "glsl-quad": "^1.0.0", "glsl-raytrace": "^1.0.0", "glsl-sdf-box": "^1.0.0", "glsl-sdf-normal": "^1.0.0", "glsl-sdf-ops": "0.0.3", "glsl-specular-phong": "^1.0.0", "glsl-tokenizer": "^2.1.2", "glsl-turntable-camera": "^1.0.0", "glslify": "^6.1.1", "glslify-import": "^3.1.0", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-each": "^0.5.0", "gulp-flatmap": "^1.0.2", "gulp-tap": "^1.0.1", "gulp-util": "^3.0.8", "gulp-watch": "^5.0.0", "node-readfiles": "^0.2.0", "npm": "^5.8.0", "raw-body": "^2.3.3", "readable-stream": "^2.3.6", "regl": "^1.3.1", "rollup": "^0.57.1", "rollup-plugin-commonjs": "^9.1.0", "rollup-plugin-inline-js": "^0.1.0", "rollup-plugin-node-resolve": "^3.3.0", "rollupify": "^0.4.0", "stream-buffers": "^3.0.2", "string-to-stream": "^1.1.1", "stringify": "^5.2.0", "through2": "^2.0.3", "vinyl": "^2.1.0", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0", "vinyl-transform": "^1.0.0"}, "devDependencies": {"eslint": "^4.19.1", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.11.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-react": "^7.7.0", "gulp-brfs": "^0.1.0", "gulp-debug": "^4.0.0", "gulp-glslify": "git+https://github.com/yuichiroharai/gulp-glslify.git", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-browserify-transform": "^1.0.1"}}