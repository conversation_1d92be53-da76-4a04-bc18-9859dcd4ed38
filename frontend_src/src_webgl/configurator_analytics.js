var Analytics = function() {

     this.waiting_events = [];
     this.started_time = null;
     this.ended_time = null;
     this.actual_event = null;

     this.event_counter = [];

     this.extended_configurator_sent_5 = false;

     this.extended_configurator_sent_5_with_row_changes = false;
     this.extended_configurator_with_row_changes = false;

     return this;
 };


 Analytics.prototype = {
     sendDataLayer: function (event, payload) {
         if (window.Site && Site.Track) {
             Site.Track.sendDataLayer(event, payload);
         }

         if (event.startsWith('conf_Click')) {
             return;
         }

         if (isLocalStorage !== false) {
             let conf = JSON.parse(window.localStorage.getItem('conf_data')) || {};
             conf[event] = conf[event] ? conf[event] + 1 : 1;
             window.localStorage.setItem('conf_data', JSON.stringify(conf));
         }

         if (this.event_counter.length < 40 && !(this.extended_configurator_sent_5 &&
             this.extended_configurator_sent_5_with_row_changes &&
             this.extended_configurator_with_row_changes)) {
             this.event_counter.push(event);
         }

         if (this.event_counter.length >= 5 && this.extended_configurator_sent_5 === false) {
             Site.Track.sendDataLayer('conf_smart_goal', {'eventAction': "5"});
             this.extended_configurator_sent_5 = true;
         }
         if (this.event_counter.length >= 5 &&
             this.extended_configurator_sent_5_with_row_changes === false &&
             this.event_counter.filter(x=>['conf_RowChange', 'conf_DoorChange', 'conf_DrawerChange' ].indexOf(x) > -1).length > 0) {
             Site.Track.sendDataLayer('conf_smart_goal', {'eventAction': "5_and_row_change"});
             this.extended_configurator_sent_5_with_row_changes = true;
         }

          if (this.event_counter.length >= 1 &&
             this.extended_configurator_with_row_changes === false &&
             this.event_counter.filter(x=>['conf_RowChange', 'conf_DoorChange', 'conf_DrawerChange' ].indexOf(x) > -1).length > 0) {
             Site.Track.sendDataLayer('conf_smart_goal', {'eventAction': "row_change"});
             this.extended_configurator_with_row_changes = true;
         }

     },

    trackWidth: function() {
        this.actual_event = 'Width';
        this.started_time = new Date();
    },
    trackHeight: function() {
        this.actual_event = 'Height';
        this.started_time = new Date();
    },
    trackProperty: function() {
        this.actual_event = 'Property';
        this.started_time = new Date();
    },
    trackDnaStart: function(dna_name) {
        if (window.Site && Site.Track) {
            Site.Track.sendDataLayer('conf_DnaStart', {'eventAction': "" + dna_name});
        }
    },
    trackDnaChange: function(dna_name) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_DnaChange', {'eventAction':"" +dna_name});
    },
    trackDepthChange: function(depth) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_DepthChange', {'eventAction':"" + depth});
    },
    trackBackpanelsChange: function(backpanels) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_BackpanelChange', {'eventAction':"" + backpanels});
    },
    trackColorChange: function(color_name) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_ColorChange', {'eventAction':"" +color_name});
    },
    trackRowSelect: function(row_number) {
        if (window.Site && Site.Track) {
            Site.Track.sendDataLayer('conf_RowSelect', {'eventAction': "" + row_number});
        }
    },
    trackRowSelectJoystick: function(row_number) {
        if (window.Site && Site.Track) {
            Site.Track.sendDataLayer('conf_RowSelectJoystick', {'eventAction': "" + row_number});
        }
    },
    trackRowChange: function(row_number, changed_to) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_RowChange', {'eventAction':"" +row_number, 'eventValue': "" +changed_to});
    },
    trackDoorChange: function(row_number, changed_to) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_DoorChange', {'eventAction':"" +row_number, 'eventValue': "" +changed_to});
    },
    trackDrawerChange: function(row_number, changed_to) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_DrawerChange', {'eventAction':"" + row_number, 'eventValue':"" + changed_to});
    },
    trackDoorToggle: function(value) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_DoorToggle', {'eventAction': "" +value});
    },
    trackClickDimensions: function() {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_ClickDimensions', {'eventAction': ""});
    },
    trackClickGallery: function(image_id) {
        this.sendDataLayer('conf_Gallery', {'eventAction': image_id});
    },
    trackClickConfiguratorView: function() {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        this.sendDataLayer('conf_ClickConfigurator', {'eventAction': ""});
    },
    trackEnd: function(ivy_object) {
        if (window.Site && Site.Track) {
            Site.Track.trackCustomization();
        }
        if (this.actual_event == "Width"){
            this.sendDataLayer("conf_" + this.actual_event, {'eventValue':(parseInt(ivy_object['get' + this.actual_event]()/10)).toString(), 'eventAction': 'width value'});
        }
        if (this.actual_event == 'Height'){
            this.sendDataLayer("conf_" + this.actual_event, {'eventValue':(parseInt(ivy_object['get' + this.actual_event]()/10)).toString(), 'eventAction': 'height value'});
        }
        if (this.actual_event == "Property"){
            this.sendDataLayer("conf_" + this.actual_event, {'eventValue':(ivy_object['get' + this.actual_event]()).toString(), 'eventAction': 'property nr'});
        }

    },
    trackPresetClick: function(category, index) {
        this.sendDataLayer('conf_PresetLoad', {'eventAction': "" +category + "_" + index});
    },
 };

module.exports = Analytics;
