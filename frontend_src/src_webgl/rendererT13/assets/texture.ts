import { Source, TextureLoader, <PERSON>ubeTextureLoader, CubeTexture, Texture } from "three";
import { flow } from "lodash-es";
import { Modifier } from "../common/modifiers";
import { ASSETS_SOURCE } from "../common/config"

export namespace TextureAssets {
    export const identifier = {
        PLYWOOD_EDGE_ALBEDO: 'plywood_edge_albedo',
        PLYWOOD_FACE_ALBEDO: 'plywood_face_albedo',
        VENEER_FACE_ALBEDO: 'veneer_face_albedo',
        VENEER_BUMP: 'veneer_bump',
        INTERIOR_ENV_MAP: 'interiorEnvMap',
        INNER_AO_MAP: 'innerAOMap',
        BG_LIGHTMAP: 'bg_shadow',
        BG_AO: 'bg_ao',
        BG_ALPHA: 'bg_alpha',
        FAKE_RAYS: 'fake_rays',
        UV_CHECKER: 'uv_checker',
    }

    const _configs = [
        { identifier: identifier.PLYWOOD_EDGE_ALBEDO, fileName: `${ASSETS_SOURCE}/texture/plywood_edge_512_albedo.png` },
        // { identifier: identifier.PLYWOOD_FACE_ALBEDO, fileName: `${ASSETS_SOURCE}/texture/plywood.png` },
        { identifier: identifier.VENEER_FACE_ALBEDO, fileName: `${ASSETS_SOURCE}/texture/veneer.png` },
        { identifier: identifier.VENEER_BUMP, fileName: `${ASSETS_SOURCE}/texture/veneer_bump.png` },
        { identifier: identifier.UV_CHECKER, fileName: `${ASSETS_SOURCE}/texture/uv_checker.jpeg` },
        { identifier: identifier.INNER_AO_MAP, fileName: `${ASSETS_SOURCE}/texture/inner_ao_512.png` },
        { identifier: identifier.BG_LIGHTMAP, fileName: `${ASSETS_SOURCE}/texture/bg_shadow.png` },
        { identifier: identifier.BG_AO, fileName: `${ASSETS_SOURCE}/texture/bg_ao.png` },
        { identifier: identifier.BG_ALPHA, fileName: `${ASSETS_SOURCE}/texture/bg_alpha.png` },
        { identifier: identifier.FAKE_RAYS, fileName: `${ASSETS_SOURCE}/texture/fake_rays_256.png` },
    ]

    const _envMaps = [
        { identifier: identifier.INTERIOR_ENV_MAP, fileNames: [
                `${ASSETS_SOURCE}/env/px.jpg`,
                `${ASSETS_SOURCE}/env/nx.jpg`,
                `${ASSETS_SOURCE}/env/py.jpg`,
                `${ASSETS_SOURCE}/env/ny.jpg`,
                `${ASSETS_SOURCE}/env/pz.jpg`,
                `${ASSETS_SOURCE}/env/nz.jpg`,
            ]
        },
    ]

    const _textureLoader = new TextureLoader();
    const _cubeTextureLoader = new CubeTextureLoader();

    export namespace Repository {
        const _cache: Map<string, Source> = new Map();
        const _envs: Map<string, CubeTexture> = new Map();

        const fetchTextures = (): Promise<any> => {
            return new Promise((resolve) => {
                _configs.map((config, idx) => {
                    _textureLoader.load(config.fileName, (texture) => {
                        _cache.set(config.identifier, texture.source);
                        if (idx === _configs.length - 1) resolve(_cache);
                    });

                })
            })
        }

        const fetchEnvMaps = (): Promise<any> => {
            return new Promise((resolve) => {
                _envMaps.map((config, idx) => {
                    _cubeTextureLoader.load(config.fileNames, (texture) => {
                        _envs.set(config.identifier, texture);
                        if (idx === _envMaps.length - 1) resolve(_envs);
                    });

                })
            })
        }

        export const fetch = (): Promise<any> => {
            return new Promise((resolve) => {
                fetchTextures().then(() => {
                    fetchEnvMaps().then(() => {
                        resolve(_cache)
                    })
                })
            })
        }

        export const getImageSourceByName = (assetName: string) => _cache.get(assetName)!;

        export const getEnvironmentMap = () => _envs.get(identifier.INTERIOR_ENV_MAP)!;

        export const getTexture = (uvCords: any, assetName: string) => flow(
            Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName(assetName)),
            Modifier.unwrapUVTexture(uvCords)
        )(new Texture())
    }
}

export const getUVWrapper = (mapRatio: { u: number, v: number, w: number }, crop: number, mode: 'plane' | 'edge' | 'none') => {
    return {
        plane: { ...calculateUV(mapRatio.u, mapRatio.v, crop), rotation: 0 },
        edge: { ...calculateUV(mapRatio.u, 0.002, crop), rotation: 0 },
        none: { ...calculateUV(mapRatio.u, mapRatio.v, crop), rotation: 0 },
    }[mode];
}

const calculateUV = (planeWidth: number, planeHeight: number, compare: number, x: number = 1, y: number = 1) => {
    let repeatX, repeatY;
    repeatX = x * planeWidth / y * planeHeight;

    if (repeatX > 1) {
        repeatX = 1;
        repeatY = ((y * planeHeight) / (x * planeWidth));

        repeatX = planeWidth / compare;
        repeatY = repeatY * repeatX;
        return {
            repeat: { x: repeatX, y: repeatY },
            offset: { x: 0, y: (repeatY - 1) / 2 * -1 }
        }
    } else {
        repeatX = ((x * planeWidth) / (y * planeHeight));
        repeatY = 1;

        repeatY = planeHeight / compare;
        repeatX = repeatX * repeatY;
        return {
            repeat: { x: repeatX, y: repeatY, },
            offset: { x: (repeatX - 1) / 2 * -1, y: 0, }
        }
    }
}

