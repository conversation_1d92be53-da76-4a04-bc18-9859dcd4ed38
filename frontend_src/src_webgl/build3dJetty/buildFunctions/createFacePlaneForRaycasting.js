import * as THREE from "three";

function createFacePlaneForRaycasting() {

        let geometry = new THREE.BoxGeometry(2000, 1000, 1);
        let material = new THREE.MeshBasicMaterial();
        material.color = new THREE.Color(0xff0000);
        material.opacity = 1;
        material.transparent = true;
        let box = new THREE.Mesh(geometry, material);

        if (!this.scene) {
            this.scene = new THREE.Scene();
        }
        this.facePlane = box;
        this.scene.add(box);
    }

export { createFacePlaneForRaycasting }